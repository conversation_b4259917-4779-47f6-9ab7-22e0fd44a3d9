{"tax_calculator.withholding_box.acc_nz": "", "tax_calculator.africa.africa": "", "tax_calculator.withholding_box.daily_allowance_contribution_fi": "", "tax_calculator.withholding_box.amo_ma": "", "tax_calculator.withholding_box.aporte_trabajador_cr": "", "tax_calculator.withholding_box.arbejdsmarkedets_tillaegspension_denmark": "", "tax_calculator.asia_pacitic.asia": "", "tax_calculator.withholding_box.auxiliar_de_transporte_co": "", "tax_calculator.tax_per_region.average_tax": "", "tax_calculator.withholding_box.average_tax_rate": "", "tax_calculator.enter_box.biweekly": "", "tax_calculator.summary_box.bonus_example": "", "tax_calculator.note_box.note": "", "tax_calculator.withholding_box.bracket_tax_no": "", "tax_calculator.tax_per_region.british_columbia": "", "tax_calculator.withholding_box.public_broadcasting_tax_fi": "", "tax_calculator.withholding_box.inss_br": "", "tax_calculator.enter_box.calculate": "", "tax_calculator.withholding_box.cantonal_tax_ch": "", "tax_calculator.withholding_box.cpf_sg": "", "tax_calculator.withholding_box.church_tax": "", "tax_calculator.feedback_box.click_here": "", "tax_calculator.withholding_box.cnss_ma": "", "tax_calculator.withholding_box.common_tax_no": "", "tax_calculator.withholding_box.communal_tax_be": "", "tax_calculator.compare_box.compare_average": "", "tax_calculator.withholding_box.frl_uy": "", "tax_calculator.europe.country": "", "tax_calculator.withholding_box.cpp_deduction": "", "tax_calculator.withholding_box.crds_fr": "", "tax_calculator.withholding_box.csg_fr": "", "tax_calculator.enter_box.day": "", "tax_calculator.withholding_box.dependency_insurance": "", "tax_calculator.taxberg_box.did_you_know": "", "tax_calculator.withholding_box.direct_federal_tax_ch": "", "tax_calculator.withholding_box.direct_tax_denmark_1": "", "tax_calculator.withholding_box.disability_insurance": "", "tax_calculator.withholding_box.seguro_educativo_pa": "", "tax_calculator.feedback_box.eg_name": "", "tax_calculator.withholding_box.el_deduction": "", "tax_calculator.feedback_box.email": "", "tax_calculator.withholding_box.epf_my": "", "tax_calculator.withholding_box.rpe_ve": "", "tax_calculator.withholding_box.employment_insurance": "", "tax_calculator.withholding_box.eis_my": "", "tax_calculator.withholding_box.sem_cr": "", "tax_calculator.europe.europe": "", "tax_calculator.withholding_box.fli_usa": "", "tax_calculator.withholding_box.federal_income_tax_usa": "", "tax_calculator.withholding_box.federal_tax_deduction": "", "tax_calculator.withholding_box.contribucion_de_salud_cl": "", "tax_calculator.withholding_box.health_insurance_il": "", "tax_calculator.withholding_box.health_insurance_cash_hu": "", "tax_calculator.withholding_box.health_education_cess_in": "", "tax_calculator.withholding_box.health_care_lu": "", "tax_calculator.withholding_box.health_insurance_pl": "", "tax_calculator.enter_box.hour": "", "tax_calculator.people_also_ask.hourly": "", "tax_calculator.withholding_box.faov_ve": "", "tax_calculator.feedback_box.how_can_we": "", "tax_calculator.withholding_box.irs_pt": "", "tax_calculator.withholding_box.income_tax": "Impuesto a las ganancias", "tax_calculator.withholding_box.income_tax_sg": "", "tax_calculator.withholding_box.income_tax_pl": "", "tax_calculator.withholding_box.individual_income_tax_tw": "", "tax_calculator.withholding_box.inps_it": "", "tax_calculator.withholding_box.ivm_cr": "", "tax_calculator.withholding_box.irpf_es": "", "tax_calculator.withholding_box.isr_mx": "", "tax_calculator.withholding_box.labour_insurance_program_tw": "", "tax_calculator.latam.latin_america": "", "tax_calculator.withholding_box.local_tax_kr": "", "tax_calculator.withholding_box.local_income_tax_fi": "", "tax_calculator.withholding_box.long_term_care_de": "", "tax_calculator.tax_per_region.manitoba": "", "tax_calculator.withholding_box.marginal_tax_rate": "", "tax_calculator.withholding_box.medical_insurance": "", "tax_calculator.withholding_box.medicare_premium_fi": "", "tax_calculator.middle_east.middle_east": "", "tax_calculator.withholding_box.military_tax_ua": "", "tax_calculator.enter_box.month": "", "tax_calculator.withholding_box.mpf_hk": "", "tax_calculator.withholding_box.mpit_sn": "", "tax_calculator.withholding_box.municipal_tax_se": "", "tax_calculator.withholding_box.national_health_insurance_kr": "", "tax_calculator.withholding_box.national_health_insurance_tw": "", "tax_calculator.withholding_box.national_insurance_il": "", "tax_calculator.withholding_box.national_pension_kr": "", "tax_calculator.withholding_box.nssf_ke": "", "tax_calculator.withholding_box.nhf_ng": "", "tax_calculator.withholding_box.national_surtax_jp": "", "tax_calculator.withholding_box.net_pay": "", "tax_calculator.withholding_box.net_pay_piechart": "", "tax_calculator.withholding_box.nhif_ke": "", "tax_calculator.withholding_box.non_occupational_ch": "", "tax_calculator.tax_per_region.northwest_territories": "", "tax_calculator.north_america.north_america": "", "tax_calculator.tax_per_region.nova_scotia": "", "tax_calculator.withholding_box.nsif_cm": "", "tax_calculator.withholding_box.nsso_be": "", "tax_calculator.withholding_box.national_insurance_contribution_no": "", "tax_calculator.tax_per_region.nunavut": "", "tax_calculator.withholding_box.contributions_to_ch": "", "tax_calculator.withholding_box.other_tax_se": "", "tax_calculator.asia_pacitic.pacific": "", "tax_calculator.withholding_box.pajak_penghasilan_id": "", "tax_calculator.withholding_box.paye_nz": "", "tax_calculator.withholding_box.paye_za": "", "tax_calculator.withholding_box.payroll_tax_at": "", "tax_calculator.withholding_box.pension": "", "tax_calculator.withholding_box.contribucion_de_pension": "", "tax_calculator.withholding_box.pension_fund_ch": "", "tax_calculator.withholding_box.pension_insurance_fi": "", "tax_calculator.withholding_box.pension_contributions_ng": "", "tax_calculator.withholding_box.pension_insurance_pl": "", "tax_calculator.withholding_box.pension_lu": "", "tax_calculator.withholding_box.pension_obligatoria_co": "", "tax_calculator.people_also_ask.people_also_ask": "", "tax_calculator.withholding_box.personal_income_hu": "", "tax_calculator.withholding_box.personal_tax_ch": "", "tax_calculator.tpeople_also_ask.per_hour": "", "tax_calculator.enter_box.per": "", "tax_calculator.people_also_ask.per_year": "", "tax_calculator.withholding_box.plan_obligatorio_de_co": "", "tax_calculator.withholding_box.premi_bpjs_id": "", "tax_calculator.tax_per_region..prince_edward_island": "", "tax_calculator.withholding_box.provincial_tax_deduction": "", "tax_calculator.europe.rank": "", "tax_calculator.taxberg_box.real_tax_rate": "", "tax_calculator.withholding_box.jubilacion_uy": "", "tax_calculator.withholding_box.retirement_contributions_sn": "", "tax_calculator.withholding_box.retirement_fund_ci": "", "tax_calculator.withholding_box.salary": "", "tax_calculator.salary_rate.salary_rate": "", "tax_calculator.withholding_box.is_ci": "", "tax_calculator.people_also_ask.salary_time_unit": "", "tax_calculator.tax_per_region.show_more": "", "tax_calculator.withholding_box.sickness_insurance_pl": "", "tax_calculator.taxberg_box.so_with_you": "", "tax_calculator.withholding_box.seguro_social_ec": "", "tax_calculator.withholding_box.social_security": "", "tax_calculator.withholding_box.social_security_fund": "", "tax_calculator.withholding_box.socso_my": "", "tax_calculator.withholding_box.css_tn": "", "tax_calculator.withholding_box.sssc_be": "", "tax_calculator.withholding_box.stamp_tax_tr": "", "tax_calculator.withholding_box.state_income_tax": "", "tax_calculator.withholding_box.sdi_usa": "", "tax_calculator.feedback_box.submit": "", "tax_calculator.summary_box.summary": "", "tax_calculator.summary_box.summary_bonus": "", "tax_calculator.summary_box.if_you_make_yearly_salary_a_year_living_in_the_region": "", "tax_calculator.taxberg_box.tax_the_employer": "", "tax_calculator.taxberg_box.tax_you_pay": "", "tax_calculator.taxberg_box.taxberg": "", "tax_calculator.tax_per_income.tax_per_income": "", "tax_calculator.tax_per_region.tax_per_region": "", "tax_calculator.feedback_box.tell_us_about": "", "tax_calculator.feedback_box.thank_you": "", "tax_calculator.taxberg_box.total_tax_paid": "", "tax_calculator.withholding_box.total_tax": "", "tax_calculator.withholding_box.total_tax_piechart": "", "tax_calculator.withholding_box.tunjangan_bpjs_id": "", "tax_calculator.withholding_box.tunjangan_id": "", "tax_calculator.enter_box.enter_your": "", "tax_calculator.withholding_box.unemployment": "", "tax_calculator.withholding_box.contribucion_del_fondo_cl": "", "tax_calculator.withholding_box.unemployment_insurance_jp": "", "tax_calculator.withholding_box.usc_ie": "", "tax_calculator.feedback_box.want_to_send": "", "tax_calculator.enter_box.weekly": "", "tax_calculator.people_also_ask.what_is_average": "", "tax_calculator.enter_box.where_do": "", "tax_calculator.withholding_box.withholding": "", "tax_calculator.enter_box.annual": "", "tax_calculator.people_also_ask.yearly": "", "tax_calculator.feedback_box.your_feedback_will": "", "tax_calculator.feedback_box.your_opinion_is": "", "tax_calculator.meta_description.location_region": "", "tax_calculator.h1.default": "", "tax_calculator.h2.default": "", "tax_calculator.meta_description.default": "", "tax_calculator.meta_description.keyword_location_region": "", "tax_calculator.h1.location_region": "", "tax_calculator.meta_title.default": "", "tax_calculator.meta_title.location_region": "", "tax_calculator.meta_title.keyword_location_region": "", "tax_calculator.withholding_box.payroll_tax_de": "", "tax_calculator.withholding_box.pension_insurance_de": "", "tax_calculator.withholding_box.unemployment_insurance_fi": "", "tax_calculator.withholding_box.unemployment_insurance_vn": "", "tax_calculator.withholding_box.unemployment_insurance_ch": "", "tax_calculator.withholding_box.unemployment_insurance_za": "", "tax_calculator.withholding_box.unemployment_insurance_tr": "", "tax_calculator.withholding_box.health_insurance_de": "", "tax_calculator.withholding_box.health_insurance_vn": "", "tax_calculator.withholding_box.health_insurance_jp": "", "tax_calculator.withholding_box.health_insurance_cz": "", "tax_calculator.withholding_box.health_insurance_ro": "", "tax_calculator.withholding_box.jubilacion_ar": "", "tax_calculator.withholding_box.seguro_social_pa": "", "tax_calculator.withholding_box.seguro_social_ve": "", "tax_calculator.withholding_box.social_insurance_cz": "", "tax_calculator.withholding_box.irpf_uy": "", "tax_calculator.withholding_box.housing_fund_cn": "", "tax_calculator.withholding_box.housing_fund_cm": "", "tax_calculator.withholding_box.communal_tax_dk": "", "tax_calculator.withholding_box.federal_income_tax_pr": "", "tax_calculator.withholding_box.pension_contributions_hu": "", "tax_calculator.withholding_box.pension_contributions_kz": "", "tax_calculator.withholding_box.unemployemnt_fund_contribution_hu": "", "tax_calculator.withholding_box.national_pension_zm": "", "tax_calculator.withholding_box.national_health_insurance_zm": "", "tax_calculator.withholding_box.quebec_pension_plan_ca": "", "tax_calculator.withholding_box.quebec_parental_insurance_ca": "", "tax_calculator.withholding_box.employer_taxes": "", "tax_calculator.withholding_box.solidarity_surcharge_de": "", "tax_calculator.withholding_box.employees_provident_fund_in": "", "tax_calculator.withholding_box.mexican_social_security_mx": "", "tax_calculator.withholding_box.surcharge": "", "tax_calculator.withholding_box.subsidy": "", "tax_calculator.withholding_box.colombia_s_obligatory_co": "", "tax_calculator.withholding_box.pension_solidarity_fund_co": "", "tax_calculator.withholding_box.withholding_tax": "", "tax_calculator.withholding_box.national_insurance_contribution_uk": "", "tax_calculator.withholding_box.state_unemployment_insurance_usa": "", "tax_calculator.withholding_box.workers_compensation_usa": "", "tax_calculator.withholding_box.supplementary_insurance_ch": "", "tax_calculator.withholding_box.personal_income_tax_it": "", "tax_calculator.withholding_box.regional_tax": "", "tax_calculator.withholding_box.municipal_tax": "", "tax_calculator.withholding_box.tax_bonus": "", "tax_calculator.withholding_box.social_health_insurance_ar": "Obra social", "tax_calculator.withholding_box.comprehensive_health_care_ar": "Programa de Atención Médica Integral", "tax_calculator.withholding_box.solidarity_tax": "", "tax_calculator.withholding_box.other_credits": "", "tax_calculator.withholding_box.social_security_system_ph": "", "tax_calculator.withholding_box.philippine_health_insurance_ph": "", "tax_calculator.withholding_box.pagibig_ph": "", "tax_calculator.withholding_box.tax_due": "", "tax_calculator.withholding_box.solidarity_tax_br": "", "tax_calculator.withholding_box.contribution_to_employee_ve": "", "tax_calculator.withholding_box.sickness_and_maternity_cr": "", "tax_calculator.withholding_box.oldage_invalidity_and_cr": "", "tax_calculator.withholding_box.tax": "", "tax_calculator.withholding_box.national_health_fund_cl": "", "tax_calculator.withholding_box.topbracket_tax": "", "tax_calculator.withholding_box.labour_market_contribution_dk": "", "tax_calculator.withholding_box.other": "", "tax_calculator.withholding_box.job_tax_deduction_se": "", "tax_calculator.withholding_box.health_insurance_contribution": "", "tax_calculator.withholding_box.income_basic_tax_tw": "", "tax_calculator.withholding_box.local_service_tax": "", "tax_calculator.withholding_box.national_contribution_ci": "", "tax_calculator.withholding_box.general_income_tax_ci": "", "tax_calculator.withholding_box.state_tax": "", "tax_calculator.withholding_box.trans_usa": "", "tax_calculator.schema.name": "", "tax_calculator.schema.description": "", "tax_calculator.withholding_box.region": "Región", "tax_calculator.tax_per_region.net_pay": "", "tax_calculator.tax_per_region.rank": "", "tax_calculator.enter_box.value_must_be": "", "tax_calculator.enter_box.value_must_be_less": "", "tax_calculator.enter_box.semimonthly": "", "tax_calculator.summary_box.if_you_make_yearly_salary_a_year_living_in_country": "", "tax_calculator.withholding_box.medicare": "", "tax_calculator.withholding_box.medicare_us": "", "tax_calculator.withholding_box.medicare_au": "", "tax_calculator.withholding_box.medicare_pr": "", "tax_calculator.withholding_box.state_disability_insurance": "", "tax_calculator.withholding_box.unemployment_insurance_de": ""}