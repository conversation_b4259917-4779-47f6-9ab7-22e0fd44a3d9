import { names, ProjectType, Tree, joinPathFragments } from "@nx/devkit";
import {
  determineProjectNameAndRootOptions,
  ProjectNameAndRootFormat,
} from "@nx/devkit/src/generators/project-name-and-root-utils";

export interface GeneratorSchema {
  name: string;
  directory?: string;
  projectNameAndRootFormat?: ProjectNameAndRootFormat;
  applicationType?: string;
}

export interface GeneratorNormalizedSchema extends GeneratorSchema {
  moduleName: string;
  npmScope: string;
  projectName: string;
  projectRoot: string;
  projectType: ProjectType;
}

export const normalizeOptions = async (
  tree: Tree,
  options: GeneratorSchema,
  projectType: ProjectType,
  generator: string,
): Promise<GeneratorNormalizedSchema> => {
  options.directory =
    options.directory ??
    joinPathFragments(`packages/${options.applicationType}/services/`, options.name);

  const { projectName, projectRoot, projectNameAndRootFormat } =
    await determineProjectNameAndRootOptions(tree, {
      name: options.name,
      projectType: projectType,
      directory: options.directory,
      projectNameAndRootFormat: options.projectNameAndRootFormat,
      callingGenerator: generator,
    });

  const projectNames = names(options.name);

  return {
    ...options,
    name: projectNames.fileName,
    moduleName: projectNames.propertyName.toLowerCase(),
    npmScope: "talent",
    projectName,
    projectRoot,
    projectType,
    projectNameAndRootFormat,
  };
};
