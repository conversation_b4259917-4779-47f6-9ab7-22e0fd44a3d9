{"name": "pdf-generator", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/pdf-generator/src", "projectType": "library", "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/pdf-generator", "main": "libs/pdf-generator/src/index.ts", "tsConfig": "libs/pdf-generator/tsconfig.lib.json", "assets": ["libs/pdf-generator/*.md"]}}}, "tags": []}