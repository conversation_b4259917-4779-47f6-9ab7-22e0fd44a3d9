# S3 bucket object

Creates S3 bucket objects with different configurations.

<!-- BEGINNING OF PRE-COMMIT-TERRAFORM DOCS HOOK -->

## Requirements

| Name                                                                     | Version |
| ------------------------------------------------------------------------ | ------- |
| <a name="requirement_terraform"></a> [terraform](#requirement_terraform) | >= 1.0  |
| <a name="requirement_aws"></a> [aws](#requirement_aws)                   | >= 5.24 |

## Providers

| Name                                             | Version |
| ------------------------------------------------ | ------- |
| <a name="provider_aws"></a> [aws](#provider_aws) | >= 5.24 |

## Modules

No modules.

## Resources

| Name                                                                                                        | Type     |
| ----------------------------------------------------------------------------------------------------------- | -------- |
| [aws_s3_object.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3_object) | resource |

## Inputs

| Name                                                                                                                     | Description                                                                                                                                                                                                                                                                                                                                                                                                       | Type          | Default | Required |
| ------------------------------------------------------------------------------------------------------------------------ | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------- | ------- | :------: |
| <a name="input_acl"></a> [acl](#input_acl)                                                                               | The canned ACL to apply. Valid values are private, public-read, public-read-write, aws-exec-read, authenticated-read, bucket-owner-read, and bucket-owner-full-control. Defaults to private.                                                                                                                                                                                                                      | `string`      | `null`  |    no    |
| <a name="input_bucket"></a> [bucket](#input_bucket)                                                                      | The name of the bucket to put the file in. Alternatively, an S3 access point ARN can be specified.                                                                                                                                                                                                                                                                                                                | `string`      | `""`    |    no    |
| <a name="input_bucket_key_enabled"></a> [bucket_key_enabled](#input_bucket_key_enabled)                                  | Whether or not to use Amazon S3 Bucket Keys for SSE-KMS.                                                                                                                                                                                                                                                                                                                                                          | `bool`        | `null`  |    no    |
| <a name="input_cache_control"></a> [cache_control](#input_cache_control)                                                 | Specifies caching behavior along the request/reply chain.                                                                                                                                                                                                                                                                                                                                                         | `string`      | `null`  |    no    |
| <a name="input_content"></a> [content](#input_content)                                                                   | Literal string value to use as the object content, which will be uploaded as UTF-8-encoded text.                                                                                                                                                                                                                                                                                                                  | `string`      | `null`  |    no    |
| <a name="input_content_base64"></a> [content_base64](#input_content_base64)                                              | Base64-encoded data that will be decoded and uploaded as raw bytes for the object content. This allows safely uploading non-UTF8 binary data, but is recommended only for small content such as the result of the gzipbase64 function with small text strings. For larger objects, use source to stream the content from a disk file.                                                                             | `string`      | `null`  |    no    |
| <a name="input_content_disposition"></a> [content_disposition](#input_content_disposition)                               | Specifies presentational information for the object.                                                                                                                                                                                                                                                                                                                                                              | `string`      | `null`  |    no    |
| <a name="input_content_encoding"></a> [content_encoding](#input_content_encoding)                                        | Specifies what content encodings have been applied to the object and thus what decoding mechanisms must be applied to obtain the media-type referenced by the Content-Type header field.                                                                                                                                                                                                                          | `string`      | `null`  |    no    |
| <a name="input_content_language"></a> [content_language](#input_content_language)                                        | The language the content is in e.g. en-US or en-GB.                                                                                                                                                                                                                                                                                                                                                               | `string`      | `null`  |    no    |
| <a name="input_content_type"></a> [content_type](#input_content_type)                                                    | A standard MIME type describing the format of the object data, e.g. application/octet-stream. All Valid MIME Types are valid for this input.                                                                                                                                                                                                                                                                      | `string`      | `null`  |    no    |
| <a name="input_create"></a> [create](#input_create)                                                                      | Whether to create this resource or not?                                                                                                                                                                                                                                                                                                                                                                           | `bool`        | `true`  |    no    |
| <a name="input_etag"></a> [etag](#input_etag)                                                                            | Used to trigger updates. This attribute is not compatible with KMS encryption, kms_key_id or server_side_encryption = "aws:kms".                                                                                                                                                                                                                                                                                  | `string`      | `null`  |    no    |
| <a name="input_file_source"></a> [file_source](#input_file_source)                                                       | The path to a file that will be read and uploaded as raw bytes for the object content.                                                                                                                                                                                                                                                                                                                            | `string`      | `null`  |    no    |
| <a name="input_force_destroy"></a> [force_destroy](#input_force_destroy)                                                 | Allow the object to be deleted by removing any legal hold on any object version. Default is false. This value should be set to true only if the bucket has S3 object lock enabled.                                                                                                                                                                                                                                | `bool`        | `false` |    no    |
| <a name="input_key"></a> [key](#input_key)                                                                               | The name of the object once it is in the bucket.                                                                                                                                                                                                                                                                                                                                                                  | `string`      | `""`    |    no    |
| <a name="input_kms_key_id"></a> [kms_key_id](#input_kms_key_id)                                                          | Amazon Resource Name (ARN) of the KMS Key to use for object encryption. If the S3 Bucket has server-side encryption enabled, that value will automatically be used. If referencing the aws_kms_key resource, use the arn attribute. If referencing the aws_kms_alias data source or resource, use the target_key_arn attribute. Terraform will only perform drift detection if a configuration value is provided. | `string`      | `null`  |    no    |
| <a name="input_metadata"></a> [metadata](#input_metadata)                                                                | A map of keys/values to provision metadata (will be automatically prefixed by x-amz-meta-, note that only lowercase label are currently supported by the AWS Go API).                                                                                                                                                                                                                                             | `map(string)` | `{}`    |    no    |
| <a name="input_object_lock_legal_hold_status"></a> [object_lock_legal_hold_status](#input_object_lock_legal_hold_status) | The legal hold status that you want to apply to the specified object. Valid values are ON and OFF.                                                                                                                                                                                                                                                                                                                | `string`      | `null`  |    no    |
| <a name="input_object_lock_mode"></a> [object_lock_mode](#input_object_lock_mode)                                        | The object lock retention mode that you want to apply to this object. Valid values are GOVERNANCE and COMPLIANCE.                                                                                                                                                                                                                                                                                                 | `string`      | `null`  |    no    |
| <a name="input_object_lock_retain_until_date"></a> [object_lock_retain_until_date](#input_object_lock_retain_until_date) | The date and time, in RFC3339 format, when this object's object lock will expire.                                                                                                                                                                                                                                                                                                                                 | `string`      | `null`  |    no    |
| <a name="input_override_default_tags"></a> [override_default_tags](#input_override_default_tags)                         | Ignore provider default_tags. S3 objects support a maximum of 10 tags.                                                                                                                                                                                                                                                                                                                                            | `bool`        | `false` |    no    |
| <a name="input_server_side_encryption"></a> [server_side_encryption](#input_server_side_encryption)                      | Specifies server-side encryption of the object in S3. Valid values are "AES256" and "aws:kms".                                                                                                                                                                                                                                                                                                                    | `string`      | `null`  |    no    |
| <a name="input_source_hash"></a> [source_hash](#input_source_hash)                                                       | Triggers updates like etag but useful to address etag encryption limitations. Set using filemd5("path/to/source") (Terraform 0.11.12 or later). (The value is only stored in state and not saved by AWS.)                                                                                                                                                                                                         | `string`      | `null`  |    no    |
| <a name="input_storage_class"></a> [storage_class](#input_storage_class)                                                 | Specifies the desired Storage Class for the object. Can be either STANDARD, REDUCED_REDUNDANCY, ONEZONE_IA, INTELLIGENT_TIERING, GLACIER, DEEP_ARCHIVE, or STANDARD_IA. Defaults to STANDARD.                                                                                                                                                                                                                     | `string`      | `null`  |    no    |
| <a name="input_tags"></a> [tags](#input_tags)                                                                            | A map of tags to assign to the object.                                                                                                                                                                                                                                                                                                                                                                            | `map(string)` | `{}`    |    no    |
| <a name="input_website_redirect"></a> [website_redirect](#input_website_redirect)                                        | Specifies a target URL for website redirect.                                                                                                                                                                                                                                                                                                                                                                      | `string`      | `null`  |    no    |

## Outputs

| Name                                                                                            | Description                                                                |
| ----------------------------------------------------------------------------------------------- | -------------------------------------------------------------------------- |
| <a name="output_s3_object_etag"></a> [s3_object_etag](#output_s3_object_etag)                   | The ETag generated for the object (an MD5 sum of the object content).      |
| <a name="output_s3_object_id"></a> [s3_object_id](#output_s3_object_id)                         | The key of S3 object                                                       |
| <a name="output_s3_object_version_id"></a> [s3_object_version_id](#output_s3_object_version_id) | A unique version ID value for the object, if bucket versioning is enabled. |

<!-- END OF PRE-COMMIT-TERRAFORM DOCS HOOK -->
