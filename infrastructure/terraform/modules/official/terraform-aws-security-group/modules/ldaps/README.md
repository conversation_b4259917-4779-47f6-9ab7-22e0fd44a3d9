# ldaps - AWS EC2-VPC Security Group Terraform module

## Usage

```hcl
module "ldaps_security_group" {
  source  = "terraform-aws-modules/security-group/aws//modules/ldaps"
  version = "~> 5.0"

  # omitted...
}
```

All automatic values **ldaps module** is using are available [here](https://github.com/terraform-aws-modules/terraform-aws-security-group/blob/master/modules/ldaps/auto_values.tf).

<!-- BEGINNING OF PRE-COMMIT-TERRAFORM DOCS HOOK -->

## Requirements

| Name                                                                     | Version |
| ------------------------------------------------------------------------ | ------- |
| <a name="requirement_terraform"></a> [terraform](#requirement_terraform) | >= 1.0  |
| <a name="requirement_aws"></a> [aws](#requirement_aws)                   | >= 3.29 |

## Providers

No providers.

## Modules

| Name                                      | Source | Version |
| ----------------------------------------- | ------ | ------- |
| <a name="module_sg"></a> [sg](#module_sg) | ../../ | n/a     |

## Resources

No resources.

## Inputs

| Name                                                                                                                                                                                                      | Description                                                                                                                                | Type                | Default                                             | Required |
| --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------ | ------------------- | --------------------------------------------------- | :------: |
| <a name="input_auto_computed_egress_rules"></a> [auto_computed_egress_rules](#input_auto_computed_egress_rules)                                                                                           | List of computed egress rules to add automatically                                                                                         | `list(string)`      | `[]`                                                |    no    |
| <a name="input_auto_computed_egress_with_self"></a> [auto_computed_egress_with_self](#input_auto_computed_egress_with_self)                                                                               | List of maps defining computed egress rules with self to add automatically                                                                 | `list(map(string))` | `[]`                                                |    no    |
| <a name="input_auto_computed_ingress_rules"></a> [auto_computed_ingress_rules](#input_auto_computed_ingress_rules)                                                                                        | List of ingress rules to add automatically                                                                                                 | `list(string)`      | `[]`                                                |    no    |
| <a name="input_auto_computed_ingress_with_self"></a> [auto_computed_ingress_with_self](#input_auto_computed_ingress_with_self)                                                                            | List of maps defining computed ingress rules with self to add automatically                                                                | `list(map(string))` | `[]`                                                |    no    |
| <a name="input_auto_egress_rules"></a> [auto_egress_rules](#input_auto_egress_rules)                                                                                                                      | List of egress rules to add automatically                                                                                                  | `list(string)`      | <pre>[<br> "all-all"<br>]</pre>                     |    no    |
| <a name="input_auto_egress_with_self"></a> [auto_egress_with_self](#input_auto_egress_with_self)                                                                                                          | List of maps defining egress rules with self to add automatically                                                                          | `list(map(string))` | `[]`                                                |    no    |
| <a name="input_auto_ingress_rules"></a> [auto_ingress_rules](#input_auto_ingress_rules)                                                                                                                   | List of ingress rules to add automatically                                                                                                 | `list(string)`      | <pre>[<br> "ldaps-tcp"<br>]</pre>                   |    no    |
| <a name="input_auto_ingress_with_self"></a> [auto_ingress_with_self](#input_auto_ingress_with_self)                                                                                                       | List of maps defining ingress rules with self to add automatically                                                                         | `list(map(string))` | <pre>[<br> {<br> "rule": "all-all"<br> }<br>]</pre> |    no    |
| <a name="input_auto_number_of_computed_egress_rules"></a> [auto_number_of_computed_egress_rules](#input_auto_number_of_computed_egress_rules)                                                             | Number of computed egress rules to create by name                                                                                          | `number`            | `0`                                                 |    no    |
| <a name="input_auto_number_of_computed_egress_with_self"></a> [auto_number_of_computed_egress_with_self](#input_auto_number_of_computed_egress_with_self)                                                 | Number of computed egress rules to create where 'self' is defined                                                                          | `number`            | `0`                                                 |    no    |
| <a name="input_auto_number_of_computed_ingress_rules"></a> [auto_number_of_computed_ingress_rules](#input_auto_number_of_computed_ingress_rules)                                                          | Number of computed ingress rules to create by name                                                                                         | `number`            | `0`                                                 |    no    |
| <a name="input_auto_number_of_computed_ingress_with_self"></a> [auto_number_of_computed_ingress_with_self](#input_auto_number_of_computed_ingress_with_self)                                              | Number of computed ingress rules to create where 'self' is defined                                                                         | `number`            | `0`                                                 |    no    |
| <a name="input_computed_egress_cidr_blocks"></a> [computed_egress_cidr_blocks](#input_computed_egress_cidr_blocks)                                                                                        | List of IPv4 CIDR ranges to use on all computed egress rules                                                                               | `list(string)`      | <pre>[<br> "0.0.0.0/0"<br>]</pre>                   |    no    |
| <a name="input_computed_egress_ipv6_cidr_blocks"></a> [computed_egress_ipv6_cidr_blocks](#input_computed_egress_ipv6_cidr_blocks)                                                                         | List of IPv6 CIDR ranges to use on all computed egress rules                                                                               | `list(string)`      | <pre>[<br> "::/0"<br>]</pre>                        |    no    |
| <a name="input_computed_egress_prefix_list_ids"></a> [computed_egress_prefix_list_ids](#input_computed_egress_prefix_list_ids)                                                                            | List of prefix list IDs (for allowing access to VPC endpoints) to use on all computed egress rules                                         | `list(string)`      | `[]`                                                |    no    |
| <a name="input_computed_egress_rules"></a> [computed_egress_rules](#input_computed_egress_rules)                                                                                                          | List of computed egress rules to create by name                                                                                            | `list(string)`      | `[]`                                                |    no    |
| <a name="input_computed_egress_with_cidr_blocks"></a> [computed_egress_with_cidr_blocks](#input_computed_egress_with_cidr_blocks)                                                                         | List of computed egress rules to create where 'cidr_blocks' is used                                                                        | `list(map(string))` | `[]`                                                |    no    |
| <a name="input_computed_egress_with_ipv6_cidr_blocks"></a> [computed_egress_with_ipv6_cidr_blocks](#input_computed_egress_with_ipv6_cidr_blocks)                                                          | List of computed egress rules to create where 'ipv6_cidr_blocks' is used                                                                   | `list(map(string))` | `[]`                                                |    no    |
| <a name="input_computed_egress_with_prefix_list_ids"></a> [computed_egress_with_prefix_list_ids](#input_computed_egress_with_prefix_list_ids)                                                             | List of computed egress rules to create where 'prefix_list_ids' is used                                                                    | `list(map(string))` | `[]`                                                |    no    |
| <a name="input_computed_egress_with_self"></a> [computed_egress_with_self](#input_computed_egress_with_self)                                                                                              | List of computed egress rules to create where 'self' is defined                                                                            | `list(map(string))` | `[]`                                                |    no    |
| <a name="input_computed_egress_with_source_security_group_id"></a> [computed_egress_with_source_security_group_id](#input_computed_egress_with_source_security_group_id)                                  | List of computed egress rules to create where 'source_security_group_id' is used                                                           | `list(map(string))` | `[]`                                                |    no    |
| <a name="input_computed_ingress_cidr_blocks"></a> [computed_ingress_cidr_blocks](#input_computed_ingress_cidr_blocks)                                                                                     | List of IPv4 CIDR ranges to use on all computed ingress rules                                                                              | `list(string)`      | `[]`                                                |    no    |
| <a name="input_computed_ingress_ipv6_cidr_blocks"></a> [computed_ingress_ipv6_cidr_blocks](#input_computed_ingress_ipv6_cidr_blocks)                                                                      | List of IPv6 CIDR ranges to use on all computed ingress rules                                                                              | `list(string)`      | `[]`                                                |    no    |
| <a name="input_computed_ingress_prefix_list_ids"></a> [computed_ingress_prefix_list_ids](#input_computed_ingress_prefix_list_ids)                                                                         | List of prefix list IDs (for allowing access to VPC endpoints) to use on all computed ingress rules                                        | `list(string)`      | `[]`                                                |    no    |
| <a name="input_computed_ingress_rules"></a> [computed_ingress_rules](#input_computed_ingress_rules)                                                                                                       | List of computed ingress rules to create by name                                                                                           | `list(string)`      | `[]`                                                |    no    |
| <a name="input_computed_ingress_with_cidr_blocks"></a> [computed_ingress_with_cidr_blocks](#input_computed_ingress_with_cidr_blocks)                                                                      | List of computed ingress rules to create where 'cidr_blocks' is used                                                                       | `list(map(string))` | `[]`                                                |    no    |
| <a name="input_computed_ingress_with_ipv6_cidr_blocks"></a> [computed_ingress_with_ipv6_cidr_blocks](#input_computed_ingress_with_ipv6_cidr_blocks)                                                       | List of computed ingress rules to create where 'ipv6_cidr_blocks' is used                                                                  | `list(map(string))` | `[]`                                                |    no    |
| <a name="input_computed_ingress_with_prefix_list_ids"></a> [computed_ingress_with_prefix_list_ids](#input_computed_ingress_with_prefix_list_ids)                                                          | List of computed ingress rules to create where 'prefix_list_ids' is used                                                                   | `list(map(string))` | `[]`                                                |    no    |
| <a name="input_computed_ingress_with_self"></a> [computed_ingress_with_self](#input_computed_ingress_with_self)                                                                                           | List of computed ingress rules to create where 'self' is defined                                                                           | `list(map(string))` | `[]`                                                |    no    |
| <a name="input_computed_ingress_with_source_security_group_id"></a> [computed_ingress_with_source_security_group_id](#input_computed_ingress_with_source_security_group_id)                               | List of computed ingress rules to create where 'source_security_group_id' is used                                                          | `list(map(string))` | `[]`                                                |    no    |
| <a name="input_create"></a> [create](#input_create)                                                                                                                                                       | Whether to create security group and all rules                                                                                             | `bool`              | `true`                                              |    no    |
| <a name="input_description"></a> [description](#input_description)                                                                                                                                        | Description of security group                                                                                                              | `string`            | `"Security Group managed by Terraform"`             |    no    |
| <a name="input_egress_cidr_blocks"></a> [egress_cidr_blocks](#input_egress_cidr_blocks)                                                                                                                   | List of IPv4 CIDR ranges to use on all egress rules                                                                                        | `list(string)`      | <pre>[<br> "0.0.0.0/0"<br>]</pre>                   |    no    |
| <a name="input_egress_ipv6_cidr_blocks"></a> [egress_ipv6_cidr_blocks](#input_egress_ipv6_cidr_blocks)                                                                                                    | List of IPv6 CIDR ranges to use on all egress rules                                                                                        | `list(string)`      | <pre>[<br> "::/0"<br>]</pre>                        |    no    |
| <a name="input_egress_prefix_list_ids"></a> [egress_prefix_list_ids](#input_egress_prefix_list_ids)                                                                                                       | List of prefix list IDs (for allowing access to VPC endpoints) to use on all egress rules                                                  | `list(string)`      | `[]`                                                |    no    |
| <a name="input_egress_rules"></a> [egress_rules](#input_egress_rules)                                                                                                                                     | List of egress rules to create by name                                                                                                     | `list(string)`      | `[]`                                                |    no    |
| <a name="input_egress_with_cidr_blocks"></a> [egress_with_cidr_blocks](#input_egress_with_cidr_blocks)                                                                                                    | List of egress rules to create where 'cidr_blocks' is used                                                                                 | `list(map(string))` | `[]`                                                |    no    |
| <a name="input_egress_with_ipv6_cidr_blocks"></a> [egress_with_ipv6_cidr_blocks](#input_egress_with_ipv6_cidr_blocks)                                                                                     | List of egress rules to create where 'ipv6_cidr_blocks' is used                                                                            | `list(map(string))` | `[]`                                                |    no    |
| <a name="input_egress_with_prefix_list_ids"></a> [egress_with_prefix_list_ids](#input_egress_with_prefix_list_ids)                                                                                        | List of egress rules to create where 'prefix_list_ids' is used                                                                             | `list(map(string))` | `[]`                                                |    no    |
| <a name="input_egress_with_self"></a> [egress_with_self](#input_egress_with_self)                                                                                                                         | List of egress rules to create where 'self' is defined                                                                                     | `list(map(string))` | `[]`                                                |    no    |
| <a name="input_egress_with_source_security_group_id"></a> [egress_with_source_security_group_id](#input_egress_with_source_security_group_id)                                                             | List of egress rules to create where 'source_security_group_id' is used                                                                    | `list(map(string))` | `[]`                                                |    no    |
| <a name="input_ingress_cidr_blocks"></a> [ingress_cidr_blocks](#input_ingress_cidr_blocks)                                                                                                                | List of IPv4 CIDR ranges to use on all ingress rules                                                                                       | `list(string)`      | `[]`                                                |    no    |
| <a name="input_ingress_ipv6_cidr_blocks"></a> [ingress_ipv6_cidr_blocks](#input_ingress_ipv6_cidr_blocks)                                                                                                 | List of IPv6 CIDR ranges to use on all ingress rules                                                                                       | `list(string)`      | `[]`                                                |    no    |
| <a name="input_ingress_prefix_list_ids"></a> [ingress_prefix_list_ids](#input_ingress_prefix_list_ids)                                                                                                    | List of prefix list IDs (for allowing access to VPC endpoints) to use on all ingress rules                                                 | `list(string)`      | `[]`                                                |    no    |
| <a name="input_ingress_rules"></a> [ingress_rules](#input_ingress_rules)                                                                                                                                  | List of ingress rules to create by name                                                                                                    | `list(string)`      | `[]`                                                |    no    |
| <a name="input_ingress_with_cidr_blocks"></a> [ingress_with_cidr_blocks](#input_ingress_with_cidr_blocks)                                                                                                 | List of ingress rules to create where 'cidr_blocks' is used                                                                                | `list(map(string))` | `[]`                                                |    no    |
| <a name="input_ingress_with_ipv6_cidr_blocks"></a> [ingress_with_ipv6_cidr_blocks](#input_ingress_with_ipv6_cidr_blocks)                                                                                  | List of ingress rules to create where 'ipv6_cidr_blocks' is used                                                                           | `list(map(string))` | `[]`                                                |    no    |
| <a name="input_ingress_with_prefix_list_ids"></a> [ingress_with_prefix_list_ids](#input_ingress_with_prefix_list_ids)                                                                                     | List of ingress rules to create where 'prefix_list_ids' is used                                                                            | `list(map(string))` | `[]`                                                |    no    |
| <a name="input_ingress_with_self"></a> [ingress_with_self](#input_ingress_with_self)                                                                                                                      | List of ingress rules to create where 'self' is defined                                                                                    | `list(map(string))` | `[]`                                                |    no    |
| <a name="input_ingress_with_source_security_group_id"></a> [ingress_with_source_security_group_id](#input_ingress_with_source_security_group_id)                                                          | List of ingress rules to create where 'source_security_group_id' is used                                                                   | `list(map(string))` | `[]`                                                |    no    |
| <a name="input_name"></a> [name](#input_name)                                                                                                                                                             | Name of security group                                                                                                                     | `string`            | n/a                                                 |   yes    |
| <a name="input_number_of_computed_egress_cidr_blocks"></a> [number_of_computed_egress_cidr_blocks](#input_number_of_computed_egress_cidr_blocks)                                                          | Number of IPv4 CIDR ranges to use on all computed egress rules                                                                             | `number`            | `0`                                                 |    no    |
| <a name="input_number_of_computed_egress_ipv6_cidr_blocks"></a> [number_of_computed_egress_ipv6_cidr_blocks](#input_number_of_computed_egress_ipv6_cidr_blocks)                                           | Number of IPv6 CIDR ranges to use on all computed egress rules                                                                             | `number`            | `0`                                                 |    no    |
| <a name="input_number_of_computed_egress_prefix_list_ids"></a> [number_of_computed_egress_prefix_list_ids](#input_number_of_computed_egress_prefix_list_ids)                                              | Number of prefix list IDs (for allowing access to VPC endpoints) to use on all computed egress rules                                       | `number`            | `0`                                                 |    no    |
| <a name="input_number_of_computed_egress_rules"></a> [number_of_computed_egress_rules](#input_number_of_computed_egress_rules)                                                                            | Number of computed egress rules to create by name                                                                                          | `number`            | `0`                                                 |    no    |
| <a name="input_number_of_computed_egress_with_cidr_blocks"></a> [number_of_computed_egress_with_cidr_blocks](#input_number_of_computed_egress_with_cidr_blocks)                                           | Number of computed egress rules to create where 'cidr_blocks' is used                                                                      | `number`            | `0`                                                 |    no    |
| <a name="input_number_of_computed_egress_with_ipv6_cidr_blocks"></a> [number_of_computed_egress_with_ipv6_cidr_blocks](#input_number_of_computed_egress_with_ipv6_cidr_blocks)                            | Number of computed egress rules to create where 'ipv6_cidr_blocks' is used                                                                 | `number`            | `0`                                                 |    no    |
| <a name="input_number_of_computed_egress_with_prefix_list_ids"></a> [number_of_computed_egress_with_prefix_list_ids](#input_number_of_computed_egress_with_prefix_list_ids)                               | Number of computed egress rules to create where 'prefix_list_ids' is used                                                                  | `number`            | `0`                                                 |    no    |
| <a name="input_number_of_computed_egress_with_self"></a> [number_of_computed_egress_with_self](#input_number_of_computed_egress_with_self)                                                                | Number of computed egress rules to create where 'self' is defined                                                                          | `number`            | `0`                                                 |    no    |
| <a name="input_number_of_computed_egress_with_source_security_group_id"></a> [number_of_computed_egress_with_source_security_group_id](#input_number_of_computed_egress_with_source_security_group_id)    | Number of computed egress rules to create where 'source_security_group_id' is used                                                         | `number`            | `0`                                                 |    no    |
| <a name="input_number_of_computed_ingress_cidr_blocks"></a> [number_of_computed_ingress_cidr_blocks](#input_number_of_computed_ingress_cidr_blocks)                                                       | Number of IPv4 CIDR ranges to use on all computed ingress rules                                                                            | `number`            | `0`                                                 |    no    |
| <a name="input_number_of_computed_ingress_ipv6_cidr_blocks"></a> [number_of_computed_ingress_ipv6_cidr_blocks](#input_number_of_computed_ingress_ipv6_cidr_blocks)                                        | Number of IPv6 CIDR ranges to use on all computed ingress rules                                                                            | `number`            | `0`                                                 |    no    |
| <a name="input_number_of_computed_ingress_prefix_list_ids"></a> [number_of_computed_ingress_prefix_list_ids](#input_number_of_computed_ingress_prefix_list_ids)                                           | Number of prefix list IDs (for allowing access to VPC endpoints) to use on all computed ingress rules                                      | `number`            | `0`                                                 |    no    |
| <a name="input_number_of_computed_ingress_rules"></a> [number_of_computed_ingress_rules](#input_number_of_computed_ingress_rules)                                                                         | Number of computed ingress rules to create by name                                                                                         | `number`            | `0`                                                 |    no    |
| <a name="input_number_of_computed_ingress_with_cidr_blocks"></a> [number_of_computed_ingress_with_cidr_blocks](#input_number_of_computed_ingress_with_cidr_blocks)                                        | Number of computed ingress rules to create where 'cidr_blocks' is used                                                                     | `number`            | `0`                                                 |    no    |
| <a name="input_number_of_computed_ingress_with_ipv6_cidr_blocks"></a> [number_of_computed_ingress_with_ipv6_cidr_blocks](#input_number_of_computed_ingress_with_ipv6_cidr_blocks)                         | Number of computed ingress rules to create where 'ipv6_cidr_blocks' is used                                                                | `number`            | `0`                                                 |    no    |
| <a name="input_number_of_computed_ingress_with_prefix_list_ids"></a> [number_of_computed_ingress_with_prefix_list_ids](#input_number_of_computed_ingress_with_prefix_list_ids)                            | Number of computed ingress rules to create where 'prefix_list_ids' is used                                                                 | `number`            | `0`                                                 |    no    |
| <a name="input_number_of_computed_ingress_with_self"></a> [number_of_computed_ingress_with_self](#input_number_of_computed_ingress_with_self)                                                             | Number of computed ingress rules to create where 'self' is defined                                                                         | `number`            | `0`                                                 |    no    |
| <a name="input_number_of_computed_ingress_with_source_security_group_id"></a> [number_of_computed_ingress_with_source_security_group_id](#input_number_of_computed_ingress_with_source_security_group_id) | Number of computed ingress rules to create where 'source_security_group_id' is used                                                        | `number`            | `0`                                                 |    no    |
| <a name="input_revoke_rules_on_delete"></a> [revoke_rules_on_delete](#input_revoke_rules_on_delete)                                                                                                       | Instruct Terraform to revoke all of the Security Groups attached ingress and egress rules before deleting the rule itself. Enable for EMR. | `bool`              | `false`                                             |    no    |
| <a name="input_tags"></a> [tags](#input_tags)                                                                                                                                                             | A mapping of tags to assign to security group                                                                                              | `map(string)`       | `{}`                                                |    no    |
| <a name="input_use_name_prefix"></a> [use_name_prefix](#input_use_name_prefix)                                                                                                                            | Whether to use name_prefix or fixed name. Should be true to able to update security group name after initial creation                      | `bool`              | `true`                                              |    no    |
| <a name="input_vpc_id"></a> [vpc_id](#input_vpc_id)                                                                                                                                                       | ID of the VPC where to create security group                                                                                               | `string`            | n/a                                                 |   yes    |

## Outputs

| Name                                                                                                              | Description                           |
| ----------------------------------------------------------------------------------------------------------------- | ------------------------------------- |
| <a name="output_security_group_arn"></a> [security_group_arn](#output_security_group_arn)                         | The ARN of the security group         |
| <a name="output_security_group_description"></a> [security_group_description](#output_security_group_description) | The description of the security group |
| <a name="output_security_group_id"></a> [security_group_id](#output_security_group_id)                            | The ID of the security group          |
| <a name="output_security_group_name"></a> [security_group_name](#output_security_group_name)                      | The name of the security group        |
| <a name="output_security_group_owner_id"></a> [security_group_owner_id](#output_security_group_owner_id)          | The owner ID                          |
| <a name="output_security_group_vpc_id"></a> [security_group_vpc_id](#output_security_group_vpc_id)                | The VPC ID                            |

<!-- END OF PRE-COMMIT-TERRAFORM DOCS HOOK -->
