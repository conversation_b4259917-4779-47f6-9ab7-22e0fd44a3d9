data "aws_route53_zone" "this" {
  name = "talent.pre"
  private_zone = true
}

resource "aws_instance" "this" {
  ami                    = "ami-0afe8671a393f91b3"
  instance_type          = "t2.small"
  subnet_id              = "subnet-09ed67eb6b8c5b0e5"
  vpc_security_group_ids = [ "sg-0f32b50881fa9c3e2" ]
  iam_instance_profile   = "Pre-devInstanceRole"

  root_block_device {
    volume_size = 16
    encrypted = false
    volume_type = "gp3"
  }

  tags = {
    Name = "ec2-pre-dev-${var.SERVICE_NAME}-${var.USER}"
    environment = var.ENVIRONMENT
    type = "ec2"
    service = var.SERVICE_NAME
    owner = var.USER
    service_type = "environment"
    group = "pre-dev"
    cc = "infrastructure"
    test = "true"
    terraform = "true"
  }

  user_data = var.USER_DATA_SCRIPT
}

resource "aws_route53_record" "this" {
  name    = "${var.USER}-${var.ENVIRONMENT}-${var.SERVICE_NAME}"
  type    = "A"
  ttl     = "30"
  records = [ aws_instance.this.private_ip ]
  zone_id = data.aws_route53_zone.this.zone_id
}
