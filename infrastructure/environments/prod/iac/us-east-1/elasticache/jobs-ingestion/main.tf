provider "aws" {
  region = local.region
}

locals {
  region = "us-east-1"
  name   = "cache-${basename(path.cwd)}"
}

data "aws_vpc" "vpc_development" {
  filter {
    name   = "tag:Name"
    values = ["prod-vpc"]
  }
}

data "aws_subnet" "dev_private_subnets" {
  for_each = toset(var.subnet_names)
  vpc_id   = data.aws_vpc.vpc_development.id

  tags = {
    Name = each.value
  }
}

module "elasticache" {
  source = "../../../../../../terraform/modules/official/terraform-aws-elasticache"

  cluster_id               = local.name
  create                   = true
  create_cluster           = true
  create_replication_group = true
  replication_group_id     = local.name
  description              = "Valkey Elasticache cluster for ${local.name}"

  engine         = "valkey"
  engine_version = "8.0"
  node_type      = "cache.r7g.xlarge"
  num_cache_nodes            = 5
  replicas_per_node_group    = 4
  automatic_failover_enabled = true
  az_mode                    = "cross-az"

  maintenance_window = "sun:05:00-sun:09:00"
  apply_immediately  = true

  # Security group
  create_security_group = false
  security_group_ids    = var.security_groups

  # Subnet Group
  subnet_ids = [for subnet_name, subnet in data.aws_subnet.dev_private_subnets : subnet.id]

  # Parameter Group
  create_parameter_group = false
  parameter_group_name   = "default.valkey8"

  tags = {
    Environment  = "prod"
    Terraform    = "true"
    Name         = "cache-${local.name}",
    cc           = "marketplace"
    owner        = "<EMAIL>"
    service      = "jobs-ingestion"
    group        = "spider"
    service_type = "backend"
    service_role = "backend"
    department   = "jobs-ingestion"
  }
}

data "aws_route53_zone" "private" {
  name         = "talent.private"
  private_zone = true
}

resource "aws_route53_record" "master" {
  allow_overwrite = true
  zone_id         = data.aws_route53_zone.private.zone_id
  name            = "master-cache-jobs-ingestion"
  type            = "CNAME"
  ttl             = 60
  records         = [module.elasticache.replication_group_primary_endpoint_address]
}


resource "aws_route53_record" "replica" {
  allow_overwrite = true
  zone_id         = data.aws_route53_zone.private.zone_id
  name            = "replica-cache-jobs-ingestion"
  type            = "CNAME"
  ttl             = 60
  records         = [module.elasticache.replication_group_reader_endpoint_address]
}
