provider "aws" {
  region = "us-east-1"
}

module "s3_bucket" {
  source = "../../../../../../terraform/modules/official/terraform-aws-s3-bucket-master"

  bucket                   = "talent-prod-${local.resource_name}"
  acl                      = "private"
  block_public_acls        = true
  block_public_policy      = true
  ignore_public_acls       = true
  restrict_public_buckets  = true
  control_object_ownership = true
  object_ownership         = "ObjectWriter"

  server_side_encryption_configuration = {
    rule = {
      apply_server_side_encryption_by_default = {
        sse_algorithm = "AES256"
      }
    }
  }

  versioning = {
    enabled = true
  }
  attach_policy = true

  policy = <<POLICY
 {
    "Version": "2012-10-17",
    "Statement": [
        {
            "Sid": "AllowAccessToCloudWatchAgentServerRoleInstancesRole",
            "Effect": "Allow",
            "Principal": {
                "AWS": "arn:aws:iam::147040575728:user/Service-S3"
            },
            "Action": [
                "s3:GetBucketLocation",
                "s3:GetObject",
                "s3:ListBucket",
                "s3:ListBucketMultipartUploads",
                "s3:AbortMultipartUpload",
                "s3:PutObject",
                "s3:ListMultipartUploadParts"
            ],
            "Resource": [
                "arn:aws:s3:::talent-prod-cassandra-backups",
                "arn:aws:s3:::talent-prod-cassandra-backups/*"
            ]
        }
    ]
}
  POLICY

  tags = {
    Environment  = "prod"
    Terraform    = "true"
    Name         = "${local.resource_name}-emanuel",
    cc           = "marketplace"
    owner        = "<EMAIL>"
    type         = "s3"
    service      = "${local.resource_name}"
    group        = "kubernetes"
    service_type = "infra"
    service_role = "backup"
    department   = "infrastructure"
  }
}

