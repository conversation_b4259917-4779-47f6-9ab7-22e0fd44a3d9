provider "aws" {
  region = "us-east-1"
}

#MAIN CONFIG
data "local_file" "config"{

  filename = "../../foundational/config/default-config.json"
}
# LOCALS
locals {
  config                = jsondecode(data.local_file.config.content)
  environment           = local.config.environment
  region                = local.config.region
  config_bucket         = "${local.environment}-${local.config.bucket}"
  config_dynamodb_table = "${local.environment}-${local.config.dynamodb_table}"
  account_id            = local.config.account_id
  vpc_cidr                   = local.config.vpc.main_cidr
}

data "terraform_remote_state" "vpc_subnetting_gateways" {
  backend = "s3"

  config = {
    encrypt        = local.config.encrypt
    region         = local.region
    key            = "${local.environment}/iac/us-east-1/foundational/vpc-subnetting-and-gateways.tfstate"
    bucket         = local.config_bucket
    dynamodb_table = local.config_dynamodb_table
  }
}

module "eks" {
  source = "../../../../../../terraform/modules/official/terraform-aws-eks"

  cluster_name    = local.eks_resource_name
  cluster_version = "1.29"

  authentication_mode = "API_AND_CONFIG_MAP"

  cluster_security_group_additional_rules = {
    ingres_https_vpc_cidr = {
      type        = "ingress"
      protocol    = "tcp"
      from_port   = "443"
      to_port     = "443"
      description = "Allow https from vpc"
      cidr_blocks = [local.vpc_cidr]
    }
  }

  // TODO : set to false on talent enviroments
  cluster_endpoint_public_access = false
  cluster_endpoint_private_access = true
  cluster_addons = {
    coredns = {
      most_recent = true
    }
    kube-proxy = {
      most_recent = true
    }
    vpc-cni = {
      most_recent = true
    }
  }

  vpc_id                        = data.terraform_remote_state.vpc_subnetting_gateways.outputs.vpc_id
  subnet_ids                    = [data.terraform_remote_state.vpc_subnetting_gateways.outputs.private_workloads_subnets_id_us_east_1a[0],data.terraform_remote_state.vpc_subnetting_gateways.outputs.private_workloads_subnets_id_us_east_1b[0],data.terraform_remote_state.vpc_subnetting_gateways.outputs.private_workloads_subnets_id_us_east_1d[0]]
  control_plane_subnet_ids      = [data.terraform_remote_state.vpc_subnetting_gateways.outputs.private_workloads_subnets_id_us_east_1a[0],data.terraform_remote_state.vpc_subnetting_gateways.outputs.private_workloads_subnets_id_us_east_1b[0],data.terraform_remote_state.vpc_subnetting_gateways.outputs.private_workloads_subnets_id_us_east_1d[0]]
  create_cluster_security_group = true
  # TODO
  #cluster_security_group_id = var.cluster_security_group_id

  # EKS Managed Node Group(s)
  eks_managed_node_group_defaults = {
    instance_types = var.instance_types_node_group_defaults
    # key_name       = "eks-default-vpc"
    block_device_mappings = {
      xvda = {
        device_name = "/dev/xvda"
        ebs = {
          volume_size           = 256
          volume_type           = "gp3"
          iops                  = 5000
          throughput            = 150
          encrypted             = true
          delete_on_termination = true
        }
      }
    }
  }

  eks_managed_node_groups = {
    miscellaneus = {
      min_size       = var.min_size
      max_size       = var.max_size
      desired_size   = var.desired_size
      instance_types = var.instance_types_node_group_defaults
      capacity_type  = var.capacity_type
      ami_type       = "AL2_ARM_64"
    }
  }
  cluster_timeouts = {
    create = "30m"
    update = "30m"
    delete = "30m"
  }

  tags = {
    Environment              = local.environment
    Terraform                = "true"
    cc                       = "infrastructure"
    owner                    = "<EMAIL>"
    group                    = "kubernetes"
    service                  = "eks"
    service_type             = "eks"
    service_role             = "infra"
    department               = "infra"
  }
}
#export TF_LOG="DEBUG"