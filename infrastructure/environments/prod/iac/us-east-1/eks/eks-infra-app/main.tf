provider "aws" {
  region = "us-east-1"

  default_tags {
    tags = {
      environment  = "prod"
      terraform    = "true"
      cc           = "infrastructure"
      department   = "infra"
      group        = "kubernetes"
      owner        = "<EMAIL>"
      service      = "eks"
      service_type = "app"
    }
  }
}

#MAIN CONFIG
data "local_file" "config" {
  filename = "../../foundational/config/default-config.json"
}
# LOCALS
locals {
  config                = jsondecode(data.local_file.config.content)
  environment           = local.config.environment
  region                = local.config.region
  config_bucket         = "${local.environment}-${local.config.bucket}"
  config_dynamodb_table = "${local.environment}-${local.config.dynamodb_table}"
  account_id            = local.config.account_id
  vpc_cidr              = local.config.vpc.main_cidr

  node_groups_jsons = fileset("${path.module}/node-groups", "*.json")
  node_groups_names = [for file in local.node_groups_jsons : replace(file, ".json", "")]

  node_groups = {
    for file in local.node_groups_names :
    file => merge(
      {
        name                 = "eks-app-worker-${file}"
        iam_role_name        = "role-eks-${file}"
        launch_template_name = "lt-eks-${file}"
      },
      jsondecode(file("${path.module}/node-groups/${file}.json"))
    )
  }
}

data "terraform_remote_state" "vpc_subnetting_gateways" {
  backend = "s3"

  config = {
    encrypt        = local.config.encrypt
    region         = local.region
    key            = "${local.environment}/iac/us-east-1/foundational/vpc-subnetting-and-gateways.tfstate"
    bucket         = local.config_bucket
    dynamodb_table = local.config_dynamodb_table
  }
}

module "eks" {
  source = "../../../../../../terraform/modules/official/terraform-aws-eks"

  cluster_name    = local.eks_resource_name
  cluster_version = "1.31"

  authentication_mode = "API_AND_CONFIG_MAP"

  # Add Karpenter discovery tags to the cluster security group
  cluster_security_group_tags = {
    "karpenter.sh/discovery" = local.eks_resource_name
  }

  # These rules will apply to cluster additional security group
  cluster_security_group_additional_rules = {
    https_ingress = {
      description = "Allow Istio webhook validator"
      protocol    = "tcp"
      from_port   = 15000
      to_port     = 16000
      type        = "ingress"
      ## aditional sg-id
      source_cluster_security_group = true
      cidr_blocks                   = [local.vpc_cidr]
    }

    egress = {
      description = "Allow oubound traffic"
      from_port   = 0
      to_port     = 0
      protocol    = "-1"
      type        = "egress"
      cidr_blocks = ["0.0.0.0/0"]
    }

    ## Rules imported from infrastructure\terraform\modules\official\terraform-aws-eks\node_groups.tf ##
    ingress_cluster_443 = {
      description                   = "Cluster API to node groups"
      protocol                      = "tcp"
      from_port                     = 443
      to_port                       = 443
      type                          = "ingress"
      source_cluster_security_group = true
      cidr_blocks                   = [local.vpc_cidr]
    }

    ingress_cluster_kubelet = {
      description                   = "Cluster API to node kubelets"
      protocol                      = "tcp"
      from_port                     = 10250
      to_port                       = 10250
      type                          = "ingress"
      source_cluster_security_group = true
      cidr_blocks                   = [local.vpc_cidr]
    }

    ingress_self_coredns_tcp = {
      description = "Node to node CoreDNS"
      protocol    = "tcp"
      from_port   = 53
      to_port     = 53
      type        = "ingress"
      self        = true
    }

    ingress_self_coredns_udp = {
      description = "Node to node CoreDNS UDP"
      protocol    = "udp"
      from_port   = 53
      to_port     = 53
      type        = "ingress"
      self        = true
    }

    ## Recommended rules ##
    ingress_nodes_ephemeral = {
      description = "Node to node ingress on ephemeral ports"
      protocol    = "tcp"
      from_port   = 1025
      to_port     = 65535
      type        = "ingress"
      self        = true
    }

    # metrics-server
    ingress_cluster_4443_webhook = {
      description                   = "Cluster API to node 4443/tcp webhook"
      protocol                      = "tcp"
      from_port                     = 4443
      to_port                       = 4443
      type                          = "ingress"
      source_cluster_security_group = true
      cidr_blocks                   = [local.vpc_cidr]
    }

    # prometheus-adapter
    ingress_cluster_6443_webhook = {
      description                   = "Cluster API to node 6443/tcp webhook"
      protocol                      = "tcp"
      from_port                     = 6443
      to_port                       = 6443
      type                          = "ingress"
      source_cluster_security_group = true
      cidr_blocks                   = [local.vpc_cidr]
    }

    # Karpenter
    ingress_cluster_8443_webhook = {
      description                   = "Cluster API to node 8443/tcp webhook"
      protocol                      = "tcp"
      from_port                     = 8443
      to_port                       = 8443
      type                          = "ingress"
      source_cluster_security_group = true
      cidr_blocks                   = [local.vpc_cidr]
    }
  }

  # Segurity rules for nodegroups
  node_security_group_additional_rules = {
    https_ingress = {
      description = "Allow Istio webhook validator"
      protocol    = "tcp"
      from_port   = 15000
      to_port     = 16000
      type        = "ingress"
      ## aditional sg-id
      source_cluster_security_group = true
    }

    # This rule allows DNS TCP traffic from karpenter nodes to nodegroup nodes, they have different security groups
    ingress_self_coredns_tcp_1 = {
      description                   = "CoreDNS from karpenter nodess"
      protocol                      = "tcp"
      from_port                     = 53
      to_port                       = 53
      type                          = "ingress"
      source_cluster_security_group = true
    }

    # This rule allows DNS UDP traffic from karpenter nodes to nodegroup nodes, they have different security groups
    ingress_self_coredns_udp_1 = {
      description                   = "CoreDNS UDP from karpenter nodes"
      protocol                      = "udp"
      from_port                     = 53
      to_port                       = 53
      type                          = "ingress"
      source_cluster_security_group = true
    }
  }

  cluster_endpoint_public_access  = false
  cluster_endpoint_private_access = true
  cluster_addons = {
    coredns = {
      addon_version = "v1.11.3-eksbuild.1"

      configuration_values = jsonencode({
        autoScaling = {
          enabled = true,
          minReplicas = 2,
          maxReplicas = 10
        }
      })

      resolve_conflicts_on_create = "OVERWRITE"

      timeouts = {
        create = "25m"
        delete = "10m"
        update = "10m"
      }
    }

    kube-proxy = {
      addon_version = "v1.31.0-eksbuild.5"
      timeouts = {
        create = "25m"
        delete = "10m"
        update = "10m"
      }
    }

    vpc-cni = {
      addon_version = "v1.18.5-eksbuild.1"
      timeouts = {
        create = "25m"
        delete = "10m"
        update = "10m"
      }
    }

    aws-ebs-csi-driver = {
      addon_version = "v1.35.0-eksbuild.1"
      timeouts = {
        create = "25m"
        delete = "10m"
        update = "10m"
      }
    }
  }

  vpc_id                        = data.terraform_remote_state.vpc_subnetting_gateways.outputs.vpc_id
  subnet_ids                    = [data.terraform_remote_state.vpc_subnetting_gateways.outputs.private_workloads_subnets_id_us_east_1a[0], data.terraform_remote_state.vpc_subnetting_gateways.outputs.private_workloads_subnets_id_us_east_1b[0], data.terraform_remote_state.vpc_subnetting_gateways.outputs.private_workloads_subnets_id_us_east_1d[0]]
  control_plane_subnet_ids      = [data.terraform_remote_state.vpc_subnetting_gateways.outputs.private_workloads_subnets_id_us_east_1a[0], data.terraform_remote_state.vpc_subnetting_gateways.outputs.private_workloads_subnets_id_us_east_1b[0], data.terraform_remote_state.vpc_subnetting_gateways.outputs.private_workloads_subnets_id_us_east_1d[0]]
  create_cluster_security_group = true

  # EKS Managed Node Group(s)
  eks_managed_node_group_defaults = {
    iam_role_additional_policies = { AmazonEBSCSIDriverPolicy = "arn:aws:iam::aws:policy/service-role/AmazonEBSCSIDriverPolicy" }
    instance_types               = var.instance_types_node_group_defaults
    iam_role_use_name_prefix     = false

    block_device_mappings = {
      xvda = {
        device_name = "/dev/xvda"
        ebs = {
          volume_size           = 40
          volume_type           = "gp3"
          iops                  = 3000
          throughput            = 150
          encrypted             = true
          delete_on_termination = true
        }
      }
    }

  }

  eks_managed_node_groups = local.node_groups

  cluster_timeouts = {
    create = "30m"
    update = "30m"
    delete = "30m"
  }
}

module "iam_eks_role_1" {
   source = "../../../../../../terraform/modules/official/terraform-aws-iam/modules/iam-eks-role"

   role_name = "role-${local.eks_resource_name}-s3"

   role_policy_arns = {
     0: "arn:aws:iam::aws:policy/AmazonS3FullAccess"
   }

   cluster_service_accounts = {
     (local.eks_resource_name) = [
       "apps:aws-role-s3",
       "apps-legacy:aws-role-s3",
       "jobs-dev:aws-role-s3",
       "monitoring:aws-role-s3",
       "k8ssandra-operator:aws-role-s3"
     ]
   }
 }

 module "iam_eks_role_2" {
   source = "../../../../../../terraform/modules/official/terraform-aws-iam/modules/iam-eks-role"
   role_name = "role-${local.eks_resource_name}-misc"
   role_policy_arns = {
     0: "arn:aws:iam::aws:policy/AdministratorAccess"
   }
   cluster_service_accounts = {
     (local.eks_resource_name) = [
       "apps:aws-role-eks-misc",
       "monitoring:aws-role-s3",
       "jobs-dev:aws-role-eks-misc",
       "gitlab-runners:gitlab-runner-prod"
     ]
   }
 }
