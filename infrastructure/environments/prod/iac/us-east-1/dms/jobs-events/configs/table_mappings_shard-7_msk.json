{"rules": [{"rule-type": "selection", "rule-id": "1", "rule-name": "include_table", "object-locator": {"schema-name": "%", "table-name": "jobs_meta_7"}, "rule-action": "include", "filters": []}, {"rule-type": "object-mapping", "rule-id": "2", "rule-name": "map_to_kafka", "rule-target": "column", "object-locator": {"schema-name": "public", "table-name": "jobs_meta_7"}, "rule-action": "map-record-to-record", "mapping-parameters": {"partition-key-type": "attribute-name", "partition-key-name": "id", "exclude-columns": ["id"], "attribute-mappings": [{"target-attribute-name": "id", "attribute-type": "scalar", "attribute-sub-type": "string", "value": "${id}"}]}}]}