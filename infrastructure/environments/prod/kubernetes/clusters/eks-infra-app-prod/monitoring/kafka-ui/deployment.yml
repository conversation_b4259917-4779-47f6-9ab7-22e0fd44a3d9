apiVersion: apps/v1
kind: Deployment
metadata:
  name: kafka-ui
  namespace: monitoring
  labels:
    app: kafka-ui
spec:
  replicas: 3
  selector:
    matchLabels:
      app: kafka-ui
  template:
    metadata:
      labels:
        app: kafka-ui
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9090"
        prometheus.io/path: "/metrics"
        sidecar.opentelemetry.io/inject: "true"
    spec:
      nodeSelector:
        app: mem-optimized-monitoring-svc
      tolerations:
        - key: app
          operator: Equal
          value: mem-optimized-monitoring-svc
          effect: NoSchedule
      containers:
        - name: kafka-ui
          image: 471112575606.dkr.ecr.us-east-1.amazonaws.com/monitoring/kafka/kafka-ui
          imagePullPolicy: IfNotPresent
          env:
            - name: KAFKA_CLUSTERS_0_NAME
              value: "employers-job-stream"
            - name: KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS
              value: "kafka-broker-employers-job-stream-0.talent.private:9092,kafka-broker-employers-job-stream-1.talent.private:9092,kafka-broker-employers-job-stream-2.talent.private:9092"

            - name: KAFKA_CLUSTERS_1_NAME
              value: "event-manager"
            - name: KAFKA_CLUSTERS_1_BOOTSTRAPSERVERS
              value: "kafka-broker-event-manager-0.talent.private:9092,kafka-broker-event-manager-1.talent.private:9092,kafka-broker-event-manager-2.talent.private:9092"

            - name: KAFKA_CLUSTERS_2_NAME
              value: "auto-campaign"
            - name: KAFKA_CLUSTERS_2_BOOTSTRAPSERVERS
              value: "kafka-broker-auto-campaign-0.talent.private:9092,kafka-broker-auto-campaign-1.talent.private:9092,kafka-broker-auto-campaign-2.talent.private:9092"

            - name: KAFKA_CLUSTERS_3_NAME
              value: "notifications"
            - name: KAFKA_CLUSTERS_3_BOOTSTRAPSERVERS
              value: "kafka-broker-notifications-0.talent.private:9092,kafka-broker-notifications-1.talent.private:9092,kafka-broker-notifications-2.talent.private:9092"

            - name: KAFKA_CLUSTERS_4_NAME
              value: "jobs-events"
            - name: KAFKA_CLUSTERS_4_BOOTSTRAPSERVERS
              value: "kafka-broker-jobs-events-0.talent.private:9092,kafka-broker-jobs-events-1.talent.private:9092,kafka-broker-jobs-events-2.talent.private:9092"

            - name: KAFKA_CLUSTERS_5_NAME
              value: "publishers-events"
            - name: KAFKA_CLUSTERS_5_BOOTSTRAPSERVERS
              value: "kafka-broker-publishers-events-0.talent.private:9092,kafka-broker-publishers-events-1.talent.private:9092,kafka-broker-publishers-events-2.talent.private:9092"

            - name: DYNAMIC_CONFIG_ENABLED
              value: "true"
            - name: PROJECT_NAME
              value: kafka-ui
            - name: PROJECT_VERSION
              value: 1.0.0
          ports:
            - containerPort: 8080