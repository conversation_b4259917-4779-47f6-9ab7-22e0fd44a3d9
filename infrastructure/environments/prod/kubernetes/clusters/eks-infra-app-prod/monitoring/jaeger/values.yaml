provisionDataStore:
  cassandra: false
  elasticsearch: false
  kafka: false

networkPolicy:
  enabled: false

storage:
  # allowed values (cassandra, elasticsearch, grpc-plugin, badger, memory)
  type: elasticsearch
  elasticsearch:
    scheme: http
    host: monitoring-elasticsearch-es-http.elastic-system.svc.cluster.local
    port: 9200
    anonymous: false
    user: elastic
    usePassword: true
    password: 0J3cAtGw6Q40Y9Z5pr3p5S5t
    ## Use existing secret (ignores previous password)
    # existingSecret:
    # existingSecretKey:
    nodesWanOnly: false
    extraEnv: []
    ## ES related env vars to be configured on the concerned components
    # - name: ES_SERVER_URLS
    #   value: http://elasticsearch-master:9200
    # - name: ES_USERNAME
    #   value: elastic
    # - name: ES_INDEX_PREFIX
    #   value: test
    ## ES related cmd line opts to be configured on the concerned components
    cmdlineParams:
      es.use-aliases: true
      # es.server-urls: http://elasticsearch-master:9200
      # es.username: elastic
      # es.index-prefix: test
    tls:
      enabled: false

ingester:
  enabled: false

agent:
  podSecurityContext: {}
  securityContext: {}
  enabled: false
  annotations: {}
  image:
    registry: ************.dkr.ecr.us-east-1.amazonaws.com
    repository: monitoring/jaeger/jaeger-agent
    tag: 1.53.0
    pullPolicy: IfNotPresent
    pullSecrets: []
  cmdlineParams: {}
  extraEnv: []
  daemonset:
    useHostPort: false
    updateStrategy:
      {}
      # type: RollingUpdate
      # rollingUpdate:
      #   maxUnavailable: 1
  service:
    annotations: {}
    # List of IP ranges that are allowed to access the load balancer (if supported)
    loadBalancerSourceRanges: []
    type: ClusterIP
    # zipkinThriftPort :accept zipkin.thrift over compact thrift protocol
    zipkinThriftPort: 5775
    # compactPort: accept jaeger.thrift over compact thrift protocol
    compactPort: 6831
    # binaryPort: accept jaeger.thrift over binary thrift protocol
    binaryPort: 6832
    # samplingPort: (HTTP) serve configs, sampling strategies
    samplingPort: 5778
  resources:
    {}
    # limits:
    #   cpu: 500m
    #   memory: 512Mi
    # requests:
    #   cpu: 256m
    #   memory: 128Mi
  serviceAccount:
    create: true
    # Explicitly mounts the API credentials for the Service Account
    automountServiceAccountToken: false
    name:
    annotations: {}
  nodeSelector: {}
  tolerations: []
  affinity: {}
  podAnnotations: {}
  ## Additional pod labels
  ## ref: https://kubernetes.io/docs/concepts/overview/working-with-objects/labels/
  podLabels: {}
  extraSecretMounts: []
  # - name: jaeger-tls
  #   mountPath: /tls
  #   subPath: ""
  #   secretName: jaeger-tls
  #   readOnly: true
  extraConfigmapMounts: []
  # - name: jaeger-config
  #   mountPath: /config
  #   subPath: ""
  #   configMap: jaeger-config
  #   readOnly: true
  envFrom: []
  useHostNetwork: false
  dnsPolicy: ClusterFirst
  priorityClassName: ""
  initContainers: []

  serviceMonitor:
    enabled: false
    additionalLabels: {}
    # https://github.com/prometheus-operator/prometheus-operator/blob/master/Documentation/api.md#relabelconfig
    relabelings: []
    # -- ServiceMonitor metric relabel configs to apply to samples before ingestion
    # https://github.com/prometheus-operator/prometheus-operator/blob/main/Documentation/api.md#endpoint
    metricRelabelings: []

collector:
  enabled: true
  image:
    registry: ************.dkr.ecr.us-east-1.amazonaws.com
    repository: monitoring/jaeger/jaeger-collector
    tag: 1.53.0
    pullPolicy: IfNotPresent
    pullSecrets: []
  dnsPolicy: ClusterFirst
  extraEnv: []
  envFrom: []
  cmdlineParams: {}
  basePath: /
  replicaCount: 1
  autoscaling:
    enabled: true
    minReplicas: 1
    maxReplicas: 3
    behavior: {}
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  service:
    type: ClusterIP
    # Cluster IP address to assign to service. Set to None to make service headless
    clusterIP: ""
    http:
      port: 14268
      targetPort: 14268
      protocol: TCP
      name: jaeger-collector-http
    grpc:
      port: 14250
      targetPort: 14250
      protocol: TCP
      name: jaeger-collector-grpc
    zipkin:
      port: 9411
      targetPort: 9411
      name: zipkin-http
    otlp:
      grpc:
        name: otel-grcp
        port: 4317
      http:
        name: otel-http
        port: 4318

  ingress:
    enabled: false
  resources:
    limits:
      memory: 1Gi
    requests:
      cpu: 500m
      memory: 512Mi
  serviceAccount:
    create: true
  nodeSelector: {}
  tolerations: 
   - key: "app"
     operator: "Equal"
     value: "mem-optimized-monitoring-svc"
     effect: "NoSchedule"
  affinity:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
        - matchExpressions:
          - key: app
            operator: In
            values:
            - mem-optimized-monitoring-svc
  podAnnotations: {}
  ## Additional pod labels
  ## ref: https://kubernetes.io/docs/concepts/overview/working-with-objects/labels/
  podLabels: {}
  extraSecretMounts: []
  # - name: jaeger-tls
  #   mountPath: /tls
  #   subPath: ""
  #   secretName: jaeger-tls
  #   readOnly: true
  extraConfigmapMounts: []
  # - name: jaeger-config
  #   mountPath: /config
  #   subPath: ""
  #   configMap: jaeger-config
  #   readOnly: true
  # samplingConfig: |-
  #   {
  #     "service_strategies": [
  #       {
  #         "service": "foo",
  #         "type": "probabilistic",
  #         "param": 0.8,
  #         "operation_strategies": [
  #           {
  #             "operation": "op1",
  #             "type": "probabilistic",
  #             "param": 0.2
  #           },
  #           {
  #             "operation": "op2",
  #             "type": "probabilistic",
  #             "param": 0.4
  #           }
  #         ]
  #       },
  #       {
  #         "service": "bar",
  #         "type": "ratelimiting",
  #         "param": 5
  #       }
  #     ],
  #     "default_strategy": {
  #       "type": "probabilistic",
  #       "param": 1
  #     }
  #   }
  priorityClassName: ""

  serviceMonitor:
    enabled: false
    additionalLabels: {}
    # https://github.com/prometheus-operator/prometheus-operator/blob/master/Documentation/api.md#relabelconfig
    relabelings: []
    # -- ServiceMonitor metric relabel configs to apply to samples before ingestion
    # https://github.com/prometheus-operator/prometheus-operator/blob/main/Documentation/api.md#endpoint
    metricRelabelings: []
  initContainers: []
  networkPolicy:
    enabled: false
    # ingressRules:
    #   namespaceSelector: {}
    #   podSelector: {}
    #   customRules: []
    # egressRules:
    #   namespaceSelector: {}
    #   podSelector: {}
    #   customRules: []

query:
  enabled: true
  basePath: /jaeger
  initContainers: []
  oAuthSidecar:
    enabled: false
  podSecurityContext: {}
  securityContext: {}
  agentSidecar:
    enabled: true
    resources:
      limits:
        cpu: 500m
        memory: 512Mi
      requests:
        cpu: 256m
        memory: 128Mi
  annotations: {}
  image:
    registry: ************.dkr.ecr.us-east-1.amazonaws.com
    repository: monitoring/jaeger/jaeger-query
    tag: 1.53.0
    digest: ""
    pullPolicy: IfNotPresent
    pullSecrets: []
  dnsPolicy: ClusterFirst
  cmdlineParams: {}
  extraEnv: []
  envFrom: []
  replicaCount: 1
  service:
    annotations: {}
    type: ClusterIP
    # List of IP ranges that are allowed to access the load balancer (if supported)
    loadBalancerSourceRanges: []
    port: 80
    # Specify a custom target port (e.g. port of auth proxy)
    # targetPort: 8080
    # Specify a specific node port when type is NodePort
    # nodePort: 32500
  ingress:
    enabled: false
  resources:
    limits:
      memory: 512Mi
    requests:
       cpu: 256m
       memory: 128Mi
  serviceAccount:
    create: true
  nodeSelector: {}
  tolerations: 
   - key: "app"
     operator: "Equal"
     value: "mem-optimized-monitoring-svc"
     effect: "NoSchedule"
  affinity:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
        - matchExpressions:
          - key: app
            operator: In
            values:
            - mem-optimized-monitoring-svc
  podAnnotations: {}
  ## Additional pod labels
  ## ref: https://kubernetes.io/docs/concepts/overview/working-with-objects/labels/
  podLabels: {}
  extraConfigmapMounts: []
  # - name: jaeger-config
  #   mountPath: /config
  #   subPath: ""
  #   configMap: jaeger-config
  #   readOnly: true
  extraVolumes: []
  priorityClassName: ""
  serviceMonitor:
    enabled: false
    additionalLabels: {}
    # https://github.com/prometheus-operator/prometheus-operator/blob/master/Documentation/api.md#relabelconfig
    relabelings: []
    # -- ServiceMonitor metric relabel configs to apply to samples before ingestion
    # https://github.com/prometheus-operator/prometheus-operator/blob/main/Documentation/api.md#endpoint
    metricRelabelings: []
  # config: |-
  #   {
  #     "dependencies": {
  #       "dagMaxNumServices": 200,
  #       "menuEnabled": true
  #     },
  #     "archiveEnabled": true,
  #     "tracking": {
  #       "gaID": "UA-000000-2",
  #       "trackErrors": true
  #     }
  #   }
  networkPolicy:
    enabled: false
    # ingressRules:
    #   namespaceSelector: {}
    #   podSelector: {}
    #   customRules: []
    # egressRules:
    #   namespaceSelector: {}
    #   podSelector: {}
    #   customRules: []

spark:
  enabled: false

esIndexCleaner:
  enabled: true
  securityContext:
    runAsUser: 1000
  podSecurityContext:
    runAsUser: 1000
  annotations: {}
  image:
    registry: ************.dkr.ecr.us-east-1.amazonaws.com
    repository: monitoring/jaeger/jaeger-es-index-cleaner
    tag: 1.53.0
    digest: ""
    pullPolicy: IfNotPresent
    pullSecrets: []
  cmdlineParams: {}
  extraEnv:
    - name: ROLLOVER
      value: 'true'
  schedule: "00 * * * *"
  successfulJobsHistoryLimit: 3
  failedJobsHistoryLimit: 3
  concurrencyPolicy: Forbid
  resources:
    limits:
      memory: 512Mi
    requests:
       cpu: 256m
       memory: 128Mi
  numberOfDays: 1
  serviceAccount:
    create: true
    annotations: {}
    # Explicitly mounts the API credentials for the Service Account
    automountServiceAccountToken: false
    name:
  nodeSelector: {}
  tolerations: 
   - key: "app"
     operator: "Equal"
     value: "mem-optimized-monitoring-svc"
     effect: "NoSchedule"
  affinity:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
        - matchExpressions:
          - key: app
            operator: In
            values:
            - mem-optimized-monitoring-svc
  extraSecretMounts: []
  extraConfigmapMounts: []
  podAnnotations: {}
  ## Additional pod labels
  ## ref: https://kubernetes.io/docs/concepts/overview/working-with-objects/labels/
  podLabels: {}
  # ttlSecondsAfterFinished: 120

esRollover:
  enabled: true
  securityContext: {}
  podSecurityContext:
    runAsUser: 1000
  annotations: {}
  image:
    registry: ************.dkr.ecr.us-east-1.amazonaws.com
    repository: monitoring/jaeger/jaeger-es-rollover
    tag: 1.53.0
    digest: ""
    pullPolicy: IfNotPresent
    pullSecrets: []
  cmdlineParams: {}
  extraEnv:
    - name: CONDITIONS
      value: '{"max_age": "6h","max_size": "5gb"}'
  schedule: "*/10 * * * *"
  successfulJobsHistoryLimit: 3
  failedJobsHistoryLimit: 3
  concurrencyPolicy: Forbid
  resources:
    limits:
      memory: 512Mi
    requests:
      cpu: 256m
      memory: 128Mi
  serviceAccount:
    create: true
  nodeSelector: {}
  tolerations: 
   - key: "app"
     operator: "Equal"
     value: "mem-optimized-monitoring-svc"
     effect: "NoSchedule"
  affinity:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
        - matchExpressions:
          - key: app
            operator: In
            values:
            - mem-optimized-monitoring-svc
  extraSecretMounts: []
  extraConfigmapMounts: []
  podAnnotations: {}
  ## Additional pod labels
  ## ref: https://kubernetes.io/docs/concepts/overview/working-with-objects/labels/
  podLabels: {}
  # ttlSecondsAfterFinished: 120
  initHook:
    extraEnv:
      []
      # - name: SHARDS
      #   value: "3"
    annotations: {}
    podAnnotations: {}
    podLabels: {}
    ttlSecondsAfterFinished: 120

esLookback:
  enabled: false

hotrod:
  enabled: false
  
# Array with extra yaml objects to install alongside the chart. Values are evaluated as a template.
extraObjects: []