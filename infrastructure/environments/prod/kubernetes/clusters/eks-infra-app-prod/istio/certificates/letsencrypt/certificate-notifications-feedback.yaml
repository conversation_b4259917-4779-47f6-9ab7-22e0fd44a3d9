apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: notifications-feedback-cert
  namespace: istio-system
spec:
  secretName: notifications-feedback-cert
  duration: 2160h # 90d
  renewBefore: 360h # 15d
  isCA: false
  privateKey:
    algorithm: RSA
    encoding: PKCS1
    size: 2048
  usages:
  - server auth
  - client auth
  dnsNames:
  - "notifications-feedback.prod.talent.com"
  issuerRef:
    name: letsencrypt-valid-certificates
    kind: ClusterIssuer
    group: cert-manager.io
