apiVersion: karpenter.sh/v1
kind: NodePool
metadata:
  name: jobseeker-svc
spec:
  template:
    metadata:
      labels:
        app: jobseeker-svc
    spec:
      expireAfter: 17280h
      requirements:
        - key: app
          operator: Exists
        - key: kubernetes.io/arch
          operator: In
          values: ["amd64"]
        - key: kubernetes.io/os
          operator: In
          values: ["linux"]
        - key: karpenter.sh/capacity-type
          operator: In
          values: ["on-demand"]
        - key: node.kubernetes.io/instance-type
          operator: In
          values: ["c5.2xlarge", "c5a.2xlarge", "c5ad.2xlarge", "c6a.2xlarge", "c6i.2xlarge"]
      nodeClassRef:
        group: karpenter.k8s.aws
        kind: EC2NodeClass
        name: jobseeker-amd-ec2nc
      taints:
        - key: app
          value: jobseeker-svc
          effect: NoSchedule
  limits:
    cpu: 24
  disruption:
    consolidationPolicy: WhenEmptyOrUnderutilized
    consolidateAfter: 1h
    budgets:
    - nodes: "10%"
      reasons:
      - "Empty"
      - "Drifted"
      - "Underutilized"
    - nodes: "3"
    - nodes: "0"
      schedule: "0 9 * * mon-fri"
      duration: 8h
      reasons: 
      - "Underutilized"
