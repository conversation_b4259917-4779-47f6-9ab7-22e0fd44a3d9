apiVersion: karpenter.sh/v1
kind: NodePool
metadata:
  name: ner-redis
spec:
  template:
    metadata:
      labels:
        app: ner-redis
    spec:
      expireAfter: 17280h
      requirements:
        - key: app
          operator: Exists
        - key: kubernetes.io/arch
          operator: In
          values: ["amd64"]
        - key: kubernetes.io/os
          operator: In
          values: ["linux"]
        - key: karpenter.sh/capacity-type
          operator: In
          values: ["on-demand"]
        - key: node.kubernetes.io/instance-type
          operator: In
          values: ["r7i.xlarge", "r5a.xlarge", "r6a.xlarge", "r6i.xlarge", "r5.xlarge"]
      nodeClassRef:
        group: karpenter.k8s.aws
        kind: EC2NodeClass
        name: ner-redis-amd-ec2nc
      taints:
        - key: app
          value: ner-redis
          effect: NoSchedule
  limits:
    cpu: 1000
  disruption:
    consolidationPolicy: WhenEmptyOrUnderutilized
    consolidateAfter: 1h
    budgets:
    - nodes: "10%"
      reasons:
      - "Empty"
      - "Drifted"
      - "Underutilized"
    - nodes: "3"
    - nodes: "0"
      schedule: "0 9 * * mon-fri"
      duration: 8h
      reasons: 
      - "Underutilized"