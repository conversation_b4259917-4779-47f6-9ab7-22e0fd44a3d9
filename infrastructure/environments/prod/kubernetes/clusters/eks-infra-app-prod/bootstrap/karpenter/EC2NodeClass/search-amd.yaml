apiVersion: karpenter.k8s.aws/v1
kind: EC2NodeClass
metadata:
  name: search-amd-ec2nc
spec:
  amiFamily: AL2
  role: "KarpenterNodeRole-eks-infra-app-prod"
  subnetSelectorTerms:
  - tags:
      karpenter.sh/discovery: "true"
  securityGroupSelectorTerms:
  - tags:
      karpenter.sh/discovery: "eks-infra-app-prod"
  amiSelectorTerms:
  - id: "ami-03413b57906e5c8b2"
  blockDeviceMappings:
  - deviceName: /dev/xvda
    ebs:
      volumeSize: 40Gi
      volumeType: gp3
      iops: 3000
      encrypted: true
      deleteOnTermination: true
      throughput: 150
  tags:
    cc: marketplace
    owner: <EMAIL>
    group: search
    type: ec2
