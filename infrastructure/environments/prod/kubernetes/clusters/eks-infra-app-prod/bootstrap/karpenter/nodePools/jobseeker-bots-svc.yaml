apiVersion: karpenter.sh/v1
kind: NodePool
metadata:
  name: jobseeker-bots-svc
spec:
  template:
    metadata:
      labels:
        app: jobseeker-bots-svc
    spec:
      expireAfter: 17280h
      requirements:
        - key: app
          operator: Exists
        - key: kubernetes.io/arch
          operator: In
          values: ["amd64"]
        - key: kubernetes.io/os
          operator: In
          values: ["linux"]
        - key: karpenter.sh/capacity-type
          operator: In
          values: ["spot"]
        - key: karpenter.k8s.aws/instance-category
          operator: In
          values: ["c"]
        - key: "karpenter.k8s.aws/instance-family"
          operator: In
          values: ["c6a","c5a","c6i","c5","c7i", "c3", "c4"]
        - key: "karpenter.k8s.aws/instance-cpu"
          operator: In
          values: ["8","16","32"]
      nodeClassRef:
        group: karpenter.k8s.aws
        kind: EC2NodeClass
        name: default-amd-ec2nc
      taints:
        - key: app
          value: jobseeker-bots-svc
          effect: NoSchedule
  limits:
    cpu: 320
  disruption:
    consolidationPolicy: WhenEmptyOrUnderutilized
    consolidateAfter: 15m
    budgets:
    - nodes: "40%"
      reasons:
      - "Empty"
      - "Drifted"
      - "Underutilized"
    - nodes: "10"

