apiVersion: karpenter.sh/v1
kind: NodePool
metadata:
  name: jobdata-svc
spec:
  template:
    metadata:
      labels:
        app: jobdata-svc
    spec:
      requirements:
        - key: app
          operator: Exists
        - key: kubernetes.io/arch
          operator: In
          values: ["amd64"]
        - key: kubernetes.io/os
          operator: In
          values: ["linux"]
        - key: karpenter.sh/capacity-type
          operator: In
          values: ["on-demand"]
        - key: karpenter.k8s.aws/instance-category
          operator: In
          values: ["c"]
        - key: "karpenter.k8s.aws/instance-family"
          operator: In
          values: ["c6a","c5a","c6i","c5","c7i"]
        - key: "karpenter.k8s.aws/instance-cpu"
          operator: In
          values: ["2","4","8","16"]

      nodeClassRef:
        group: karpenter.k8s.aws
        kind: EC2NodeClass
        name: jobdata-amd-gp3-500gb
      taints:
        - key: app
          value: jobdata-svc
          effect: NoSchedule
  limits:
    cpu: 180
  disruption:
    consolidationPolicy: WhenEmptyOrUnderutilized
    consolidateAfter: 2m
    budgets:
      - nodes: 100%
