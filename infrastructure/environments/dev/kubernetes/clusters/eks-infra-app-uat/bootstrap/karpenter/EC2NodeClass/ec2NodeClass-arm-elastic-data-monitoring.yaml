apiVersion: karpenter.k8s.aws/v1
kind: EC2NodeClass
metadata:
  name: node-class-arm-elastic-monitoring
spec:
  amiFamily: AL2 # Amazon Linux 2
  role: "KarpenterNodeRole-eks-infra-uat-dev" # replace with your cluster name
  subnetSelectorTerms:
    - tags:
        karpenter.sh/discovery:  "true" # replace with your cluster name
  securityGroupSelectorTerms:
    - tags:
        karpenter.sh/discovery: "eks-infra-uat-dev" # replace with your cluster name
  amiSelectorTerms:
    - id: "ami-0164d2524bf3468bf" # {ARM_AMI_ID}
    # - id: "ami-03413b57906e5c8b2"  # {AMD_AMI_ID}
#   - id: "${GPU_AMI_ID}" # <- GPU Optimized AMD AMI 
#   - name: "amazon-eks-node-${K8S_VERSION}-*" # <- automatically upgrade when a new AL2 EKS Optimized AMI is released. This is unsafe for production workloads. Validate AMIs in lower environments before deploying them to production.
  tags:
    cc: infrastructure
    owner: <EMAIL>
    environment: dev
    group: monitoring
    service: elastic-monitoring