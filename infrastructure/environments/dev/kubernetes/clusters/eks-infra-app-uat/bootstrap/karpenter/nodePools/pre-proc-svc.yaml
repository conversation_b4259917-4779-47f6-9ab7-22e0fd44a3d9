apiVersion: karpenter.sh/v1
kind: NodePool
metadata:
  name: pre-proc-svc
spec:
  template:
    metadata:
      labels:
        app: pre-proc-svc
    spec:
      requirements:
        - key: app
          operator: Exists
        - key: kubernetes.io/arch
          operator: In
          values: ["amd64"]
        - key: kubernetes.io/os
          operator: In
          values: ["linux"]
        - key: karpenter.sh/capacity-type
          operator: In
          values: ["spot","on-demand"]
        - key: karpenter.k8s.aws/instance-category
          operator: In
          values: ["c"]
        - key: "karpenter.k8s.aws/instance-family"
          operator: In
          values: ["c6a","c5a","c6i","c5","c7i", "c3", "c4"]
        - key: "karpenter.k8s.aws/instance-cpu"
          operator: In
          values: ["8"]

      nodeClassRef:
        group: karpenter.k8s.aws
        kind: EC2NodeClass
        name: ec2nodeclass-gp3-500gb-amd
      taints:
        - key: app
          value: pre-proc-svc
          effect: NoSchedule
  limits:
    cpu: 80
  disruption:
    consolidationPolicy: WhenEmptyOrUnderutilized
    consolidateAfter: 2m
    budgets:
      - nodes: 100%
