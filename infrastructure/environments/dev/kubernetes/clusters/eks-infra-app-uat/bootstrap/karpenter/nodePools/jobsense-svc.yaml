apiVersion: karpenter.sh/v1
kind: NodePool
metadata:
  name: jobsense-svc
spec:
  template:
    metadata:
      labels:
        app: jobsense-svc
    spec:
      requirements:
        - key: app
          operator: Exists
        - key: kubernetes.io/arch
          operator: In
          values: ["amd64"]
        - key: kubernetes.io/os
          operator: In
          values: ["linux"]
        - key: karpenter.sh/capacity-type
          operator: In
          values: ["spot","on-demand"]
        - key: node.kubernetes.io/instance-type
          operator: In
          values: ["c6a.4xlarge", "c6a.large", "c4.large", "c3.large", "c3.xlarge"]
      nodeClassRef:
        group: karpenter.k8s.aws
        kind: EC2NodeClass
        name: 40gb-gp3-amd-jobsense
      taints:
        - key: app
          value: jobsense-svc
          effect: NoSchedule
  limits:
    cpu: 160
  disruption:
    consolidationPolicy: WhenEmptyOrUnderutilized
    consolidateAfter: 2m
    budgets:
      - nodes: 100%
