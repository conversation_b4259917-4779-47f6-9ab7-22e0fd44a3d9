apiVersion: karpenter.sh/v1
kind: NodePool
metadata:
  name: geocode-svc
spec:
  template:
    metadata:
      labels:
        app: geocode-svc
    spec:
      requirements:
        - key: app
          operator: Exists
        - key: kubernetes.io/arch
          operator: In
          values: ["amd64"]
        - key: kubernetes.io/os
          operator: In
          values: ["linux"]
        - key: karpenter.sh/capacity-type
          operator: In
          values: ["spot","on-demand"]
        - key: node.kubernetes.io/instance-type
          operator: In
          values: ["m5a.2xlarge", "m3.xlarge", "m5a.xlarge", "m4.xlarge"]
      nodeClassRef:
        group: karpenter.k8s.aws
        kind: EC2NodeClass
        name: 40gb-gp3-amd-geocode
      taints:
        - key: app
          value: geocode-svc
          effect: NoSchedule
  limits:
    cpu: 80
  disruption:
    consolidationPolicy: WhenEmptyOrUnderutilized
    consolidateAfter: 2m
    budgets:
      - nodes: 100%
