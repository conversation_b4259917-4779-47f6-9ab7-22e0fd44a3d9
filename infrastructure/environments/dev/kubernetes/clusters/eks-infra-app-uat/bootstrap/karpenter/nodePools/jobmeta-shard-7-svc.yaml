apiVersion: karpenter.sh/v1
kind: NodePool
metadata:
  name: jobmeta-shard-7-svc
spec:
  template:
    metadata:
      labels:
        app: jobmeta-shard-7-svc
    spec:
      requirements:
        - key: app
          operator: Exists
        - key: kubernetes.io/arch
          operator: In
          values: ["amd64"]
        - key: kubernetes.io/os
          operator: In
          values: ["linux"]
        - key: karpenter.sh/capacity-type
          operator: In
          values: ["on-demand"]
        - key: node.kubernetes.io/instance-type
          operator: In
          values: ["r5dn.2xlarge"]
      nodeClassRef:
        group: karpenter.k8s.aws
        kind: EC2NodeClass
        name: 40gb-gp3-raid0-amd-jobmeta
      taints:
        - key: app
          value: jobmeta-shard-7-svc
          effect: NoSchedule
  limits:
    cpu: 400
  disruption:
    consolidationPolicy: WhenEmpty
    consolidateAfter: 2m
