# Install

### Install the operator using helm:
```bash
helm repo add jaegertracing https://jaegertracing.github.io/helm-charts --force-update
helm upgrade --install jaeger jaegertracing/jaeger --namespace monitoring -f infrastructure/environments/dev/kubernetes/clusters/eks-infra-app-dev/monitoring/jaeger/values.yaml
```
## Apply virtual services:
```bash
- kubectl apply -f infrastructure/environments/dev/kubernetes/clusters/eks-infra-app-dev/monitoring/jaeger/vs-jaeger.yml -n monitoring
- kubectl apply -f infrastructure/environments/dev/kubernetes/clusters/eks-infra-app-dev/monitoring/jaeger/vs-jaeger-collector.yml -n monitoring
```