apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  annotations:
    meta.helm.sh/release-name: kube-prometheus-stack
    meta.helm.sh/release-namespace: monitoring
  labels:
    app: kube-prometheus-stack
    app.kubernetes.io/instance: kube-prometheus-stack
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/part-of: kube-prometheus-stack
    app.kubernetes.io/version: 66.2.1
    chart: kube-prometheus-stack-66.2.1
    heritage: Helm
    release: kube-prometheus-stack
  name: talent-latency-alerts
  namespace: monitoring
spec:
  groups:
    - name: PodLatencyAlerts
      rules:
        - alert: HighPodLatency
          expr: |
            (sum by (source_workload, source_workload_namespace, deployment) (label_replace(rate(istio_request_duration_milliseconds_sum{connection_security_policy="mutual_tls",destination_workload!="publishers-rec-api-deployment",destination_workload_namespace=~"apps",reporter=~"destination",source_workload=~"(.*)",source_workload_namespace=~"(istio-system|apps)"}[5m]), "deployment", "$1", "destination_workload", "(.*)")) / sum by (source_workload, source_workload_namespace, deployment) (label_replace(rate(istio_request_duration_milliseconds_count{connection_security_policy="mutual_tls",destination_workload!="publishers-rec-api-deployment",destination_workload_namespace=~"apps",reporter=~"destination",source_workload=~"(.*)",source_workload_namespace=~"(istio-system|apps)"}[5m]), "deployment", "$1", "destination_workload", "(.*)"))) > 250
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "High latency in deployment {{ $labels.deployment }}-PROD "
            environment: PROD
            description: "Deployment {{ $labels.deployment }} in namespace {{ $labels.namespace }} is experiencing high latency."
            grafana_url: "http://grafana.monitoring.talent.com/d/ddtd6rhft2hvka/microservices-detailed-insights?orgId=1&refresh=5s&var-namespace=apps&var-pod={{ $labels.deployment }}"
            runbook_url: "https://talent-teams.atlassian.net/wiki/spaces/HQ/pages/2309160985/High+Pod+Latency+Alerts"