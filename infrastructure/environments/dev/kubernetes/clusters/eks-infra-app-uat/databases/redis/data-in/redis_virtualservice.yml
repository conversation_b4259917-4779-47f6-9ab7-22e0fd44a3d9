apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  namespace: redis
  name: data-in-redis-virtualservice
  labels:
    app: data-in-redis
spec:
  hosts:
    - data-in-redis.talent.com
  gateways:
    - gw-redis-data-in
  tcp:
    - match:
        - port: 6379
      route:
        - destination:
            host: data-in-redis-service.redis.svc.cluster.local
            port:
              number: 6379