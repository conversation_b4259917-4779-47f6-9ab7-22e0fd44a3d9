apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: data-in-redis
  namespace: redis
spec:
  serviceName: data-in-redis-service
  replicas: 6  # Number of nodes in the cluster
  selector:
    matchLabels:
      app: data-in-redis
  template:
    metadata:
      labels:
        app: data-in-redis
    spec:
      nodeSelector:
        app: redis
      tolerations:
        - effect: NoSchedule
          key: app
          operator: Equal
          value: redis
      containers:
      - name: data-in-redis
        image: 730335359603.dkr.ecr.us-east-1.amazonaws.com/databases/redis/redis:6.0
        command: ["redis-server", "/usr/local/etc/redis/redis.conf"]
        ports:
        - containerPort: 6379
        - containerPort: 16379  # Cluster communication port
        resources:
          requests:
            memory: 10Gi
            cpu: 1000m
          limits:
            memory: 64Gi
        volumeMounts:
        - name: redis-config
          mountPath: /usr/local/etc/redis
        - name: redis-data
          mountPath: /usr/share/data
      volumes:
      - name: redis-config
        configMap:
          name: data-in-redis-config
          items:
          - key: redis.conf
            path: redis.conf
  volumeClaimTemplates:
  - metadata:
      name: redis-data
    spec:
      accessModes: [ "ReadWriteOnce" ]
      resources:
        requests:
          storage: 35Gi
      storageClassName: redis-storage-class