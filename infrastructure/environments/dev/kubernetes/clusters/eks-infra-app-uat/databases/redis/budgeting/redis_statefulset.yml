apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: budgeting-redis
  namespace: redis
spec:
  serviceName: budgeting-redis-service
  replicas: 1
  selector:
    matchLabels:
      app: budgeting-redis
  template:
    metadata:
      labels:
        app: budgeting-redis
    spec:
      #nodeSelector:
        #app: redis
      #tolerations:
        #- effect: NoSchedule
          #key: app
          #operator: Equal
          #value: redis
      containers:
      - name: budgeting-redis
        image: 730335359603.dkr.ecr.us-west-2.amazonaws.com/databases/redis/redis:6.0
        command: ["redis-server", "/usr/local/etc/redis/redis.conf"]
        ports:
        - containerPort: 6379
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "4096Mi"
            cpu: "4000m"
        volumeMounts:
        - name: redis-config
          mountPath: /usr/local/etc/redis
        - name: redis-data
          mountPath: /usr/share/data
      volumes:
      - name: redis-config
        configMap:
          name: budgeting-redis-config
          items:
          - key: redis.conf
            path: redis.conf
  volumeClaimTemplates:
  - metadata:
      name: redis-data
    spec:
      accessModes: [ "ReadWriteOnce" ]
      resources:
        requests:
          storage: 20Gi
      storageClassName: redis-storage-class