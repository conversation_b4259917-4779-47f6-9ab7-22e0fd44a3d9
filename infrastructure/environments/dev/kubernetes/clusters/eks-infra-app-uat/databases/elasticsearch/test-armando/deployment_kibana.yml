apiVersion: kibana.k8s.elastic.co/v1
kind: Kibana
metadata:
  name: test-kibana
spec:
  version: 8.13.4
  count: 1
  http:
    tls:
      selfSignedCertificate:
        disabled: true
  elasticsearchRef:
    name: test-elasticsearch
  podTemplate:
    spec:
      containers:
      - name: kibana
        env:
          - name: NODE_OPTIONS
            value: "--max-old-space-size=2048"
        resources:
          requests:
              memory: 50Mi
              cpu: 100m
          limits:
            memory: 2.5Gi
            cpu: 2