apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: data-in-misc-kibana-virtualservice
  labels:
    app: data-in-misc-elasticsearch
spec:
  hosts:
    - data-in-misc-elasticsearch.talent.com
  gateways:
    - gateway-elastic-data-in
  tcp:
    - match:
        - port: 5601
      route:
        - destination:
            host: data-in-misc-kibana-kb-http.elastic-system.svc.cluster.local
            port:
              number: 5601