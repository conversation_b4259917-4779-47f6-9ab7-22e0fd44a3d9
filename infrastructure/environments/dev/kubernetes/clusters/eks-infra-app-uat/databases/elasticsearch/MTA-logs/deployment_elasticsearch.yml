apiVersion: elasticsearch.k8s.elastic.co/v1
kind: Elasticsearch
metadata:
  name: mta-logs-elasticsearch
  labels:
    app: mta-logs-elasticsearch
spec:
  version: 8.13.4
  auth:
    fileRealm:
      - secretName: search-freddy-test
  volumeClaimDeletePolicy: DeleteOnScaledownOnly
  http:
    tls:
      selfSignedCertificate:
        disabled: true
  nodeSets:
    - name: data-node
      count: 1
      config:
        node.store.allow_mmap: false
        node.attr.cpu: arm
      podTemplate:
        metadata:
            annotations:
              prometheus.io/path: "/metrics"
              prometheus.io/port: "9090"
              prometheus.io/scrape: "true"
              sidecar.opentelemetry.io/inject: "true"
        spec:
          serviceAccountName: elasticsearch-s3 
          tolerations:
            - effect: NoSchedule
              operator: "Equal"
              key: app
              value: elastic-mta-logs
          affinity:
            nodeAffinity:
              requiredDuringSchedulingIgnoredDuringExecution:
                nodeSelectorTerms:
                  - matchExpressions:
                    - key: app
                      operator: In
                      values:
                        - "elastic-mta-logs"
          initContainers:
            - name: install-plugins
              image: docker.elastic.co/elasticsearch/elasticsearch:8.13.4  # Match your ES version
              command:
                - sh
                - -c
                - |
                  bin/elasticsearch-plugin install --batch analysis-icu
              volumeMounts:
                - mountPath: /usr/share/elasticsearch/plugins
                  name: plugins
          containers:
            - name: elasticsearch
              env:
              - name: ES_JAVA_OPTS
                value: -Xms3g -Xmx3g
              resources:
                requests:
                  memory: "6Gi"
                  cpu: "1"
                limits:
                  memory: "6Gi"
                  cpu: "1"
              volumeMounts:
                - mountPath: /usr/share/elasticsearch/data
                  name: elasticsearch-data
                - mountPath: /usr/share/elasticsearch/plugins
                  name: plugins
          volumes:
            - name: plugins
              emptyDir: {}
      volumeClaimTemplates:
        - metadata:
            name: elasticsearch-data
          spec:
            accessModes:
              - ReadWriteOnce
            resources:
              requests:
                storage: 100Gi
            storageClassName: elasticsearch-storage-class