# Create a keyspace for your database

CREATE KEYSPACE jobs
WITH replication = {'class':'NetworkTopologyStrategy', 'dc1' : 2};

# Use the created keyspace

USE jobs;

# Create your table

CREATE TABLE jobs (
id text,
title text,
company_name text,
scan_id smallint,
geo_city text,
geo_country text,
geo_region1 text,
geo_region2 text,
feedcode text,
location text,
lat float,
lon float,
PRIMARY KEY ((scan_id), id)
) WITH CLUSTERING ORDER BY (id ASC);

# Create indexes

CREATE INDEX IF NOT EXISTS idx_id ON jobs(id);

# Create materialized views

CREATE MATERIALIZED VIEW jobs_by_feedcode AS
SELECT feedcode, id, title, company_name, geo_city, geo_country, geo_region1, geo_region2, location, lat, lon
FROM jobs
GROUP BY feedcode;

# Create users for your keyspace

CREATE ROLE campaign WITH PASSWORD = 'pass_12345' AND LOGIN = true;

## Grant permissions for the user

GRANT ALL ON KEYSPACE jobs TO jobs_db;
