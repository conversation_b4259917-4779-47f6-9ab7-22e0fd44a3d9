keyspace: stress_test
keyspace_definition: |
  CREATE KEYSPACE IF NOT EXISTS stress_test WITH replication = {'class': 'NetworkTopologyStrategy', 'replication_factor': 3} AND durable_writes = true;

table: jobs
table_definition: |
  CREATE TABLE IF NOT EXISTS stress_test.jobs (
    id bigint,
    bucket int,
    enrich_company_id text,
    enrich_company_industry text,
    enrich_company_name text,
    enrich_company_name_normalized text,
    enrich_date_expired date,
    enrich_date_posted date,
    enrich_general_labor_version text,
    enrich_geo_city text,
    enrich_geo_country text,
    enrich_geo_lat double,
    enrich_geo_location_searched text,
    enrich_geo_lon double,
    enrich_geo_region1 text,
    enrich_geo_region2 text,
    enrich_geo_remote text,
    enrich_is_general_labor int,
    enrich_jobdesc_length text,
    enrich_jobtype text,
    enrich_jobtype_version text,
    enrich_language text,
    enrich_soc_onetsoc_code text,
    source_apply_email text,
    source_benefits text,
    source_city text,
    source_client_tag text,
    source_company_name text,
    source_country text,
    source_cpa int,
    source_date_expired text,
    source_date_posted text,
    source_experience_required text,
    source_job_budget float,
    source_job_temp text,
    source_jobdesc_html text,
    source_jobdesc_text text,
    source_jobtype text,
    source_link text,
    source_location text,
    source_location_raw text,
    source_logo text,
    source_ppc int,
    source_remote text,
    source_reqid text,
    source_salary text,
    source_state text,
    source_title text,
    system_apply_type text,
    system_company_name text,
    system_crawlgroup int,
    system_date_expired date,
    system_date_found date,
    system_date_re_found date,
    system_date_updated date,
    system_feed_type text,
    system_feedcode text,
    system_flag_postprocess int,
    system_hash_jobdesc_company text,
    system_hash_jobdesc_title text,
    system_hash_source_unique_job text,
    system_hash_title_company text,
    system_job_source text,
    system_jobhash text,
    system_scanid int,
    system_status smallint,
    system_updates_today int,
    systsem_scanid int,
    PRIMARY KEY ((bucket), id)
  ) WITH CLUSTERING ORDER BY (id ASC)
    AND bloom_filter_fp_chance = 0.01
    AND caching = {'keys': 'ALL', 'rows_per_partition': 'NONE'}
    AND comment = ''
    AND compaction = {'class': 'org.apache.cassandra.db.compaction.SizeTieredCompactionStrategy', 'max_threshold': 32, 'min_threshold': 4}
    AND compression = {'sstable_compression': 'org.apache.cassandra.io.compress.LZ4Compressor', 'chunk_length_kb': '16'}
    AND crc_check_chance = 1.0
    AND default_time_to_live = 0
    AND gc_grace_seconds = 864000
    AND max_index_interval = 2048
    AND min_index_interval = 128
    AND speculative_retry = '99p';

columnspec:
  - name: id
    size: fixed(10) # size in bytes for the ID
    population: uniform(1..10000000) # Generates unique IDs
  
  - name: bucket
    population: uniform(0..60000)

  - name: enrich_company_id
    size: uniform(5..15)
    population: gaussian(1..100000)

  - name: enrich_company_name
    size: uniform(10..50)
    population: gaussian(1..100000)

  - name: enrich_geo_country
    size: fixed(2)

  - name: enrich_date_posted
    size: fixed(10)
    population: seq(1577836800..1609459200)

  - name: source_title
    size: uniform(20..100)
    population: gaussian(1..100000)

  - name: source_jobdesc_text
    size: uniform(500..2000)
    population: gaussian(1..500000)

  - name: source_jobdesc_html
    size: uniform(500..2000)
    population: gaussian(1..500000)

  - name: source_link
    size: uniform(50..100)
    population: gaussian(1..100000)

  - name: system_jobhash
    size: fixed(32)
    population: gaussian(1..100000)

insert:
  partitions: fixed(1) # each write goes to a single partition
  batchtype: UNLOGGED
  select: fixed(1)/1000 # reads are rare

queries:
  simple1:
    cql: select * from jobs where system_feedcode = ? AND system_scanid = ? AND bucket = ? AND id = ?
    fields: samerow

/opt/cassandra/tools/bin/cassandra-stress user profile=/etc/cassandra/custom_profile.yaml ops\(insert=1\) n=100000 -rate threads=32 -mode native cql3 user=jobs0-superuser password=rEDONF-O5wZbj2JyBa4F