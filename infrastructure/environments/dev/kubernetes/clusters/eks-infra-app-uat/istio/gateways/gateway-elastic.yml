apiVersion: networking.istio.io/v1beta1
kind: Gateway
metadata:
  name: gateway-elastic
spec:
  selector:
    istio: gw-elastic
  servers:
  - hosts:
    - '*.talent.com'
    port:
      name: http
      number: 80
      protocol: HTTP
  - hosts:
    - '*.talent.com'
    port:
      name: https
      number: 443
      protocol: HTTPS
    tls:
      mode: PASSTHROUGH
  - hosts:
    - '*.talent.com'
    port:
      name: tcp
      number: 3000
      protocol: TCP
  - hosts:
    - '*.talent.com'
    port:
      name: grpc
      number: 50051
      protocol: TCP
  - hosts:
    - '*.talent.com'
    port:
      name: tcp-elastic-kibana
      number: 5601
      protocol: TCP
  - hosts:
    - '*.talent.com'
    port:
      name: tcp-elastic
      number: 9200
      protocol: TCP