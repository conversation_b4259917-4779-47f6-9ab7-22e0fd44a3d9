apiVersion: install.istio.io/v1alpha1
kind: IstioOperator
metadata:
  name: istio
spec:
  values:
    global:
      meshID:
      multiCluster:
        clusterName: eks-infra-uat-dev
      network:
      proxy:
        resources:
          requests:
            cpu: 200m
            memory: 512Mi
          limits:
            cpu: 1000m
            memory: 2Gi
    telemetry:
      enabled: true
      v2:
        enabled: true
  hub: 730335359603.dkr.ecr.us-east-1.amazonaws.com/istio
  components:
    pilot:
      enabled: true
      k8s:
        overlays:
        - kind: HorizontalPodAutoscaler
          name: istiod
          patches:
            - path: spec.maxReplicas
              value: 9
            - path: spec.minReplicas
              value: 3
    ingressGateways:
#    - name: istio-ingress-public-notifications
#      label:
#        istio: ingress-gw-public-notifications
#        app: ingress-istio-gw-public-notifications
#      enabled: true
#      k8s:
#        serviceAnnotations:
#          service.beta.kubernetes.io/aws-load-balancer-backend-protocol: "tcp"
#          service.beta.kubernetes.io/aws-load-balancer-connection-idle-timeout: "60"
#          service.beta.kubernetes.io/aws-load-balancer-cross-zone-load-balancing-enabled: "true"
#          service.beta.kubernetes.io/aws-load-balancer-internal: "false" #deprecated in version 2.2.0
#          service.beta.kubernetes.io/aws-load-balancer-scheme: "internet-facing"
#          service.beta.kubernetes.io/aws-load-balancer-ssl-cert: "arn:aws:acm:us-east-1:730335359603:certificate/507f22f2-1c33-4e82-9282-07363efb637c"
#          service.beta.kubernetes.io/aws-load-balancer-ssl-ports: "443"
#          service.beta.kubernetes.io/aws-load-balancer-subnets: "subnet-065091ffb2b3a8da5,subnet-0ea88ccc82db45815,subnet-006dd2f65b3fb5c8b"
#          service.beta.kubernetes.io/aws-load-balancer-target-node-labels: "app=istio-ingress"
#          service.beta.kubernetes.io/aws-load-balancer-type: "nlb"
#          service.beta.kubernetes.io/aws-load-balancer-additional-resource-tags: name=public-notifications,cc=notifications,owner=<EMAIL>,environment=eks-infra-uat-dev,group=notifications,service=notifications
#        podAnnotations:
#          prometheus.io/scrape: "true"
#          prometheus.io/port: "9090"
#          prometheus.io/path: "/metrics"
#          sidecar.opentelemetry.io/inject: "true"
#        hpaSpec:
#          maxReplicas: 3
#          minReplicas: 2
#        nodeSelector:
#          app: istio-ingress
#        tolerations:
#        - key: "app"
#          operator: "Equal"
#          value: "istio-ingress"
#          effect: "NoSchedule"
#        service:
#          ports:
#          - port: 80
#            targetPort: 8070
#            name: http
#            protocol: TCP
#          - port: 443
#            targetPort: 8443
#            name: https
    - name: istio-ingress-jobseeker
      label:
        istio: ingress-gw-jobseeker
        app: ingress-istio-gw-jobseeker
      enabled: true
      k8s:
        serviceAnnotations:
          service.beta.kubernetes.io/aws-load-balancer-subnets: "subnet-002c669eb615a1bee,subnet-04a811868fc68cca7,subnet-0ed9fb7692cccbc0e"
          service.beta.kubernetes.io/aws-load-balancer-scheme: "internal"
          service.beta.kubernetes.io/aws-load-balancer-nlb-target-type: "ip"
          service.beta.kubernetes.io/aws-load-balancer-additional-resource-tags: name=ingress-jobseeker,cc=employer,owner=<EMAIL>,environment=eks-infra-uat-dev,group=jobseeker,service=jobseeker
        podAnnotations:
          prometheus.io/scrape: "true"
          prometheus.io/port: "9090"
          prometheus.io/path: "/metrics"
          sidecar.opentelemetry.io/inject: "true"
        hpaSpec:
          maxReplicas: 12
          minReplicas: 2
        nodeSelector:
          app: istio-ingress
        tolerations:
        - key: "app"
          operator: "Equal"
          value: "istio-ingress"
          effect: "NoSchedule"
        service:
          ports:
          - port: 80
            targetPort: 8070
            name: http
            protocol: TCP
          - port: 443
            targetPort: 8443
            name: https
#    - name: istio-ingressgateway
#      enabled: true
#      k8s:
#        serviceAnnotations:
#          service.beta.kubernetes.io/aws-load-balancer-internal: "true"
#          service.beta.kubernetes.io/aws-load-balancer-subnets: "subnet-002c669eb615a1bee,subnet-04a811868fc68cca7,subnet-0ed9fb7692cccbc0e"
#        podAnnotations:
#          prometheus.io/scrape: "true"
#          prometheus.io/port: "9090"
#          prometheus.io/path: "/metrics"
#          sidecar.opentelemetry.io/inject: "true"
#        hpaSpec:
#          maxReplicas: 3
#          minReplicas: 2
#        nodeSelector:
#          app: istio-ingress
#        tolerations:
#        - key: "app"
#          operator: "Equal"
#          value: "istio-ingress"
#          effect: "NoSchedule"
#        service:
#          ports:
#          - port: 15021
#            targetPort: 15021
#            name: status-port
#            protocol: TCP
#          - port: 80
#            targetPort: 8070
#            name: http2
#            protocol: TCP
#          - port: 443
#            targetPort: 8443
#            name: https
#            protocol: TCP
#          - port: 15012
#            targetPort: 15012
#            name: tcp-istiod
#            protocol: TCP
#          - port: 15443
#            targetPort: 15443
#            name: tls
#            protocol: TCP
#          - port: 3000
#            targetPort: 3000
#            name: http-custom-1
#            protocol: TCP
#          - port: 5000
#            targetPort: 5000
#            name: http-custom-2
#            protocol: TCP
#          - port: 9042
#            targetPort: 9042
#            name: k8ssandra
#            protocol: TCP
#          - port: 4317
#            targetPort: 4317
#            name: jaeger
#            protocol: TCP
#          - port: 8080
#            targetPort: 8080
#            name: http-custom-3
#            protocol: TCP
#          - port: 8081
#            targetPort: 8081
#            name: stargate-auth
#            protocol: TCP
#          - port: 8082
#            targetPort: 8082
#            name: stargate-rest
#            protocol: TCP
#          - port: 9200
#            targetPort: 9200
#            name: elasticsearch
#            protocol: TCP
#          - port: 5601
#            targetPort: 5601
#            name: elasticsearch-kibana
#            protocol: TCP
#          - port: 8983
#            targetPort: 8983
#            name: k8ssandra-solr
#          - port: 30001
#            targetPort: 30001
#            name: jupyter
#          - port: 4040
#            targetPort: 4040
#            name: spark
#          - port: 6379
#            targetPort: 6379
#            name: redis
#    - name: istio-ingres-elastic
#      label:
#        istio: gw-elastic
#        app: istio-gw-elastic
#      enabled: true
#      k8s:
#        serviceAnnotations:
#          service.beta.kubernetes.io/aws-load-balancer-internal: "true"
#          service.beta.kubernetes.io/aws-load-balancer-subnets: "subnet-0ed9fb7692cccbc0e, subnet-002c669eb615a1bee, subnet-04a811868fc68cca7"
#        podAnnotations:
#          prometheus.io/scrape: "true"
#          prometheus.io/port: "9090"
#          prometheus.io/path: "/metrics"
#          sidecar.opentelemetry.io/inject: "true"
#        hpaSpec:
#          maxReplicas: 3
#          minReplicas: 2
#        nodeSelector:
#          app: istio-ingress
#        tolerations:
#        - key: "app"
#          operator: "Equal"
#          value: "istio-ingress"
#          effect: "NoSchedule"
#        service:
#          ports:
#          - port: 9200
#            targetPort: 9200
#            name: elasticsearch
#            protocol: TCP
#          - port: 5601
#            targetPort: 5601
#            name: elasticsearch-kibana
#            protocol: TCP
#    - name: istio-ingres-elastic-data-in
#      label:
#        istio: gw-elastic-data-in
#        app: istio-gw-elastic-data-in
#      enabled: true
#      k8s:
#        serviceAnnotations:
#          service.beta.kubernetes.io/aws-load-balancer-internal: "true"
#          service.beta.kubernetes.io/aws-load-balancer-subnets: "subnet-0ed9fb7692cccbc0e, subnet-002c669eb615a1bee, subnet-04a811868fc68cca7"
#        podAnnotations:
#          prometheus.io/scrape: "true"
#          prometheus.io/port: "9090"
#          prometheus.io/path: "/metrics"
#          sidecar.opentelemetry.io/inject: "true"
#        hpaSpec:
#          maxReplicas: 3
#          minReplicas: 2
#        nodeSelector:
#          app: istio-ingress
#        tolerations:
#        - key: "app"
#          operator: "Equal"
#          value: "istio-ingress"
#          effect: "NoSchedule"
#        service:
#          ports:
#          - port: 9200
#            targetPort: 9200
#            name: elasticsearch
#            protocol: TCP
#          - port: 5601
#            targetPort: 5601
#            name: elasticsearch-kibana
#            protocol: TCP
#    - name: istio-ingres-elastic-soconet
#      label:
#        istio: gw-elastic-soconet
#        app: istio-gw-elastic-soconet
#      enabled: true
#      k8s:
#        serviceAnnotations:
#          service.beta.kubernetes.io/aws-load-balancer-internal: "true"
#          service.beta.kubernetes.io/aws-load-balancer-subnets: "subnet-0ed9fb7692cccbc0e, subnet-002c669eb615a1bee, subnet-04a811868fc68cca7"
#        podAnnotations:
#          prometheus.io/scrape: "true"
#          prometheus.io/port: "9090"
#          prometheus.io/path: "/metrics"
#          sidecar.opentelemetry.io/inject: "true"
#        hpaSpec:
#          maxReplicas: 3
#          minReplicas: 2
#        nodeSelector:
#          app: istio-ingress
#        tolerations:
#        - key: "app"
#          operator: "Equal"
#          value: "istio-ingress"
#          effect: "NoSchedule"
#        service:
#          ports:
#          - port: 9200
#            targetPort: 9200
#            name: elasticsearch
#            protocol: TCP
#          - port: 5601
#            targetPort: 5601
#            name: elasticsearch-kibana
#            protocol: TCP
#    - name: istio-ingres-elastic-monetization
#      label:
#        istio: gw-elastic-monetization
#        app: istio-gw-elastic-monetization
#      enabled: true
#      k8s:
#        serviceAnnotations:
#          service.beta.kubernetes.io/aws-load-balancer-internal: "true"
#          service.beta.kubernetes.io/aws-load-balancer-subnets: "subnet-0ed9fb7692cccbc0e, subnet-002c669eb615a1bee, subnet-04a811868fc68cca7"
#        podAnnotations:
#          prometheus.io/scrape: "true"
#          prometheus.io/port: "9090"
#          prometheus.io/path: "/metrics"
#          sidecar.opentelemetry.io/inject: "true"
#        hpaSpec:
#          maxReplicas: 3
#          minReplicas: 2
#        nodeSelector:
#          app: istio-ingress
#        tolerations:
#        - key: "app"
#          operator: "Equal"
#          value: "istio-ingress"
#          effect: "NoSchedule"
#        service:
#          ports:
#          - port: 9200
#            targetPort: 9200
#            name: elasticsearch
#            protocol: TCP
#          - port: 5601
#            targetPort: 5601
#            name: elasticsearch-kibana
#            protocol: TCP
#    - name: istio-ingres-elastic-mta
#      label:
#        istio: gw-elastic-mta
#        app: istio-gw-elastic-mta
#      enabled: true
#      k8s:
#        serviceAnnotations:
#          service.beta.kubernetes.io/aws-load-balancer-internal: "true"
#          service.beta.kubernetes.io/aws-load-balancer-subnets: "subnet-0ed9fb7692cccbc0e, subnet-002c669eb615a1bee, subnet-04a811868fc68cca7"
#        podAnnotations:
#          prometheus.io/scrape: "true"
#          prometheus.io/port: "9090"
#          prometheus.io/path: "/metrics"
#          sidecar.opentelemetry.io/inject: "true"
#        hpaSpec:
#          maxReplicas: 3
#          minReplicas: 2
#        nodeSelector:
#          app: istio-ingress
#        tolerations:
#        - key: "app"
#          operator: "Equal"
#          value: "istio-ingress"
#          effect: "NoSchedule"
#        service:
#          ports:
#          - port: 9200
#            targetPort: 9200
#            name: elasticsearch
#            protocol: TCP
#          - port: 5601
#            targetPort: 5601
#            name: elasticsearch-kibana
#            protocol: TCP
#    - name: istio-ingres-elastic-jobs
#      label:
#        istio: gw-elastic-jobs
#        app: istio-gw-elastic-jobs
#      enabled: true
#      k8s:
#        serviceAnnotations:
#          service.beta.kubernetes.io/aws-load-balancer-internal: "true"
#          service.beta.kubernetes.io/aws-load-balancer-subnets: "subnet-0ed9fb7692cccbc0e, subnet-002c669eb615a1bee, subnet-04a811868fc68cca7"
#        podAnnotations:
#          prometheus.io/scrape: "true"
#          prometheus.io/port: "9090"
#          prometheus.io/path: "/metrics"
#          sidecar.opentelemetry.io/inject: "true"
#        hpaSpec:
#          maxReplicas: 3
#          minReplicas: 2
#        nodeSelector:
#          app: istio-ingress
#        tolerations:
#        - key: "app"
#          operator: "Equal"
#          value: "istio-ingress"
#          effect: "NoSchedule"
#        service:
#          ports:
#          - port: 9200
#            targetPort: 9200
#            name: elasticsearch
#            protocol: TCP
#          - port: 5601
#            targetPort: 5601
#            name: elasticsearch-kibana
#            protocol: TCP
#    - name: istio-ingres-elastic-cache
#      label:
#        istio: gw-elastic-cache
#        app: istio-gw-elastic-cache
#      enabled: true
#      k8s:
#        serviceAnnotations:
#          service.beta.kubernetes.io/aws-load-balancer-internal: "true"
#          service.beta.kubernetes.io/aws-load-balancer-subnets: "subnet-0ed9fb7692cccbc0e, subnet-002c669eb615a1bee, subnet-04a811868fc68cca7"
#        podAnnotations:
#          prometheus.io/scrape: "true"
#          prometheus.io/port: "9090"
#          prometheus.io/path: "/metrics"
#          sidecar.opentelemetry.io/inject: "true"
#        hpaSpec:
#          maxReplicas: 3
#          minReplicas: 2
#        nodeSelector:
#          app: istio-ingress
#        tolerations:
#        - key: "app"
#          operator: "Equal"
#          value: "istio-ingress"
#          effect: "NoSchedule"
#        service:
#          ports:
#          - port: 9200
#            targetPort: 9200
#            name: elasticsearch
#            protocol: TCP
#          - port: 5601
#            targetPort: 5601
#            name: elasticsearch-kibana
#            protocol: TCP
#    - name: istio-ingres-rabbit-data-in
#      label:
#        istio: gw-rabbit-data-in
#        app: istio-gw-rabbit-data-in
#      enabled: true
#      k8s:
#        serviceAnnotations:
#          service.beta.kubernetes.io/aws-load-balancer-internal: "true"
#          service.beta.kubernetes.io/aws-load-balancer-subnets: "subnet-0ed9fb7692cccbc0e, subnet-002c669eb615a1bee, subnet-04a811868fc68cca7"
#        podAnnotations:
#          prometheus.io/scrape: "true"
#          prometheus.io/port: "9090"
#          prometheus.io/path: "/metrics"
#          sidecar.opentelemetry.io/inject: "true"
#        hpaSpec:
#          maxReplicas: 3
#          minReplicas: 2
#        nodeSelector:
#          app: istio-ingress
#        tolerations:
#        - key: "app"
#          operator: "Equal"
#          value: "istio-ingress"
#          effect: "NoSchedule"
#        service:
#          ports:
#          - port: 5672
#            targetPort: 5672
#            name: rabbitmq-amqp
#            protocol: TCP
#          - port: 15672
#            targetPort: 15672
#            name: rabbitmq-management
#            protocol: TCP
#    - name: istio-ingres-elastic-jobsense
#      label:
#        istio: gw-elastic-jobsense
#        app: istio-gw-elastic-jobsense
#      enabled: true
#      k8s:
#        serviceAnnotations:
#          service.beta.kubernetes.io/aws-load-balancer-internal: "true"
#          service.beta.kubernetes.io/aws-load-balancer-subnets: "subnet-0ed9fb7692cccbc0e, subnet-002c669eb615a1bee, subnet-04a811868fc68cca7"
#        podAnnotations:
#          prometheus.io/scrape: "true"
#          prometheus.io/port: "9090"
#          prometheus.io/path: "/metrics"
#          sidecar.opentelemetry.io/inject: "true"
#        hpaSpec:
#          maxReplicas: 3
#          minReplicas: 2
#        nodeSelector:
#          app: istio-ingress
#        tolerations:
#        - key: "app"
#          operator: "Equal"
#          value: "istio-ingress"
#          effect: "NoSchedule"
#        service:
#          ports:
#          - port: 9200
#            targetPort: 9200
#            name: elasticsearch
#            protocol: TCP
#          - port: 5601
#            targetPort: 5601
#            name: elasticsearch-kibana
#            protocol: TCP
#    - name: istio-ingres-elastic-geo
#      label:
#        istio: gw-elastic-geo
#        app: istio-gw-elastic-geo
#      enabled: true
#      k8s:
#        serviceAnnotations:
#          service.beta.kubernetes.io/aws-load-balancer-internal: "true"
#          service.beta.kubernetes.io/aws-load-balancer-subnets: "subnet-0ed9fb7692cccbc0e, subnet-002c669eb615a1bee, subnet-04a811868fc68cca7"
#        podAnnotations:
#          prometheus.io/scrape: "true"
#          prometheus.io/port: "9090"
#          prometheus.io/path: "/metrics"
#          sidecar.opentelemetry.io/inject: "true"
#        hpaSpec:
#          maxReplicas: 3
#          minReplicas: 2
#        nodeSelector:
#          app: istio-ingress
#        tolerations:
#        - key: "app"
#          operator: "Equal"
#          value: "istio-ingress"
#          effect: "NoSchedule"
#        service:
#          ports:
#          - port: 9200
#            targetPort: 9200
#            name: elasticsearch
#            protocol: TCP
#          - port: 5601
#            targetPort: 5601
#            name: elasticsearch-kibana
#            protocol: TCP
#    - name: istio-ingres-elastic-monitoring
#      label:
#        istio: gw-elastic-monitoring
#        app: istio-gw-elastic-monitoring
#      enabled: true
#      k8s:
#        serviceAnnotations:
#          service.beta.kubernetes.io/aws-load-balancer-internal: "true"
#          service.beta.kubernetes.io/aws-load-balancer-subnets: "subnet-0ed9fb7692cccbc0e, subnet-002c669eb615a1bee, subnet-04a811868fc68cca7"
#        podAnnotations:
#          prometheus.io/scrape: "true"
#          prometheus.io/port: "9090"
#          prometheus.io/path: "/metrics"
#          sidecar.opentelemetry.io/inject: "true"
#        hpaSpec:
#          maxReplicas: 3
#          minReplicas: 2
#        nodeSelector:
#          app: istio-ingress
#        tolerations:
#        - key: "app"
#          operator: "Equal"
#          value: "istio-ingress"
#          effect: "NoSchedule"
#        service:
#          ports:
#          - port: 9200
#            targetPort: 9200
#            name: elasticsearch
#            protocol: TCP
#          - port: 5601
#            targetPort: 5601
#            name: elasticsearch-kibana
#            protocol: TCP
#    - name: istio-ingres-elastic-jobredirect-events
#      label:
#        istio: gw-elastic-jobredirect-events
#        app: istio-gw-elastic-jobredirect-events
#      enabled: true
#      k8s:
#        serviceAnnotations:
#          service.beta.kubernetes.io/aws-load-balancer-internal: "true"
#          service.beta.kubernetes.io/aws-load-balancer-subnets: "subnet-0ed9fb7692cccbc0e, subnet-002c669eb615a1bee, subnet-04a811868fc68cca7"
#        podAnnotations:
#          prometheus.io/scrape: "true"
#          prometheus.io/port: "9090"
#          prometheus.io/path: "/metrics"
#          sidecar.opentelemetry.io/inject: "true"
#        hpaSpec:
#          maxReplicas: 3
#          minReplicas: 2
#        nodeSelector:
#          app: istio-ingress
#        tolerations:
#        - key: "app"
#          operator: "Equal"
#          value: "istio-ingress"
#          effect: "NoSchedule"
#        service:
#          ports:
#          - port: 9200
#            targetPort: 9200
#            name: elasticsearch
#            protocol: TCP
#          - port: 5601
#            targetPort: 5601
#            name: elasticsearch-kibana
#            protocol: TCP
#    - name: istio-eastwestgateway
#      label:
#        istio: eastwestgateway
#        app: istio-eastwestgateway
#      enabled: true
#      k8s:
#        serviceAnnotations:
#          service.beta.kubernetes.io/aws-load-balancer-internal: "true"
#          service.beta.kubernetes.io/aws-load-balancer-subnets: "subnet-002c669eb615a1bee,subnet-04a811868fc68cca7,subnet-0ed9fb7692cccbc0e"
#        podAnnotations:
#          prometheus.io/scrape: "true"
#          prometheus.io/port: "9090"
#          prometheus.io/path: "/metrics"
#          sidecar.opentelemetry.io/inject: "true"
#        hpaSpec:
#          maxReplicas: 3
#          minReplicas: 2
#        nodeSelector:
#          app: istio-ingress
#        tolerations:
#        - key: "app"
#          operator: "Equal"
#          value: "istio-ingress"
#          effect: "NoSchedule"
#        service:
#          ports:
#          - name: status-port
#            port: 15021
#            targetPort: 15021
#          - name: tls
#            port: 15443
#            targetPort: 15443
#          - name: tls-istiod
#            port: 15012
#            targetPort: 15012
#          - name: tls-webhook
#            port: 15017
#            targetPort: 15017
#    - name: istio-ingres-redis-budgeting
#      label:
#        istio: gw-redis-budgeting
#        app: istio-gw-redis-budgeting
#      enabled: true
#      k8s:
#        serviceAnnotations:
#          service.beta.kubernetes.io/aws-load-balancer-internal: "true"
#          service.beta.kubernetes.io/aws-load-balancer-subnets: "subnet-0ed9fb7692cccbc0e, subnet-002c669eb615a1bee, subnet-04a811868fc68cca7"
#        podAnnotations:
#          prometheus.io/scrape: "true"
#          prometheus.io/port: "9090"
#          prometheus.io/path: "/metrics"
#          sidecar.opentelemetry.io/inject: "true"
#        hpaSpec:
#          maxReplicas: 3
#          minReplicas: 2
#        nodeSelector:
#          app: istio-ingress
#        tolerations:
#        - key: "app"
#          operator: "Equal"
#          value: "istio-ingress"
#          effect: "NoSchedule"
#        service:
#          ports:
#          - port: 6379
#            targetPort: 6379
#            name: redis-tcp
#            protocol: TCP
#    - name: istio-ingres-redis-currency
#      label:
#        istio: gw-redis-currency
#        app: istio-gw-redis-currency
#      enabled: true
#      k8s:
#        serviceAnnotations:
#          service.beta.kubernetes.io/aws-load-balancer-internal: "true"
#          service.beta.kubernetes.io/aws-load-balancer-subnets: "subnet-0ed9fb7692cccbc0e, subnet-002c669eb615a1bee, subnet-04a811868fc68cca7"
#        podAnnotations:
#          prometheus.io/scrape: "true"
#          prometheus.io/port: "9090"
#          prometheus.io/path: "/metrics"
#          sidecar.opentelemetry.io/inject: "true"
#        hpaSpec:
#          maxReplicas: 3
#          minReplicas: 2
#        nodeSelector:
#          app: istio-ingress
#        tolerations:
#        - key: "app"
#          operator: "Equal"
#          value: "istio-ingress"
#          effect: "NoSchedule"
#        service:
#          ports:
#          - port: 6379
#            targetPort: 6379
#            name: redis-tcp
#            protocol: TCP
#    - name: istio-ingres-redis-jobredirect
#      label:
#        istio: gw-redis-jobredirect
#        app: istio-gw-redis-jobredirect
#      enabled: true
#      k8s:
#        serviceAnnotations:
#          service.beta.kubernetes.io/aws-load-balancer-internal: "true"
#          service.beta.kubernetes.io/aws-load-balancer-subnets: "subnet-0ed9fb7692cccbc0e, subnet-002c669eb615a1bee, subnet-04a811868fc68cca7"
#        podAnnotations:
#          prometheus.io/scrape: "true"
#          prometheus.io/port: "9090"
#          prometheus.io/path: "/metrics"
#          sidecar.opentelemetry.io/inject: "true"
#        hpaSpec:
#          maxReplicas: 3
#          minReplicas: 2
#        nodeSelector:
#          app: istio-ingress
#        tolerations:
#        - key: "app"
#          operator: "Equal"
#          value: "istio-ingress"
#          effect: "NoSchedule"
#        service:
#          ports:
#          - port: 6379
#            targetPort: 6379
#            name: redis-tcp
#            protocol: TCP
#    - name: istio-ingres-redis-jobs-processing
#      label:
#        istio: gw-redis-jobs-processing
#        app: istio-gw-redis-jobs-processing
#      enabled: true
#      k8s:
#        serviceAnnotations:
#          service.beta.kubernetes.io/aws-load-balancer-internal: "true"
#          service.beta.kubernetes.io/aws-load-balancer-subnets: "subnet-0ed9fb7692cccbc0e, subnet-002c669eb615a1bee, subnet-04a811868fc68cca7"
#        podAnnotations:
#          prometheus.io/scrape: "true"
#          prometheus.io/port: "9090"
#          prometheus.io/path: "/metrics"
#          sidecar.opentelemetry.io/inject: "true"
#        hpaSpec:
#          maxReplicas: 3
#          minReplicas: 2
#        nodeSelector:
#          app: istio-ingress
#        tolerations:
#        - key: "app"
#          operator: "Equal"
#          value: "istio-ingress"
#          effect: "NoSchedule"
#        service:
#          ports:
#          - port: 6379
#            targetPort: 6379
#            name: redis-tcp
#            protocol: TCP
#    - name: istio-ingres-redis-jobseeker
#      label:
#        istio: gw-redis-jobseeker
#        app: istio-gw-redis-jobseeker
#      enabled: true
#      k8s:
#        serviceAnnotations:
#          service.beta.kubernetes.io/aws-load-balancer-internal: "true"
#          service.beta.kubernetes.io/aws-load-balancer-subnets: "subnet-0ed9fb7692cccbc0e, subnet-002c669eb615a1bee, subnet-04a811868fc68cca7"
#        podAnnotations:
#          prometheus.io/scrape: "true"
#          prometheus.io/port: "9090"
#          prometheus.io/path: "/metrics"
#          sidecar.opentelemetry.io/inject: "true"
#        hpaSpec:
#          maxReplicas: 3
#          minReplicas: 2
#        nodeSelector:
#          app: istio-ingress
#        tolerations:
#        - key: "app"
#          operator: "Equal"
#          value: "istio-ingress"
#          effect: "NoSchedule"
#        service:
#          ports:
#          - port: 6379
#            targetPort: 6379
#            name: redis-tcp
#            protocol: TCP
#    - name: istio-ingres-redis-ner
#      label:
#        istio: gw-redis-ner
#        app: istio-gw-redis-ner
#      enabled: true
#      k8s:
#        serviceAnnotations:
#          service.beta.kubernetes.io/aws-load-balancer-internal: "true"
#          service.beta.kubernetes.io/aws-load-balancer-subnets: "subnet-0ed9fb7692cccbc0e, subnet-002c669eb615a1bee, subnet-04a811868fc68cca7"
#        podAnnotations:
#          prometheus.io/scrape: "true"
#          prometheus.io/port: "9090"
#          prometheus.io/path: "/metrics"
#          sidecar.opentelemetry.io/inject: "true"
#        hpaSpec:
#          maxReplicas: 3
#          minReplicas: 2
#        nodeSelector:
#          app: istio-ingress
#        tolerations:
#        - key: "app"
#          operator: "Equal"
#          value: "istio-ingress"
#          effect: "NoSchedule"
#        service:
#          ports:
#          - port: 6379
#            targetPort: 6379
#            name: redis-tcp
#            protocol: TCP
#    - name: istio-ingres-redis-notification
#      label:
#        istio: gw-redis-notifications
#        app: istio-gw-redis-notifications
#      enabled: true
#      k8s:
#        serviceAnnotations:
#          service.beta.kubernetes.io/aws-load-balancer-internal: "true"
#          service.beta.kubernetes.io/aws-load-balancer-subnets: "subnet-0ed9fb7692cccbc0e, subnet-002c669eb615a1bee, subnet-04a811868fc68cca7"
#        podAnnotations:
#          prometheus.io/scrape: "true"
#          prometheus.io/port: "9090"
#          prometheus.io/path: "/metrics"
#          sidecar.opentelemetry.io/inject: "true"
#        hpaSpec:
#          maxReplicas: 3
#          minReplicas: 2
#        nodeSelector:
#          app: istio-ingress
#        tolerations:
#        - key: "app"
#          operator: "Equal"
#          value: "istio-ingress"
#          effect: "NoSchedule"
#        service:
#          ports:
#          - port: 6379
#            targetPort: 6379
#            name: redis-tcp
#            protocol: TCP
#    - name: istio-ingres-redis-pixel-n-postback
#      label:
#        istio: gw-redis-pixel-n-postback
#        app: istio-gw-redis-pixel-n-postback
#      enabled: true
#      k8s:
#        serviceAnnotations:
#          service.beta.kubernetes.io/aws-load-balancer-internal: "true"
#          service.beta.kubernetes.io/aws-load-balancer-subnets: "subnet-0ed9fb7692cccbc0e, subnet-002c669eb615a1bee, subnet-04a811868fc68cca7"
#        podAnnotations:
#          prometheus.io/scrape: "true"
#          prometheus.io/port: "9090"
#          prometheus.io/path: "/metrics"
#          sidecar.opentelemetry.io/inject: "true"
#        hpaSpec:
#          maxReplicas: 3
#          minReplicas: 2
#        nodeSelector:
#          app: istio-ingress
#        tolerations:
#        - key: "app"
#          operator: "Equal"
#          value: "istio-ingress"
#          effect: "NoSchedule"
#        service:
#          ports:
#          - port: 6379
#            targetPort: 6379
#            name: redis-tcp
#            protocol: TCP
#    - name: istio-ingres-redis-publishers
#      label:
#        istio: gw-redis-publishers
#        app: istio-gw-redis-publishers
#      enabled: true
#      k8s:
#        serviceAnnotations:
#          service.beta.kubernetes.io/aws-load-balancer-internal: "true"
#          service.beta.kubernetes.io/aws-load-balancer-subnets: "subnet-0ed9fb7692cccbc0e, subnet-002c669eb615a1bee, subnet-04a811868fc68cca7"
#        podAnnotations:
#          prometheus.io/scrape: "true"
#          prometheus.io/port: "9090"
#          prometheus.io/path: "/metrics"
#          sidecar.opentelemetry.io/inject: "true"
#        hpaSpec:
#          maxReplicas: 3
#          minReplicas: 2
#        nodeSelector:
#          app: istio-ingress
#        tolerations:
#        - key: "app"
#          operator: "Equal"
#          value: "istio-ingress"
#          effect: "NoSchedule"
#        service:
#          ports:
#          - port: 6379
#            targetPort: 6379
#            name: redis-tcp
#            protocol: TCP
#    - name: istio-ingres-redis-users
#      label:
#        istio: gw-redis-users
#        app: istio-gw-redis-users
#      enabled: true
#      k8s:
#        serviceAnnotations:
#          service.beta.kubernetes.io/aws-load-balancer-internal: "true"
#          service.beta.kubernetes.io/aws-load-balancer-subnets: "subnet-0ed9fb7692cccbc0e, subnet-002c669eb615a1bee, subnet-04a811868fc68cca7"
#        podAnnotations:
#          prometheus.io/scrape: "true"
#          prometheus.io/port: "9090"
#          prometheus.io/path: "/metrics"
#          sidecar.opentelemetry.io/inject: "true"
#        hpaSpec:
#          maxReplicas: 3
#          minReplicas: 2
#        nodeSelector:
#          app: istio-ingress
#        tolerations:
#        - key: "app"
#          operator: "Equal"
#          value: "istio-ingress"
#          effect: "NoSchedule"
#        service:
#          ports:
#          - port: 6379
#            targetPort: 6379
#            name: redis-tcp
#            protocol: TCP
#    - name: istio-ingres-redis-data-in
#      label:
#        istio: gw-redis-data-in
#        app: istio-gw-redis-data-in
#      enabled: true
#      k8s:
#        serviceAnnotations:
#          service.beta.kubernetes.io/aws-load-balancer-internal: "true"
#          service.beta.kubernetes.io/aws-load-balancer-subnets: "subnet-0ed9fb7692cccbc0e, subnet-002c669eb615a1bee, subnet-04a811868fc68cca7"
#        podAnnotations:
#          prometheus.io/scrape: "true"
#          prometheus.io/port: "9090"
#          prometheus.io/path: "/metrics"
#          sidecar.opentelemetry.io/inject: "true"
#        hpaSpec:
#          maxReplicas: 3
#          minReplicas: 2
#        nodeSelector:
#          app: istio-ingress
#        tolerations:
#          - key: "app"
#            operator: "Equal"
#            value: "istio-ingress"
#            effect: "NoSchedule"
#        service:
#          ports:
#            - port: 6379
#              targetPort: 6379
#              name: redis-tcp
#              protocol: TCP
  meshConfig:
    localityLbSetting:
      enabled: false
    enableTracing: true
    enableAutoMtls: true
    accessLogFile: /dev/stdout
    accessLogEncoding: JSON
    accessLogFormat: |
      {
        "protocol": "%PROTOCOL%",
        "upstream_service_time": "%REQ(x-envoy-upstream-service-time)%",
        "upstream_local_address": "%UPSTREAM_LOCAL_ADDRESS%",
        "duration": "%DURATION%",
        "upstream_transport_failure_reason": "%UPSTREAM_TRANSPORT_FAILURE_REASON%",
        "route_name": "%ROUTE_NAME%",
        "downstream_local_address": "%DOWNSTREAM_LOCAL_ADDRESS%",
        "user_agent": "%REQ(USER-AGENT)%",
        "response_code": "%RESPONSE_CODE%",
        "response_flags": "%RESPONSE_FLAGS%",
        "start_time": "%START_TIME%",
        "method": "%REQ(:METHOD)%",
        "request_id": "%REQ(X-REQUEST-ID)%",
        "upstream_host": "%UPSTREAM_HOST%",
        "x_forwarded_for": "%REQ(X-FORWARDED-FOR)%",
        "client_ip": "%REQ(True-Client-Ip)%",
        "requested_server_name": "%REQUESTED_SERVER_NAME%",
        "bytes_received": "%BYTES_RECEIVED%",
        "bytes_sent": "%BYTES_SENT%",
        "upstream_cluster": "%UPSTREAM_CLUSTER%",
        "downstream_remote_address": "%DOWNSTREAM_REMOTE_ADDRESS%",
        "authority": "%REQ(:AUTHORITY)%",
        "path": "%REQ(X-ENVOY-ORIGINAL-PATH?:PATH)%",
        "response_code_details": "%RESPONSE_CODE_DETAILS%"
      }
    enablePrometheusMerge: false
    defaultConfig:
      holdApplicationUntilProxyStarts: true
      concurrency: 2
      tracing:
        sampling: 50
        zipkin:
          address: localhost:9411
    defaultProviders:
      metrics:
      - prometheus
