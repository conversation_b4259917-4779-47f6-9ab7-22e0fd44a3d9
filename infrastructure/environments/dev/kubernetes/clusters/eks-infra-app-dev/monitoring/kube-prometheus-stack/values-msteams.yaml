# config.yaml
---
replicaCount: 1
image:
  repository: quay.io/prometheusmsteams/prometheus-msteams
  tag: v1.5.1

connectors:
# in alertmanager, this will be used as http://prometheus-msteams:2000/sre
#- sre: 'https://talentcom.webhook.office.com/webhookb2/3dfcf2a9-017c-4e57-b728-c908d2e642e8@a7b464e0-870c-43fc-8e72-6de7aeddfcd5/IncomingWebhook/97045c913cfa44baadb32d52c27191b3/e584eb5d-d1bc-4809-8e18-b2812a965130/V2hH8qf7lYzHaifuSJSfEEm2nz0-o9M4vSHChoHl2M1ow1'
- test: 'https://talentcom.webhook.office.com/webhookb2/3dfcf2a9-017c-4e57-b728-c908d2e642e8@a7b464e0-870c-43fc-8e72-6de7aeddfcd5/IncomingWebhook/ebf17173b0d74db4aee5355c676bb793/e584eb5d-d1bc-4809-8e18-b2812a965130/V2sEQCoG0Aol87O_rJuvsDW7rHlHtXZHbqaG8H6y_6L1I1'
# in alertmanager, this will be used as http://prometheus-msteams:2000/data-in
#- dataIn: 'https://talentcom.webhook.office.com/webhookb2/f0a35b0c-1192-4719-a489-d83afd206911@a7b464e0-870c-43fc-8e72-6de7aeddfcd5/IncomingWebhook/96324389d34a49ce87e5c9defc8617e4/e584eb5d-d1bc-4809-8e18-b2812a965130/V2haAjDHknfUPgzxc9WlcoMAyB_v2YYKCt7vU8dnI5ntg1'
# in alertmanager, this will be used as http://prometheus-msteams:2000/emails
#- emails: 'https://talentcom.webhook.office.com/webhookb2/f0a35b0c-1192-4719-a489-d83afd206911@a7b464e0-870c-43fc-8e72-6de7aeddfcd5/IncomingWebhook/eb6113ad80ce4f878f0f2fbfaf4304e6/e584eb5d-d1bc-4809-8e18-b2812a965130/V2E7-k-E4_TTTLXSn1YV8VTH1iz97ISVPej3lCsta77_41'
# in alertmanager, this will be used as http://prometheus-msteams:2000/employers-publishers
#- employers-publishers: 'https://talentcom.webhook.office.com/webhookb2/f0a35b0c-1192-4719-a489-d83afd206911@a7b464e0-870c-43fc-8e72-6de7aeddfcd5/IncomingWebhook/45a1d8aed6fd4806ab934022ebf4dac5/e584eb5d-d1bc-4809-8e18-b2812a965130/V2gXX3CjFJvJshGDAynt4HPkcl8iWICQj44VVCjXI6TOY1'
## in alertmanager, this will be used as http://prometheus-msteams:2000/event-redshift
#- event-redshift: 'https://talentcom.webhook.office.com/webhookb2/f0a35b0c-1192-4719-a489-d83afd206911@a7b464e0-870c-43fc-8e72-6de7aeddfcd5/IncomingWebhook/4904de9fa79c4bd5b384a6fdf2077253/e584eb5d-d1bc-4809-8e18-b2812a965130/V2w_63F7fY9rPy_alIopV1YlpK-wNWxKEftpjwHRrVyec1'
## in alertmanager, this will be used as http://prometheus-msteams:2000/frontend
#- frontend: 'https://talentcom.webhook.office.com/webhookb2/f0a35b0c-1192-4719-a489-d83afd206911@a7b464e0-870c-43fc-8e72-6de7aeddfcd5/IncomingWebhook/abf4095da1b54426a17eb68d02465924/e584eb5d-d1bc-4809-8e18-b2812a965130/V2d7r0WdNaoyhy6beyDpUlnqeua9QpNgxgzv5trlU-fvE1'
## in alertmanager, this will be used as http://prometheus-msteams:2000/monetizations
#- monetizations: 'https://talentcom.webhook.office.com/webhookb2/f0a35b0c-1192-4719-a489-d83afd206911@a7b464e0-870c-43fc-8e72-6de7aeddfcd5/IncomingWebhook/c9bfb4923df4440fac03469a8d10dcba/e584eb5d-d1bc-4809-8e18-b2812a965130/V23tsDY6Lsm22gD-3m7K3lJBf3PtU_VidVt3UG6gNfTKM1'
## in alertmanager, this will be used as http://prometheus-msteams:2000/redirections
#- redirections: 'https://talentcom.webhook.office.com/webhookb2/f0a35b0c-1192-4719-a489-d83afd206911@a7b464e0-870c-43fc-8e72-6de7aeddfcd5/IncomingWebhook/bff3aa2d3b2a4f1a8208bc8d7d1999d0/e584eb5d-d1bc-4809-8e18-b2812a965130/V2U62rUd4-rk7cPzBTGDayyi7NnRYpCSoTzanylDHRSRM1'
## in alertmanager, this will be used as http://prometheus-msteams:2000/search
#- search: 'https://talentcom.webhook.office.com/webhookb2/f0a35b0c-1192-4719-a489-d83afd206911@a7b464e0-870c-43fc-8e72-6de7aeddfcd5/IncomingWebhook/165d1ba4416549129106bddfb9b76041/e584eb5d-d1bc-4809-8e18-b2812a965130/V2przoFwpBoLFxrSOphAY-U4_5nJ-LrQh47-kOuRRjlRs1'

# extraEnvs is useful for adding extra environment variables such as proxy settings
# extraEnvs:
#   HTTP_PROXY: http://corporateproxy:8080
#   HTTPS_PROXY: http://corporateproxy:8080
# container:
#   additionalArgs:
#     - -debug

# Enable metrics for prometheus operator
metrics:
  serviceMonitor:
    enabled: true
    additionalLabels:
      release: prometheus-msteams # change this accordingly
    scrapeInterval: 30s