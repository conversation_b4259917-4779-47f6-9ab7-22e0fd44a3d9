apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-valid-certificates
  namespace: istio-system
spec:
  acme:
    email: <EMAIL>
    # server refers to letsencrypt prod environment NOT TALENT PROD ENVIRONMENT
    server: https://acme-v02.api.letsencrypt.org/directory
    privateKeySecretRef:
      # Secret resource that will be used to store the account's private key.      
      name: letsencrypt-valid-certificates
    solvers:
    - http01:
        ingress:
          class: istio
          # ingressTemplate prevents the generation of new lb from the ingress resource
          ingressTemplate:
            metadata:
              annotations:
                acme.cert-manager.io/http01-edit-in-place: "true"
                # Specify the Istio gateway to use
                cert-manager.io/issuer-name: "letsencrypt-valid-certificates"
                cert-manager.io/cluster-issuer: "letsencrypt-valid-certificates"
                kubernetes.io/ingress.class: "istio"
                # Use the istio-ingress-public gateway
                networking.istio.io/ingress-gateway: "istio-system/istio-ingress-public"
                external-dns.alpha.kubernetes.io/hostname: "a0aa69264b4724f339e9662a2108bb90-**********.us-east-1.elb.amazonaws.com"

