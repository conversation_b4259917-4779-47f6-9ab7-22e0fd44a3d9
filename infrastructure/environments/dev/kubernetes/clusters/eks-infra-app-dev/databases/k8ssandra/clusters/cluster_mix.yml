apiVersion: k8ssandra.io/v1alpha1
kind: K8ssandraCluster
metadata:
  name: cluster0
spec:
  cassandra:
    serverVersion: "4.0.4"
    clusterName: "cluster0"
    telemetry:
      prometheus:
        enabled: true
      mcac:
        enabled: false
    tolerations:
      - effect: NoSchedule
        key: app
        operator: Equal
        value: k8ssandra
    resources:
      requests:
        memory: 28Gi
        cpu: 7000m
      limits:
        memory: 30Gi
    datacenters:
      - metadata:
          name: dc1
        size: 3
        stopped: false
        racks:
          - name: rack1
            nodeAffinityLabels:
              app: k8ssandra
          - name: rack2
            nodeAffinityLabels:
              app: k8ssandra
          - name: rack3
            nodeAffinityLabels:
              app: k8ssandra
        storageConfig:
          cassandraDataVolumeClaimSpec:
            storageClassName: gp3
            accessModes:
              - ReadWriteOnce
            resources:
              requests:
                storage: 150Gi
        config:
          jvmOptions:
            heapSize: 18Gi
  reaper:
    httpManagement:
      enabled: true
    heapSize: 15Gi
    autoScheduling:
      enabled: false
    telemetry:
      prometheus:
        enabled: true

  # medusa:
  #   storageProperties:
  #     storageProvider: s3
  #     bucketName: talent-dev-jobs-backup
  #     prefix: test
  #     storageSecretRef:
  #       name: medusa-bucket-key
  #     secure: false

  # stargate:
  #   size: 1
  #   heapSize: 20Gi
  #   tolerations:
  #     - effect: NoSchedule
  #       key: app
  #       operator: Equal
  #       value: k8ssandra
  