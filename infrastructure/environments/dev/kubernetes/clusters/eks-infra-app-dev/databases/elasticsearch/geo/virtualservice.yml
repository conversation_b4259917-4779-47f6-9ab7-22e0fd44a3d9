apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: geo-elasticsearch-virtualservice
  labels:
    app: geo-elasticsearch
spec:
  hosts:
    - geo-elasticsearch.talent.com
  gateways:
    - gateway-elastic-geo
  tcp:
    - match:
        - port: 9200
      route:
        - destination:
            host: geo-elasticsearch-es-http.elastic-system.svc.cluster.local
            port:
              number: 9200