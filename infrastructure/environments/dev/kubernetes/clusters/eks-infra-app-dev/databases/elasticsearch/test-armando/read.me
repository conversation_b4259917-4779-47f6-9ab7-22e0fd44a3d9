https://www.elastic.co/guide/en/cloud-on-k8s/current/k8s-deploy-eck.html


kubectl create -f https://download.elastic.co/downloads/eck/2.12.1/crds.yaml
kubectl apply -f https://download.elastic.co/downloads/eck/2.12.1/operator.yaml

kubectl -n elastic-system logs -f statefulset.apps/elastic-operator

kubectl apply -f deployment_elasticsearch.yml


kubectl get elasticsearch
kubectl get pods --selector='elasticsearch.k8s.elastic.co/cluster-name=quickstart'
kubectl logs -f quickstart-es-default-0


kubectl port-forward service/quickstart-es-http 9200

kubectl get service quickstart-es-http

# byV7iG66W03bIRU9MH7853PF
PASSWORD=$(kubectl get secret search-elasticsearch-es-elastic-user -o go-template='{{.data.elastic | base64decode}}')

6L8RFWQM5O1c1XK47v0Hp6o3

kubectl apply -f deployment_kibana.yml
kubectl get kibana

kubectl get pod --selector='kibana.k8s.elastic.co/name=quickstart'


kubectl get service quickstart-kb-http