apiVersion: kibana.k8s.elastic.co/v1
kind: Kibana
metadata:
  name: jobs-kibana
spec:
  version: 8.13.4
  count: 1
  http:
    tls:
      selfSignedCertificate:
        disabled: true
  elasticsearchRef:
    name: jobs-elasticsearch
  podTemplate:
    spec:
      tolerations:
        - key: app
          operator: Equal
          value: mem-optimized-monitoring-svc
          effect: NoSchedule
      affinity:
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - preference:
                matchExpressions:
                  - key: app
                    operator: In
                    values:
                      - mem-optimized-monitoring-svc
              weight: 1
      containers:
        - name: kibana
          env:
            - name: NODE_OPTIONS
              value: "--max-old-space-size=2048"
          resources:
            requests:
              memory: 50Mi
              cpu: 100m
            limits:
              memory: 2.5Gi
              cpu: 2