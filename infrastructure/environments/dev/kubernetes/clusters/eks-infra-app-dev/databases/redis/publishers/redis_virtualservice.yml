apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  namespace: redis
  name: publisherss-redis-virtualservice
  labels:
    app: publishers-redis
spec:
  hosts:
    - publishers-redis.talent.com
  gateways:
    - gw-redis-publishers
  tcp:
    - match:
        - port: 6379
      route:
        - destination:
            host: publishers-redis-service.redis.svc.cluster.local
            port:
              number: 6379