apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: currency-redis
  namespace: redis
spec:
  serviceName: currency-redis-service
  replicas: 1
  selector:
    matchLabels:
      app: currency-redis
  template:
    metadata:
      labels:
        app: currency-redis
    spec:
      nodeSelector:
        app: redis-shared
      tolerations:
        - effect: NoSchedule
          key: app
          operator: Equal
          value: redis-shared
      containers:
      - name: currency-redis
        image: 730335359603.dkr.ecr.us-east-1.amazonaws.com/databases/redis/redis:8.0.2
        command: ["redis-server", "/usr/local/etc/redis/redis.conf"]
        ports:
        - containerPort: 6379
        resources:
          requests:
            memory: "10Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
        volumeMounts:
        - name: redis-config
          mountPath: /usr/local/etc/redis
        - name: redis-data
          mountPath: /usr/share/data
      volumes:
      - name: redis-config
        configMap:
          name: currency-redis-config
          items:
          - key: redis.conf
            path: redis.conf
  volumeClaimTemplates:
  - metadata:
      name: redis-data
    spec:
      accessModes: [ "ReadWriteOnce" ]
      resources:
        requests:
          storage: 20Gi
      storageClassName: redis-storage-class