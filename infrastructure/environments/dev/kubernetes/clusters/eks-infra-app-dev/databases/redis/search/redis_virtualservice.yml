apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  namespace: redis
  name: search-redis-virtualservice
  labels:
    app: search-redis
spec:
  hosts:
    - search-redis.talent.com
  gateways:
    - gw-redis-search
  tcp:
    - match:
        - port: 6379
      route:
        - destination:
            host: search-redis-service.redis.svc.cluster.local
            port:
              number: 6379