apiVersion: karpenter.sh/v1
kind: NodePool
metadata:
  name: elastic-cache
spec:
  template:
    metadata:
      labels:
        app: elastic-cache
    spec:
      requirements:
        - key: app
          operator: Exists
        - key: kubernetes.io/arch
          operator: In
          values: ["arm64"]
        - key: kubernetes.io/os
          operator: In
          values: ["linux"]
        - key: karpenter.sh/capacity-type
          operator: In
          values: ["spot","on-demand"]
        - key: node.kubernetes.io/instance-type
          operator: In
          values: ["c6g.2xlarge"]
      nodeClassRef:
        group: karpenter.k8s.aws
        kind: EC2NodeClass
        name: node-class-arm-elastic-cache
      taints:
        - key: app
          value: elastic-cache
          effect: NoSchedule
  limits:
    cpu: 60

  disruption:
    consolidationPolicy: WhenEmptyOrUnderutilized
    consolidateAfter: 2m
    budgets:
      - nodes: 100%
