apiVersion: karpenter.sh/v1
kind: NodePool
metadata:
  name: mem-optimized-svc
spec:
  template:
    metadata:
      labels:
        app: mem-optimized-svc
    spec:
      requirements:
        - key: app
          operator: Exists
        - key: kubernetes.io/arch
          operator: In
          values: ["amd64"]
        - key: kubernetes.io/os
          operator: In
          values: ["linux"]
        - key: karpenter.sh/capacity-type
          operator: In
          values: ["spot","on-demand"]
        - key: karpenter.k8s.aws/instance-category
          operator: In
          values: ["r"]
        - key: "karpenter.k8s.aws/instance-family"
          operator: In
          values: ["r6a","r5a","r6i","r5","r7i","r3", "r4"]
        - key: "karpenter.k8s.aws/instance-cpu"
          operator: In
          values: ["1","2","4"]
      nodeClassRef:
        group: karpenter.k8s.aws
        kind: EC2NodeClass
        name: mem-optimized-amd-ec2nc
      taints:
        - key: app
          value: mem-optimized-svc
          effect: NoSchedule
  limits:
    cpu: 20
  disruption:
    consolidationPolicy: WhenEmptyOrUnderutilized
    consolidateAfter: 2m
    budgets:
      - nodes: 100%