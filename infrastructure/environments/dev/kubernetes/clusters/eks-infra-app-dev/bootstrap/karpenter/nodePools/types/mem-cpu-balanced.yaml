apiVersion: karpenter.sh/v1
kind: NodePool
metadata:
  name: mem-cpu-balanced-svc
spec:
  template:
    metadata:
      labels:
        app: mem-cpu-balanced-svc
    spec:
      requirements:
        - key: app
          operator: Exists
        - key: kubernetes.io/arch
          operator: In
          values: ["amd64"]
        - key: kubernetes.io/os
          operator: In
          values: ["linux"]
        - key: karpenter.sh/capacity-type
          operator: In
          values: ["on-demand"]
        - key: "karpenter.k8s.aws/instance-family"
          operator: In
          values: ["m7i","m7i-flex","m7a"]
        - key: "karpenter.k8s.aws/instance-cpu"
          operator: In
          values: ["2","4","8"]
      nodeClassRef:
        group: karpenter.k8s.aws
        kind: EC2NodeClass
        name: ec2nodeclass-gp3-40gb-amd
      taints:
        - key: app
          value: mem-cpu-balanced-svc
          effect: NoSchedule

    #budgets:
    #- nodes: 10%
    # On Weekdays during business hours, don't do any deprovisioning.
    #- schedule: "0 9 * * mon-fri"
    #  duration: 8h
     # nodes: "0"
  limits:
    cpu: 1000
    #memory: 100Gi
    
  # Priority given to the NodePool when the scheduler considers which NodePool
  # to select. Higher weights indicate higher priority when comparing NodePools.
  # Specifying no weight is equivalent to specifying a weight of 0.
  #weight: 10
  disruption:
    consolidationPolicy: WhenEmptyOrUnderutilized
    consolidateAfter: 2m
