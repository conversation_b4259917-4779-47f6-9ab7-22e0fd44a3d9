apiVersion: karpenter.sh/v1
kind: NodePool
metadata:
  name: seo-svc
spec:
  template:
    metadata:
      labels:
        app: seo-svc
    spec:
      requirements:
        - key: app
          operator: Exists
        - key: kubernetes.io/arch
          operator: In
          values: ["amd64"]
        - key: kubernetes.io/os
          operator: In
          values: ["linux"]
        - key: karpenter.sh/capacity-type
          operator: In
          values: ["on-demand"]
        - key: node.kubernetes.io/instance-type
          operator: In
          values: ["c5.xlarge","c5a.xlarge","c5ad.xlarge","c6a.xlarge","c6i.xlarge"]
        # - key: karpenter.k8s.aws/instance-category
        #   operator: In
        #   values: ["c"]
        # - key: karpenter.k8s.aws/instance-generation
        #   operator: Gt
        #   values: ["2"]
      nodeClassRef:
        group: karpenter.k8s.aws
        kind: EC2NodeClass
        name: node-class-amd
      taints:
        - key: app
          value: seo-svc
          effect: NoSchedule

    #budgets:
    #- nodes: 10%
    # On Weekdays during business hours, don't do any deprovisioning.
    #- schedule: "0 9 * * mon-fri"
    #  duration: 8h
     # nodes: "0"
  limits:
    cpu: 12
    #memory: 100Gi
    
  # Priority given to the NodePool when the scheduler considers which NodePool
  # to select. Higher weights indicate higher priority when comparing NodePools.
  # Specifying no weight is equivalent to specifying a weight of 0.
  #weight: 10
  disruption:
    consolidationPolicy: WhenEmptyOrUnderutilized
    consolidateAfter: 2m
