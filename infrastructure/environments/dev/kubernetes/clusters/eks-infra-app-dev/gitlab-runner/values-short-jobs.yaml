gitlabUrl: https://git.talent.com/
imagePullPolicy: IfNotPresent
runnerRegistrationToken: glrt-W8KwXjFJ8YDG-qu4sdUA
automountServiceAccountToken: true
terminationGracePeriodSeconds: 900
shutdown_timeout: 900
revisionHistoryLimit: 2
logLevel: debug
concurrent: 25
checkInterval: 5
replicas: 1

podLabels:
  type: job

nodeSelector:
  app: gitlab-runner-m

tolerations:
  - key: app
    operator: Equal
    value: gitlab-runner-m
    effect: NoSchedule

topologySpreadConstraints:
  - maxSkew: 1
    topologyKey: kubernetes.io/hostname
    whenUnsatisfiable: DoNotSchedule
    labelSelector:
      matchLabels:
        type: job
        app: gitlab-runner-long-jobs

runners:
  privileged: true
  maximumTimeout: 900
  config: |
    [[runners]]
      [runners.feature_flags]
        FF_ENABLE_BASH_EXIT_CODE_CHECK = true
        FF_SCRIPT_SECTIONS = true
        FF_USE_FASTZIP = true  #  for faster artifact uploads
        FF_USE_DIRECT_DOWNLOAD = true  #  for faster artifact downloads

      [runners.kubernetes]
        namespace = "gitlab-runners"
        pull_policy = "if-not-present"
        tls_verify = false
        image = "************.dkr.ecr.us-east-1.amazonaws.com/gitlab-runner:v1"
        privileged = true
        service_account = "gitlab-runner-long-jobs"
        cache_dir = "/cache"
        pod_labels_overwrite_allowed = "talent.*"

        # Main build container resources
               cpu_request = "1"
         memory_request = "2Gi"
        ephemeral_storage_request = "1Gi"
        
        # Helper container resources
        helper_cpu_request = "0.25"
        helper_memory_request = "512Mi"
        helper_ephemeral_storage_request = "512Mi"
        
        # Service container resources
        service_cpu_request = "1"
        service_memory_request = "1Gi"
        service_ephemeral_storage_request = "1Gi"

        [runners.kubernetes.node_selector]
          app = "gitlab-runner-s"
          
        [runners.kubernetes.node_tolerations]
          "app=gitlab-runner-s" = "NoSchedule"

        [[runners.kubernetes.volumes.host_path]]
          name = "hostpath-cache"
          mount_path = "/cache"
          read_only = false
          host_path = "/cache"

securityContext:
  allowPrivilegeEscalation: true
  privileged: true
  capabilities:
    drop: ["ALL"]
    
rbac:
  create: true
  serviceAccountName: gitlab-runner-long-jobs
  serviceAccountAnnotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/role-eks-infra-app-dev-misc

  rules:
    - apiGroups: [""]
      resources: ["pods"]
      verbs: ["list", "get", "watch", "create", "patch", "delete"]
    - apiGroups: [""]
      resources: ["pods/exec"]
      verbs: ["create", "patch", "delete"]
    - apiGroups: [""]
      resources: ["pods/log"]
      verbs: ["get"]
    - apiGroups: [""]
      resources: ["pods/attach"]
      verbs: ["list", "get", "create", "delete", "update"]
    - apiGroups: [""]
      resources: ["secrets"]
      verbs: ["list", "get", "create", "delete", "update"]      
    - apiGroups: [""]
      resources: ["configmaps"]
      verbs: ["list", "get", "create", "delete", "update"]
    - apiGroup: [""]
      resources: ["services"]
      verbs: ["get", "list", "watch", "create", "patch", "delete"]
    - apiGroup: [""]
      resources: ["serviceAccounts"]
      verbs: ["get"]