---
- hosts: control_plane
  name: Apply <PERSON><PERSON>sql
  become: true
  become_user: root
  vars:
    helm_chart_url: "https://charts.bitnami.com/bitnami"
    deployment_name: "tln-mysql"
    deployment_namespace: "talent"
  tasks:
    - name: Create the variables environment for kubeconfig
      shell: | 
        export NS={{ deployment_namespace }}
        export KC=/home/<USER>/.kube/config
        export KUBECONFIG_SAVED="$KC"
        echo $NS
        echo $KC
        echo $KUBECONFIG_SAVED
        chmod -R 755 $KC

    - name: Add helm repo
      kubernetes.core.helm_repository:
       name: bitnami
       repo_url: "{{ helm_chart_url }}"
    

    - name: Copy file tln-mysql-values
      ansible.builtin.copy:
        src: helms/mysql/tln-mysql-values.yaml
        dest: /tmp/helm_mysql.yml
        owner: ubuntu
        group: users
        mode: '0744'

    - name: Remove "{{ deployment_name }}" release and waiting suppression ending
      community.kubernetes.helm:
        name: "{{ deployment_name }}"
        release_namespace: "{{ deployment_namespace }}"
        state: absent
        wait: true

    - name: Install Mysql Chart - "{{ deployment_name }}"
      kubernetes.core.helm:
        name: "{{ deployment_name }}"
        namespace: "{{ deployment_namespace }}"
        chart_ref: bitnami/mysql
        create_namespace: true
        replace: true
        values_files: 
          - "/tmp/helm_mysql.yml"
