verbose_logging = true

    [[servers]]
    host = "ldap.foxpass.com"
    port = 636
    use_ssl = true
    start_tls = false
    ssl_skip_verify = false
    bind_dn = "cn=grafana-kubernetes,dc=talent,dc=com"
    bind_password =  "XPqXMazjR5ql8QUm"
    search_filter = "(uid=%s)"
    search_base_dns = ["dc=talent,dc=com"]

    [servers.attributes]
    member_of = "memberOf"
    email =  "mail"
    username = "uid"
    name = "gn"
    surname = "sn"

    [[servers.group_mappings]]
    group_dn = "cn=infrastructure,ou=groups,dc=talent,dc=com"
    org_role = "Admin"
    grafana_admin = true
    org.id = 1

    [[servers.group_mappings]]
    group_dn = "cn=monitoring,ou=groups,dc=talent,dc=com"
    org_role = "Admin"
    grafana_admin = true
    rg.id = 1

    [[servers.group_mappings]]
    group_dn = "cn=security,ou=groups,dc=talent,dc=com"
    org_role = "Admin"
    grafana_admin = true
    org.id = 1

    [[servers.group_mappings]]
    group_dn = "cn=grafana-editors,ou=groups,dc=talent,dc=com"
    org_role = "Editor"
    org.id = 1


    [[servers.group_mappings]]
    group_dn = "*"
    org_role = "Viewer"
    org.id = 1
