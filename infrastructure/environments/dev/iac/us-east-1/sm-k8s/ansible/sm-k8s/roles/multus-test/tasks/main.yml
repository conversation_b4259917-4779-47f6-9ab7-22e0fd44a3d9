---
# tasks file for multus
    - name: Download Multus manifest to the cluster.
      ansible.builtin.get_url:
        url: https://raw.githubusercontent.com/k8snetworkplumbingwg/multus-cni/master/deployments/multus-daemonset-thick.yml
        dest: ~/multus-daemonset-thick.yml
        mode: '0664'
      #when: multus_install | bool 

    - name: Apply Multus manifest to the cluster.
      kubernetes.core.k8s:
        state: present
        src: ~/multus-daemonset-thick.yml
      #when: multus_install | bool 

    # apply the multus network confs 
    # ens5
    - name: Copy file multus-cni-ens5.yaml
      ansible.builtin.copy:
        src: multus-cni-ens5.yaml
        dest: /tmp/multus-cni-ens5.yaml
      #when: multus_install_ens5 | bool 

    - name: Deploy the multus-cni-ens5 
      shell: | 
        cd /tmp/
        kubectl apply -f multus-cni-ens5.yaml
      # when: multus_install_ens5 | bool 


    # apply the multus pods
    # ens5 single 
    - name: Copy file multus-test-pod-ens5.yaml
      ansible.builtin.copy:
        src: multus-test-pod-ens5.yaml
        dest: /tmp/multus-test-pod-ens5.yaml
      #when: multus_install_ens5 | bool 

    - name: Deploy the multus-cni-ens5 pod
      shell: | 
        cd /tmp/
        kubectl apply -f multus-test-pod-ens5.yaml
      #when: multus_install_ens5 | bool 

    # eth0 single 
    - name: Copy file multus-test-pod-eth0.yaml
      ansible.builtin.copy:
        src: multus-test-pod-eth0.yaml
        dest: /tmp/multus-test-pod-eth0.yaml
      #when: multus_install_eth0 | bool 

    - name: Deploy the multus-cni-eth0 pod
      shell: | 
        cd /tmp/
        kubectl apply -f multus-test-pod-eth0.yaml
      #when: multus_install_eth0 | bool 

    # ens5 single 
    - name: Copy file multus-test-pod-ens5-multiple-ips.yaml
      ansible.builtin.copy:
        src: multus-test-pod-ens5-multiple-ips.yaml
        dest: /tmp/multus-test-pod-ens5-multiple-ips.yaml
      #when: multus_install_ens5 | bool 

    - name: Deploy the multus-cni-ens5 pod
      shell: | 
        cd /tmp/
        kubectl apply -f multus-test-pod-ens5-multiple-ips.yaml
      #when: multus_install_ens5 | bool 

