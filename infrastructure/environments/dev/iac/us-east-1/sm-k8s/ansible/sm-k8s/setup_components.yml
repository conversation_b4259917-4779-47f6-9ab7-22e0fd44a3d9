---
- name: Install components on compute instances
  hosts: cluster
  gather_facts: true
  roles:
    # - role: facts
    #   tags: always

    # - role: kubernetes
    #   tags: kubernetes

    # - role: krew
    #   tags: krew
    #   vars:
    #     node_package_manager: "{{ ansible_facts.pkg_mgr }}"

    # - role: containerd
    #   tags: containerd
    #   vars:
    #     architecture: "{{ architectures[ansible_facts.architecture] }}"

    - role: nerdctl
      tags: nerdctl

    - role: helm
      tags: helm
