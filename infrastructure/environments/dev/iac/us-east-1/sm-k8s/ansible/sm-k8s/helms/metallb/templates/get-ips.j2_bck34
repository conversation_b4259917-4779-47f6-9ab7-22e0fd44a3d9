{# ---
apiVersion: metallb.io/v1beta2
kind: ConfigMap
metadata:
  namespace: metallb-system
  name: config #}
---
apiVersion: metallb.io/v1beta1
kind: IPAddressPool
metadata:
  name: default
  namespace: metallb-system
spec:
  addresses:
{% for host in groups['control_plane'] %}
  - {{ hostvars[host].ansible_host }}/32
{% endfor %}
---
apiVersion: metallb.io/v1beta1
kind: L2Advertisement
metadata:
  name: default
  namespace: metallb-system
spec:
  ipAddressPools:
  - default