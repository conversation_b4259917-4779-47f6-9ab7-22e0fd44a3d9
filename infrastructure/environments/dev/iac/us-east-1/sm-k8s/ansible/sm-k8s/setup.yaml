############## Bootstrap WAIT !!!!  ###############
- hosts: all
  name: bootstrap wait
  roles:
    - { role: bootstrap-wait, tags: bootstrap-wait }
  become: true

#only when we have the bare-bone / Official clean ubuntu image 
- hosts: all
  name: bootstrap node
  roles:
    - { role: bootstrap, tags: bootstrap }
  become: true


############## Install basics and commons  ###############
- hosts: all
  name: setup kube prerequisites
  roles:
    - { role: kube-prerequisites, tags: kube-prerequisites }
  become: true

############## Install containerd docker engine and Docker Compose###############
- hosts: all
  name: Install containerd
  roles:
    - { role: containerd, tags: containerd }
  become: true

- hosts: all
  name : Install kubeadm kuebctl kubelet
  roles:
    - { role: kubelet-kubeadm-kubectl, tags: kubelet-kubeadm-kubectl}
  become: true
  
############## Configure the cluster ###############
- hosts: control_plane
  name: cluster init and installing flannel networking solution
  become: true
  roles:
    - { role: init-cluster, tags: init-cluster }

############## Join the worker nodes to the cluster ###############
- hosts: worker
  name: copy token to workers
  become: true
  tasks:
    - name: save join token to workers
      copy: 
          src: "./clusters/{{ cluster_name }}/buffer/join.sh"
          dest: "./"
    - name: execute join token
      command: sh join.sh

# ############## Configure common components  ###############
- name: Setup components
  ansible.builtin.import_playbook: setup_components.yml
  tags: components


# ############## Configure cert-manager  ###############
#
- name: Setup cert-manager
  ansible.builtin.import_playbook: setup_cert_manager.yml
  tags: cert-manager
  when: cert_manager_install | bool
 

### MetalLB System Helms (Please do not change) 
- name: Setup Metallb - Loadbalancer manager
  ansible.builtin.import_playbook: helm_metallb.yml
  tags: metallb
  when: metallb_install | bool

############## Configure Metrics  ###############
# we do really need this - but just in case we do not .......... just comment it out
- name: Setup Metrics
  ansible.builtin.import_playbook: helm_metrics.yml
  tags: metrics
  when: metrics_install | bool

############## Configure the data storage  ###############
- name: Setup longhorn
  ansible.builtin.import_playbook: setup_longhorn.yml
  tags: longhorn
  when: longhorn_install | bool

- name: Setup openebs
  ansible.builtin.import_playbook: setup_openebs.yml
  tags: openebs
  when: openebs_install | bool

# ############## Configure the dns records  ###############
# - name: Dns Records
#   ansible.builtin.import_playbook: setup_route53.yml
#   tags: route53_records
#   when: route53_install | bool

# ############## Configure kube prometheus  ###############
- name: Setup Prometheus
  ansible.builtin.import_playbook: setup_kube_prometheus.yml
  tags: prometheus
  when: prometheus_install | bool


# ############## Configure Docker on control node  ###############
# this is for authenticating to AWS ECR
- name: Setup Docker
  ansible.builtin.import_playbook: setup_docker.yml
  tags: docker
  #when: docker_install | bool



#################################################################################
#################################################################################
#################################################################################
#################################################################################
#################################################################################
#################################################################################
#################################################################################
#################################################################################
#################################################################################
#################################################################################
#################################################################################
#################################################################################

# ############## Configure ingress controller  ###############
# it also adds a demo app just to check the working state of the ingress
- name: Setup nginx ingress controller
  ansible.builtin.import_playbook: setup_ingress_controller.yml
  tags: nginx-ingress
  when: nginx_install | bool

#################
#################

# ############## Configure ISTIO controller  ###############
- name: Setup ISTIO ingress controller
  ansible.builtin.import_playbook: setup_istio.yml
  tags: istio-envoy
  when: istio_install | bool

### RERUN THE MetalLB System Helms (Please do not change) 
- name: Setup Metallb - Loadbalancer manager second time to restart the services and get the primary ip as the lb ip 
  ansible.builtin.import_playbook: helm_metallb.yml
  tags: metallb
  when: metallb_install | bool

#################################################################################
#################################################################################
#################################################################################
#################################################################################
#################################################################################
#################################################################################
#################################################################################
#################################################################################
#################################################################################
#################################################################################
#################################################################################
#################################################################################

# ############## Create base certs for the cluster ###############
- name: Setup setup-certs
  ansible.builtin.import_playbook: setup_certs.yml
  tags: certs
  when: create_base_certs | bool

  

############## Configure Kubernetes Dashboard ###############
# its a nice to have feature - maybe for production no but  .......... if really no then just comment it out
# users and access leveles need to be set first which is not yet fixed ! 27-03-2024
- name: Setup Kubernetes Dashboard
  ansible.builtin.import_playbook: setup_k8_dashboard.yml
  tags: kubernetes-dashboard
  when: kubedash_install | bool



# ############## Test the multus_cni by deploying pods ###############
# # we do NOT really need this - but just in case we do .......... just comment it out
# # be aware that to use this pods you need to have the multus install to true on the hosts ini during the creation of the cluster 
# # otherwise you will need to install multus by hand or using the general command for tha same - read the info .txt
- name: Setup multus_test_pod
  ansible.builtin.import_playbook: setup_multus_test.yml
  tags: multus_test_pods
  when: multus_install_pods | bool





# ############## Configure jaeger  ###############
#
- name: Setup jaeger
  ansible.builtin.import_playbook: setup_jaeger.yml
  tags: jaeger
  when: jaeger_install | bool

# ############## Configure ArgoCD  ###############
- name: Setup ArgoCD
  ansible.builtin.import_playbook: setup_argocd.yml
  tags: argocd
  when: argocd_install | bool

# # ############## Configure octant  ###############
# if need the octant use this : export ANSIBLE_HOST_KEY_CHECKING=False && ansible-playbook -i ./files/inventory.yml '../sm-k8s/setup_octant.yml'
# from terraform directory 
- name: Setup Octant
  ansible.builtin.import_playbook: setup_octant.yml
  tags: octant
  when: octant_install | bool

############## Configure KUBEVIOUS  ###############
# #We do not really need this but it is a nice to have just for debugging !
- name: Setup kubevious
  ansible.builtin.import_playbook: setup_kubevious.yml
  tags: kubevious
  when: kubevious_install | bool





# # #####################################################
# # #####################################################
# # #####################################################
# # #####################################################

# # ######## Finalizing the clusters bootstrap  #########
# # please always keep this at the end of the play 
# - hosts: all
#   name: Secure SSH and finalize bootstrap 
#   roles:
#     #- { role: secure_ssh, tags: secure_ssh }
#     #- { role: bootstrap_end, tags: bootstrap_end }
#     # from now on you can login using talents ldap/foxpass account on port 7743
#   become: true




  