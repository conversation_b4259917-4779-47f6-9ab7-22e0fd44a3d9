# Foxpass LDAP Defaults
# See ldap.conf(5) for details
# This file should be world readable but not world writable.
#Bind variables
URI     ldaps://ldap.foxpass.com
BINDDN  cn=neuvoo,dc=talent,dc=com
BINDPW  SXkZISKH6RtazSg7
# The amount of time, in seconds, to wait while trying to connect to the LDAP server
bind_timelimit 30
# The amount of time, in seconds, to wait while performing an LDAP query.
timelimit 30
# Must be set or su<PERSON> will ignore LDAP; may be specified multiple times.
sudoers_base   ou=SUDOers,dc=talent,dc=com
# verbose sudoers matching from ldap (change for troubleshooting)
sudoers_debug 0
# Enable support for time-based entries in sudoers.
sudoers_timed yes
# TLS certificates (needed for GnuTLS)
TLS_CACERT      /etc/ssl/certs/ca-certificates.crt
EOF