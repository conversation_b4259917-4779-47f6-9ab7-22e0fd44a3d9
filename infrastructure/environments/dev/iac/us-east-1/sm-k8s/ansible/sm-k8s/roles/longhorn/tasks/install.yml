---
- name: "Create longhorn-system namespace"
  kubernetes.core.k8s:
    name: "longhorn-system"
    api_version: v1
    kind: Namespace
    state: present

- name: Add longhorn repository
  kubernetes.core.helm_repository:
    name: longhorn
    repo_url: https://charts.longhorn.io

- name: Separately update the repository cache
  kubernetes.core.helm:
    name: dummy
    namespace: kube-system
    state: absent
    update_repo_cache: true

- name: Deploy longhorn
  kubernetes.core.helm:
    name: "longhorn"
    chart_ref: longhorn/longhorn
    release_namespace: "longhorn-system"

- name: Check if storage class exists
  kubernetes.core.k8s_info:
    name: longhorn
    kind: StorageClass
    namespace: "longhorn-system"
  register: result

- name: Create storage class
  kubernetes.core.k8s:
    state: present
    template: storage_class.yml.j2
  when: result.resources | length == 0



# - name: copy the predefined common pv
#   template:
#     src: common-pv.yml
#     dest: /tmp/common-pv.yml

# - name: Pause for 20 seconds for the Longhorn to get deployed
#   ansible.builtin.pause:
#     seconds: 20

# - name: Create the common pv
#   shell: | 
#     cd /tmp/
#     kubectl apply -f common-pv.yml
