locals {
  timestamp = "${timestamp()}"
  timestamp_sanitized = "${replace("${local.timestamp}", "/[-| |T|Z|:]/", "")}"
  # if no value for the key name is set in the vars -  then this will create a unique key based on time we run the terraform apply 
  #cluster_name= var.cluster_name != "" ? var.cluster_name : "${var.cluster_name}"-"${local.timestamp_sanitized}"
  #project = var.cluster_name
}

resource "aws_iam_instance_profile" "worker_profile" {
  name = "${var.cluster_name}-wrk-g1"
  role = var.iam_role
}

resource "aws_instance" "workers" {
  ami             = var.default_ami_id
  count           = var.worker_count
  instance_type   = var.instance_type
  key_name        = var.key_pair_name
  security_groups = [ var.security_group_id ]
  #subnet_id      = data.aws_subnets.default_subnets.ids[0]
  #subnet_id      = "subnet-09ed67eb6b8c5b0e5"
  subnet_id       = var.subnet_groups
  iam_instance_profile = aws_iam_instance_profile.worker_profile.name
  associate_public_ip_address = false

  tags = {
    #Name = "worker_${count.index}"
    #Name = "${join("-", list(format("%s %s", var.cluster_name, "worker",count.index)))}"
    Name = "${var.cluster_name}-wrk-g1-${count.index}"
    hostname = "${var.cluster_name}-wrk-g1-${count.index}"
    terraform = "true"
    project = var.project
    cc      = var.cc
    costcenter = var.costcenter
    owner  = var.owner
    #type    = var.instance_type
    #type    = "${var.instance_type}"
    worker-group = "1"
    base_url = var.base_url
  }

  root_block_device {
    delete_on_termination = true
    volume_size = var.disk_size
    volume_type = var.disk_type
  }
}