variable "cluster_name" {
  type    = string
  description = "Cluster name for use on the nodes created !"
  default = ""
}
variable "controller_count" {
  type = string
}
variable "subnet_groups" {
  type = string
}
variable default_ami_id {
  type = string
}
variable "security_group_id" {
  type = string
}
variable "key_pair_name" {
  type = string
}
variable "instance_type" {
    type = string
}
variable "project" {
    type = string
}
variable "owner" {
    type = string
}
variable "cc" {
    type = string
}
variable "costcenter" {
    type = string
}
variable "iam_role" {
    type = string
}
variable "disk_type" {
    type = string
}
variable "disk_size" {
    type = string
}
variable "base_url" {
    type = string
}
