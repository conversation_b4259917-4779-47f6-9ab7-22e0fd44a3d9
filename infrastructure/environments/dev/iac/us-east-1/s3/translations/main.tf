provider "aws" {
  region = "us-east-1"
}

module "s3_bucket" {
  source = "../../../../../../terraform/modules/official/terraform-aws-s3-bucket-master"

  bucket                   = "talent-dev-${local.resource_name}"
  acl                      = "private"
  block_public_acls        = true
  block_public_policy      = true
  ignore_public_acls       = true
  restrict_public_buckets  = true
  control_object_ownership = true
  object_ownership         = "ObjectWriter"

  server_side_encryption_configuration = {
    rule = {
      apply_server_side_encryption_by_default = {
        sse_algorithm = "AES256"
      }
    }
  }

  versioning = {
    enabled = true
  }

  tags = {
    terraform    = "true",
    environment  = "dev"
    Name = "talent-dev-${local.resource_name}",
    service = "${local.resource_name}"
    cc = "job_seeker"
    owner = "<EMAIL>"
    type = "s3"
    service_type = "frontend"
    department = "product"
    group = "bucket"
  }
}

