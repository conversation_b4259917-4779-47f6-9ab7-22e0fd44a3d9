output "vpc_id" {
    description = "Created VPC ID"
    value = module.vpc_main.vpc_id
}
output "egress_vpc_id" {
  description = "Created VPC ID for egress purposes with nat"
  value = module.vpc_nat_main.vpc_id
}

/*
output "natgw_ids" {
    description = "Created NAT Gateways IDs"
    value = module.nat_gateway.natgw_ids
}*/

output "natgw_egress_id_us_east_1a" {
  description = "Created NAT Gateways IDs"
  value = module.private_nat_gateway_private_workload-us-east-1a.natgw_ids
}

output "natgw_egress_id_us_east_1b" {
  description = "Created NAT Gateways IDs"
  value = module.private_nat_gateway_private_workload-us-east-1b.natgw_ids
}
output "natgw_egress_id_us_east_1d" {
  description = "Created NAT Gateways IDs"
  value = module.private_nat_gateway_private_workload-us-east-1d.natgw_ids
}

output "natgw_id_us_east_1a" {
  description = "Created NAT Gateways IDs"
  value = module.nat_gateway_private_workload-us-east-1a.natgw_ids
}

output "natgw_id_us_east_1b" {
  description = "Created NAT Gateways IDs"
  value = module.nat_gateway_private_workload-us-east-1b.natgw_ids
}
output "natgw_id_us_east_1d" {
  description = "Created NAT Gateways IDs"
  value = module.nat_gateway_private_workload-us-east-1d.natgw_ids
}

output "igw_id" {
    description = "Created Internet Gateway ID"
    value = module.internet_gateway.igw_id
}

output "egress_vpc_igw_id" {
  description = "Created Internet Gateway ID"
  value = module.internet_gateway_nat.igw_id
}

output "egress_private_subnet_1a" {
    description = "IDs of created subnets for nat private"
  value = module.subnet_egress_private_nat_1a.subnets
}

output "egress_private_subnet_1b" {
  description = "IDs of created subnets for nat private"
  value = module.subnet_egress_private_nat_1b.subnets
}

output "egress_public_subnet_1a" {
  description = "IDs of created subnets for nat private"
  value = module.subnet_egress_public_nat_1a.subnets
}

output "egress_public_subnet_1b" {
  description = "IDs of created subnets for nat private"
  value = module.subnet_egress_public_nat_1b.subnets
}

output "egress_public_subnet_1d" {
  description = "IDs of created subnets for nat private"
  value = module.subnet_egress_public_nat_1d.subnets
}

output "egress_private_subnet_1d" {
  description = "IDs of created subnets for nat private"
  value = module.subnet_egress_private_nat_1d.subnets
}

output "private_workloads_subnets_id_us_east_1a" {
  description = "IDs of created subnets for workloads"
  value = module.private_workload_subnets_1a.subnets
}

output "private_workloads_subnets_id_us_east_1b" {
    description = "IDs of created subnets for workloads"
  value = module.private_workload_subnets_1b.subnets
}
output "private_workloads_subnets_id_us_east_1d" {
    description = "IDs of created subnets for workloads"
  value = module.private_workload_subnets_1d.subnets
}

output "public_workloads_subnets_ids_us_east_1a" {
  description = "IDs of created subnets for workloads zone 1a"
  value = module.public_workload_subnets_1a.subnets
}

output "public_workloads_subnets_ids_us_east_1b" {
  description = "IDs of created subnets for workloads zone 1a"
  value = module.public_workload_subnets_1b.subnets
}

output "public_workloads_subnets_ids_us_east_1d" {
  description = "IDs of created subnets for workloads zone 1a"
  value = module.public_workload_subnets_1d.subnets
}

#output "workloads_subnets_cidrs" {
#    description = "CIDRs of created subnets for workloads"
#  value = module.private_workload_subnets.subnets
#}

output "firewall_subnets_ids_us_east_1a" {
    description = "IDs of created subnets for firewalls zone 1a"
  value = module.firewall_subnets_1a.subnets
}

output "firewall_subnets_ids_us_east_1b" {
  description = "IDs of created subnets for firewalls zone 1b"
  value = module.firewall_subnets_1b.subnets
}
output "firewall_subnets_ids_us_east_1d" {
  description = "IDs of created subnets for firewalls zone 1d"
  value = module.firewall_subnets_1d.subnets
}

output "firewall_subnets_cidrs_1a" {
  description = "CIDRs of created subnets for firewalls"
  value = module.firewall_subnets_1a.subnets_cidr_blocks
}

output "firewall_subnets_cidrs_1b" {
  description = "CIDRs of created subnets for firewalls"
  value = module.firewall_subnets_1b.subnets_cidr_blocks
}

output "firewall_subnets_cidrs_1d" {
  description = "CIDRs of created subnets for firewalls"
  value = module.firewall_subnets_1d.subnets_cidr_blocks
}

output "network_services_subnets_id_us_east_1a" {
  description = "IDs of created subnets for network services zone 1a"
  value = module.network_services_subnets_1a.subnets
}
output "network_services_subnets_id_us_east_1b" {
  description = "IDs of created subnets for network services zone 1b"
  value = module.network_services_subnets_1b.subnets
}
output "network_services_subnets_id_us_east_1d" {
  description = "IDs of created subnets for network services zone 1d"
  value = module.network_services_subnets_1d.subnets
}

output "network_services_subnets_cidrs_1a" {
    description = "CIDRs of created subnets for network services"
  value = module.network_services_subnets_1a.subnets_cidr_blocks
}
output "network_services_subnets_cidrs_1b" {
  description = "CIDRs of created subnets for network services"
  value = module.network_services_subnets_1b.subnets_cidr_blocks
}
output "network_services_subnets_cidrs_1d" {
  description = "CIDRs of created subnets for network services"
  value = module.network_services_subnets_1d.subnets_cidr_blocks
}


output "vpn_subnets_id_1a" {
    description = "IDs of created subnets for vpn"
  value = module.vpn_subnets_1a.subnets
}

output "vpn_subnets_id_1b" {
  description = "IDs of created subnets for vpn"
  value = module.vpn_subnets_1b.subnets
}

output "vpn_subnets_id_1d" {
  description = "IDs of created subnets for vpn"
  value = module.vpn_subnets_1d.subnets
}

output "vpn_subnets_cidrs_1a" {
    description = "CIDRs of created subnets for vpn"
  value = module.vpn_subnets_1a.subnets_cidr_blocks
}

output "vpn_subnets_cidrs_1b" {
  description = "CIDRs of created subnets for vpn"
  value = module.vpn_subnets_1b.subnets_cidr_blocks
}

output "vpn_subnets_cidrs_1d" {
  description = "CIDRs of created subnets for vpn"
  value = module.vpn_subnets_1d.subnets_cidr_blocks
}