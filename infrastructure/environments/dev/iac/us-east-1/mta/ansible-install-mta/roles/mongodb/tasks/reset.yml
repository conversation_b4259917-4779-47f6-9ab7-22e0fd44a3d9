---
# This playbook is for development purposes.
# It simply removes all mongo related files quick 'n dirty
# This is configured to 'quickly' start with a fresh setup again

- name: reset | remove mongo from system
  shell: "{{ item }}"
  loop:
    - "systemctl stop mongod ; true"
    - "rm -f /etc/apt/sources.list.d/mongod*"
    - "apt remove mongo* -y"
    - "rm -rf /etc/mongo*"
    - "rm -rf /var/lib/mongo*"
    - "rm -rf /var/log/mongo*"
    - "rm -rf {{ mongo_systemlog.path }}"
    - "rm -rf {{ mongo_storage.dbPath }}"
    - "rm {{ mongo_security.keyFile | default(omit) }} | echo"
    - "rm -f /etc/systemd/system/disable-transparent-hugepage*"
    - userdel mongodb | echo
    - groupdel mongodb | echo
    - rm -rf /etc/tuned/virtual-guest-no-thp/tuned.conf
    - "rm -rf {{ mongo_backup.path }}"
    - userdel mongodb | echo  # weird bug, but sometimes the user isn't removed
    - "rm -f /usr/lib/systemd/system/mongod.service"

- name: reset | remove backup scripts
  cron:
    name: "{{ item }}"
    state: absent
  loop:
    - 'remove mongodb old backup files'
    - 'create backup of mongo'

- name: reset | reload daemons
  systemd:
    daemon_reload: true
