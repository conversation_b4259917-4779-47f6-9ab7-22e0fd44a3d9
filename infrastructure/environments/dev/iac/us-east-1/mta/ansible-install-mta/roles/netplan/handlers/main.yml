---

- name: Generate netplan networking
  become: yes
  ansible.builtin.command:
    cmd: netplan generate
  changed_when: true

- name: Apply netplan networking
  become: yes
  ansible.builtin.command:
    cmd: netplan apply
  changed_when: true

- name: Gather network facts
  become: yes
  ansible.builtin.setup:
    gather_subset:
      - network

- name: Reboot managed node
  become: yes
  ansible.builtin.reboot:
    reboot_timeout: 300

...
