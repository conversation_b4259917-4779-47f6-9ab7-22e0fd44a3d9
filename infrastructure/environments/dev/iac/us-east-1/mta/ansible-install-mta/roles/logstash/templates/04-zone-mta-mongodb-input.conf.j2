input {
	mongodb {
		uri => "mongodb://{{ prv_ip[0] }}:27017/logs"
		placeholder_db_dir => "/opt/application/logstash/logstash-mongodb/"
		placeholder_db_name => "logstash_sqlite_zone-files.db"
		collection => "mail.files"
		batch_size => 500
	}
}

filter {
	mutate {
	remove_field => [ "_id" ]
	}
}

output
{
    elasticsearch
    {
      hosts => {{ logstash_elasticsearch_hosts | to_json }}
      action => "index"
      #sniffing => true
      #manage_template => true
      index => "{{ es_index }}-files"
      #document_type => "%{[@metadata][type]}"
      user => "{{ es_user }}" 
      password => "{{ es_password}}"
      #data_stream => false
      #data_stream => true
      #data_stream_type => "microservice"
    }
}