---
- include_tasks: ubuntu.yml
  when: ansible_os_family == "Debian"

- name: get the mac
  shell: ip a |grep link/ether |awk -F " " '{print $2}'
  register: target_mac


# - name: configure an ipv4 host record
#   nios_host_record:
#     name: mta100.dev.talent.com
#     ipv4:
#       - address: 
#         - '************'
#     aliases:
#       - mta101.dev.talent.com
#       - mta102.dev.talent.com
#     state: present



  # - name: get the list of tags
  #   shell: ip a |grep link/ether |awk -F " " '{print $2}'
  #   register: target_mac
  #   #when: inside_aws

  # - name: start the service
  #   service:
  #     name: NetworkManager
  #     state: started
  #     enabled: true










# - name: Check whether network configurations are given.
#   ansible.builtin.assert:
#     that:
#       - netplan_ethernets is defined and netplan_ethernets | length > 0

# # - name: Copy file config file
# #   ansible.builtin.copy:
# #     src: ./roles/netplan/tasks/config.yaml
# #     dest: "{{ netplan_configuration_dir }}"
# #   become: True
# #   become_user: root


# - name: Gather package facts
#   ansible.builtin.package_facts:
#     manager: auto

# - name: Check whether package ifupdown is installed
#   ansible.builtin.debug:
#     msg: |
#       "Package 'ifupdown' is installed in version '{{ ansible_facts.packages['ifupdown'][0].version }}'.
#       Please consider removing it after Netplan role has been executed successfully."
#   when: "'ifupdown' in ansible_facts.packages"

# - name: Install netplan packages.
#   become: yes
#   ansible.builtin.package:
#     name: "{{ netplan_packages }}"
#     state: present
#     update_cache: yes

# - name: Delete pre-existing network settings files.
#   when: netplan_remove_existing_configs
#   block:
#     - name: Get netplan config files.
#       ansible.builtin.find:
#         paths: "{{ netplan_configuration_dir }}"
#         file_type: file
#         excludes:
#           - "{{ netplan_configuration_file }}"
#       register: netplan_config_files

#     - name: Remove netplan config files.
#       ansible.builtin.file:
#         path: "{{ item.path }}"
#         state: absent
#       with_items: "{{ netplan_config_files['files'] }}"
#       become: True
#       become_user: root

# - name: Create a netplan config file from template.
#   ansible.builtin.template:
#     src: "{{ netplan_configuration_file_template }}"
#     dest: "{{ netplan_configuration_file_path }}"
#     owner: root
#     group: root
#     mode: '0600'
#     backup: yes
#   notify:
#     - Generate netplan networking
#     - Apply netplan networking
#     - Gather network facts
#   become: True
#   become_user: root

# - name: Check if file ifstate exists.
#   ansible.builtin.stat:
#     path: "{{ ifupdown_ifstate_file }}"
#   register: file_ifstate

# - name: Check whether to reboot host if file ifstate exists.
#   when: file_ifstate.stat.exists
#   block:
#     - name: Read in ifstate file content.
#       ansible.builtin.slurp:
#         src: "{{ ifupdown_ifstate_file }}"
#       register: slurped_file

#     - name: Compare ifstate file content to determine whether reboot is necessary.
#       ansible.builtin.set_fact:
#         reboot_to_switch_to_netplan: "{{ slurped_file.content != 'lo=lo\n' | b64encode }}"

#     - name: Trigger reboot to switch to netplan.
#       ansible.builtin.debug:
#         msg: |
#           "Reboot is required to switch from ifupdown to netplan.
#            Triggering the reboot handler now."
#       changed_when: reboot_to_switch_to_netplan | bool
#       when:
#         - reboot_to_switch_to_netplan | bool
#       notify: Reboot managed node

# ...
