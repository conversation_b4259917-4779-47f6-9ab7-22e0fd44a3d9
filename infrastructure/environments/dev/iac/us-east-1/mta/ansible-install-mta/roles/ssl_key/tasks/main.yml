---
# tasks file for roles/ssl-certificate




- name: Create private key (RSA, 4096 bits)
  community.crypto.openssl_privatekey:
    path: /opt/application/zone-mta/zone-mta-template/keys/mta300/1/{{opendkim_key}}.{{dns_domain_name}}.certificate.key

# - name: Create private key (X25519) with password protection
#   community.crypto.openssl_privatekey:
#     path: /opt/application/zone-mta/zone-mta-template/keys/mta300/1/{{opendkim_key}}.{{dns_domain_name}}.certificate.key
#     type: X25519
#     passphrase: +@l3N+


- name: Create certificate signing request (CSR) for self-signed certificate
  community.crypto.openssl_csr_pipe:
    privatekey_path: /opt/application/zone-mta/zone-mta-template/keys/mta300/1/{{opendkim_key}}.{{dns_domain_name}}.certificate.key
    common_name: "{{dns_domain_name}}"
    organization_name: Talent.com, Inc.
    subject_alt_name: 
      - "DNS:{{opendkim_domains[1]}}"
      - "DNS:{{opendkim_domains[2]}}"
      - "DNS:{{opendkim_domains[3]}}"
    #subject_alt_name: ["DNS:{{opendkim_domains[1]}}","DNS:{{opendkim_domains[2]}}","DNS:{{opendkim_domains[3]}}"]

        #passphrase: +@l3N+
  register: csr

- name: Create self-signed certificate from CSR
  community.crypto.x509_certificate:
    path: /opt/application/zone-mta/zone-mta-template/keys/mta300/1/{{opendkim_key}}.{{dns_domain_name}}.certificate.pem
    csr_content: "{{ csr.csr }}"
    privatekey_path: /opt/application/zone-mta/zone-mta-template/keys/mta300/1/{{opendkim_key}}.{{dns_domain_name}}.certificate.key
    provider: selfsigned
    #passphrase: +@l3N+









# - name: check parameters
#   fail:
#     msg: ssl_certificate_public_content or ssl_certificate_key_content is empty
#   when: >-
#     (not ssl_certificate_selfsigned_create) and
#     (not ssl_certificate_public_content or not ssl_certificate_key_content)

# - name: install openssl
#   become: true
#   package:
#     name: openssl
#     state: present
#   when: ssl_certificate_install_openssl

# - name: create certificates directory
#   become: true
#   file:
#     path: "{{ item | dirname }}"
#     state: directory
#     mode: 0755
#   with_items:
#     - "{{ ssl_certificate_public_path }}"
#     - "{{ ssl_certificate_intermediate_path }}"
#     - "{{ ssl_certificate_bundled_path }}"
#     - "{{ ssl_certificate_key_path }}"

# # Create from content of variable e.g. from vault
# # Only the key needs to be kept private

# - name: write SSL public certificate
#   become: true
#   copy:
#     content: "{{ ssl_certificate_public_content }}"
#     dest: "{{ ssl_certificate_public_path }}"
#     mode: 0444
#   when: 'ssl_certificate_public_content | length > 0'
#   notify: ssl certificate changed

# - name: write SSL intermediate certificate
#   become: true
#   copy:
#     content: "{{ ssl_certificate_intermediate_content }}"
#     dest: "{{ ssl_certificate_intermediate_path }}"
#     mode: 0444
#   when: 'ssl_certificate_intermediate_content | length > 0'
#   notify: ssl certificate changed

# - name: write SSL certificate key
#   become: true
#   copy:
#     content: "{{ ssl_certificate_key_content }}"
#     dest: "{{ ssl_certificate_key_path }}"
#     mode: 0400
#   no_log: true
#   when: 'ssl_certificate_key_content | length > 0'
#   notify: ssl certificate changed

# # Self-signed
# # http://serialized.net/2013/04/simply-generating-self-signed-ssl-certs-with-ansible/

# - name: generate self-signed SSL certificate
#   become: true
#   command: >
#     openssl req -new -nodes -x509 -subj
#     {{ ssl_certificate_selfsigned_subject }}
#     -days {{ ssl_certificate_selfsigned_days }}
#     -keyout {{ ssl_certificate_key_path }}
#     -out {{ ssl_certificate_public_path }}
#     -extensions v3_ca
#   args:
#     # Don't overwrite existing certificate
#     creates: "{{ ssl_certificate_key_path }}"
#   when: ssl_certificate_selfsigned_create
#   notify: ssl certificate changed

# # Create combined certificate and key

# - name: read public certificate
#   become: true
#   slurp:
#     src: "{{ ssl_certificate_public_path }}"
#   register: _ssl_certificate_public_content

# - name: read certificate key
#   become: true
#   slurp:
#     src: "{{ ssl_certificate_key_path }}"
#   register: _ssl_certificate_key_content
#   no_log: true

# - name: write bundled certificate
#   become: true
#   copy:
#     content: |-
#       {{ _ssl_certificate_public_content.content | b64decode | trim }}
#       {{ ssl_certificate_intermediate_content }}
#     dest: "{{ ssl_certificate_bundled_path }}"
#     mode: 0444
#   when: "ssl_certificate_bundled_path | length > 0"
#   notify: ssl certificate changed

# - name: write SSL combined certificate key
#   become: true
#   copy:
#     content: |-
#       {{ _ssl_certificate_public_content.content | b64decode | trim }}
#       {{ ssl_certificate_intermediate_content | trim }}
#       {{ _ssl_certificate_key_content.content | b64decode }}
#     dest: "{{ ssl_certificate_combined_path }}"
#     mode: 0400
#   no_log: true
#   when: "ssl_certificate_combined_path | length > 0"
#   notify: ssl certificate changed
