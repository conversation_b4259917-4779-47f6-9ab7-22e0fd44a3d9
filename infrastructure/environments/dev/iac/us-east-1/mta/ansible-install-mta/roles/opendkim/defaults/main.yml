---
## Default Variables for ansible-dkim role

### Opendkim debian/ubuntu package configuration

dkim_default_config_file: /etc/default/opendkim

dkim_opendkim_config_dir: /etc/opendkim

dkim_user: opendkim
dkim_group: opendkim

### DKIM  configuration variables
## The "selector" for opendkim keys and DNS records generation
dkim_selector: email

## The e-mail address that manages opendkim
dkim_admin_email: "{{ admin_email | default ( <EMAIL> ) }}"
# This variable, in the role's namespace `dkim_`, is defined with the value of `admin_email` for backward
# compatibility of the role, and eventually to harmonise this value accross a whole set of roles.
# In your playbooks, you MUST set either `dkim_admin_email` or `admin_email`

## The list of domains you want opendkim to sign mails for MUST be defined as a list:
# dkim_domains:
#   - domain1.tld
#   - domain2.tld
#   - domain3.tld
dkim_domains: "{{ opendkim_domains }}"


## The list of trusted hosts for opendkim
dkim_trustedhosts:
  - 127.0.0.1
  - localhost

## Whether open<PERSON><PERSON> uses the same key for all domains or one key per domain
dkim_same_key: true

## When generating key with opendkim-gkeygen, default RSA keylen is 2048
## Other currently possible keylengths: 1024 or 4096.
dkim_rsa_keylen: 2048

## Select your MTA (postfix or sendmail)
dkim_mta: postfix

dkim_postfix_config_file: /etc/postfix/main.cf

## The parameters to set in postfix configuration
## Default paramenters, defined in vars/main.yml, configure OPendkim as postfix milter
dkim_postfix_config: '{{ dkim_postfix_config_default }}'

#sendmail config file and folder
dkim_sendmail_config_folder: /etc/mail/
dkim_sendmail_config_file: "{{ dkim_sendmail_config_folder }}sendmail.mc"

# generate and deploy certificates, or just generate, and keep existing config
# this allows time to generate keys, publish them in dns, and then do a deploy
dkim_generate_only: false
