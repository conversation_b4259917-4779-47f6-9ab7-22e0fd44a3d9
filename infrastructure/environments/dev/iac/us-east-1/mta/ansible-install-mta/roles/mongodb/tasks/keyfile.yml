---
- name: keyfile | set fact to copy keyfile from a host
  set_fact:
    keyfile_host: "{{ item }}"
  when: hostvars[item].mongo_primary
  run_once: true
  loop: "{{ ansible_play_hosts }}"

- name: keyfile | stat
  stat:
    path: "{{ mongo_security.keyFile }}"
  register: keyfile_check

- name: keyfile | set fact
  set_fact:
    keyfile_fact: "{{ keyfile_check.stat.exists }}"

- name: keyfile | block to check which host(s) have the file
  block:

    - name: keyfile | create dict of keyfile check
      set_fact:
        keyfile_status: "{{ dict(keys|zip(values)) }}"
      vars:
        keys: "{{ ansible_play_hosts }}"
        values: "{{ ansible_play_hosts |
                    map('extract', hostvars, ['keyfile_fact'])
                    | list }}"

    - name: keyfile | set fact when no host has the file
      set_fact:
        keyfile_none: true
      when: keyfile_status.values() | list is not any

    - name: keyfile | set fact when a host does not has the file
      set_fact:
        keyfile_all: false
      when: keyfile_status.values() | list is any

    - name: keyfile | set fact when all hosts have the files
      set_fact:
        keyfile_all: true
      when: keyfile_status.values() | list is all

  run_once: true

- name: keyfile | block to copy keyfile when none has one
  block:

    - name: keyfile | create when none has a keyfile
      shell: >-
        openssl rand -base64 741 > {{ mongo_security.keyFile }} ;
        chmod 400 {{ mongo_security.keyFile }} ;
        chown mongodb: {{ mongo_security.keyFile }}
      register: new_keyfile
      when: mongo_primary

    - name: keyfile | fetch keyfile
      fetch:
        src: "{{ mongo_security.keyFile }}"
        flat: true
        dest: /tmp/mongo_keyfile
      delegate_to: "{{ keyfile_host }}"

    - name: keyfile | copy required files to remaining hosts
      copy:
        src: /tmp/mongo_keyfile
        dest: "{{ mongo_security.keyFile }}"
        mode: '0400'
        owner: mongodb
        group: mongodb

  when: keyfile_none is defined

- name: keyfile | block to copy keyfile when at least one is missing
  block:

    - name: keyfile | fetch keyfile
      fetch:
        src: "{{ mongo_security.keyFile }}"
        flat: true
        dest: /tmp/mongo_keyfile
      delegate_to: "{{ keyfile_host }}"

    - name: keyfile | copy required files to remaining hosts
      copy:
        src: /tmp/mongo_keyfile
        dest: "{{ mongo_security.keyFile }}"
        mode: '0400'
        owner: mongodb
        group: mongodb

  when: keyfile_all is defined and not keyfile_all

- name: keyfile | ensure absence from localhost
  file:
    path: /tmp/mongo_keyfile
    state: absent
  run_once: true
  delegate_to: localhost
  become: false
