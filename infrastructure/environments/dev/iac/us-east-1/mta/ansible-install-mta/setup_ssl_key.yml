---
- name: Deploy Key generator
  hosts: "nodes"
  become: yes
  #become_method: sudo -i
  become_user: root
  gather_facts: no
  # vars_prompt:
  #   - name: "new_static_ip"
  #     prompt: "Enter the new static IP address"
  #     private: no
  vars:
    # # SSL Certificate Common Name
    # cert_common_name: "{{ dns_domain_name }}"
    # # SSL Certificate Subject Alternative Name (SAN)
    # cert_subject_alt_name:
    #   #- "DNS:*.{{ dns_domain_name }}"
    #   - "DNS:{{ opendkim_key }}.{{ dns_domain_name }}"
    # # Keep in-line of modern cert expiry requirement - 13 months max
    # ownca_not_after: "+397d"
    # # Allow custom CA name, Organisation (shows up nicely in certificate mangers)
    # openssl_csr_common_name: "Talent-Signed CA"
    # openssl_csr_organization_name: "Self-Signed Talent.com"



    # Server path to SSL public certificate
    ssl_certificate_public_path: /opt/application/zone-mta/zone-mta-template/keys/{{ mta_name }}/{{ opendkim_key }}.{{ dns_domain_name }}.crt
    # Server path to SSL intermediate certificate(s)
    ssl_certificate_intermediate_path: /opt/application/zone-mta/zone-mta-template/keys/{{ mta_name }}/{{ opendkim_key }}.{{ dns_domain_name }}.intermediate.crt
    # Server path to SSL bundled public and intermediate certificates
    ssl_certificate_bundled_path: /opt/application/zone-mta/zone-mta-template/keys/{{ mta_name }}/{{ opendkim_key }}.{{ dns_domain_name }}.bundled.crt
    # Server path to SSL certificate key
    ssl_certificate_key_path: /opt/application/zone-mta/zone-mta-template/keys/{{ mta_name }}/{{ opendkim_key }}.{{ dns_domain_name }}.server.key
    # Server path to SSL combined certificate and key, set to empty to disable
    ssl_certificate_combined_path: /opt/application/zone-mta/zone-mta-template/keys/{{ mta_name }}/{{ opendkim_key }}.{{ dns_domain_name }}.combined.pem
    # Text content of the public certificate
    ssl_certificate_public_content: ''
    # Text content of the intermediate certificate(s)
    ssl_certificate_intermediate_content: ''
    # Text content of the certificate key
    ssl_certificate_key_content: ''
    # Create a self-signed certificate if necessary
    ssl_certificate_selfsigned_create: true
    # Certificate subject
    ssl_certificate_selfsigned_subject: |-
      /C=CA/ST=Quebec/L=Montreal/O=Talent.com/OU=It/CN={{ opendkim_key }}.{{ dns_domain_name }}/emailAddress=<EMAIL>
    #/C=UK/ST=Scotland/L=Dundee/O=OME/CN={{ ansible_fqdn }}
    # Certificate validity (days)
    ssl_certificate_selfsigned_days: 365
    # Whether or not to install OpenSSL
    ssl_certificate_install_openssl: true

    ######################################################### SSL_KEYS
    openssl_csr_common_name: "{{ opendkim_key }}.{{ dns_domain_name }}"
    openssl_csr_organization_name: "Talent.com, INC."
    cert_common_name: "{{ opendkim_key }}.{{ dns_domain_name }}"
    cert_subject_alt_name: "DNS:{{ opendkim_key }}.{{ dns_domain_name }}"
    ownca_not_after: +365d # #"{{ ca_certificate_valid_tln_period | default(++52w0d0h') }}"

  tasks:
    #- fail: msg="Do not use 'all' or any inventory group names. Please specify the IP Address of a single host."
    #  when: ansible_limit == "all" or ansible_limit == "" or ansible_limit == "" or ansible_limit == "" # limit by hosts file groupings.

    - name:
      include_role:
        name: ssl_keys