##########################################################################
# Use this file to over write the default values from the Variable file 
#
#
# For example if you want to change the default region in the variable.tf 
# file change that here .....
#
# (all other values is just as an example of overwriting values on the 
# variable.tf file)
############################################
aws_region                  = "us-east-1"
#instance_type               = "m6i.large" #2cpu-8Ram
instance_type               = "m6i.xlarge" #4cpu-16Ram
# aws_ec2_ami                 = "ami-0e001c9271cf7f3b9"
# subnet_group                = "subnet-065091ffb2b3a8da5"
# key-name                    = "tlan-dev-11032024"
# profile                     = "471112575606_AWSAdministratorAccess"
# vpc_id                      = "vpc-01b9c6bb8f72cde01"
# vpc_security_group_ids      = ["sg-091fee45e67a7f559"]
