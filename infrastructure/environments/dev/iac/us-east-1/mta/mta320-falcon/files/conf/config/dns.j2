# If true then caches DNS results to Redis
caching=true
cacheTTL=600 # TTL of cached dns keys in seconds

# Define nameservers to use (IP addresses only). If using a local DNS cache server, then set caching=false
nameservers=[]

#caching=false
#nameservers=["127.0.0.1"]

blockDomains=[]

# If true then messages to local interfaces are blocked (eg. you can not send to username@localhost)
blockLocalAddresses=false
