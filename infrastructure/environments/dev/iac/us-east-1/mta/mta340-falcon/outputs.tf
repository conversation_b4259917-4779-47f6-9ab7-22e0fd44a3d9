

output "MTA_name" {
  value = "${var.mta_name}"
}
output "MTA_zone_mta_private_ip" {
  value = "${aws_instance.zone-mta.private_ip}"
}


output "mta-eip-0_elastic_prv_ip" {
  value = aws_eip_association.mta-eip-0-association.private_ip_address
}
output "mta-eip-0_elastic_pub_ip" {
  value = aws_eip_association.mta-eip-0-association.public_ip
}

output "mta-eip-1_elastic_prv_ip" {
  value = aws_eip_association.mta-eip-1-association.private_ip_address
}
output "mta-eip-1_elastic_pub_ip" {
  value = aws_eip_association.mta-eip-1-association.public_ip
}

output "mta-eip-2_elastic_prv_ip" {
  value = aws_eip_association.mta-eip-2-association.private_ip_address
}
output "mta-eip-2_elastic_pub_ip" {
  value = aws_eip_association.mta-eip-2-association.public_ip
}

output "mta-eip-3_elastic_prv_ip" {
  value = aws_eip_association.mta-eip-3-association.private_ip_address
}
output "mta-eip-3_elastic_pub_ip" {
  value = aws_eip_association.mta-eip-3-association.public_ip
}

output "mta-eip-4_elastic_prv_ip" {
  value = aws_eip_association.mta-eip-4-association.private_ip_address
}
output "mta-eip-4_elastic_pub_ip" {
  value = aws_eip_association.mta-eip-4-association.public_ip
}

output "mta-eip-5_elastic_prv_ip" {
  value = aws_eip_association.mta-eip-5-association.private_ip_address
}
output "mta-eip-5_elastic_pub_ip" {
  value = aws_eip_association.mta-eip-5-association.public_ip
}

output "mta-eip-6_elastic_prv_ip" {
  value = aws_eip_association.mta-eip-6-association.private_ip_address
}
output "mta-eip-6_elastic_pub_ip" {
  value = aws_eip_association.mta-eip-6-association.public_ip
}

output "mta-eip-7_elastic_prv_ip" {
  value = aws_eip_association.mta-eip-7-association.private_ip_address
}
output "mta-eip-7_elastic_pub_ip" {
  value = aws_eip_association.mta-eip-7-association.public_ip
}



# output "mta-eip-0_elastic_prv_ip" {
#   value = aws_eip.mta-eip-0.private_ip
# }
# output "mta-eip-0_elastic_pub_ip" {
#   value = aws_eip.mta-eip-0.public_ip
# }
# output "mta-eip-1_elastic_prv_ip" {
#   value = aws_eip.mta-eip-1.private_ip
# }
# output "mta-eip-1_elastic_pub_ip" {
#   value = aws_eip.mta-eip-1.public_ip
# }
# output "mta-eip-2_elastic_prv_ip" {
#   value = aws_eip.mta-eip-2.private_ip
# }
# output "mta-eip-2_elastic_pub_ip" {
#   value = aws_eip.mta-eip-2.public_ip
# }
# output "mta-eip-3_elastic_prv_ip" {
#   value = aws_eip.mta-eip-3.private_ip
# }
# output "mta-eip-3_elastic_pub_ip" {
#   value = aws_eip.mta-eip-3.public_ip
# }
# output "mta-eip-4_elastic_prv_ip" {
#   value = aws_eip.mta-eip-4.private_ip
# }
# output "mta-eip-4_elastic_pub_ip" {
#   value = aws_eip.mta-eip-4.public_ip
# }
# output "mta-eip-5_elastic_prv_ip" {
#   value = aws_eip.mta-eip-5.private_ip
# }
# output "mta-eip-5_elastic_pub_ip" {
#   value = aws_eip.mta-eip-5.public_ip
# }
# output "mta-eip-6_elastic_prv_ip" {
#   value = aws_eip.mta-eip-6.private_ip
# }
# output "mta-eip-6_elastic_pub_ip" {
#   value = aws_eip.mta-eip-6.public_ip
# }
# output "mta-eip-7_elastic_prv_ip" {
#   value = aws_eip.mta-eip-7.private_ip
# }
# output "mta-eip-7_elastic_pub_ip" {
#   value = aws_eip.mta-eip-7.public_ip
# }
# output "mta-eip-8_elastic_prv_ip" {
#   value = aws_eip.mta-eip-8.private_ip
# }
# output "mta-eip-8_elastic_pub_ip" {
#   value = aws_eip.mta-eip-8.public_ip
# }
# output "mta-eip-9_elastic_prv_ip" {
#   value = aws_eip.mta-eip-9.private_ip
# }
# output "mta-eip-9_elastic_pub_ip" {
#   value = aws_eip.mta-eip-9.public_ip
# }
# output "mta-eip-10_elastic_prv_ip" {
#   value = aws_eip.mta-eip-10.private_ip
# }
# output "mta-eip-10_elastic_pub_ip" {
#   value = aws_eip.mta-eip-10.public_ip
# }
# output "mta-eip-11_elastic_prv_ip" {
#   value = aws_eip.mta-eip-11.private_ip
# }
# output "mta-eip-11_elastic_pub_ip" {
#   value = aws_eip.mta-eip-11.public_ip
# }
# output "mta-eip-12_elastic_prv_ip" {
#   value = aws_eip.mta-eip-12.private_ip
# }
# output "mta-eip-12_elastic_pub_ip" {
#   value = aws_eip.mta-eip-12.public_ip
# }
# output "mta-eip-13_elastic_prv_ip" {
#   value = aws_eip.mta-eip-13.private_ip
# }
# output "mta-eip-13_elastic_pub_ip" {
#   value = aws_eip.mta-eip-13.public_ip
# }
# output "mta-eip-14_elastic_prv_ip" {
#   value = aws_eip.mta-eip-14.private_ip
# }
# output "mta-eip-14_elastic_pub_ip" {
#   value = aws_eip.mta-eip-14.public_ip
# }
