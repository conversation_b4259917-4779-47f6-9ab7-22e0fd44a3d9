all:
  vars:
    aws_profile: ${aws_profile}
    mta_name: ${mta_name}
    mta_iac_folder_path: ${mta_iac_folder_path}
    dns_domain_name: ${dns_domain_name}
    opendkim_key: ${opendkim_key}
    opendkim_domains:
%{ for index, odkim_dom in opendkim_domains ~}
      - "${odkim_dom}"
%{ endfor ~}

    # set email name for the opendkim service !
    admin_email: "${admin_email}"
    # always set to true if have multi- ip setup - 
    # and set the bellow values to correspond to the multiple ips you have on the TF main code
    netplan_install: true
    # always set to true
    mongodb_install: true
    # always set to true
    redis_install: true
    # set to false we do that when installing zone-mta - no need anymore !
    npm_install: false
    # always set to true
    secure_ssh_install: true
    # set to true
    zone_mta_install: true
    # set to false / not yet implemented and maybe never will ......
    postfix_install: false
    # set to true 
    opendkim_install: true
    # set to true
    logstash_install: true
    # which is the Logs elasticsearch cluster / server  to send the logs to 
    logstash_elasticsearch_hosts:
%{ for index, lgs_es in logstash_elasticsearch_hosts ~}
      - "${lgs_es}"
%{ endfor ~}
    # logs elasticsearch user 
    es_user: ${es_user}
    # logs elasticsearch password
    es_password: ${es_password}
    es_index: ${es_index}
    #ports 
    smtp_port: ${smtp_port}
    #secure ssh to ldap !
    secure_ssh_install: true
    # ips for the netplan (do not forget to set new ips here if have more than the basic 3 )
    ip_prv_0: ${mta-0}
    ip_pub_0: ${mta-eip-0}
    ip_prv_1: ${mta-1}
    ip_pub_1: ${mta-eip-1}
    ip_prv_2: ${mta-2}
    ip_pub_2: ${mta-eip-2}
    ip_prv_3: ${mta-3}
    ip_pub_3: ${mta-eip-3}
    ip_prv_4: ${mta-4}
    ip_pub_4: ${mta-eip-4}
    ip_prv_5: ${mta-5}
    ip_pub_5: ${mta-eip-5}
    ip_prv_6: ${mta-6}
    ip_pub_6: ${mta-eip-6}
    ip_prv_7: ${mta-7}
    ip_pub_7: ${mta-eip-7}



    # Private Ips for ansible
    prv_ip:
%{ for index, prv_ip in private_ip_list ~}
      - "${prv_ip}"
%{ endfor ~}
    #if_names for mta 
    interface_names:
%{ for index, ifname in interface_names ~}
      - "${ifname}"
%{ endfor ~}
    #if_names for ansible
%{ for index, ifname in interface_names ~}
    interface_names_${index}: "${ifname}"
%{ endfor ~}
    # for Ansible netplan
    prv_ips: 
      - "${mta-0}"
      - "${mta-1}"
      - "${mta-2}"
      - "${mta-3}"
      - "${mta-4}"
      - "${mta-5}"
      - "${mta-6}"
      - "${mta-7}"

    pub_ips: 
      - "${mta-eip-0}"
      - "${mta-eip-1}"
      - "${mta-eip-2}"
      - "${mta-eip-3}"
      - "${mta-eip-4}"
      - "${mta-eip-5}"
      - "${mta-eip-6}"
      - "${mta-eip-7}"

    #config
    starttls: "${starttls}"
    authentication: "${authentication}"
    maxRecipients: ${maxRecipients}
    secure: "${secure}"
    key: ${key_path}
    cert: ${cert_path}
    logger: "${logger}"
    loglevel: "${loglevel}"
    hideSTARTTLS: "${hideSTARTTLS}"
    hide8BITMIME: "${hide8BITMIME}"
    useXClient: "${useXClient}"
    # ansible specific
    ansible_ssh_port: 22
    ansible_ssh_private_key_file: ../common/files/tlan-falcon-05092024.pem
    ansible_user: ubuntu
    become: true
    become_user: root
     

  children:
    cluster:
      vars:
        # Variables common to all nodes.
        ansible_python_interpreter: /usr/bin/python3
        ansible_ssh_extra_args: '-o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null'
        ansible_ssh_port: 22
        ansible_ssh_private_key_file: ../common/files/tlan-falcon-05092024.pem
        ansible_user: ubuntu
      children:
        nodes:
    nodes:
      # Set a node for as the base mta.
      hosts:
        mtas:
          ansible_host: ${mta-0}
    association:
      hosts:
        assoc-0:
          ansible_host: ${mta-0},${mta-eip-0} 
        assoc-1:
          ansible_host: ${mta-1},${mta-eip-1}
        assoc-2:
          ansible_host: ${mta-2},${mta-eip-2}
        assoc-3:
          ansible_host: ${mta-3},${mta-eip-3}
        assoc-4:
          ansible_host: ${mta-4},${mta-eip-4} 
        assoc-5:
          ansible_host: ${mta-5},${mta-eip-5}
        assoc-6:
          ansible_host: ${mta-6},${mta-eip-6}
        assoc-7:
          ansible_host: ${mta-7},${mta-eip-7}


    vpc-ips:
      hosts:
        pub-0:
          ansible_host: ${mta-eip-0}
        pub-1:
          ansible_host: ${mta-eip-1}
        pub-2:
          ansible_host: ${mta-eip-2}
        pub-3:
          ansible_host: ${mta-eip-3}
        pub-4:
          ansible_host: ${mta-eip-4}
        pub-5:
          ansible_host: ${mta-eip-5}
        pub-6:
          ansible_host: ${mta-eip-6}
        pub-7:
          ansible_host: ${mta-eip-7}


    elastic:
      hosts:
        pub-0:
          ansible_host: ${mta-eip-0}
        pub-1:
          ansible_host: ${mta-eip-1}
        pub-2:
          ansible_host: ${mta-eip-2}
        pub-3:
          ansible_host: ${mta-eip-3}
        pub-4:
          ansible_host: ${mta-eip-4}
        pub-5:
          ansible_host: ${mta-eip-5}
        pub-6:
          ansible_host: ${mta-eip-6}
        pub-7:
          ansible_host: ${mta-eip-7}