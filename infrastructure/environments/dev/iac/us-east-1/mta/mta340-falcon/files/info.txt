
sudo apt install awscli
sudo apt install ansible
ansible-galaxy collection install kubernetes.core






/home/<USER>/GIT_TALENT/talent/infrastructure/environments/dev/iac/us-east-1/mta/mta340-falcon
Region us-east-1 Virginia


############################################################################################################################################################################################################################################################################################################################################################################################
# 1st copy and paste the programmatic access and also paste it on the terminal
export AWS_ACCESS_KEY_ID="********************"
export AWS_SECRET_ACCESS_KEY="t0K2Kx9AJ/JwydAP2tPDRutU0tbOnvTYNFCXa2Zm"
export AWS_SESSION_TOKEN="IQoJb3JpZ2luX2VjECYaCXVzLWVhc3QtMSJGMEQCIAvIrc5NMCvjqfnf31aZ7ABtyibsT/+mAB7JHdk9CQVqAiBNFD6JpTZJvD7kKM0/28Xc7C7nnXtxhlrM25PEFVuftiqZAwjv//////////8BEAAaDDUwMTg3NzExNzA5MiIMWJ2doY19WwYwf4XoKu0Cr5pAww7kIFv03uVt7brAdjVzZjfB4a0Myi8uHZzpOtWbR73j5g145tLwzdulIZXRomYPAsy/Tsk9vQahhetkpxmRwudmynw4y0c13jynJea12izoS8oUl8Zs70n7ZLr8IeE7iS6K2UB7mgxW7M1PfZjzoEv+5t8BnDkwbe/kTUPqqsM/ZVXuH4Rt/EUChxeARra9WBa/mJD/3ViC7tvG/WjGgPofmE5ts8Ac6U1cB/Dq1PJrI27Q/Qf6tPb10ejgcElGMVFqqI+ldvQrMaX5asmRPps3UY1iRoB33d01J59nTpZqv09h6bvAKIbQs+wofRLlvxG78opajmoizQMzENabhYAqhHs+B5Swv49vQeTe/C8gb8YUMMWc9HDkRT9DNwsk+3eZHlGBJnv/byIO3NXdFsXnsQdKL+DQtvdf1abJnX3v95RP0ut+7GxmzQuGGTPB7UKKTbXSqKZGcOjUBhpSaMW8RawjU6PMZCQw+bqTrgY6pwGZWeqaeA8isGiIeAW63s86dk4+MAyMXxDEokSf8Wtr2UIfn/cdzRvOYuPykCxqYvIPufRM1/sMW1yMzTa9gUYCSELwRB1l2GPFqd+/pfPwfeJzRtKNYa5DU3Z/kC7lhlgdI1ydGXkHNWq0ngKdivcLYB/wSqR71uMCOPiGss+W3Yz7KHR0V0wl3Dz4j8OHj5gby6R9J4jv8ClVdqNibSAiplx6LKWVxg=="
##########################################################################################################################################################################################################################################################################################################################################################################################################################################################################################################################################################################################






################################################################################################################################################################################################################################################################################################################
# 2 run this on the termainal
export SESSION=$(aws sts assume-role --role-arn "arn:aws:iam::501877117092:role/service-infrastructure-terraform-role" --role-session-name AWSCLI-Session --duration-seconds 3600 --output json) 
export AWS_ACCESS_KEY_ID=$(echo $SESSION | jq -r '.Credentials.AccessKeyId') 
export AWS_SECRET_ACCESS_KEY=$(echo $SESSION | jq -r '.Credentials.SecretAccessKey') 
export AWS_SESSION_TOKEN=$(echo $SESSION | jq -r '.Credentials.SessionToken') 
echo $SESSION
echo $AWS_ACCESS_KEY_ID
echo $AWS_SECRET_ACCESS_KEY
echo $AWS_SESSION_TOKEN
aws sts get-caller-identity
aws eks --region us-east-1 update-kubeconfig --name 471112575606_AWSAdministratorAccess

###############################################################################################################################################################################################################################################################################################################



>  ~/.aws/credentials && nano  ~/.aws/credentials
copy the Option 2 from the programmatic access and paste it in the terminal and save 





terraform init
terraform init --upgrade
terraform plan -out aws-tf_zone-mta_ansible.tfplan
terraform apply  aws-tf_zone-mta_ansible.tfplan
terraform output > ../zone_mta_ip_info.yaml





# Oneliner !
terraform init --upgrade && terraform plan -out aws-tf_zone-mta_ansible.tfplan
terraform apply aws-tf_zone-mta_ansible.tfplan && terraform output > ./files/zone_mta_ip_info.yaml

########################
terraform init --upgrade && terraform plan -out ./files/aws-tf_zone-mta_ansible.tfplan && terraform apply ./files/aws-tf_zone-mta_ansible.tfplan && terraform output > ./files/zone_mta_ip_info.yaml

##terraform plan -refresh-only -out aws-tf_zone-mta_ansible.tfplan && terraform apply aws-tf_zone-mta_ansible.tfplan && terraform output > ../zone_mta_ip_info.yaml

########################
#terraform destroy

root
Hapgl1L51UQj18n3lgcHJb3nf1FznzXzWo6A9EF0

Hapgl1L51UQj18n3lgcHJb3nf1FznzXzWo6A9EF

export ANSIBLE_HOST_KEY_CHECKING=False && ansible-playbook -i ./files/inventory.yml '../ansible-install-mta/setup.yaml'
#export ANSIBLE_HOST_KEY_CHECKING=False && ansible-playbook -i ./files/inventory.yml '../ansible-install-mta/setup_netplan.yml'
#export ANSIBLE_HOST_KEY_CHECKING=False && ansible-playbook -i ./files/inventory.yml '../ansible-install-mta/setup_redis.yml'
#export ANSIBLE_HOST_KEY_CHECKING=False && ansible-playbook -i ./files/inventory.yml '../ansible-install-mta/setup_mongodb.yml'
export ANSIBLE_HOST_KEY_CHECKING=False && ansible-playbook -i ./files/inventory.yml '../ansible-install-mta/setup_zone-mta.yml'
export ANSIBLE_HOST_KEY_CHECKING=False && ansible-playbook -i ./files/inventory.yml '../ansible-install-mta/setup_opendkim.yml'
##export ANSIBLE_HOST_KEY_CHECKING=False && ansible-playbook -i ./files/inventory.yml '../ansible-install-mta/setup_postfix.yml'
##export ANSIBLE_HOST_KEY_CHECKING=False && ansible-playbook -i ./files/inventory.yml '../ansible-install-mta/setup_npm.yml'
#export ANSIBLE_HOST_KEY_CHECKING=False && ansible-playbook -i ./files/inventory.yml '../ansible-install-mta/setup_secure_ssh.yml'
#export ANSIBLE_HOST_KEY_CHECKING=False && ansible-playbook -i ./files/inventory.yml '../ansible-install-mta/setup_logstash.yml'
#export ANSIBLE_HOST_KEY_CHECKING=False && ansible-playbook -i ./files/inventory.yml '../ansible-install-mta/setup_secure_ssh.yml'


curl ifconfig.me



export ANSIBLE_HOST_KEY_CHECKING=False && ansible-playbook -i ./files/inventory.yml '../ansible-install-mta/setup_npm.yml'

export ANSIBLE_HOST_KEY_CHECKING=False && ansible-playbook -i ./files/inventory.yml '../ansible-install-mta/setup_secure_ssh.yml'

export ANSIBLE_HOST_KEY_CHECKING=False && ansible-playbook -i ./files/inventory.yml '../ansible-install-mta/setup_bootstrap.yml'


export ANSIBLE_HOST_KEY_CHECKING=False && ansible-playbook -i ./files/inventory.yml '../ansible-install-mta/setup_ssl_key.yml'
