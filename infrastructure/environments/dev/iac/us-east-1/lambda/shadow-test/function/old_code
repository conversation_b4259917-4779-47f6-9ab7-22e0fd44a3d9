import boto3
import json
from datetime import datetime, timedelta, timezone
from dateutil.tz import tzutc

def lambda_handler(event, context):
    condition_timestamp = datetime.now(tz=tzutc()) - timedelta( minutes=20)  #dynamic condition
    #condition_timestamp = datetime(2023, 2, 17, tzinfo=tzutc()) #Fixed condition
    bucket_name = 'talent-cloudflare-logs'


     # Get current time in UTC
    current_time = datetime.now(timezone.utc)

    # Format time strings
    date_prefix = current_time.strftime('%Y%m%d')



    s3 = boto3.client('s3')

    paginator = s3.get_paginator('list_objects_v2')

    s3_filtered_list = [obj["Key"] for page in paginator.paginate(Bucket=bucket_name,Prefix=date_prefix) for obj in page["Contents"] if obj['LastModified'] > condition_timestamp]
    #print(s3_filtered_list)
    return s3_filtered_list