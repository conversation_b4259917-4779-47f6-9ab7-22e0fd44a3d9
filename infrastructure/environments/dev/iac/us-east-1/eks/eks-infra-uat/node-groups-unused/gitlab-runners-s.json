{"min_size": 8, "desired_size": 9, "max_size": 12, "capacity_type": "SPOT", "instance_types": ["t3a.2xlarge", "c6a.2xlarge", "c5a.2xlarge", "t3.2xlarge"], "taints": [{"key": "app", "value": "gitlab-runner-s", "effect": "NO_SCHEDULE"}], "labels": {"app": "gitlab-runner-s"}, "block_device_mappings": {"xvda": {"device_name": "/dev/xvda", "ebs": {"volume_size": 200, "volume_type": "gp3", "iops": 3000, "throughput": 150, "encrypted": true, "delete_on_termination": true}}}, "tags": {"cc": "infrastructure", "environment": "uat", "group": "gitlab-runner", "owner": "<EMAIL>", "service": "runner", "terraform": "true"}}