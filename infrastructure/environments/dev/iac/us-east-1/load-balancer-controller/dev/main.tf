provider "aws" {
  region = "us-east-1"

  default_tags {
    tags = {
      environment = "dev"
      terraform = "true"
      cc = "infrastructure"
      department = "infrastructure"
      group = "kubernetes"
      owner = "<EMAIL>"
      service = "eks"
      service_type = "app"
    }
  }
}

resource "aws_iam_policy" "alb_controller_policy" {
  name        = "AWSLoadBalancerControllerIAMPolicy"
  path        = "/"
  description = "Policy for AWS Load Balancer Controller"
  policy      = file("${path.module}/iam_policy.json") 
}

resource "aws_iam_role" "alb_controller_role" {
  name = "eks-alb-controller-role"
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [{
      Effect = "Allow"
      Principal = {
        Federated = var.aws_iam_openid_connect_provider_arn
      }
      Action = "sts:AssumeRoleWithWebIdentity"
      Condition = {
        StringEquals = {
          "oidc.eks.${var.region}.amazonaws.com/id/${var.cluster_oidc_id}:sub" : "system:serviceaccount:kube-system:aws-load-balancer-controller"
        }
      }
    }]
  })
}

resource "aws_iam_role_policy_attachment" "alb_controller_attach" {
  policy_arn = aws_iam_policy.alb_controller_policy.arn
  role       = aws_iam_role.alb_controller_role.name
}

# the yaml of the service account is in the kubernetes folder infrastructure/environments/dev/iac/us-east-1/load-balancer-controller/kubernetes

# reference: https://docs.aws.amazon.com/eks/latest/userguide/lbc-helm.html