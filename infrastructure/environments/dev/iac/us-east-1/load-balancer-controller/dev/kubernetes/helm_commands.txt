### Dev account - dev environment

helm install aws-load-balancer-controller eks/aws-load-balancer-controller \
  -n kube-system \
  --set clusterName=eks-infra-app-dev \
  --set serviceAccount.create=false \
  --set serviceAccount.name=aws-load-balancer-controller


### Dev account - uat environment


helm install aws-load-balancer-controller eks/aws-load-balancer-controller \
  -n kube-system \
  --set clusterName=eks-infra-uat-dev \
  --set serviceAccount.create=false \
  --set serviceAccount.name=aws-load-balancer-controller