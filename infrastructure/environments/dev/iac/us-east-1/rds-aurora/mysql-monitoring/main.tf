
module "cluster" {
  source = "../../../../../../terraform/modules/official/terraform-aws-rds-aurora"

  name            = basename(path.cwd)
  engine          = "aurora-mysql"
  database_name   = "grafana"
  master_username = "admin"
  master_password = "m4sq4l4r0r4Fgr"

  engine_mode = "provisioned"
  instance_class = "db.serverless"
  engine_version = "8.0.mysql_aurora.3.07.0"

  manage_master_user_password = false

  # manage_master_user_password = false
  instances = {
    1 = {}
  }

  serverlessv2_scaling_configuration = {
    max_capacity = 128
    min_capacity = 0.5
  }

  vpc_security_group_ids = ["sg-018a956650cd8f6a8"]
  vpc_id                 = "vpc-01b9c6bb8f72cde01"
  db_subnet_group_name   = "rds-serverless-dev-subnet"

  storage_encrypted   = false
  apply_immediately   = true
  monitoring_interval = 10
  skip_final_snapshot = true

  # Performance Insights configuration
  performance_insights_enabled        = true
  performance_insights_retention_period = 7
  # performance_insights_kms_key_id   = "arn:aws:kms:us-east-1:123456789012:key/your-kms-key-id"

  tags = {
    environment = "dev"
    terraform   = "true"
    cc          = "infrastructure"
    owner       = "<EMAIL>"
    type        = "rds"
    service     = "mysql-monitoring"
    group       = "mysql-monitoring"
  }
}

data "aws_route53_zone" "this" {
  name         = "talent.local"
  private_zone = true
}

resource "aws_route53_record" "writer" {
  allow_overwrite = true
  zone_id = data.aws_route53_zone.this.zone_id
  name    = "${basename(path.cwd)}"
  type    = "CNAME"
  ttl     = 60
  records = [ module.cluster.cluster_endpoint ]
}

resource "aws_route53_record" "reader" {
  allow_overwrite = true
  zone_id = data.aws_route53_zone.this.zone_id
  name    = "${basename(path.cwd)}-reader"
  type    = "CNAME"
  ttl     = 60
  records = [ module.cluster.cluster_reader_endpoint ]
}

data "aws_route53_zone" "private" {
  name         = "talent.private"
  private_zone = true
}

resource "aws_route53_record" "reader2" {
  allow_overwrite = true
  zone_id = data.aws_route53_zone.private.zone_id
  name    = "${basename(path.cwd)}-reader"
  type    = "CNAME"
  ttl     = 60
  records = [ module.cluster.cluster_reader_endpoint ]
}

resource "aws_route53_record" "writer2" {
  allow_overwrite = true
  zone_id = data.aws_route53_zone.private.zone_id
  name    = "${basename(path.cwd)}"
  type    = "CNAME"
  ttl     = 60
  records = [ module.cluster.cluster_endpoint ]
}