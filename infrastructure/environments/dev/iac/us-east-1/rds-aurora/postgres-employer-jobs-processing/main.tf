
module "cluster" {
  source = "../../../../../../terraform/modules/official/terraform-aws-rds-aurora"

  name            = basename(path.cwd)
  engine          = "aurora-postgresql"
  master_username = "jobs"
  master_password = "p0stgr3st4r0r4F.#"

  manage_master_user_password = false

  engine_mode         = "provisioned"
  instance_class      = "db.serverless"
  engine_version      = "14"
  skip_final_snapshot = true

  # Performance Insights configuration
  performance_insights_enabled        = true
  performance_insights_retention_period = 7
  # performance_insights_kms_key_id   = "arn:aws:kms:us-east-1:123456789012:key/your-kms-key-id"
  
  serverlessv2_scaling_configuration = {
    max_capacity = 128
    min_capacity = 0.5
  }

  instances = {
    one = {},
    2 = {}
  }



  snapshot_identifier    = "postgres-employer-jobs-processing-26062024"
  vpc_id                 = "vpc-01b9c6bb8f72cde01"
  vpc_security_group_ids = ["sg-018a956650cd8f6a8"]
  db_subnet_group_name   = "rds-serverless-dev-subnet"

  storage_encrypted   = true
  apply_immediately   = true
  monitoring_interval = 10

  create_db_cluster_parameter_group = true
  db_cluster_parameter_group_family = "aurora-postgresql14"
  db_cluster_parameter_group_parameters = [{
    apply_method = "pending-reboot"
    name         = "rds.logical_replication"
    value        = 1
    }, {
    apply_method = "pending-reboot"
    name         = "wal_sender_timeout"
    value        = 300000
    }, {
      apply_method = "immediate"
      name = "autovacuum_analyze_scale_factor"
      value = "0.025"
    }, {
      apply_method = "immediate"
      name = "autovacuum_vacuum_cost_delay"
      value = "10"
    }]

  tags = {
    environment = "dev"
    Terraform   = "true"
    Name        = "postgres-employers-job-processing-rds",
    cc          = "employer"
    owner       = "<EMAIL>"
    type        = "rds"
    service     = "postgres-employers-job-processing"
    group       = "employers"
  }
}

data "aws_route53_zone" "this" {
  name         = "talent.local"
  private_zone = true
}

resource "aws_route53_record" "writer" {
  allow_overwrite = true
  zone_id         = data.aws_route53_zone.this.zone_id
  name            = "${basename(path.cwd)}"
  type            = "CNAME"
  ttl             = 60
  records         = [module.cluster.cluster_endpoint]
}

resource "aws_route53_record" "reader" {
  allow_overwrite = true
  zone_id         = data.aws_route53_zone.this.zone_id
  name            = "${basename(path.cwd)}-reader"
  type            = "CNAME"
  ttl             = 60
  records         = [module.cluster.cluster_reader_endpoint]
}

data "aws_route53_zone" "private" {
  name         = "talent.private"
  private_zone = true
}

resource "aws_route53_record" "writer2" {
  allow_overwrite = true
  zone_id = data.aws_route53_zone.private.zone_id
  name    = "${basename(path.cwd)}"
  type    = "CNAME"
  ttl     = 60
  records = [ module.cluster.cluster_endpoint ]
}

resource "aws_route53_record" "reader2" {
  allow_overwrite = true
  zone_id = data.aws_route53_zone.private.zone_id
  name    = "${basename(path.cwd)}-reader"
  type    = "CNAME"
  ttl     = 60
  records = [ module.cluster.cluster_reader_endpoint ]
}