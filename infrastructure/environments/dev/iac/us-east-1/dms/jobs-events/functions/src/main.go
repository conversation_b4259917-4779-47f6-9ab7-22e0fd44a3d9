package main

import (
	"bufio"
	"compress/gzip"
	"context"
	"encoding/csv"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"strconv"
	"strings"
	"time"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/aws/aws-sdk-go-v2/service/s3/types"
	"github.com/segmentio/kafka-go"
	"github.com/sony/sonyflake"
)

// -----------------------------------------------------------------------------
// Global Vars
// -----------------------------------------------------------------------------

var (
	// Kafka brokers:
	PublisherBrokers = []string{
		"kafka-broker-employers-job-stream-0.talent.private:9092",
		"kafka-broker-employers-job-stream-1.talent.private:9092",
		"kafka-broker-employers-job-stream-2.talent.private:9092",
	}

	// Bucket, region, and Kafka topic prefix:
	Bucket               = "talent-dev-jobs"
	Region               = "us-east-1"
	PublisherTopicPrefix = "job_ppc_processed_migration_"
	NumShards            = 8
)

// main entry point for AWS Lambda in Go
func main() {
	lambda.Start(lambdaHandler)
}

// -----------------------------------------------------------------------------
// Lambda Handler
// -----------------------------------------------------------------------------

// lambdaHandler is triggered by an S3 event containing a .gz file with CSV data.
func lambdaHandler(ctx context.Context, s3Event events.S3Event) (string, error) {
	log.Printf("Received S3 Trigger Event: %+v\n", s3Event)

	if len(s3Event.Records) == 0 {
		log.Println("No records in S3 event")
		return "ERROR", nil
	}

	record := s3Event.Records[0]
	bucket := record.S3.Bucket.Name
	key := record.S3.Object.Key
	log.Printf("S3 bucket=%s, key=%s\n", bucket, key)

	// 1) Download the GZIP CSV from S3
	start := time.Now()
	csvRows, err := downloadAndParseGzipCSV(ctx, bucket, key)
	if err != nil {
		log.Printf("Error parsing GZIP CSV from S3: %v", err)
		return "ERROR", err
	}
	durationMS := time.Since(start).Milliseconds()
	log.Printf("Download csv file and decode in %d ms\n", durationMS)
	// 2) For each row, split new vs old, check logic, maybe fetch job desc, send to Kafka
	for _, row := range csvRows {
		newVals, oldVals := splitNewOldFields(row)
		err := processRow(ctx, newVals, oldVals)
		if err != nil {
			log.Printf("Error processing row (ID=%v): %v", newVals["id"], err)
			// Decide whether to continue or return an error
		}
	}

	log.Printf("Successfully processed %d rows from CSV.\n", len(csvRows))
	return "OK", nil
}

// processRow implements the logic you had in Python:
// - If `oldVals` is empty => treat as a new job => send to Kafka
// - Else compare `system_hash_html` vs `old_system_hash_html` => possibly fetch from S3
// - Then send final newVals to Kafka
func processRow(ctx context.Context, newVals, oldVals map[string]string) error {
	// Parse jobID from newVals
	jobID, err := strconv.Atoi(newVals["id"])
	if err != nil {
		return fmt.Errorf("invalid or missing 'id': %w", err)
	}
	// Suppose the shard is in the key or we hardcode a shard=0 for demonstration
	shard := ShardFromSonyflakeID(uint64(jobID))

	// Convert shard to string for the Kafka topic
	shardStr := strconv.Itoa(int(shard))
	topic := PublisherTopicPrefix + shardStr

	// Check if oldVals is empty
	if len(oldVals) == 0 {
		log.Printf("No 'old_values' => new job. Sending 'new_values' to Kafka. (ID=%d)", jobID)
		jobDesc, _ := getJobDescFromS3(ctx, jobID, int(shard))
		newVals["job_desc_html"] = jobDesc

		return sendToKafka(ctx, newVals, topic, jobID)
	}

	// We have oldVals. Compare system_hash_html
	newHash := newVals["system_hash_html"]
	oldHash := oldVals["system_hash_html"] // remember we removed "old_" prefix
	// If oldHash is empty or differs from newHash => fetch from S3
	if oldHash == "" || (newHash != oldHash) {
		log.Printf("Hash differs (new=%s, old=%s) => retrieving file from S3", newHash, oldHash)
		jobDesc, err := getJobDescFromS3(ctx, jobID, int(shard))
		if err != nil {
			log.Printf("Error retrieving job description from S3 for ID=%d: %v\n", jobID, err)
		} else if jobDesc != "" {
			newVals["job_desc_html"] = jobDesc
		}
	}

	// Send final newVals to Kafka
	return sendToKafka(ctx, newVals, topic, jobID)
}

// sendToKafka is a helper to marshal `newVals` to JSON and produce to Kafka
func sendToKafka(ctx context.Context, newVals map[string]string, topic string, jobID int) error {
	msgValue, err := json.Marshal(newVals)
	if err != nil {
		return fmt.Errorf("error marshalling newVals to JSON: %w", err)
	}
	err = sendMessageToKafka(ctx, PublisherBrokers, topic, strconv.Itoa(jobID), string(msgValue))
	if err != nil {
		return fmt.Errorf("error sending to Kafka: %w", err)
	}
	return nil
}

// splitNewOldFields takes a row of CSV (key=header, value=row cell) and splits
// it into newVals (regular) and oldVals (headers that start with "old_").
func splitNewOldFields(row map[string]string) (map[string]string, map[string]string) {
	newVals := make(map[string]string)
	oldVals := make(map[string]string)

	for k, v := range row {
		if strings.HasPrefix(k, "old_") {
			// e.g. if k="old_system_hash_html", rename key -> "system_hash_html"
			strippedKey := strings.TrimPrefix(k, "old_")
			oldVals[strippedKey] = v
		} else {
			// normal field
			newVals[k] = v
		}
	}
	return newVals, oldVals
}

// -----------------------------------------------------------------------------
// CSV Parsing Logic
// -----------------------------------------------------------------------------

// downloadAndParseGzipCSV downloads a .gz file from S3, decompresses it,
// and parses the CSV. Returns a slice of map[columnName]columnValue.
func downloadAndParseGzipCSV(ctx context.Context, bucket, key string) ([]map[string]string, error) {
	// 1) Download object
	gzReader, err := downloadAndDecompressGzip(ctx, bucket, key)
	if err != nil {
		return nil, err
	}
	defer gzReader.Close()

	// 2) Parse CSV
	csvReader := csv.NewReader(bufio.NewReader(gzReader))

	// First row is header
	headers, err := csvReader.Read()
	if err == io.EOF {
		return nil, fmt.Errorf("empty CSV file")
	}
	if err != nil {
		return nil, fmt.Errorf("error reading header: %w", err)
	}
	for i, h := range headers {
		headers[i] = strings.TrimSpace(h)
	}

	var results []map[string]string
	for {
		row, err := csvReader.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			return nil, fmt.Errorf("error reading row: %w", err)
		}
		if len(row) != len(headers) {
			log.Printf("Skipping row: expected %d columns, got %d\n", len(headers), len(row))
			continue
		}

		rowMap := make(map[string]string, len(headers))
		for i, val := range row {
			rowMap[headers[i]] = val
		}
		results = append(results, rowMap)
	}
	return results, nil
}

// downloadAndDecompressGzip downloads a GZIP file from S3 and returns an open gzip.Reader
func downloadAndDecompressGzip(ctx context.Context, bucket, key string) (*gzip.Reader, error) {
	cfg, err := config.LoadDefaultConfig(ctx, config.WithRegion(Region))
	if err != nil {
		return nil, fmt.Errorf("failed to load AWS config: %w", err)
	}

	s3Client := s3.NewFromConfig(cfg)
	getResp, err := s3Client.GetObject(ctx, &s3.GetObjectInput{
		Bucket: aws.String(bucket),
		Key:    aws.String(key),
	})
	if err != nil {
		var noSuchKey *types.NoSuchKey
		if errors.As(err, &noSuchKey) {
			return nil, fmt.Errorf("object not found: %v", err)
		}
		return nil, fmt.Errorf("failed to get object: %w", err)
	}

	gzReader, err := gzip.NewReader(getResp.Body)
	if err != nil {
		getResp.Body.Close() // ensure body is closed if NewReader fails
		return nil, fmt.Errorf("error creating gzip reader: %w", err)
	}
	// we defer gzReader.Close() in the caller
	return gzReader, nil
}

// -----------------------------------------------------------------------------
// getJobDescFromS3 (Preserved Method)
// -----------------------------------------------------------------------------

// getJobDescFromS3 downloads a file from S3 based on 'shard' and 'jobID'.
// Returns the file content as a string or empty if an error occurs.
func getJobDescFromS3(ctx context.Context, jobID, shard int) (string, error) {
	// Construct a path: e.g. "shard-001/html/{jobID}.txt"
	// Adjust as needed
	key := fmt.Sprintf("shard-%03d/html/%d.txt", shard, jobID)
	log.Printf("Downloading job description from S3: bucket=%s, key=%s", Bucket, key)

	start := time.Now()
	content, err := downloadFileFromS3(ctx, Bucket, key)
	if err != nil {
		log.Printf("Error reading job description from S3: %v", err)
		return "", err
	}
	durationMS := time.Since(start).Milliseconds()
	log.Printf("Job description for ID=%d downloaded in %d ms\n", jobID, durationMS)
	return content, nil
}

// downloadFileFromS3 reuses the AWS SDK to fetch a text file from S3 and returns it as a string.
func downloadFileFromS3(ctx context.Context, bucket, key string) (string, error) {
	cfg, err := config.LoadDefaultConfig(ctx, config.WithRegion(Region))
	if err != nil {
		return "", fmt.Errorf("failed to load AWS config: %w", err)
	}

	s3Client := s3.NewFromConfig(cfg)
	resp, err := s3Client.GetObject(ctx, &s3.GetObjectInput{
		Bucket: aws.String(bucket),
		Key:    aws.String(key),
	})
	if err != nil {
		var noSuchKey *types.NoSuchKey
		if errors.As(err, &noSuchKey) {
			return "", fmt.Errorf("object not found: %v", err)
		}
		return "", fmt.Errorf("failed to get object: %w", err)
	}
	defer resp.Body.Close()

	var sb strings.Builder
	_, err = io.Copy(&sb, resp.Body)
	if err != nil {
		return "", fmt.Errorf("error reading S3 object body: %w", err)
	}

	return sb.String(), nil
}

// -----------------------------------------------------------------------------
// Kafka Logic
// -----------------------------------------------------------------------------

// sendMessageToKafka uses segmentio/kafka-go to send a message with key/value as strings.
func sendMessageToKafka(ctx context.Context, brokers []string, topic, key, value string) error {
	if len(brokers) == 0 {
		return fmt.Errorf("no broker URLs provided")
	}
	writer := kafka.NewWriter(kafka.WriterConfig{
		Brokers:  brokers,
		Topic:    topic,
		Balancer: &kafka.Hash{}, // Use hash algorithm to send messages with same key to the same partition.
		// Async:     true,          // Do not wait for messages to be sent before continuing execution.
		BatchTimeout: 10 * time.Millisecond,
		BatchSize:    1, // Send 1000 messages at a time.
		RequiredAcks: 0, // Do not wait for acks from brokers.
	})
	defer writer.Close()

	start := time.Now()
	err := writer.WriteMessages(ctx, kafka.Message{
		Key:   []byte(key),
		Value: []byte(value),
	})
	if err != nil {
		return fmt.Errorf("failed to send message to Kafka: %w", err)
	}
	elapsed := time.Since(start).Milliseconds()
	log.Printf("Message sent to topic=%s in %d ms\n", topic, elapsed)
	return nil
}

// ShardFromSonyflakeID takes a Sonyflake ID and a number of shards,
// decomposes the ID to get its sequence component, and returns (sequence % numShards).
func ShardFromSonyflakeID(id uint64) uint64 {
	// Decompose the Sonyflake ID into its components
	components := sonyflake.Decompose(id)

	// Extract the sequence component
	seq := components["sequence"]

	// Compute the shard as sequence modulo numShards
	return seq % uint64(NumShards)
}
