provider "aws" {
  region = local.region
}

locals {
  region = "us-east-1"
  name   = "dms-${basename(path.cwd)}"

}
module "database_migration_service" {
  source = "../../../../../../terraform/modules/official/terraform-aws-dms"

  use_existing_instance                = true
  existing_replication_instance_arn    = var.replication_instance_arn_dms
  repl_instance_apply_immediately      = true
  repl_instance_engine_version         = "3.5.3"
  repl_instance_publicly_accessible    = false
  repl_instance_vpc_security_group_ids = var.security_groups

  # Subnet group
  repl_subnet_group_name        = local.name
  repl_subnet_group_description = "DMS Subnet group for ${local.name}"
  repl_subnet_group_subnet_ids  = var.vpc_subnet_ids

  # Existing IAM Role for DMS (added)
  create_iam_roles  = false # Prevent Terraform from creating new IAM roles
  use_existing_iam_roles = true
  existing_dms_access_for_endpoint_role_arn = var.DmsRoleArn
  existing_dms_cloudwatch_logs_role_arn = var.DmsRoleArn
  existing_dms_vpc_role_arn = var.DmsRoleArn
  
  
  # Endpoints
  endpoints = {
    source = {
      database_name               = var.db_name
      endpoint_id                 = "${local.name}-source"
      endpoint_type               = "source"
      engine_name                 = "aurora"
      extra_connection_attributes = "heartbeatFrequency=1"
      secrets_manager_arn         = var.secrets_manager_arn
      secrets_manager_access_role_arn = var.secrets_manager_access_role_arn
      tags                        = { EndpointType = "source" }
    }
  }  
  s3_endpoints = {
  "${local.name}-s3-target" = {
      endpoint_id             = "${local.name}-s3-target"
      endpoint_type           = "target"
      engine_name             = "s3"
      bucket_name             = var.S3TargetBucketName
      bucket_folder           = var.S3FolderName
      data_format             = "parquet"
      compression_type        = "GZIP"
      enable_statistics       = true
      parquet_timestamp_in_millisecond = true
      encryption_mode         = "SSE_S3"
      service_access_role_arn = var.DmsRoleArn
      tags = { EndpointType = "target" }
    }
  }
  
  replication_tasks = {
    postgresql_to_s3 = {
      replication_task_id       = "${local.name}"
      migration_type            = "full-load-and-cdc"
      replication_task_settings = file("${path.module}/configs/task_mappings.json")
      table_mappings            = file("${path.module}/configs/table_mappings.json")
      source_endpoint_key       = "source"
      target_endpoint_key       = "${local.name}-s3-target"
      tags                      = { Task = "MYSQL-to-S3" }
    }
  }

  tags = {
    name = "${local.name}"
    cc = "data"
    creator = "terraform"
    owner = "<EMAIL>"
    environment = "prod"
    service = "${local.name}"
    group = "employer"
    type = "dms"
  }
}
