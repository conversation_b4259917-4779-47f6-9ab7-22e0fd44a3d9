# variable "aws_region" {
#   type        = string
#   description = "The AWS region where resources will be created."
#   default     = "us-east-1"
# }

# variable "workgroup_name" {
#   type        = string
#   description = "The name of the Redshift Serverless workgroup."
#   default     = "dev"
# }

# variable "namespace_name" {
#   type        = string
#   description = "The name of the Redshift Serverless namespace."
#   default     = "dev"
# }

# variable "database_name" {
#   type        = string
#   description = "The name of the initial database in the Redshift cluster."
#   default     = "dev"
# }

# variable "base_capacity" {
#   type        = number
#   description = "The initial compute capacity of the Redshift cluster."
#   default     = 128
# }

# variable "subnet_ids" {
#   type        = list(string)
#   description = "List of subnet IDs where the Redshift cluster will be deployed."
#   default     = ["subnet-065091ffb2b3a8da5", "subnet-0ea88ccc82db45815", "subnet-006dd2f65b3fb5c8b"]
# }

# variable "security_group_ids" {
#   type        = list(string)
#   description = "List of security group IDs to associate with the Redshift cluster."
#   default     = ["sg-091fee45e67a7f559"]
# }


# variable "environment" { 
#   type        = string
#   description = "The environment (e.g., dev, prod) for tagging resources."
#   default     = "dev"
# }

# variable "secret_name" {
#   type        = string
#   description = "The name of the AWS Secrets Manager secret containing the Redshift credentials."
#   default     = "dev/redshift/serverless"
# }

# variable "iam_role_name" {
#   description = "The name of the IAM role"
#   type        = string
#   default     = "redshift-role-1" 
# }

# variable "endpoint_name" {
#   description = "The name of the Redshift Serverless endpoint."
#   type        = string
#   default     = "dev-endpoint"
#   validation {
#     condition     = length(var.endpoint_name) > 0 && length(var.endpoint_name) <= 30
#     error_message = "The endpoint_name must be between 1 and 30 characters."
#   }
# }

