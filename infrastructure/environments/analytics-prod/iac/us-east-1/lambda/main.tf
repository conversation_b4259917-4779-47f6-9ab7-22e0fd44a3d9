provider "aws" {
  region = var.aws_region
}

# Load schedules.json content as a local variable
locals {
  schedules = jsondecode(file("${path.module}/schedules.json"))
}

# Define lambda environment variables by parsing the loaded JSON
locals {
  lambda_environment_variables = {
    for lambda in local.schedules["lambdas"] :
    lambda.script => lambda.environment
  }

  # Map script names to function names for consistent referencing
  script_to_function_map = {
    "adsense_api_lambda.py"  = "adsense_api"
    "adwords_api_lambda.py"  = "adwords_api"
    "facebook_api_lambda.py"  = "facebook_api"
    "bing_api_lambda" = "bing_api"
  }
}

# Define Lambda configurations based on the parsed JSON
locals {
  lambda_configs = [
    {
      name            = "adsense_api"
      script_name     = "adsense_api_lambda.py"
      handler         = "lambda_function.lambda_handler"
      s3_key          = "lambda/adsense_api.zip"
      environment     = local.lambda_environment_variables["adsense_api_lambda.py"]
    },
    {
      name            = "adwords_api"
      script_name     = "adwords_api_lambda.py"
      handler         = "lambda_function.lambda_handler"
      s3_key          = "lambda/adwords_api.zip"
      environment     = local.lambda_environment_variables["adwords_api_lambda.py"]
    },
    {
      name            = "bing_api"
      script_name     = "bing_api_lambda.py"
      handler         = "lambda_function.lambda_handler"
      s3_key          = "lambda/adwords_api.zip"
      environment     = local.lambda_environment_variables["bing_api_lambda.py"]
    },
    {
      name            = "facebook_api"
      script_name     = "facebook_api_lambda.py"
      handler         = "lambda_function.lambda_handler"
      s3_key          = "lambda/facebook_api.zip"
      environment     = local.lambda_environment_variables["facebook_api_lambda.py"]
    }
  ]
}

# Create the IAM role for Lambda
resource "aws_iam_role" "lambda_iam_role" {
  name = "lambda-iam-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
      }
    ]
  })
}

# Attach required policies to the Lambda IAM role
resource "aws_iam_role_policy_attachment" "lambda_iam_role_policies" {
  for_each = toset([
    "arn:aws:iam::aws:policy/AmazonEventBridgeSchedulerFullAccess",
    "arn:aws:iam::aws:policy/AmazonKinesisFirehoseFullAccess",
    "arn:aws:iam::aws:policy/AmazonS3FullAccess",
    "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole",
    "arn:aws:iam::aws:policy/service-role/AWSLambdaRole",
    "arn:aws:iam::aws:policy/SecretsManagerReadWrite"
  ])

  role       = aws_iam_role.lambda_iam_role.name
  policy_arn = each.value
}

# Custom policy for secrets and decryption
resource "aws_iam_policy" "lambda_secret_policy" {
  name        = "lambda-secret-policy"
  description = "Policy for Lambda to access secrets and decrypt"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "VisualEditor0"
        Effect = "Allow"
        Action = [
          "secretsmanager:GetSecretValue",
          "kms:Decrypt"
        ]
        Resource = "*"
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "lambda_secret_policy_attachment" {
  role       = aws_iam_role.lambda_iam_role.name
  policy_arn = aws_iam_policy.lambda_secret_policy.arn
}

# Define Lambda functions
resource "aws_lambda_function" "lambda_functions" {
  for_each = { for config in local.lambda_configs : config.script_name => config }

  function_name = each.value.name
  s3_bucket     = var.s3_bucket
  s3_key        = each.value.s3_key
  handler       = each.value.handler
  runtime       = "python3.11"
  timeout       = 300 # Set timeout to 5 minutes
  role          = aws_iam_role.lambda_iam_role.arn

  environment {
    variables = each.value.environment
  }

  tags = var.tags
}

# Define EventBridge rule configurations based on schedules.json
locals {
  lambda_eventbridge_rules = [
    for lambda in local.schedules["lambdas"] : {
      script_name = lambda.script
      name        = local.script_to_function_map[lambda.script]
      cron        = lambda.schedules[0].cron
      enabled     = true
    }
    if (try(length(lambda.schedules), 0) > 0)
  ]
}



# Create EventBridge rules for each Lambda with a cron schedule
resource "aws_cloudwatch_event_rule" "lambda_scheduled_rule" {
  for_each = { for rule in local.lambda_eventbridge_rules : rule.script_name => rule }

  name                = "lambda-schedule-${each.value.name}"
  description         = "EventBridge rule to trigger ${each.value.name}"
  schedule_expression = each.value.cron
}

# Add permissions for EventBridge to invoke the Lambda
resource "aws_lambda_permission" "allow_eventbridge_invoke_lambda" {
  for_each = aws_cloudwatch_event_rule.lambda_scheduled_rule

  statement_id  = "AllowExecutionFromEventBridge"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.lambda_functions[each.key].function_name
  principal     = "events.amazonaws.com"
  source_arn    = each.value.arn
}

# Create an EventBridge target for each rule to invoke the corresponding Lambda function
resource "aws_cloudwatch_event_target" "lambda_scheduled_target" {
  for_each = aws_cloudwatch_event_rule.lambda_scheduled_rule

  rule      = each.value.name
  target_id = "lambda-target"
  arn       = aws_lambda_function.lambda_functions[each.key].arn
}







# Define the API Gateway REST API
resource "aws_api_gateway_rest_api" "bing_api" {
  name        = "bing_api_lambda"
  description = "API Gateway for Bing API Lambda Function"
}

# Define the API Gateway Resource
resource "aws_api_gateway_resource" "bing_api_resource" {
  rest_api_id = aws_api_gateway_rest_api.bing_api.id
  parent_id   = aws_api_gateway_rest_api.bing_api.root_resource_id
  path_part   = "bing_api_lambda"

}

# Define the API Gateway Method
resource "aws_api_gateway_method" "bing_api_method" {
  rest_api_id   = aws_api_gateway_rest_api.bing_api.id
  resource_id   = aws_api_gateway_resource.bing_api_resource.id
  http_method   = "POST"
  authorization = "NONE"
}

# Integrate API Gateway with the Lambda Function
resource "aws_api_gateway_integration" "bing_api_integration" {
  rest_api_id = aws_api_gateway_rest_api.bing_api.id
  resource_id = aws_api_gateway_resource.bing_api_resource.id
  http_method = aws_api_gateway_method.bing_api_method.http_method
  integration_http_method = "POST"
  type        = "AWS_PROXY"
  uri         = aws_lambda_function.lambda_functions["bing_api_lambda.py"].invoke_arn
}

# Grant API Gateway permission to invoke the Lambda
resource "aws_lambda_permission" "bing_api_permission" {
  statement_id  = "AllowExecutionFromApiGateway"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.lambda_functions["bing_api_lambda.py"].function_name
  principal     = "apigateway.amazonaws.com"
  source_arn    = "${aws_api_gateway_rest_api.bing_api.execution_arn}/*/*"
}

# Deploy the API Gateway
resource "aws_api_gateway_deployment" "bing_api_deployment" {
  depends_on = [aws_api_gateway_integration.bing_api_integration]
  rest_api_id = aws_api_gateway_rest_api.bing_api.id
}

# Output the API Gateway Endpoint
output "bing_api_url" {
  value = "${aws_api_gateway_deployment.bing_api_deployment.invoke_url}/bing"
}
