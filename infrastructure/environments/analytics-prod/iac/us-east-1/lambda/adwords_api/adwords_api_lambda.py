from datetime import timedelta, datetime
import requests
import time
import os
import json
import boto3  # Import Boto3
from botocore.exceptions import ClientError

# Retrieve environment variables for security
adword_client_id = os.environ['ADWORD_CLIENT_ID']
developer_token = os.environ['DEVELOPER_TOKEN']
login_customer_id = os.environ['LOGIN_CUSTOMER_ID']
firehose_stream_name = os.environ['FIREHOSE_STREAM_NAME']  # Firehose stream name
secret_name = os.environ['SECRET_NAME']

session = boto3.session.Session()
# Initialize Firehose client
firehose_client = session.client(service_name='firehose')
# Initialize secret_manager client
secret_client = session.client(service_name='secretsmanager')
try:
    get_secret_value_response = secret_client.get_secret_value(SecretId=secret_name)
except ClientError as e:
    raise e

SecretString = get_secret_value_response['SecretString']
record_dict = json.loads(SecretString)
adwords_secret = record_dict.get('adwords_secret')
adwords_token = record_dict.get('adwords_token')

response = requests.post(
    f'https://www.googleapis.com/oauth2/v4/token?client_secret={adwords_secret}&grant_type=refresh_token&refresh_token={adwords_token}&client_id={adword_client_id}'
)
access_token = response.json()['access_token']

customer_ids = {
    "talent.com[world]": "1524320303",
    "talent.com[fr]": "9198421539",
    "talent.com[b2b]": "5442572084",
    "talent.com[be]": "4624659264",
    "talent.com[ca]": "3536523970",
    "talent.com[ch]": "3159902527",
    "talent.com[de]": "2621818800",
    "talent.com[it]": "3156211458",
    "talent.com[nl]": "9463059748",
    "talent.com[uk]": "8524676129",
    "talent.com[us]": "5017176041"
}

def get_detail_data(client_customer_id, date_start=None, date_end=None):
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json",
        "developer-token": developer_token,
        "Accept": "application/json",
        "Host": "googleads.googleapis.com",
        "User-Agent": "curl",
        "login-customer-id": login_customer_id
    }

    # If date_start or date_end are not specified, set them to yesterday's date
    if date_start is None or date_end is None:
        yesterday = (datetime.now() - timedelta(1)).strftime('%Y-%m-%d')
        date_start = date_start or yesterday
        date_end = date_end or yesterday

    # Create a list of dates if date_start and date_end are not equal to yesterday
    date_start_dt = datetime.strptime(date_start, '%Y-%m-%d').date()
    date_end_dt = datetime.strptime(date_end, '%Y-%m-%d').date()

    # Generate a list of dates between date_start and date_end
    if date_start_dt != (datetime.now().date() - timedelta(1)) or date_end_dt != (datetime.now().date() - timedelta(1)):
        date_range = []
        current_date = date_start_dt
        while current_date <= date_end_dt:
            date_range.append(current_date.strftime('%Y-%m-%d'))
            current_date += timedelta(days=1)
    else:
        date_range = [date_start]

    output = []
    for single_date in date_range:
        parameters = {
            "query": f"""SELECT campaign.id, campaign.name, segments.date, metrics.cost_micros, metrics.clicks,
                            metrics.impressions, customer.currency_code
                        FROM campaign
                        WHERE campaign.status IN (ENABLED, PAUSED, REMOVED)
                        AND segments.date = '{single_date}'"""  # Query for a single day
        }
        parameters = json.dumps(parameters)
        url = f"https://googleads.googleapis.com/v17/customers/{client_customer_id}/googleAds:searchStream"
        response = requests.post(url, data=parameters, headers=headers)
        response = response.json()
        if (len(response) > 0 and 'results' not in response[0]) or len(response) == 0:
            print(f"No valid data for customer ID {client_customer_id} on date {single_date}")
            continue  # Skip to the next date if no valid data
        for v in response[0]['results']:
            explode_name_data = v['campaign']['name'].split('-')
            country = explode_name_data[1]
            if len(country) > 2 or country == '':
                continue
            campaign_id = v['campaign']['id']
            output.append({
                'source': 'adwords',
                'date': v['segments']['date'],
                'company_context': explode_name_data[0],
                'country': country,
                'campaign_id': campaign_id,
                'cost_local': int(v['metrics']['costMicros']) / 1_000_000,
                'clicks': int(v['metrics']['clicks']),
                'impressions': int(v['metrics']['impressions']),
                'campaign_name': v['campaign']['name'],
                'currency_code': v['customer']['currencyCode'],
                'event_type': 'adwords',
                'unixtime': int(time.mktime(datetime.strptime(v['segments']['date'], '%Y-%m-%d').timetuple()))
            })
    return output

# Lambda Handler
def lambda_handler(event, context):
    array_total = []
    # Loop through each customer ID
    for name, customer_id in customer_ids.items():
        try:
            date_start = event.get('date_start', None)
            date_end = event.get('date_end', None)
            res = get_detail_data(customer_id, date_start, date_end)
            if res:
                array_total.extend(res)
        except Exception as e:
            print(f"No valid data for customer ID {customer_id}: {e}")
    
    # Prepare to send records in batches
    if array_total:
        firehose_records = []
        for record in array_total:
            # Prepare each record for Firehose
            firehose_records.append({
                'Data': json.dumps(record) + '\n'  # Append newline for proper parsing
            })
        # Send output to AWS Firehose in batches of 500
        for i in range(0, len(firehose_records), 500):
            batch = firehose_records[i:i + 500]  # Get the current batch of 500 records
            response = firehose_client.put_record_batch(
                DeliveryStreamName=firehose_stream_name,
                Records=batch
            )

    return {
        'statusCode': 200,
        'body': json.dumps('Data sent to Firehose successfully!')
    }
