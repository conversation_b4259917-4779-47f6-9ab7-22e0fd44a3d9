{"stream_name": "firehose-stream-user-event", "destination": "extended_s3", "bucket_arn": "arn:aws:s3:::talent-prod-datalake", "buffering_interval": 60, "buffering_size": 64, "cloudwatch_log_group": "/aws/kinesisfirehose/firehose-stream-user-event", "cloudwatch_log_stream": "DestinationDelivery", "compression_format": "UNCOMPRESSED", "data_format_conversion_enabled": true, "input_format_configuration": {"deserializer": {"open_x_json_ser_de": {"case_insensitive": true, "column_to_json_key_mappings": {}, "convert_dots_in_json_keys_to_underscores": false}}}, "output_format_configuration": {"serializer": {"parquet_ser_de": {"block_size_bytes": 268435456, "compression": "SNAPPY", "enable_dictionary_compression": false, "max_padding_bytes": 0, "page_size_bytes": 1048576, "writer_version": "V1"}}}, "schema_configuration": {"catalog_id": "", "database_name": "datalake", "region": "us-east-1", "role_arn": "${role_arn}", "table_name": "user_event", "version_id": "LATEST"}, "dynamic_partitioning_enabled": true, "retry_duration": 300, "error_output_prefix": "event/user_error/", "role_arn": "${role_arn}", "prefix": "event/user/event_type=!{partitionKeyFromQuery:event_type}/year=!{partitionKeyFromQuery:year}/month=!{partitionKeyFromQuery:month}/day=!{partitionKeyFromQuery:day}/hour=!{partitionKeyFromQuery:hour}/", "processing_configuration": {"enabled": true, "processors": [{"type": "MetadataExtraction", "parameters": [{"parameter_name": "JsonParsingEngine", "parameter_value": "JQ-1.6"}, {"parameter_name": "MetadataExtractionQuery", "parameter_value": "{event_type:.event_type,year:.unixtime| strftime(\"%Y\"),month:.unixtime| strftime(\"%m\"),day:.unixtime| strftime(\"%d\"),hour:.unixtime| strftime(\"%H\")}"}]}]}, "s3_backup_mode": "Disabled", "s3_backup_configuration": []}