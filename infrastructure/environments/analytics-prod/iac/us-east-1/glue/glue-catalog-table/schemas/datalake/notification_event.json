{"location": "s3://talent-prod-datalake/event/notification/", "columns": [{"name": "id", "type": "string"}, {"name": "email_id", "type": "string"}, {"name": "date", "type": "string"}, {"name": "country", "type": "string"}, {"name": "language", "type": "string"}, {"name": "template_id", "type": "string"}, {"name": "group_name", "type": "string"}, {"name": "campaign_name", "type": "string"}, {"name": "test_version", "type": "string"}, {"name": "sender", "type": "string"}, {"name": "server_ip", "type": "string"}, {"name": "sunset_group", "type": "string"}, {"name": "search_id", "type": "string"}, {"name": "initiator_category", "type": "string"}, {"name": "mrt_code", "type": "double"}, {"name": "unixtime", "type": "timestamp"}, {"name": "job_card_tag_ids", "type": "array<string>"}, {"name": "statsig_test_id", "type": "string"}, {"name": "user_id", "type": "string"}, {"name": "ip_country", "type": "string"}, {"name": "user_agent", "type": "string"}, {"name": "device", "type": "string"}, {"name": "bounce_type", "type": "string"}, {"name": "bounce_status_code", "type": "string"}, {"name": "error_type", "type": "string"}, {"name": "error_code", "type": "string"}, {"name": "unsubscribe_type", "type": "string"}, {"name": "click_type", "type": "string"}, {"name": "is_mobile", "type": "smallint"}, {"name": "dns", "type": "string"}, {"name": "to_domain", "type": "string"}, {"name": "isp_group", "type": "string"}, {"name": "hostname", "type": "string"}, {"name": "reason_text", "type": "string"}, {"name": "deferred_type", "type": "string"}, {"name": "deferred_text", "type": "string"}], "partitionKeys": [{"name": "event_type", "type": "string"}, {"name": "year", "type": "int"}, {"name": "month", "type": "int"}, {"name": "day", "type": "int"}, {"name": "hour", "type": "int"}]}