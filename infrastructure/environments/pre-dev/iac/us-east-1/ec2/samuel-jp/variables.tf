variable "USER" {
  type        = string
  description = "Name of the user who wants to apply"
  default     = "samuel"
}

variable "ENVIRONMENT" {
  type        = string
  description = "Environment where to deploy"
  default     = "pre-dev"
}

variable "SERVICE_NAME" {
  type        = string
  description = "Service name to be used to idenfiy the docker images and services"
  default     = "employers-job-processing"
}

variable "BRANCH" {
  type        = string
  description = "The branch name where the repository will be pointing"
  default     = "dev"
}