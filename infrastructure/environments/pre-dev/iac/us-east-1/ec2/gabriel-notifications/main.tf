data "aws_route53_zone" "this" {
  name = "talent.pre"
  private_zone = true
}

resource "aws_instance" "this" {
  ami                    = "ami-0afe8671a393f91b3"
  instance_type          = "t3a.2xlarge"
  subnet_id              = "subnet-002c669eb615a1bee"
  vpc_security_group_ids = [ "sg-091fee45e67a7f559" ]
  iam_instance_profile   = "Pre-devInstanceRole"

  root_block_device {
    volume_size = 100
    encrypted = false
    volume_type = "gp3"
  }

  tags = {
    Name = "ec2-pre-dev-${var.SERVICE_NAME}-${var.USER}"
    environment = var.ENVIRONMENT
    type = "ec2"
    service = var.SERVICE_NAME
    owner = "<EMAIL>"
    service_type = "environment"
    group = "pre-dev"
    cc = "job_seeker"
    test = "true"
    terraform = "true"
  }

  user_data = templatefile("../scripts/init.sh", {
    SERVICE_NAME = var.SERVICE_NAME,
    USER         = var.USER,
    ENVIRONMENT  = var.ENVIRONMENT,
    BRANCH       = var.BRANCH
  })
}

resource "aws_route53_record" "this" {
  name    = "${var.USER}-${var.SERVICE_NAME}"
  type    = "A"
  ttl     = "30"
  records = [ aws_instance.this.private_ip ]
  zone_id = data.aws_route53_zone.this.zone_id
}
