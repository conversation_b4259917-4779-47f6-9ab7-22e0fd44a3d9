.analytics-dev-job:
  image: $AWS_ACCOUNT_ID.dkr.ecr.us-east-1.amazonaws.com/gitlab-runner:v1
  
  before_script:
    - eval $(aws sts assume-role --role-arn "arn:aws:iam::************:role/service-gitlab-runner-********************" --role-session-name G<PERSON><PERSON>ab<PERSON> --query 'Credentials.[AccessKeyId,SecretAccessKey,SessionToken]' --output text | awk '{ print "export AWS_ACCESS_KEY_ID="$1"\nexport AWS_SECRET_ACCESS_KEY="$2"\nexport AWS_SESSION_TOKEN="$3 }')

include:
  - local: '/infrastructure/environments/analytics-dev/common-template.yml'
  - local: '/infrastructure/environments/analytics-dev/deploy-template.yml'
