variable "S3TargetBucketName" {
  type        = string
  description = "The S3 bucket for our DMS target endpoints"
  default     = "talent-dev-datalake"
}

variable "S3FolderName" {
  type        = string
  description = "The S3 folder name"
  default     = "dms"
}

variable "DmsRoleArn" {
  type        = string
  description = "The IAM role for DMS services to use."
  default     = "arn:aws:iam::730335359603:role/dms_vpc_role"
}

variable "vpc_subnet_ids" {
  description = "A list of VPC Subnet IDs"
  type        = list(string)
  default     = ["subnet-09ed67eb6b8c5b0e5","subnet-071488abc21ab321d"]
}

variable "security_groups" {
  description = "Security groups for the replication instance"
  type        = list(string)
  default     = ["sg-01d4e13dd2c8df189"]
}

variable "db_name" {
  description = "Database Name"
  type        = string
  default     = "employers"
}


variable "secrets_manager_arn" {
  description = "ARN of the Secrets Manager secret for the database credentials"
  type        = string
  default     = "arn:aws:secretsmanager:us-east-1:730335359603:secret:rds-db-credentials/cluster/talent-employer-db-GgPiO9"
}

variable "replication_instance_arn_dms" {
  description = "ARN of the provisioned DMS instance"
  type        = string
  default     = "arn:aws:dms:us-east-1:730335359603:rep:dms-datalake-dev-instance"
}


variable "existing_dms_cloudwatch_logs_role_arn" {
  type        = string
  description = "The IAM role for DMS services to use."
  default     = "arn:aws:iam::730335359603:role/dms_vpc_role"
}


variable "existing_dms_access_for_endpoint_role_arn" {
  type        = string
  description = "The IAM role for DMS services to use."
  default     = "arn:aws:iam::730335359603:role/dms_vpc_role"
}

variable "secrets_manager_access_role_arn" {
  type        = string
  description = "The IAM role for DMS services to use."
  default     = "arn:aws:iam::730335359603:role/dms_vpc_role"
}
