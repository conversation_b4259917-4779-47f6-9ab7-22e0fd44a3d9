
# main.tf
provider "aws" {
  region = "us-east-1"
}

# Map tables to their databases
locals {
  table_database_map = {
    "user_event": "datalake",
    "employers_event": "datalake",
    "search_event": "datalake",
    "publisher_event": "datalake",
    "notification_event": "datalake",
    "monetization_event": "datalake",
    "jobs_event": "datalake",
    "jobs_event_migration": "datalake",
    "jobredirect_event": "datalake",
    "jobs_ingestion_logs": "datalake",
    "sem_api": "datalake"
  }

  table_configs = {
    for file in fileset("./schemas/datalake/", "**/*.json") :
    file => jsondecode(file("${path.module}/schemas/datalake/${file}"))
  }
  
  partition_keys = {
    for file, config in local.table_configs : file => {
      for idx, pk in try(config["partitionKeys"], []) : idx => {
        name = pk.name
        type = pk.type
        # comment = try(pk.comment, null)
      }
    }
  }
}


# Create tables in each database
module "tables" {
  for_each = fileset("./schemas/datalake/", "**/*.json")
  source = "../../../../../../terraform/modules/official/terraform-aws-glue/glue-catalog-table"

  catalog_table_name = replace(basename(each.key), ".json", "")

  storage_descriptor = {
    location = jsondecode(file("${path.module}/schemas/datalake/${each.key}"))["location"]
    input_format = "org.apache.hadoop.hive.ql.io.parquet.MapredParquetInputFormat"
    output_format = "org.apache.hadoop.hive.ql.io.parquet.MapredParquetOutputFormat"
    columns = jsondecode(file("${path.module}/schemas/datalake/${each.key}"))["columns"]

    
    ser_de_info = {
      name                  = "datalake"
      serialization_library = "org.apache.hadoop.hive.ql.io.parquet.serde.ParquetHiveSerDe"
      parameters = {
        "serialization.format" = 1
      }

    }
  }
  
  partition_keys = local.partition_keys[each.key]

  database_name = local.table_database_map[replace(basename(each.key), ".json", "")]
  parameters = {
    "classification" = "parquet",
  }
}
