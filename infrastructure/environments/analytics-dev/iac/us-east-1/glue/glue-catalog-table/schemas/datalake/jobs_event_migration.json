{"location": "s3://talent-dev-datalake/event/jobs_migration/", "columns": [{"name": "id", "type": "string"}, {"name": "unixtime", "type": "timestamp"}, {"name": "system_legacy_id", "type": "string"}, {"name": "system_feedcode", "type": "string"}, {"name": "system_scanid", "type": "int"}, {"name": "system_crawlgroup", "type": "int"}, {"name": "system_status", "type": "int"}, {"name": "system_date_found", "type": "string"}, {"name": "system_jobhash", "type": "string"}, {"name": "system_hash_source_unique_job", "type": "string"}, {"name": "system_hash_jobdesc_company", "type": "string"}, {"name": "system_hash_title_company", "type": "string"}, {"name": "system_hash_jobdesc_title", "type": "string"}, {"name": "system_date_updated", "type": "string"}, {"name": "system_date_expired", "type": "string"}, {"name": "system_date_re_found", "type": "string"}, {"name": "system_updates_today", "type": "int"}, {"name": "system_job_source", "type": "string"}, {"name": "system_apply_type", "type": "string"}, {"name": "system_feed_type", "type": "string"}, {"name": "system_company_name", "type": "string"}, {"name": "system_flag_postprocess", "type": "int"}, {"name": "system_is_exclusive", "type": "int"}, {"name": "system_show_salary_on_front", "type": "int"}, {"name": "source_title", "type": "string"}, {"name": "source_location", "type": "string"}, {"name": "source_location_raw", "type": "string"}, {"name": "source_jobtype", "type": "string"}, {"name": "source_company_name", "type": "string"}, {"name": "source_link", "type": "string"}, {"name": "source_jobdesc_text", "type": "string"}, {"name": "source_date_posted", "type": "string"}, {"name": "source_date_expired", "type": "string"}, {"name": "source_apply_email", "type": "string"}, {"name": "source_reqid", "type": "string"}, {"name": "source_city", "type": "string"}, {"name": "source_state", "type": "string"}, {"name": "source_country", "type": "string"}, {"name": "source_job_pixel", "type": "string"}, {"name": "source_job_temp", "type": "string"}, {"name": "source_ppc", "type": "int"}, {"name": "source_cpa", "type": "double"}, {"name": "source_job_budget", "type": "double"}, {"name": "source_salary", "type": "string"}, {"name": "source_benefits", "type": "string"}, {"name": "source_experience_required", "type": "string"}, {"name": "source_logo", "type": "string"}, {"name": "source_remote", "type": "string"}, {"name": "source_apply_api_endpoint", "type": "string"}, {"name": "source_apply_api_labels", "type": "string"}, {"name": "source_apply_client_jobid", "type": "string"}, {"name": "source_client_tag", "type": "string"}, {"name": "source_client_jobid", "type": "string"}, {"name": "source_apply_extra_info", "type": "string"}, {"name": "source_vendor_campaign", "type": "string"}, {"name": "enrich_date_posted", "type": "string"}, {"name": "enrich_date_expired", "type": "string"}, {"name": "enrich_geo_city", "type": "string"}, {"name": "enrich_geo_country", "type": "string"}, {"name": "enrich_geo_region1", "type": "string"}, {"name": "enrich_geo_region2", "type": "string"}, {"name": "enrich_geo_remote", "type": "string"}, {"name": "enrich_geo_lat", "type": "double"}, {"name": "enrich_geo_lon", "type": "double"}, {"name": "enrich_geo_location_searched", "type": "string"}, {"name": "enrich_jobdesc_length", "type": "int"}, {"name": "enrich_language", "type": "string"}, {"name": "enrich_soc_onetsoc_code", "type": "string"}, {"name": "enrich_soc_occupation_title", "type": "string"}, {"name": "enrich_soc_matched_title", "type": "string"}, {"name": "enrich_soc_top_title_score", "type": "string"}, {"name": "enrich_soc_top_desc_score", "type": "string"}, {"name": "enrich_soc_major_group", "type": "string"}, {"name": "enrich_soc_minor_group", "type": "string"}, {"name": "enrich_soc_broad_group", "type": "string"}, {"name": "enrich_soc_detailed_group", "type": "string"}, {"name": "enrich_soc_job_zone", "type": "string"}, {"name": "enrich_soc_classifier", "type": "string"}, {"name": "enrich_soc_detected_language", "type": "string"}, {"name": "enrich_soc_card_id", "type": "string"}, {"name": "enrich_soc_local_classification_code", "type": "string"}, {"name": "enrich_soc_local_classification_type", "type": "string"}, {"name": "enrich_jobtype", "type": "string"}, {"name": "enrich_jobtype_version", "type": "string"}, {"name": "enrich_is_general_labor", "type": "int"}, {"name": "enrich_general_labor_version", "type": "string"}, {"name": "enrich_company_name_normalized", "type": "string"}, {"name": "enrich_company_id", "type": "string"}, {"name": "enrich_company_industry", "type": "string"}, {"name": "enrich_company_name", "type": "string"}, {"name": "enrich_experience_required_snippet", "type": "string"}, {"name": "enrich_ai_experience_required_min", "type": "string"}, {"name": "enrich_ai_experience_required_max", "type": "string"}, {"name": "enrich_ai_benefits", "type": "string"}, {"name": "enrich_ai_benefits_normalized", "type": "string"}, {"name": "enrich_ai_qualifications_normalized", "type": "string"}, {"name": "enrich_ai_requirements", "type": "string"}, {"name": "enrich_ai_schedules", "type": "string"}, {"name": "enrich_ai_skills", "type": "string"}, {"name": "enrich_ai_seniority", "type": "string"}, {"name": "enrich_education_required", "type": "string"}, {"name": "enrich_education_required_normalized", "type": "string"}, {"name": "enrich_education_required_snippet", "type": "string"}, {"name": "enrich_salary_min", "type": "double"}, {"name": "enrich_salary_max", "type": "double"}, {"name": "enrich_salary_avg", "type": "double"}, {"name": "enrich_salary_snippet", "type": "string"}, {"name": "enrich_salary_yearly", "type": "double"}, {"name": "enrich_salary_monthly", "type": "double"}, {"name": "enrich_salary_match", "type": "string"}, {"name": "enrich_salary_type", "type": "string"}, {"name": "enrich_salary_currency", "type": "string"}, {"name": "enrich_salary_version", "type": "string"}, {"name": "enrich_salary_valid", "type": "boolean"}, {"name": "enrich_title_occupation1", "type": "string"}, {"name": "enrich_title_occupation1_text", "type": "string"}, {"name": "enrich_title_occupation2", "type": "string"}, {"name": "enrich_title_occupation2_text", "type": "string"}, {"name": "enrich_title_occupation_version", "type": "string"}], "partitionKeys": [{"name": "event_type", "type": "string"}, {"name": "year", "type": "int"}, {"name": "month", "type": "int"}, {"name": "day", "type": "int"}, {"name": "hour", "type": "int"}]}