{"location": "s3://talent-dev-datalake/event/employers/", "columns": [{"name": "ac_profit_protection", "type": "smallint"}, {"name": "account_currency", "type": "string"}, {"name": "account_id", "type": "int"}, {"name": "account_name", "type": "string"}, {"name": "account_type", "type": "string"}, {"name": "active", "type": "smallint"}, {"name": "address", "type": "string"}, {"name": "agency_tag", "type": "string"}, {"name": "amount", "type": "int"}, {"name": "amount_cad", "type": "double"}, {"name": "api_client_name", "type": "string"}, {"name": "apply_step", "type": "string"}, {"name": "apply_type", "type": "string"}, {"name": "assigned_by", "type": "string"}, {"name": "associations_id", "type": "string"}, {"name": "ats_applicant_tracking", "type": "string"}, {"name": "auto_campaign_api", "type": "string"}, {"name": "auto_campaign_api_tag_name", "type": "string"}, {"name": "auto_campaign_api_token", "type": "string"}, {"name": "billed_client_country", "type": "string"}, {"name": "billed_client_industry", "type": "string"}, {"name": "billed_client_name", "type": "string"}, {"name": "billed_client_parent_id", "type": "string"}, {"name": "billed_client_parent_name", "type": "string"}, {"name": "billed_client_state", "type": "string"}, {"name": "billed_client_type", "type": "string"}, {"name": "billing_period", "type": "date"}, {"name": "budget_holder_country", "type": "string"}, {"name": "budget_holder_industry", "type": "string"}, {"name": "budget_holder_name", "type": "string"}, {"name": "budget_holder_parent_id", "type": "string"}, {"name": "budget_holder_parent_name", "type": "string"}, {"name": "budget_holder_state", "type": "string"}, {"name": "budget_holder_type", "type": "string"}, {"name": "campaign_id", "type": "int"}, {"name": "campaign_name", "type": "string"}, {"name": "campaign_objective", "type": "string"}, {"name": "category", "type": "string"}, {"name": "cct_modifier", "type": "string"}, {"name": "cem", "type": "smallint"}, {"name": "checked_by_cs", "type": "smallint"}, {"name": "cid_name", "type": "string"}, {"name": "city", "type": "string"}, {"name": "click_last_month", "type": "int"}, {"name": "client_campaign_id", "type": "string"}, {"name": "client_job_id_name", "type": "string"}, {"name": "client_paid", "type": "smallint"}, {"name": "client_sub_id", "type": "string"}, {"name": "client_sub_id_decryption", "type": "string"}, {"name": "close_date", "type": "date"}, {"name": "company_label", "type": "string"}, {"name": "company_type", "type": "string"}, {"name": "company_website", "type": "string"}, {"name": "contact_email", "type": "string"}, {"name": "contact_hsid", "type": "string"}, {"name": "contact_name", "type": "string"}, {"name": "conversion_data", "type": "double"}, {"name": "conversion_target_cost", "type": "double"}, {"name": "conversion_type", "type": "string"}, {"name": "country", "type": "string"}, {"name": "cpa_target", "type": "double"}, {"name": "cpc_column", "type": "smallint"}, {"name": "cpc_modifier", "type": "double"}, {"name": "created", "type": "timestamp"}, {"name": "created_by", "type": "string"}, {"name": "cs_last_checked", "type": "timestamp"}, {"name": "currency", "type": "string"}, {"name": "customer_so_email", "type": "string"}, {"name": "customer_success_id", "type": "string"}, {"name": "customer_success_owner", "type": "string"}, {"name": "date_created", "type": "timestamp"}, {"name": "date_end", "type": "date"}, {"name": "date_start", "type": "date"}, {"name": "deal_currency_code", "type": "string"}, {"name": "deal_id", "type": "string"}, {"name": "deal_name", "type": "string"}, {"name": "deal_owner", "type": "string"}, {"name": "deal_owner_email", "type": "string"}, {"name": "deal_owner_id", "type": "string"}, {"name": "deal_stage", "type": "string"}, {"name": "device", "type": "string"}, {"name": "discount", "type": "double"}, {"name": "discount_end_date", "type": "date"}, {"name": "discount_start_date", "type": "date"}, {"name": "ecpa_status", "type": "smallint"}, {"name": "employer_data_apply_delivery", "type": "string"}, {"name": "employer_data_apply_email", "type": "string"}, {"name": "employer_data_apply_extra_questions", "type": "string"}, {"name": "employer_data_apply_standard_questions", "type": "string"}, {"name": "employer_data_apply_type", "type": "string"}, {"name": "employer_data_campaign_id", "type": "int"}, {"name": "employer_data_client_tag", "type": "string"}, {"name": "employer_data_execution_time", "type": "int"}, {"name": "employer_data_is_fast_apply", "type": "smallint"}, {"name": "employer_data_job_source", "type": "string"}, {"name": "employer_data_ppc", "type": "double"}, {"name": "employer_data_ppcu", "type": "double"}, {"name": "empty_properties", "type": "string"}, {"name": "end_of_month_exposure", "type": "smallint"}, {"name": "entity_change_date", "type": "date"}, {"name": "error_message", "type": "string"}, {"name": "exposure_multiplier", "type": "smallint"}, {"name": "external_conversion_data", "type": "smallint"}, {"name": "feedcode", "type": "string"}, {"name": "force_redirection", "type": "string"}, {"name": "front_loaded_status", "type": "smallint"}, {"name": "group", "type": "string"}, {"name": "host", "type": "string"}, {"name": "id", "type": "string"}, {"name": "invoice_option", "type": "string"}, {"name": "ip", "type": "string"}, {"name": "ip_mask", "type": "string"}, {"name": "is_fully_auto_campaign", "type": "smallint"}, {"name": "jobredirect_id", "type": "string"}, {"name": "language", "type": "string"}, {"name": "last_click", "type": "timestamp"}, {"name": "last_date_start", "type": "date"}, {"name": "last_interaction_date", "type": "timestamp"}, {"name": "last_values", "type": "string"}, {"name": "lead_type", "type": "string"}, {"name": "mapped", "type": "smallint"}, {"name": "merge_deal_id", "type": "string"}, {"name": "name", "type": "string"}, {"name": "next_discount_apply", "type": "double"}, {"name": "note", "type": "string"}, {"name": "nuuid", "type": "string"}, {"name": "old_qbo_id", "type": "string"}, {"name": "old_talent_company", "type": "string"}, {"name": "old_yaypay_link", "type": "string"}, {"name": "old_yaypay_link_expires_at", "type": "date"}, {"name": "open_budget", "type": "smallint"}, {"name": "panda_doc_status", "type": "string"}, {"name": "paused", "type": "smallint"}, {"name": "payment_required", "type": "smallint"}, {"name": "payment_term", "type": "string"}, {"name": "percentage", "type": "double"}, {"name": "performance_overview", "type": "string"}, {"name": "phone_number", "type": "string"}, {"name": "pipeline", "type": "string"}, {"name": "po_end_date", "type": "date"}, {"name": "po_number1", "type": "string"}, {"name": "po_number2", "type": "string"}, {"name": "po_number3", "type": "string"}, {"name": "po_start_date", "type": "date"}, {"name": "postal_code", "type": "string"}, {"name": "ppc", "type": "double"}, {"name": "ppc_url_format_value", "type": "string"}, {"name": "ppc_url_parameter", "type": "string"}, {"name": "primary_contact", "type": "string"}, {"name": "primary_contact_email", "type": "string"}, {"name": "priority", "type": "string"}, {"name": "qbo_id", "type": "string"}, {"name": "quickbook_invoice_doc_number", "type": "string"}, {"name": "reason", "type": "string"}, {"name": "referer", "type": "string"}, {"name": "region", "type": "string"}, {"name": "reliable_conversion", "type": "smallint"}, {"name": "removed", "type": "smallint"}, {"name": "removed_by", "type": "string"}, {"name": "requires_qa", "type": "smallint"}, {"name": "rolling_average", "type": "double"}, {"name": "rolling_cpa", "type": "double"}, {"name": "scheme", "type": "string"}, {"name": "secondary_deal_id", "type": "string"}, {"name": "secondary_deal_owner", "type": "string"}, {"name": "secondary_do_email", "type": "string"}, {"name": "segment", "type": "string"}, {"name": "send_budget_email", "type": "smallint"}, {"name": "server_ip", "type": "string"}, {"name": "sftp_site_id", "type": "string"}, {"name": "show_clicks", "type": "smallint"}, {"name": "smart_bidding", "type": "smallint"}, {"name": "source", "type": "string"}, {"name": "source_ppc", "type": "smallint"}, {"name": "spent_last_month", "type": "double"}, {"name": "spike_manager", "type": "smallint"}, {"name": "spike_manager_creator_email", "type": "smallint"}, {"name": "spike_manager_status", "type": "smallint"}, {"name": "sponsored", "type": "smallint"}, {"name": "status", "type": "string"}, {"name": "status_24h_commitment", "type": "smallint"}, {"name": "step", "type": "string"}, {"name": "subid_parameter", "type": "string"}, {"name": "talent_company", "type": "string"}, {"name": "tax_id", "type": "string"}, {"name": "timeframe", "type": "string"}, {"name": "total_jobs_processed", "type": "int"}, {"name": "tracker", "type": "string"}, {"name": "tracker_type", "type": "string"}, {"name": "type", "type": "string"}, {"name": "type_id", "type": "string"}, {"name": "ucid", "type": "string"}, {"name": "unixtime", "type": "timestamp"}, {"name": "updated", "type": "timestamp"}, {"name": "updated_by", "type": "string"}, {"name": "uri", "type": "string"}, {"name": "useragent", "type": "string"}, {"name": "valid_deal", "type": "smallint"}, {"name": "validated", "type": "smallint"}, {"name": "value_id", "type": "string"}, {"name": "vip_account", "type": "string"}, {"name": "webhook_tag_id", "type": "string"}, {"name": "webhook_url", "type": "string"}, {"name": "whoami", "type": "string"}, {"name": "yaypay_link", "type": "string"}, {"name": "yaypay_link_expires_at", "type": "date"}, {"name": "client_job_id", "type": "string"}, {"name": "hash_id", "type": "string"}, {"name": "ip_address", "type": "string"}, {"name": "ip_hash", "type": "string"}], "partitionKeys": [{"name": "event_type", "type": "string"}, {"name": "year", "type": "int"}, {"name": "month", "type": "int"}, {"name": "day", "type": "int"}, {"name": "hour", "type": "int"}]}