{"location": "s3://talent-dev-datalake/event/jobredirect/", "columns": [{"name": "id", "type": "string"}, {"name": "answer_type", "type": "string"}, {"name": "tracker", "type": "string"}, {"name": "ip_address", "type": "string"}, {"name": "ip_hash", "type": "string"}, {"name": "unixtime", "type": "timestamp"}, {"name": "original_log_time", "type": "timestamp"}, {"name": "nuuid", "type": "string"}, {"name": "job_id", "type": "string"}, {"name": "email_id", "type": "string"}, {"name": "cache_email_id", "type": "string"}, {"name": "search_id", "type": "string"}, {"name": "cache_search_id", "type": "string"}, {"name": "campaign_id", "type": "int"}, {"name": "account_id", "type": "int"}, {"name": "user_id", "type": "string"}, {"name": "ucid", "type": "string"}, {"name": "uri", "type": "string"}, {"name": "referer", "type": "string"}, {"name": "cache_referer", "type": "string"}, {"name": "host", "type": "string"}, {"name": "ip_mask", "type": "string"}, {"name": "ip_country_code", "type": "string"}, {"name": "ip_region", "type": "string"}, {"name": "ip_city", "type": "string"}, {"name": "useragent", "type": "string"}, {"name": "device", "type": "string"}, {"name": "cf_bot_score", "type": "int"}, {"name": "source", "type": "string"}, {"name": "cache_source", "type": "string"}, {"name": "utm_product", "type": "string"}, {"name": "cache_utm_product", "type": "string"}, {"name": "utm_medium", "type": "string"}, {"name": "cache_utm_medium", "type": "string"}, {"name": "utm_campaign", "type": "string"}, {"name": "cache_utm_campaign", "type": "string"}, {"name": "context", "type": "string"}, {"name": "cache_context", "type": "string"}, {"name": "utm_id", "type": "string"}, {"name": "cache_utm_id", "type": "string"}, {"name": "utm_term", "type": "string"}, {"name": "acquisition_sub_id", "type": "string"}, {"name": "cache_acquisition_sub_id", "type": "string"}, {"name": "publisher_account_id", "type": "int"}, {"name": "cache_publisher_account_id", "type": "int"}, {"name": "publisher_product_id", "type": "int"}, {"name": "cache_publisher_product_id", "type": "int"}, {"name": "channel_1", "type": "string"}, {"name": "cache_channel_1", "type": "string"}, {"name": "channel_2", "type": "string"}, {"name": "cache_channel_2", "type": "string"}, {"name": "channel_3", "type": "string"}, {"name": "cache_channel_3", "type": "string"}, {"name": "puid", "type": "string"}, {"name": "exposure", "type": "int"}, {"name": "job_ppc", "type": "int"}, {"name": "job_ppc_u", "type": "int"}, {"name": "ppc_modifier", "type": "double"}, {"name": "pre_ecpc_ppc", "type": "int"}, {"name": "pre_ecpc_ppc_u", "type": "int"}, {"name": "billed_ppc", "type": "int"}, {"name": "billed_ppc_cad", "type": "int"}, {"name": "net_billed_ppc", "type": "int"}, {"name": "net_billed_ppc_cad", "type": "int"}, {"name": "billed_warning", "type": "string"}, {"name": "billed_warning_group", "type": "string"}, {"name": "billed_discount_rate", "type": "int"}, {"name": "account_budget", "type": "int"}, {"name": "campaign_budget", "type": "int"}, {"name": "campaign_budget_type", "type": "string"}, {"name": "billed_is_valid", "type": "smallint"}, {"name": "billed_currency", "type": "string"}, {"name": "charged_warning", "type": "string"}, {"name": "charged_ppc", "type": "double"}, {"name": "charged_ppc_cad", "type": "double"}, {"name": "charged_is_valid", "type": "smallint"}, {"name": "charged_currency", "type": "string"}, {"name": "bid_share", "type": "int"}, {"name": "campaign_monthly_spent", "type": "double"}, {"name": "campaign_flexible_spent", "type": "int"}, {"name": "campaign_spent_today", "type": "double"}, {"name": "account_spent_budget", "type": "double"}, {"name": "campaign_cumulative_allowance", "type": "int"}, {"name": "front_loaded_daily_allowance", "type": "int"}, {"name": "job_flag_active", "type": "smallint"}, {"name": "active_clone_used", "type": "smallint"}, {"name": "active_sibling_used", "type": "smallint"}, {"name": "client_sub_id", "type": "string"}, {"name": "redirect_to", "type": "string"}, {"name": "redirect_type", "type": "string"}, {"name": "action", "type": "string"}, {"name": "ip_geo_blocked", "type": "smallint"}, {"name": "date_indexed", "type": "string"}, {"name": "serp_page", "type": "int"}, {"name": "oapply", "type": "string"}, {"name": "cache_oapply", "type": "string"}, {"name": "pagetype", "type": "string"}, {"name": "cta_position", "type": "string"}, {"name": "is_spike", "type": "smallint"}, {"name": "gclid", "type": "string"}, {"name": "cache_gclid", "type": "string"}, {"name": "fbclid", "type": "string"}, {"name": "cache_fbclid", "type": "string"}, {"name": "msclid", "type": "string"}, {"name": "cache_msclid", "type": "string"}], "partitionKeys": [{"name": "event_type", "type": "string"}, {"name": "year", "type": "int"}, {"name": "month", "type": "int"}, {"name": "day", "type": "int"}, {"name": "hour", "type": "int"}]}