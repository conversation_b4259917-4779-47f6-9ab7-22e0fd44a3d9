{"location": "s3://talent-dev-datalake/event/user/", "columns": [{"name": "unixtime", "type": "timestamp"}, {"name": "id", "type": "string"}, {"name": "nuuid", "type": "string"}, {"name": "source", "type": "string"}, {"name": "host", "type": "string"}, {"name": "latitude", "type": "string"}, {"name": "longitude", "type": "string"}, {"name": "referrer_url", "type": "string"}, {"name": "uri", "type": "string"}, {"name": "language", "type": "string"}, {"name": "country", "type": "string"}, {"name": "city", "type": "string"}, {"name": "state", "type": "string"}, {"name": "zip_code", "type": "string"}, {"name": "page_url", "type": "string"}, {"name": "page_name", "type": "string"}, {"name": "utm_medium", "type": "string"}, {"name": "utm_campaign", "type": "string"}, {"name": "utm_product", "type": "string"}, {"name": "utm_id", "type": "string"}, {"name": "utm_term", "type": "string"}, {"name": "acquisition_sub_id", "type": "string"}, {"name": "channel_1", "type": "string"}, {"name": "channel_2", "type": "string"}, {"name": "channel_3", "type": "string"}, {"name": "puid", "type": "string"}, {"name": "oapply", "type": "string"}, {"name": "gclid", "type": "string"}, {"name": "fbclid", "type": "string"}, {"name": "msclid", "type": "string"}, {"name": "device_name", "type": "string"}, {"name": "device_type", "type": "string"}, {"name": "operating_system", "type": "string"}, {"name": "device_resolution", "type": "string"}, {"name": "browser_name", "type": "string"}, {"name": "user_id", "type": "string"}, {"name": "is_logged_in", "type": "int"}, {"name": "context", "type": "string"}, {"name": "publisher_account_id", "type": "int"}, {"name": "cache_publisher_account_id", "type": "int"}, {"name": "publisher_product_id", "type": "int"}, {"name": "cache_publisher_product_id", "type": "int"}, {"name": "cache_email_id", "type": "string"}, {"name": "cache_search_id", "type": "string"}, {"name": "cache_referer", "type": "string"}, {"name": "cache_source", "type": "string"}, {"name": "cache_publisher", "type": "string"}, {"name": "cache_utm_product", "type": "string"}, {"name": "cache_utm_medium", "type": "string"}, {"name": "cache_utm_campaign", "type": "string"}, {"name": "cache_utm_id", "type": "string"}, {"name": "cache_oapply", "type": "string"}, {"name": "cache_acquisition_sub_id", "type": "string"}, {"name": "cache_channel_1", "type": "string"}, {"name": "cache_channel_2", "type": "string"}, {"name": "cache_channel_3", "type": "string"}, {"name": "cache_context", "type": "string"}, {"name": "pageloadtime_millisec", "type": "int"}, {"name": "bot_type", "type": "string"}, {"name": "cf_bot_score", "type": "int"}, {"name": "indexability", "type": "smallint"}, {"name": "canonical_url", "type": "string"}, {"name": "ref", "type": "string"}, {"name": "search_reference_id", "type": "string"}, {"name": "search_id", "type": "string"}, {"name": "is_user_initiated", "type": "smallint"}, {"name": "trigger_from", "type": "string"}, {"name": "results_count", "type": "int"}, {"name": "job_viewed", "type": "array<struct<job_id:string,page:int>>"}, {"name": "job_id", "type": "string"}, {"name": "job_card_rank", "type": "int"}, {"name": "click_location", "type": "string"}, {"name": "impression_id", "type": "string"}, {"name": "page", "type": "int"}, {"name": "ppc_value", "type": "int"}, {"name": "ppc_currency", "type": "string"}, {"name": "job_view_type", "type": "string"}, {"name": "time_spent", "type": "int"}, {"name": "button_position", "type": "string"}, {"name": "job_impressions", "type": "array<string>"}, {"name": "question", "type": "string"}, {"name": "value", "type": "array<string>"}, {"name": "filter_name", "type": "string"}, {"name": "filter_value", "type": "string"}, {"name": "job_count", "type": "int"}, {"name": "filter_value_cleared", "type": "string"}, {"name": "cleared_filters", "type": "array<string>"}, {"name": "method", "type": "string"}, {"name": "sign_in_process_initiated_id", "type": "string"}, {"name": "is_stay_signed", "type": "smallint"}, {"name": "registration_source", "type": "string"}, {"name": "subid", "type": "string"}, {"name": "region", "type": "string"}, {"name": "timezone", "type": "string"}, {"name": "sign_up_process_started_id", "type": "string"}, {"name": "email_domain", "type": "string"}, {"name": "talentapply<PERSON>", "type": "string"}, {"name": "apply_step_data", "type": "string"}, {"name": "apply_submit_data", "type": "string"}, {"name": "parent_event_id", "type": "string"}, {"name": "error_id", "type": "string"}, {"name": "error_type", "type": "string"}, {"name": "suggested_keyword", "type": "string"}, {"name": "adsense_value", "type": "double"}, {"name": "cache_gclid", "type": "string"}, {"name": "cache_msclid", "type": "string"}, {"name": "ppcu", "type": "int"}], "partitionKeys": [{"name": "event_type", "type": "string"}, {"name": "year", "type": "int"}, {"name": "month", "type": "int"}, {"name": "day", "type": "int"}, {"name": "hour", "type": "int"}]}