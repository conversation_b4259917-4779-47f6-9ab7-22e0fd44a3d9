#S3 Datalake
s3-test-datalake:
  extends: .test-template
  variables:
    RESOURCE_PATH: "/infrastructure/environments/analytics-dev/iac/us-east-1/s3/datalake"
  rules:
    - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'
      changes:
        - 'infrastructure/environments/analytics-dev/iac/us-east-1/s3/datalake/**/*'

s3-plan-datalake:
  extends: .plan-template
  variables:
    RESOURCE_PATH: "/infrastructure/environments/analytics-dev/iac/us-east-1/s3/datalake"
  dependencies:
    - s3-test-datalake
  rules:
    - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'
      changes:
        - 'infrastructure/environments/analytics-dev/iac/us-east-1/s3/datalake/**/*'
      when: manual

# s3-apply-datalake:
#   extends: .apply-template
#   variables:
#     RESOURCE_PATH: "/infrastructure/environments/analytics-dev/iac/us-east-1/s3/datalake"
#   dependencies:
#     - s3-plan-datalake
#   rules:
#     - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'
#       changes:
#         - 'infrastructure/environments/analytics-dev/iac/us-east-1/s3/datalake/**/*'
#       when: manual


#S3 datalake-archive
s3-test-datalake-archive:
  extends: .test-template
  variables:
    RESOURCE_PATH: "/infrastructure/environments/analytics-dev/iac/us-east-1/s3/datalake-archive"
  rules:
    - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'
      changes:
        - 'infrastructure/environments/analytics-dev/iac/us-east-1/s3/datalake-archive/**/*'

s3-plan-datalake-archive:
  extends: .plan-template
  variables:
    RESOURCE_PATH: "/infrastructure/environments/analytics-dev/iac/us-east-1/s3/datalake-archive"
  dependencies:
    - s3-test-datalake-archive
  rules:
    - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'
      changes:
        - 'infrastructure/environments/analytics-dev/iac/us-east-1/s3/datalake-archive/**/*'
      when: manual

# s3-apply-datalake-archive:
#   extends: .apply-template
#   variables:
#     RESOURCE_PATH: "/infrastructure/environments/analytics-dev/iac/us-east-1/s3/datalake-archive"
#   dependencies:
#     - s3-plan-datalake-archive
#   rules:
#     - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'
#       changes:
#         - 'infrastructure/environments/analytics-dev/iac/us-east-1/s3/datalake-archive/**/*'
#       when: manual


#S3 data-lake-schema-blueprint
s3-test-datalake-schema-blueprint:
  extends: .test-template
  variables:
    RESOURCE_PATH: "/infrastructure/environments/analytics-dev/iac/us-east-1/s3/datalake-schema-blueprint"
  rules:
    - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'
      changes:
        - 'infrastructure/environments/analytics-dev/iac/us-east-1/s3/datalake-schema-blueprint/**/*'

s3-plan-datalake-schema-blueprint:
  extends: .plan-template
  variables:
    RESOURCE_PATH: "/infrastructure/environments/analytics-dev/iac/us-east-1/s3/datalake-schema-blueprint"
  dependencies:
    - s3-test-datalake-schema-blueprint
  rules:
    - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'
      changes:
        - 'infrastructure/environments/analytics-dev/iac/us-east-1/s3/datalake-schema-blueprint/**/*'
      when: manual

# s3-apply-datalake-schema-blueprint:
#   extends: .apply-template
#   variables:
#     RESOURCE_PATH: "/infrastructure/environments/analytics-dev/iac/us-east-1/s3/datalake-schema-blueprint"
#   dependencies:
#     - s3-plan-datalake-schema-blueprint
#   rules:
#     - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'
#       changes:
#         - 'infrastructure/environments/analytics-dev/iac/us-east-1/s3/datalake-schema-blueprint/**/*'
#       when: manual
  

#S3 athena-queries-saved
s3-test-athena-queries-saved:
  extends: .test-template
  variables:
    RESOURCE_PATH: "/infrastructure/environments/analytics-dev/iac/us-east-1/s3/athena-queries-saved"
  rules:
    - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'
      changes:
        - 'infrastructure/environments/analytics-dev/iac/us-east-1/s3/athena-queries-saved/**/*'

s3-plan-athena-queries-saved:
  extends: .plan-template
  variables:
    RESOURCE_PATH: "/infrastructure/environments/analytics-dev/iac/us-east-1/s3/athena-queries-saved"
  dependencies:
    - s3-test-athena-queries-saved
  rules:
    - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'
      changes:
        - 'infrastructure/environments/analytics-dev/iac/us-east-1/s3/athena-queries-saved/**/*'
      when: manual

# s3-apply-athena-queries-saved:
#   extends: .apply-template
#   variables:
#     RESOURCE_PATH: "/infrastructure/environments/analytics-dev/iac/us-east-1/s3/athena-queries-saved"
#   dependencies:
#     - s3-plan-athena-queries-saved
#   rules:
#     - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'
#       changes:
#         - 'infrastructure/environments/analytics-dev/iac/us-east-1/s3/athena-queries-saved/**/*'
#       when: manual


#S3 logging
s3-test-datalake-logging:
  extends: .test-template
  variables:
    RESOURCE_PATH: "/infrastructure/environments/analytics-dev/iac/us-east-1/s3/datalake-logging"
  rules:
    - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'
      changes:
        - 'infrastructure/environments/analytics-dev/iac/us-east-1/s3/datalake-logging/**/*'

s3-plan-datalake-logging:
  extends: .plan-template
  variables:
    RESOURCE_PATH: "/infrastructure/environments/analytics-dev/iac/us-east-1/s3/datalake-logging"
  dependencies:
    - s3-test-datalake-logging
  rules:
    - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'
      changes:
        - 'infrastructure/environments/analytics-dev/iac/us-east-1/s3/datalake-logging/**/*'
      when: manual

s3-apply-datalake-logging:
  extends: .apply-template
  variables:
    RESOURCE_PATH: "/infrastructure/environments/analytics-dev/iac/us-east-1/s3/datalake-logging"
  dependencies:
    - s3-plan-datalake-logging
  rules:
    - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'
      changes:
        - 'infrastructure/environments/analytics-dev/iac/us-east-1/s3/datalake-logging/**/*'
      when: manual


#S3 dbt artifacts
s3-test-dbt-docs:
  extends: .test-template
  variables:
    RESOURCE_PATH: "/infrastructure/environments/analytics-dev/iac/us-east-1/s3/dbt-docs"
  rules:
    - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'
      changes:
        - 'infrastructure/environments/analytics-dev/iac/us-east-1/s3/dbt-docs/**/*'

s3-plan-dbt-docs:
  extends: .plan-template
  variables:
    RESOURCE_PATH: "/infrastructure/environments/analytics-dev/iac/us-east-1/s3/dbt-docs"
  dependencies:
    - s3-test-dbt-docs
  rules:
    - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'
      changes:
        - 'infrastructure/environments/analytics-dev/iac/us-east-1/s3/dbt-docs/**/*'
      when: manual

# s3-apply-dbt-docs:
#   extends: .apply-template
#   variables:
#     RESOURCE_PATH: "/infrastructure/environments/analytics-dev/iac/us-east-1/s3/dbt-docs"
#   dependencies:
#     - s3-plan-dbt-docs
#   rules:
#     - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'
#       changes:
#         - 'infrastructure/environments/analytics-dev/iac/us-east-1/s3/dbt-docs/**/*'
#       when: manual

# We redeployed a provisioned redshift cluster instead
# #Redshift Deployment
# redshift-test:
#   extends: .test-template
#   variables:
#     RESOURCE_PATH: "/infrastructure/environments/analytics-dev/iac/us-east-1/redshift/serverless"
#   rules:
#     - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'
#       changes:
#         - 'infrastructure/environments/analytics-dev/iac/us-east-1/redshift/serverless/**/*'

# redshift-plan:
#   extends: .plan-template
#   variables:
#     RESOURCE_PATH: "/infrastructure/environments/analytics-dev/iac/us-east-1/redshift/serverless"
#   dependencies:
#     - redshift-test
#   rules:
#     - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'
#       changes:
#         - 'infrastructure/environments/analytics-dev/iac/us-east-1/redshift/serverless/**/*'
#       when: manual

# redshift-apply:
#   extends: .apply-template
#   variables:
#     RESOURCE_PATH: "/infrastructure/environments/analytics-dev/iac/us-east-1/redshift/serverless"
#   dependencies:
#     - redshift-plan
#   rules:
#     - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'
#       changes:
#         - 'infrastructure/environments/analytics-dev/iac/us-east-1/redshift/serverless/**/*'
#       when: manual

# redshift-destroy:
#   extends: .destroy-template
#   variables:
#     RESOURCE_PATH: "/infrastructure/environments/analytics-dev/iac/us-east-1/redshift/serverless"
#   dependencies:
#     - redshift-apply
#   rules:
#     - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'
#       changes:
#         - 'infrastructure/environments/analytics-dev/iac/us-east-1/redshift/serverless/**/*'
#       when: manual


#Ec2 Deployment
ec2-test:
  extends: .test-template
  variables:
    RESOURCE_PATH: "/infrastructure/environments/analytics-dev/iac/us-east-1/ec2/ec2-powerbi"
  rules:
    - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'
      changes:
        - 'infrastructure/environments/analytics-dev/iac/us-east-1/ec2/ec2-powerbi/**/*'

ec2-plan:
  extends: .plan-template
  variables:
    RESOURCE_PATH: "/infrastructure/environments/analytics-dev/iac/us-east-1/ec2/ec2-powerbi"
  dependencies:
    - ec2-test
  rules:
    - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'
      changes:
        - 'infrastructure/environments/analytics-dev/iac/us-east-1/ec2/ec2-powerbi/**/*'
      when: manual

ec2-apply:
  extends: .apply-template
  variables:
    RESOURCE_PATH: "/infrastructure/environments/analytics-dev/iac/us-east-1/ec2/ec2-powerbi"
  dependencies:
    - ec2-plan
  rules:
    - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'
      changes:
        - 'infrastructure/environments/analytics-dev/iac/us-east-1/ec2/ec2-powerbi/**/*'
      when: manual


#DMS Deployment
dms-test-currencydb:
  extends: .test-template
  variables:
    RESOURCE_PATH: "/infrastructure/environments/analytics-dev/iac/us-east-1/dms/currency-db"
  rules:
    - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'
      changes:
        - 'infrastructure/environments/analytics-dev/iac/us-east-1/dms/currency-db/**/*'

dms-plan-currencydb:
  extends: .plan-template
  variables:
    RESOURCE_PATH: "/infrastructure/environments/analytics-dev/iac/us-east-1/dms/currency-db"
  dependencies:
    - dms-test-currencydb
  rules:
    - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'
      changes:
        - 'infrastructure/environments/analytics-dev/iac/us-east-1/dms/currency-db/**/*'
      when: manual

dms-apply-currencydb:
  extends: .apply-template
  variables:
    RESOURCE_PATH: "/infrastructure/environments/analytics-dev/iac/us-east-1/dms/currency-db"
  dependencies:
    - dms-plan-currencydb
  rules:
    - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'
      changes:
        - 'infrastructure/environments/analytics-dev/iac/us-east-1/dms/currency-db/**/*'
      when: manual

#Replica Instances for DMS
replica-test-instance:
  extends: .test-template
  variables:
    RESOURCE_PATH: "/infrastructure/environments/analytics-dev/iac/us-east-1/dms/replication-datalake"
  rules:
    - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'
      changes:
        - 'infrastructure/environments/analytics-dev/iac/us-east-1/dms/replication-datalake/**/*'

replica-plan-instance:
  extends: .plan-template
  variables:
    RESOURCE_PATH: "/infrastructure/environments/analytics-dev/iac/us-east-1/dms/replication-datalake"
  dependencies:
    - replica-test-instance
  rules:
    - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'
      changes:
        - 'infrastructure/environments/analytics-dev/iac/us-east-1/dms/replication-datalake/**/*'
      when: manual

replica-apply-instance:
  extends: .apply-template
  variables:
    RESOURCE_PATH: "/infrastructure/environments/analytics-dev/iac/us-east-1/dms/replication-datalake"
  dependencies:
    - replica-plan-instance
  rules:
    - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'
      changes:
        - 'infrastructure/environments/analytics-dev/iac/us-east-1/dms/replication-datalake/**/*'
      when: manual

#DMS Deployment
dms-test-publisherdb:
  extends: .test-template
  variables:
    RESOURCE_PATH: "/infrastructure/environments/analytics-dev/iac/us-east-1/dms/publishers-db"
  rules:
    - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'
      changes:
        - 'infrastructure/environments/analytics-dev/iac/us-east-1/dms/publishers-db/**/*'

dms-plan-publisherdb:
  extends: .plan-template
  variables:
    RESOURCE_PATH: "/infrastructure/environments/analytics-dev/iac/us-east-1/dms/publishers-db"
  dependencies:
    - dms-test-publisherdb
  rules:
    - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'
      changes:
        - 'infrastructure/environments/analytics-dev/iac/us-east-1/dms/publishers-db/**/*'
      when: manual

dms-apply-publisherdb:
  extends: .apply-template
  variables:
    RESOURCE_PATH: "/infrastructure/environments/analytics-dev/iac/us-east-1/dms/publishers-db"
  dependencies:
    - dms-plan-publisherdb
  rules:
    - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'
      changes:
        - 'infrastructure/environments/analytics-dev/iac/us-east-1/dms/publishers-db/**/*'
      when: manual

#DMS Deployment
dms-test-talent-employerdb:
  extends: .test-template
  variables:
    RESOURCE_PATH: "/infrastructure/environments/analytics-dev/iac/us-east-1/dms/talent-employer-db"
  rules:
    - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'
      changes:
        - 'infrastructure/environments/analytics-dev/iac/us-east-1/dms/talent-employer-db/**/*'

dms-plan-talent-employerdb:
  extends: .plan-template
  variables:
    RESOURCE_PATH: "/infrastructure/environments/analytics-dev/iac/us-east-1/dms/talent-employer-db"
  dependencies:
    - dms-test-talent-employerdb
  rules:
    - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'
      changes:
        - 'infrastructure/environments/analytics-dev/iac/us-east-1/dms/talent-employer-db/**/*'
      when: manual

dms-apply-talent-employerdb:
  extends: .apply-template
  variables:
    RESOURCE_PATH: "/infrastructure/environments/analytics-dev/iac/us-east-1/dms/talent-employer-db"
  dependencies:
    - dms-plan-talent-employerdb
  rules:
    - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'
      changes:
        - 'infrastructure/environments/analytics-dev/iac/us-east-1/dms/talent-employer-db/**/*'
      when: manual

#DMS Deployment
dms-test-employer-job-db:
  extends: .test-template
  variables:
    RESOURCE_PATH: "/infrastructure/environments/analytics-dev/iac/us-east-1/dms/employer-job-processing-db"
  rules:
    - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'
      changes:
        - 'infrastructure/environments/analytics-dev/iac/us-east-1/dms/employer-job-processing-db/**/*'

dms-plan-employer-job-db:
  extends: .plan-template
  variables:
    RESOURCE_PATH: "/infrastructure/environments/analytics-dev/iac/us-east-1/dms/employer-job-processing-db"
  dependencies:
    - dms-test-employer-job-db
  rules:
    - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'
      changes:
        - 'infrastructure/environments/analytics-dev/iac/us-east-1/dms/employer-job-processing-db/**/*'
      when: manual

dms-apply-employer-job-db:
  extends: .apply-template
  variables:
    RESOURCE_PATH: "/infrastructure/environments/analytics-dev/iac/us-east-1/dms/employer-job-processing-db"
  dependencies:
    - dms-plan-employer-job-db
  rules:
    - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'
      changes:
        - 'infrastructure/environments/analytics-dev/iac/us-east-1/dms/employer-job-processing-db/**/*'
      when: manual


#DMS Deployment
dms-test-seo-db:
  extends: .test-template
  variables:
    RESOURCE_PATH: "/infrastructure/environments/analytics-dev/iac/us-east-1/dms/seo-db"
  rules:
    - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'
      changes:
        - 'infrastructure/environments/analytics-dev/iac/us-east-1/dms/seo-db/**/*'

dms-plan-seo-db:
  extends: .plan-template
  variables:
    RESOURCE_PATH: "/infrastructure/environments/analytics-dev/iac/us-east-1/dms/seo-db"
  dependencies:
    - dms-plan-seo-db
  rules:
    - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'
      changes:
        - 'infrastructure/environments/analytics-dev/iac/us-east-1/dms/seo-db/**/*'
      when: manual

# dms-apply-seo-db:
#   extends: .apply-template
#   variables:
#     RESOURCE_PATH: "/infrastructure/environments/analytics-dev/iac/us-east-1/dms/seo-db"
#   dependencies:
#     - dms-apply-seo-db
#   rules:
#     - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'
#       changes:
#         - 'infrastructure/environments/analytics-dev/iac/us-east-1/dms/seo-db/**/*'
#       when: manual

#DMS Deployment
dms-test-monetization-db:
  extends: .test-template
  variables:
    RESOURCE_PATH: "/infrastructure/environments/analytics-dev/iac/us-east-1/dms/monetization-db"
  rules:
    - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'
      changes:
        - 'infrastructure/environments/analytics-dev/iac/us-east-1/dms/monetization-db/**/*'

dms-plan-monetization-db:
  extends: .plan-template
  variables:
    RESOURCE_PATH: "/infrastructure/environments/analytics-dev/iac/us-east-1/dms/monetization-db"
  dependencies:
    - dms-test-monetization-db
  rules:
    - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'
      changes:
        - 'infrastructure/environments/analytics-dev/iac/us-east-1/dms/monetization-db/**/*'
      when: manual

dms-apply-monetization-db:
  extends: .apply-template
  variables:
    RESOURCE_PATH: "/infrastructure/environments/analytics-dev/iac/us-east-1/dms/monetization-db"
  dependencies:
    - dms-plan-monetization-db
  rules:
    - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'
      changes:
        - 'infrastructure/environments/analytics-dev/iac/us-east-1/dms/monetization-db/**/*'
      when: manual
  
#DMS Deployment
dms-test-users-db:
  extends: .test-template
  variables:
    RESOURCE_PATH: "/infrastructure/environments/analytics-dev/iac/us-east-1/dms/users-db"
  rules:
    - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'
      changes:
        - 'infrastructure/environments/analytics-dev/iac/us-east-1/dms/users-db/**/*'

dms-plan-users-db:
  extends: .plan-template
  variables:
    RESOURCE_PATH: "/infrastructure/environments/analytics-dev/iac/us-east-1/dms/users-db"
  dependencies:
    - dms-test-users-db
  rules:
    - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'
      changes:
        - 'infrastructure/environments/analytics-dev/iac/us-east-1/dms/users-db/**/*'
      when: manual

dms-apply-users-db:
  extends: .apply-template
  variables:
    RESOURCE_PATH: "/infrastructure/environments/analytics-dev/iac/us-east-1/dms/users-db"
  dependencies:
    - dms-plan-users-db
  rules:
    - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'
      changes:
        - 'infrastructure/environments/analytics-dev/iac/us-east-1/dms/users-db/**/*'
      when: manual

#Glue Database Deployment
glue-test-database:
  extends: .test-template
  variables:
    RESOURCE_PATH: "/infrastructure/environments/analytics-dev/iac/us-east-1/glue/glue-catalog-database"
  rules:
    - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'
      changes:
        - 'infrastructure/environments/analytics-dev/iac/us-east-1/glue/glue-catalog-database/**/*'

glue-plan-database:
  extends: .plan-template
  variables:
    RESOURCE_PATH: "/infrastructure/environments/analytics-dev/iac/us-east-1/glue/glue-catalog-database"
  dependencies:
    - glue-test-database
  rules:
    - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'
      changes:
        - 'infrastructure/environments/analytics-dev/iac/us-east-1/glue/glue-catalog-database/**/*'
      when: manual

# glue-apply-database:
#   extends: .apply-template
#   variables:
#     RESOURCE_PATH: "/infrastructure/environments/analytics-dev/iac/us-east-1/glue/glue-catalog-database"
#   dependencies:
#     - glue-plan-database
#   rules:
#     - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'
#       changes:
#         - 'infrastructure/environments/analytics-dev/iac/us-east-1/glue/glue-catalog-database/**/*'
#       when: manual


#Glue Crawler Deployment
glue-test-crawler:
  extends: .test-template
  variables:
    RESOURCE_PATH: "/infrastructure/environments/analytics-dev/iac/us-east-1/glue/glue-crawler"
  rules:
    - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'
      changes:
        - 'infrastructure/environments/analytics-dev/iac/us-east-1/glue/glue-crawler/**/*'

glue-plan-crawler:
  extends: .plan-template
  variables:
    RESOURCE_PATH: "/infrastructure/environments/analytics-dev/iac/us-east-1/glue/glue-crawler"
  dependencies:
    - glue-test-crawler
  rules:
    - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'
      changes:
        - 'infrastructure/environments/analytics-dev/iac/us-east-1/glue/glue-crawler/**/*'
      when: manual

# glue-apply-crawler:
#   extends: .apply-template
#   variables:
#     RESOURCE_PATH: "/infrastructure/environments/analytics-dev/iac/us-east-1/glue/glue-crawler"
#   dependencies:
#     - glue-plan-crawler
#   rules:
#     - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'
#       changes:
#         - 'infrastructure/environments/analytics-dev/iac/us-east-1/glue/glue-crawler/**/*'
#       when: manual

#Glue Tables Deployment
glue-test-table:
  extends: .test-template
  variables:
    RESOURCE_PATH: "/infrastructure/environments/analytics-dev/iac/us-east-1/glue/glue-catalog-table"
  rules:
    - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'
      changes:
        - 'infrastructure/environments/analytics-dev/iac/us-east-1/glue/glue-catalog-table/**/*'

glue-plan-table:
  extends: .plan-template
  variables:
    RESOURCE_PATH: "/infrastructure/environments/analytics-dev/iac/us-east-1/glue/glue-catalog-table"
  dependencies:
    - glue-test-table
  rules:
    - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'
      changes:
        - 'infrastructure/environments/analytics-dev/iac/us-east-1/glue/glue-catalog-table/**/*'
      when: manual

# glue-apply-table:
#   extends: .apply-template
#   variables:
#     RESOURCE_PATH: "/infrastructure/environments/analytics-dev/iac/us-east-1/glue/glue-catalog-table"
#   dependencies:
#     - glue-plan-table
#   rules:
#     - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'
#       changes:
#         - 'infrastructure/environments/analytics-dev/iac/us-east-1/glue/glue-catalog-table/**/*'
#       when: manual

#Glue Database Deployment
glue-test-catalog:
  extends: .test-template
  variables:
    RESOURCE_PATH: "/infrastructure/environments/analytics-dev/iac/us-east-1/glue/glue-catalog-database"
  rules:
    - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'
      changes:
        - 'infrastructure/environments/analytics-dev/iac/us-east-1/glue/glue-catalog-database/**/*'

glue-plan-catalog:
  extends: .plan-template
  variables:
    RESOURCE_PATH: "/infrastructure/environments/analytics-dev/iac/us-east-1/glue/glue-catalog-database"
  dependencies:
    - glue-test-catalog
  rules:
    - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'
      changes:
        - 'infrastructure/environments/analytics-dev/iac/us-east-1/glue/glue-catalog-database/**/*'
      when: manual

# glue-apply-catalog:
#   extends: .apply-template
#   variables:
#     RESOURCE_PATH: "/infrastructure/environments/analytics-dev/iac/us-east-1/glue/glue-catalog-database"
#   dependencies:
#     - glue-plan-catalog
#   rules:
#     - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'
#       changes:
#         - 'infrastructure/environments/analytics-dev/iac/us-east-1/glue/glue-catalog-database/**/*'
#       when: manual


#Kinesis stream Deployment
kinesis-test-stream:
  extends: .test-template
  variables:
    RESOURCE_PATH: "/infrastructure/environments/analytics-dev/iac/us-east-1/kinesis-stream"
  rules:
    - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'
      changes:
        - 'infrastructure/environments/analytics-dev/iac/us-east-1/kinesis-stream/**/*'

Kinesis-plan-stream:
  extends: .plan-template
  variables:
    RESOURCE_PATH: "/infrastructure/environments/analytics-dev/iac/us-east-1/kinesis-stream"
  dependencies:
    - kinesis-test-stream
  rules:
    - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'
      changes:
        - 'infrastructure/environments/analytics-dev/iac/us-east-1/kinesis-stream/**/*'
      when: manual

# Kinesis-apply-stream:
#   extends: .apply-template
#   variables:
#     RESOURCE_PATH: "/infrastructure/environments/analytics-dev/iac/us-east-1/kinesis-stream"
#   dependencies:
#     - Kinesis-plan-stream
#   rules:
#     - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'
#       changes:
#         - 'infrastructure/environments/analytics-dev/iac/us-east-1/kinesis-stream/**/*'
#       when: manual


#Redshift Provisioned Deployment
redshift-pro-test:
  extends: .test-template
  variables:
    RESOURCE_PATH: "/infrastructure/environments/analytics-dev/iac/us-east-1/redshift/provisioned"
  rules:
    - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'
      changes:
        - 'infrastructure/environments/analytics-dev/iac/us-east-1/redshift/provisioned/**/*'

redshift-pro-plan:
  extends: .plan-template
  variables:
    RESOURCE_PATH: "/infrastructure/environments/analytics-dev/iac/us-east-1/redshift/provisioned"
  dependencies:
    - redshift-pro-test
  rules:
    - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'
      changes:
        - 'infrastructure/environments/analytics-dev/iac/us-east-1/redshift/provisioned/**/*'
      when: manual

# redshift-pro-apply:
#   extends: .apply-template
#   variables:
#     RESOURCE_PATH: "/infrastructure/environments/analytics-dev/iac/us-east-1/redshift/provisioned"
#   dependencies:
#     - redshift-pro-plan
#   rules:
#     - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'
#       changes:
#         - 'infrastructure/environments/analytics-dev/iac/us-east-1/redshift/provisioned/**/*'
#       when: manual
