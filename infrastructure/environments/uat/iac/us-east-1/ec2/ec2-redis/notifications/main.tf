#Jobs ingestion single instance Master-data-client for Demo

module "ec2_instance" {
  source = "../../../../../../../terraform/modules/official/terraform-aws-ec2-instance"


  name                   = "${basename(path.cwd)}-redis-single-node"
  ami                    = "ami-04a02acc7820fb173"
  instance_type          = "t2.medium"
  monitoring             = true
  vpc_security_group_ids = var.security_groups
  subnet_id              = var.vpc_subnet_1a

  tags = {
    terraform   = "true"
    environment = "uat"
    name        = "${basename(path.cwd)}-redis-single-node"
    cc          = "job_seeker"
    group       = "database"
    type        = "ec2"
    owner       = "manuj"
    service     = "redis"
    departnent  = basename(path.cwd)
  }

  volume_tags = {
    cc          = "job_seeker"
    type        = "ebs"
  }
}

data "aws_route53_zone" "private" {
  name         = "talent.private"
  private_zone = true
}

resource "aws_route53_record" "private" {
  allow_overwrite = true
  zone_id = data.aws_route53_zone.private.zone_id
  name    = "redis-${basename(path.cwd)}"
  type    = "A"
  ttl     = 60
  records = [ module.ec2_instance.private_ip ]
}