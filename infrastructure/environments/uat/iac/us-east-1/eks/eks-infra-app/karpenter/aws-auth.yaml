apiVersion: v1
data:
  mapRoles: |
    - groups:
      - system:bootstrappers
      - system:nodes
      rolearn: arn:aws:iam::************:role/infra_monitoring-eks-node-group-20240527184827668100000001
      username: system:node:{{EC2PrivateDNSName}}
    - groups:
      - system:bootstrappers
      - system:nodes
      rolearn: arn:aws:iam::************:role/KarpenterNodeRole-dev-eks-infra-monitoring
      username: system:node:{{EC2PrivateDNSName}}
kind: ConfigMap
metadata:
  creationTimestamp: "2024-05-27T19:06:33Z"
  name: aws-auth
  namespace: kube-system
  resourceVersion: "2293"
  uid: 62bfe5cb-b45b-43c6-9a0d-b23ad304d3b1
