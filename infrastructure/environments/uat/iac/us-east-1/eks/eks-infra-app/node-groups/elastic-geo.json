{"min_size": 6, "desired_size": 6, "max_size": 6, "capacity_type": "ON_DEMAND", "instance_types": ["m6g.2xlarge"], "ami_type": "AL2_ARM_64", "taints": [{"key": "app", "value": "elastic-geo", "effect": "NO_SCHEDULE"}], "labels": {"app": "elastic-geo"}, "tags": {"cc": "marketplace", "environment": "uat", "group": "geocode", "owner": "daniel-<PERSON><PERSON>o", "service": "elastic-geo", "terraform": "true"}}