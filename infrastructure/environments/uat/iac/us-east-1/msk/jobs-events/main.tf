provider "aws" {
  region = local.region
  default_tags {
    tags = {
      owner   = "emanuel"
      cc      = "marketplace"
      type    = "msk"
    }
  }
}

data "local_file" "config"{

  filename = "../../config.json"
}
# LOCALS
locals {
  name   = "${basename(path.cwd)}"

  config                = jsondecode(data.local_file.config.content)
  environment           = local.config.environment
  region                = local.config.region
  config_bucket         = "${local.environment}-${local.config.bucket}"
  config_dynamodb_table = "${local.environment}-${local.config.dynamodb_table}"
  account_id            = local.config.account_id

  tags = {
    name    = local.name
    owner   = "emanuel"
    cc      = "marketplace"
    type    = "msk"
  }
}

### INSTANCIATE DEPENDENCIES
data "terraform_remote_state" "vpc_subnetting_gateways" {
  backend = "s3"

  config = {
    encrypt        = local.config.encrypt
    region         = local.region
    key            = "${local.environment}/iac/us-east-1/foundational/vpc-subnetting-and-gateways.tfstate"
    bucket         = local.config_bucket
    dynamodb_table = local.config_dynamodb_table
  }
}

################################################################################
# MSK Cluster - Default
################################################################################

module "msk_cluster" {
  source = "../../../../../../terraform/modules/official/terraform-aws-msk-kafka-cluster"

  name                   = local.name
  kafka_version          = "3.7.x.kraft"
  number_of_broker_nodes = 3

  broker_node_client_subnets  = [
    data.terraform_remote_state.vpc_subnetting_gateways.outputs.private_workloads_subnets_id_us_east_1a[0],
    data.terraform_remote_state.vpc_subnetting_gateways.outputs.private_workloads_subnets_id_us_east_1b[0],
    data.terraform_remote_state.vpc_subnetting_gateways.outputs.private_workloads_subnets_id_us_east_1d[0]
  ]
  broker_node_instance_type   = "kafka.m7g.large"
  broker_node_security_groups = [ "sg-0b44d84b39703b8ee" ]

  broker_node_storage_info = {
    ebs_storage_info = {
      volume_size = 1024
    }
  }

  scaling_max_capacity = 5000
  scaling_target_value = 60

  cloudwatch_logs_enabled = true
  configuration_server_properties = {
    "auto.create.topics.enable" = true
    "delete.topic.enable"       = true
    "default.replication.factor"= 3
    "min.insync.replicas" = 1
    "num.io.threads" = 2
    "num.network.threads" = 1
    "num.partitions" = 1
    "num.replica.fetchers" = 2
    "replica.lag.time.max.ms" = 30000
    "socket.receive.buffer.bytes" = 102400
    "socket.request.max.bytes" = 104857600
    "socket.send.buffer.bytes" = 102400
    "unclean.leader.election.enable" = true
    "zookeeper.session.timeout.ms" = 18000
  }

  client_authentication = {
    sasl = {
      iam   = false
      scram = false
    },
    unauthenticated = true
  }

  encryption_in_transit_client_broker =  "TLS_PLAINTEXT"

  tags = local.tags
}

# data "aws_route53_zone" "this" {
#   name         = "talent.local"
#   private_zone = true
# }

# resource "aws_route53_record" "this" {
#   count = 3

#   allow_overwrite = true
#   zone_id = data.aws_route53_zone.this.zone_id
#   name    = "kafka-broker-${basename(path.cwd)}-${count.index}"
#   type    = "CNAME"
#   ttl     = 60
#   records = [ split(",", replace(module.msk_cluster.bootstrap_brokers_plaintext, ":9092", ""))[count.index] ]
# }

# data "aws_route53_zone" "private" {
#   name         = "talent.private"
#   private_zone = true
# }

# resource "aws_route53_record" "private" {
#   count = 3

#   allow_overwrite = true
#   zone_id = data.aws_route53_zone.private.zone_id
#   name    = "kafka-broker-${basename(path.cwd)}-${count.index}"
#   type    = "CNAME"
#   ttl     = 60
#   records = [ split(",", replace(module.msk_cluster.bootstrap_brokers_plaintext, ":9092", ""))[count.index] ]
# }