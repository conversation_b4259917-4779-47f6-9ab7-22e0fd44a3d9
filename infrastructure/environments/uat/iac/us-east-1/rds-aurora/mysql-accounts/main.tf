module "cluster" {
  source = "../../../../../../terraform/modules/official/terraform-aws-rds-aurora"

  name            = basename(path.cwd)
  engine          = "aurora-mysql"
  master_username = "accounts"
  master_password = "eJ8Y#BbCeB%s"

  manage_master_user_password = false
  
  engine_mode = "provisioned"
  instance_class = "db.serverless"
  engine_version = "8.0.mysql_aurora.3.07.0"

  serverlessv2_scaling_configuration = {
    max_capacity = 128
    min_capacity = 0.5
  }

  instances = {
    1 = {}
  }

  snapshot_identifier = "arn:aws:rds:us-east-1:************:cluster-snapshot:caccounts-********"
  vpc_id                 = "vpc-027971fe02a92e407" // TODO: Get from remote state
  vpc_security_group_ids = [ "sg-0b44d84b39703b8ee" ] // TODO: Get from remote state
  db_subnet_group_name   = "rds-serverless-subnet" // TODO: Get from remote state
  
  storage_encrypted   = true
  apply_immediately   = true
  monitoring_interval = 10
  skip_final_snapshot = true

  # Performance Insights configuration
  performance_insights_enabled        = true
  performance_insights_retention_period = 7
  # performance_insights_kms_key_id   = "arn:aws:kms:us-east-1:************:key/your-kms-key-id"

  # Tags
  tags = {
    environment  = "uat"
    terraform    = "true"
    name         = "tf-aurora-db-mysql-accounts-uat"
    cc           = "infrastructure"
    owner        = "Freddy"
    type         = "rds"
    service      = "rds-accounts"
  }
}

# data "aws_route53_zone" "this" {
#   name         = "talent.local"
#   private_zone = true
# }

# resource "aws_route53_record" "reader" {
#   allow_overwrite = true
#   zone_id = data.aws_route53_zone.this.zone_id
#   name    = "${basename(path.cwd)}-reader"
#   type    = "CNAME"
#   ttl     = 60
#   records = [ module.cluster.cluster_reader_endpoint ]
# }

# resource "aws_route53_record" "writer" {
#   allow_overwrite = true
#   zone_id = data.aws_route53_zone.this.zone_id
#   name    = "${basename(path.cwd)}"
#   type    = "CNAME"
#   ttl     = 60
#   records = [ module.cluster.cluster_endpoint ]
# }

# data "aws_route53_zone" "private" {
#   name         = "talent.private"
#   private_zone = true
# }

# resource "aws_route53_record" "reader2" {
#   allow_overwrite = true
#   zone_id = data.aws_route53_zone.private.zone_id
#   name    = "${basename(path.cwd)}-reader"
#   type    = "CNAME"
#   ttl     = 60
#   records = [ module.cluster.cluster_reader_endpoint ]
# }

# resource "aws_route53_record" "writer2" {
#   allow_overwrite = true
#   zone_id = data.aws_route53_zone.private.zone_id
#   name    = "${basename(path.cwd)}"
#   type    = "CNAME"
#   ttl     = 60
#   records = [ module.cluster.cluster_endpoint ]
# }