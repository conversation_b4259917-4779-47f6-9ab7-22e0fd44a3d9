apiVersion: apps/v1
kind: Deployment
metadata:
   name: jupiter-spark
spec:
   replicas: 1  
   selector:
      matchLabels:
         app: spark
   template:
      metadata:
         labels:
            app: spark
      spec:
        serviceAccountName: spark-operator-spark
        affinity:
          nodeAffinity:
            requiredDuringSchedulingIgnoredDuringExecution:
              nodeSelectorTerms:
                - matchExpressions:
                    - key: app
                      operator: In
                      values:
                        - jobs-analytics
        tolerations:
          - key: app
            operator: Equal
            value: jobs-analytics
            effect: NoSchedule
        containers:
          - name: jupiter-spark-container
            image: ************.dkr.ecr.us-east-1.amazonaws.com/jupyter/pyspark-notebook
            imagePullPolicy: IfNotPresent
            ports:
            - containerPort: 8888
            env:
            - name: JUPYTER_ENABLE_LAB
              value: "yes"
            - name: JUPYTER_TOKEN
              value: "jobsjupytersparktoken$19!"
            volumeMounts:
            - mountPath: /home/<USER>/work
              name: jobs-jupyter-storage
            - name: spark-config
              mountPath: /spark-conf
              readOnly: true
        initContainers:
          - name: volume-permissions
            image: busybox
            command: ["sh", "-c", "chown -R 1000:100 /home/<USER>/work"]
            volumeMounts:
                - mountPath: /home/<USER>/work
                  name: jobs-jupyter-storage
        volumes:
          - name: jobs-jupyter-storage
            persistentVolumeClaim:
                claimName: jobs-jupyter-pvc
          - name: spark-config
            configMap:
              name: jobs-spark-config
              items:
              - key: spark-defaults.conf
                path: spark-defaults.conf
              - key: executor-pod-template.yaml
                path: executor-pod-template.yaml
---
apiVersion: v1
kind: Service
metadata:
   name: jupiter-spark-svc
spec:
   type: NodePort
   selector:
      app: spark
   ports:
      - name: jupyter
        port: 8888
        targetPort: 8888
        nodePort: 30001
        protocol: TCP
      - name: spark
        port: 4040
        targetPort: 4040
        protocol: TCP
---
apiVersion: v1
kind: Service
metadata:
  name: jupiter-spark-driver-headless
spec:
  clusterIP: None
  selector:
    app: spark