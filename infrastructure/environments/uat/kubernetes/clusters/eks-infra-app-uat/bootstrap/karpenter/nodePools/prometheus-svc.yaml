apiVersion: karpenter.sh/v1
kind: NodePool
metadata:
  name: prometheus-svc
spec:
  template:
    metadata:
      labels:
        app: prometheus-svc
    spec:
      taints:
      - key: app
        value: prometheus-svc
        effect: NoSchedule
      nodeClassRef:
        group: karpenter.k8s.aws
        kind: EC2NodeClass
        name: monitoring-amd-ec2nc
      requirements:
      - key: app
        operator: Exists
      - key: kubernetes.io/arch
        operator: In
        values: ["amd64"]
      - key: kubernetes.io/os
        operator: In
        values: ["linux"]
      - key: karpenter.sh/capacity-type
        operator: In
        values: ["on-demand"]
      - key: "karpenter.k8s.aws/instance-family"
        operator: In
        values: ["r7i"]
      - key: "karpenter.k8s.aws/instance-cpu"
        operator: In
        values: ["8"]

  disruption:
    consolidationPolicy: WhenEmptyOrUnderutilized
    consolidateAfter: 1h
    budgets:
    - nodes: "10%"
      reasons:
      - "Empty"
      - "Drifted"
      - "Underutilized"
    - nodes: "3"
    - nodes: "0"
      schedule: "0 9 * * mon-fri"
      duration: 8h

  limits:
    cpu: 1000
