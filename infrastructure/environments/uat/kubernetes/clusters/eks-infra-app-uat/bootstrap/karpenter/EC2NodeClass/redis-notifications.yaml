apiVersion: karpenter.k8s.aws/v1
kind: EC2NodeClass
metadata:
  name: notifications-redis-amd-ec2nc
spec:
  amiFamily: AL2
  role: "KarpenterNodeRole-eks-infra-app-uat"
  subnetSelectorTerms:
  - tags:
      karpenter.sh/discovery: "true"
  securityGroupSelectorTerms:
  - tags:
      karpenter.sh/discovery: "eks-infra-app-uat"
  amiSelectorTerms:
  - id: "ami-03413b57906e5c8b2"
  blockDeviceMappings:
  - deviceName: /dev/xvda
    ebs:
      volumeSize: 40Gi
      volumeType: gp3
      iops: 3000
      encrypted: true
      deleteOnTermination: false
      throughput: 150
  tags:
    cc: marketplace
    owner: Abelardo
    group: notifications-redis
