apiVersion: karpenter.sh/v1
kind: NodePool
metadata:
  name: jobmeta-shard-4-svc
spec:
  template:
    metadata:
      labels:
        app: jobmeta-shard-4-svc
    spec:
      requirements:
        - key: app
          operator: Exists
        - key: kubernetes.io/arch
          operator: In
          values: ["amd64"]
        - key: kubernetes.io/os
          operator: In
          values: ["linux"]
        - key: karpenter.sh/capacity-type
          operator: In
          values: ["on-demand"]
        - key: node.kubernetes.io/instance-type
          operator: In
          values: ["r5dn.2xlarge"]
      nodeClassRef:
        group: karpenter.k8s.aws
        kind: EC2NodeClass
        name: jobmeta-raid0-amd-ec2nc
      taints:
        - key: app
          value: jobmeta-shard-4-svc
          effect: NoSchedule
  limits:
    cpu: 400
  disruption:
    consolidationPolicy: WhenEmptyOrUnderutilized
    consolidateAfter: 1h
    budgets:
    - nodes: "10%"
      reasons:
      - "Empty"
      - "Drifted"
      - "Underutilized"
    - nodes: "3"
    - nodes: "0"
      schedule: "0 9 * * mon-fri"
      duration: 8h
      reasons: 
      - "Underutilized"
