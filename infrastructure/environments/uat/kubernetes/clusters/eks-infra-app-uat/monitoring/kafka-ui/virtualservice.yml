apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: kafka-ui-virtualservice
  namespace: monitoring
  labels:
    app: kafka-ui
spec:
  hosts:
    - kafka-ui.talent.com
    - kafka-ui.monitoring.talent.com
    - kafka-ui.eks-infra-app-uat.uat.talent.com
  gateways:
    - apps/apps-gateway
  http:
    - match:
        - uri:
            prefix: /
      route:
        - destination:
            host: kafka-ui-service.monitoring.svc.cluster.local
            port:
              number: 8080