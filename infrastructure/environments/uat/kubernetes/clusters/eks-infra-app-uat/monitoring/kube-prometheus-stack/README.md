kubectl create secret generic scrape-config --from-file scrape -n monitoring


helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
helm repo update
helm upgrade kube-prometheus-stack prometheus-community/kube-prometheus-stack -f values.yaml -n monitoring
 kubectl create secret generic scrape-config --from-file /home/<USER>/talent/infrastructure/environments/uat/kubernetes/clusters/eks-infra-app-uat/monitoring/kube-prometheus-stack/scrape -n monitoring
secret/scrape-config created
➜  postgres-jobs git:(feature/uat-resource) ✗ kubectl create secret generic ldap-toml --from-file /home/<USER>/talent/infrastructure/environments/uat/kubernetes/clusters/eks-infra-app-uat/monitoring/kube-prometheus-stack/ldap-toml -n monitoring
secret/ldap-toml 
k apply -f /home/<USER>/talent/infrastructure/environments/uat/kubernetes/clusters/eks-infra-app-uat/monitoring/kube-prometheus-stack/sa-prometheus.yaml -n monitoring