apiVersion: networking.istio.io/v1beta1
kind: Gateway
metadata:
  name: gateway-public-notifications
spec:
  selector:
    istio: ingress-gw-public-notifications
  servers:
  - hosts:
    - '*.talent.com'
    - '*.uat.talent.com'
    port:
      name: http
      number: 80
      protocol: HTTP
  - hosts:
    - '*.talent.com'
    - '*.uat.talent.com'
    port:
      name: https
      number: 443
      protocol: HTTP #TODO
    #tls:
      #mode: SIMPLE
      #credentialName: "tls-secret"