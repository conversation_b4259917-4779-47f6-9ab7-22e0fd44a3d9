apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: geo-kibana-virtualservice
  labels:
    app: geo-elasticsearch
spec:
  hosts:
    - geo-elasticsearch.talent.com
  gateways:
    - gateway-elastic-geo
  tcp:
    - match:
        - port: 5601
      route:
        - destination:
            host: geo-kibana-kb-http.elastic-system.svc.cluster.local
            port:
              number: 5601