apiVersion: kibana.k8s.elastic.co/v1
kind: Kibana
metadata:
  name: monitoring-kibana
  namespace: elastic-system
spec:
  version: 8.13.4
  count: 3
  http:
    tls:
      selfSignedCertificate:
        disabled: true
  elasticsearchRef:
    name: monitoring-elasticsearch
  podTemplate:
    spec:
      tolerations:
        - key: app
          operator: Equal
          value: mem-optimized-monitoring-svc
          effect: NoSchedule
      nodeSelector:
        app: mem-optimized-monitoring-svc
      containers:
      - name: kibana
        env:
          - name: NODE_OPTIONS
            value: "--max-old-space-size=2048"
        resources:
          requests:
              memory: 50Mi
              cpu: 100m
          limits:
            memory: 2.5Gi
            cpu: 2