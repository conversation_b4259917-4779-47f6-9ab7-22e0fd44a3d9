installCRDs: true

replicaCount: 1

image:
  repository: 891376983520.dkr.ecr.us-east-1.amazonaws.com/databases/elasticsearch/eck-operator
  pullPolicy: IfNotPresent
  tag: 2.12.1

resources:
  limits:
    cpu: 1
    memory: 1Gi
  requests:
    cpu: 100m
    memory: 150Mi

webhook:
  # enabled determines whether the webhook is installed.
  enabled: true
config:
  # logVerbosity defines the logging level. Valid values are as follows:
  # -2: Errors only
  # -1: Errors and warnings
  #  0: Errors, warnings, and information
  #  number greater than 0: Errors, warnings, information, and debug details.
  logVerbosity: "0"
  # containerRegistry: 891376983520.dkr.ecr.us-east-1.amazonaws.com/databases/elasticsearch

  metrics:
    port: "9090"