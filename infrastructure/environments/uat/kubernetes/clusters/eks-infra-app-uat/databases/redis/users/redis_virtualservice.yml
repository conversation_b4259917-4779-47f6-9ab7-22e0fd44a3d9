apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  namespace: redis
  name: users-redis-virtualservice
  labels:
    app: users-redis
spec:
  hosts:
    - users-redis.talent.com
  gateways:
    - gw-redis-users
  tcp:
    - match:
        - port: 6379
      route:
        - destination:
            host: users-redis-service.redis.svc.cluster.local
            port:
              number: 6379