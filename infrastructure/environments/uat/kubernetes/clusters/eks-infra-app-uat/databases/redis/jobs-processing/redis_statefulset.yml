apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: jobs-processing-redis
  namespace: redis
spec:
  serviceName: jobs-processing-redis-service
  replicas: 1
  selector:
    matchLabels:
      app: jobs-processing-redis
  template:
    metadata:
      labels:
        app: jobs-processing-redis
    spec:
      tolerations:
        - effect: NoSchedule
          operator: "Equal"
          key: app
          value: jobs-processing-redis
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: app
                    operator: In
                    values:
                      - "jobs-processing-redis"
      containers:
      - name: jobs-processing-redis
        image: 891376983520.dkr.ecr.us-east-1.amazonaws.com/databases/redis/redis:6.0
        command: ["redis-server", "/usr/local/etc/redis/redis.conf"]
        ports:
        - containerPort: 6379
        resources:
          requests:
            memory: "20Gi"
            cpu: "250m"
          limits:
            memory: "30Gi"
            cpu: "4000m"
        volumeMounts:
        - name: redis-config
          mountPath: /usr/local/etc/redis
        - name: redis-data
          mountPath: /usr/share/data
      volumes:
      - name: redis-config
        configMap:
          name: jobs-processing-redis-config
          items:
          - key: redis.conf
            path: redis.conf
  volumeClaimTemplates:
  - metadata:
      name: redis-data
    spec:
      accessModes: [ "ReadWriteOnce" ]
      resources:
        requests:
          storage: 20Gi
      storageClassName: redis-storage-class