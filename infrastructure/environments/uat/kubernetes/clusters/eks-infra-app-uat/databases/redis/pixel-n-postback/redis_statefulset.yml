apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: pixel-n-postback-redis
  namespace: redis
spec:
  serviceName: pixel-n-postback-redis-service
  replicas: 1
  selector:
    matchLabels:
      app: pixel-n-postback-redis
  template:
    metadata:
      labels:
        app: pixel-n-postback-redis
    spec:
      tolerations:
        - effect: NoSchedule
          operator: "Equal"
          key: app
          value: pixel-n-postback-redis
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: app
                    operator: In
                    values:
                      - "pixel-n-postback-redis"
      containers:
      - name: pixel-n-postback-redis
        image: 891376983520.dkr.ecr.us-east-1.amazonaws.com/databases/redis/redis:6.0
        command: ["redis-server", "/usr/local/etc/redis/redis.conf"]
        ports:
        - containerPort: 6379
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "4096Mi"
            cpu: "4000m"
        volumeMounts:
        - name: redis-config
          mountPath: /usr/local/etc/redis
        - name: redis-data
          mountPath: /usr/share/data
      volumes:
      - name: redis-config
        configMap:
          name: pixel-n-postback-redis-config
          items:
          - key: redis.conf
            path: redis.conf
  volumeClaimTemplates:
  - metadata:
      name: redis-data
    spec:
      accessModes: [ "ReadWriteOnce" ]
      resources:
        requests:
          storage: 20Gi
      storageClassName: redis-storage-class