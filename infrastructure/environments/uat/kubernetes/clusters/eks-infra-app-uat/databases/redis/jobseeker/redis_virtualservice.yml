apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  namespace: redis
  name: jobseekers-redis-virtualservice
  labels:
    app: jobseeker-redis
spec:
  hosts:
    - jobseeker-redis.talent.com
  gateways:
    - gw-redis-jobseeker
  tcp:
    - match:
        - port: 6379
      route:
        - destination:
            host: jobseeker-redis-service.redis.svc.cluster.local
            port:
              number: 6379