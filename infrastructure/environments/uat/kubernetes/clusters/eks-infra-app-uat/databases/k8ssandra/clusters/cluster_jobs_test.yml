apiVersion: k8ssandra.io/v1alpha1
kind: K8ssandraCluster
metadata:
  name: jobs1
spec:
  cassandra:
    serverVersion: "4.0.4"
    clusterName: "jobs1"
    telemetry:
      prometheus:
        enabled: true
      mcac:
        enabled: false
    tolerations:
      - effect: NoSchedule
        key: app
        operator: Equal
        value: k8ssandra-jobstest
    resources:
      requests:
        memory: 20Gi
        cpu: 7000m
      limits:
        memory: 22Gi
    datacenters:
      - metadata:
          name: j1-dc1
        size: 6
        stopped: false
        racks:
          - name: rack1
            nodeAffinityLabels:
              app: k8ssandra-jobstest
          - name: rack2
            nodeAffinityLabels:
              app: k8ssandra-jobstest
          - name: rack3
            nodeAffinityLabels:
              app: k8ssandra-jobstest
        storageConfig:
          cassandraDataVolumeClaimSpec:
            storageClassName: gp3
            accessModes:
              - ReadWriteOnce
            resources:
              requests:
                storage: 500Gi
        config:
          jvmOptions:
            heapSize: 8Gi
          cassandraYaml:
            batch_size_warn_threshold_in_kb: 50
            batch_size_fail_threshold_in_kb: 100
            internode_compression: all
            memtable_allocation_type: heap_buffers
            commitlog_total_space_in_mb: 4096
            commitlog_segment_size_in_mb: 128
            concurrent_writes: 64
            
  # reaper:
  #   httpManagement:
  #     enabled: true
  #   heapSize: 8Gi
  #   autoScheduling:
  #     enabled: false
  #   telemetry:
  #     prometheus:
  #       enabled: true

  # medusa:
  #   cassandraUserSecretRef:
  #     name: medusa-secret
  #   storageProperties:
  #     storageProvider: s3
  #     bucketName: talent-uat-cassandra-backups
  #     credentialsType: role-based
  #     storageSecretRef:
  #       name: medusa-bucket-key
  #     secure: false
  #     region: us-east-1

  # stargate:
  #   size: 1
  #   heapSize: 3Gi
  #   tolerations:
  #     - effect: NoSchedule
  #       key: app
  #       operator: Equal
  #       value: k8ssandra-jobstest
  