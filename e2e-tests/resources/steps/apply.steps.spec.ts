import { Page } from "playwright";
const { test, expect } = require("@playwright/test");
import ApplyJobs from "../pages/apply.page";
import * as selectors from "../selectors.json";

/**
 *
 */
export default class ApplyJobsSteps {
  /**
   *
   */
  constructor(page: Page, applyJobs: ApplyJobs) {
    this.page = page;
    this.applyJobs = applyJobs;
  }

  private page: Page;
  private applyJobs: ApplyJobs;

  /**
   *
   */
  async clickJob() {
    await test.step("Clicking the apply button", async () => {
      // await this.page.waitForSelector(selectors.jobs.searchJobsSpan);
      // await this.page.searhJobSpan().click();
      // await this.page.waitForSelector(selectors.jobs.cardJobs);
      // await this.applyJobs.jobCard().click();
      await this.page.waitForSelector(selectors.apply.applyButton);
      await this.applyJobs.applyButton().click();
      await this.page.waitForLoadState('load');
      await this.page.waitForTimeout(2000);
      await expect(selectors.apply.buttonCloseModal).toBeDefined();
      await expect(selectors.apply.buttonSubmit).toBeDefined();
    });
  }
  /**
   *
   */
  async modalCheck() {
    await test.step("Checking modal load", async () => {
      await expect(selectors.apply.buttonCloseModal).toBeDefined();
      await expect(selectors.apply.buttonSubmit).toBeDefined();
    });
  }
}
