import styled from "styled-components";
import theme from "@talent-front-libs/ui/theme/themes";
import { BaseButton } from "../..";

export const ButtonAlertStyle = styled(BaseButton).attrs((props) => ({
  ...props,
  size: "small",
}))`
  height: 36px;
  padding: 0 16px;
  gap: 4px;
  background-color: ${theme.light.color.purple[500]};
  border-color: ${theme.light.color.white};
  color: ${theme.light.color.white};
`;
