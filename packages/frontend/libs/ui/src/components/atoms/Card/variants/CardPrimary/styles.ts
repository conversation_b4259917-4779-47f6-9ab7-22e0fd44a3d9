import styled, { css } from "styled-components";
import theme from "@talent-front-libs/ui/theme/themes";
import { CardPrimaryStyleProps } from "./interfaces";
import { BaseCard } from "../..";

const colorsCards = {
  primary: {
    backgroundColor: theme.light.color.white,
    border: theme.light.color.white,
    borderActive: theme.light.color.purple[500],
    ShadowHover: theme.light.shadow.sm3,
  },
};

export const CardPrimaryIsActive = css`
  border-color: ${colorsCards.primary.borderActive};
}
`;
export const CardPrimaryContainer = styled(BaseCard)<CardPrimaryStyleProps>`
  position: relative;
  background-color: ${colorsCards.primary.backgroundColor};
  border-radius: 4px;
  border: 1px solid ${colorsCards.primary.border};
  transition: box-shadow 0.2s;
  padding: 16px;
  cursor: pointer;
  flex: 1;
  gap: 10px;
  &:hover,
  &:focus {
    box-shadow: ${colorsCards.primary.ShadowHover};
    outline: none;
  }
  ${(p) => p.$isActive && CardPrimaryIsActive};
`;
