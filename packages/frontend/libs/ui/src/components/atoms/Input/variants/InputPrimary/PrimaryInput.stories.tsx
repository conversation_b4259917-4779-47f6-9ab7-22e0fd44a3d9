import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { InputPrimary } from ".";

const meta: Meta<typeof InputPrimary> = {
  title: "Components/atoms/Input/variants/InputPrimary",
  component: InputPrimary,
  parameters: {
    backgrounds: {
      default: 'White'
    }         
  },
  argTypes: {
    disabled: {
      control: "boolean",
      description: "If true, the input will be disabled.",
    },
    warning: {
      control: "text",
      description: "Displays a warning message below the input.",
    },
    supportingText: {
      control: "text",
      description: "Displays supporting text or instructions related to the input.",
    },
    label: {
      control: "text",
      description: "The label for the input.",
    },
    value: {
      control: "text",
      description: "The input value.",
    },
  },
};
export default meta;
type Story = StoryObj<typeof InputPrimary>;

export const Default: Story = {
  args: {
    label: "Name",
    value: "",
    disabled: false,
  },
};
