import { forwardRef, useImperativeHandle, useRef, useState } from "react";
import { StyledInputWrapper } from "./styles";
import AuthCode, { AuthCodeRef } from "./validator";

/**
 *
 */
export const InputCodeValidator = forwardRef(({onChange, disabled} : {onChange: any; disabled: any}, ref) => {

  const [isError, setIsError] = useState(false)

  const authCodeRef = useRef<AuthCodeRef>(null);

  useImperativeHandle(ref, () => ({
    clear: () => authCodeRef.current?.clear(),
    focus: () => authCodeRef.current?.focus(),
    onError: () => {
      setIsError(true)
    }
  }));

  const handleOnChange = (e: any) => {
    if (isError) {
      setIsError(false)
    }
    onChange(e);
  };

  return (
    <StyledInputWrapper isError={isError} >
      <AuthCode allowedCharacters="numeric" ref={authCodeRef} onChange={handleOnChange} disabled={disabled} />
    </StyledInputWrapper>
  );
});
