import React from "react";
import { FormFieldProps } from "./interfaces";
import { InputStateStylesType } from "../Input/variants/InputPrimary/interfaces";
import {
  Label,
  Messages,
  InputPrimaryContainer,
  StyledInputWrapper,
  TextMessage,
  Wrapper,
} from "./styles";

/**
 * Common Form field
 * Base component to build forms
 *
 * @component
 * @example
 * <FormField
 *  name="password"
 *  type="password"
 *  label="Password"
 *  placeholder="Enter your password"
 *  inputMode="text"
 *  register={register}
 * />
 *
 * @param {PrimaryInputProps} props - The properties passed to the `InputPrimary` component.
 * @param {string} props.warning - Optional warning message to display an error state of the input.
 * @param {string} props.supportingText - Optional text providing additional information or guidance to the user.
 * @param {InputIconStyle} props.iconStyle - Optional style and behavior properties for an icon within the input field.
 * Other inherited properties from `BaseInputProps`:
 * @param {React.HTMLInputTypeAttribute} props.type - The HTML input type (e.g., text, email).
 * @param {"decimal" | "email" | "numeric" | "text"} props.inputMode - Specifies the mode of input, such as numeric or text.
 * @param {string} props.name - The name of the input element, used when submitting a form.
 * @param {string | number} [props.value] - The input's current value.
 * @param {string} [props.label] - The text label associated with the input field.
 * @param {string} [props.id] - The unique identifier for the input field.
 * @param {string} [props.className] - CSS class for additional styling.
 * @param {boolean} [props.isMobile] - Indicates if the input should adapt to mobile styling.
 * @param {boolean} [props.disabled] - If true, the input will be disabled and non-interactive.
 * @param {string} [props.placeholder] - Placeholder text for the input field.
 * @param {string} [props.datatestid] - Test ID for testing purposes.
 * @param {(e?: React.ChangeEvent<HTMLInputElement>) => void} [props.onChange] - Handler for change events.
 * @param {(e?: React.FocusEvent<HTMLInputElement, Element>) => void} [props.onFocus] - Handler for focus events.
 * @param {(e?: React.FocusEvent<HTMLInputElement, Element>) => void} [props.onBlur] - Handler for blur events.
 * @param {(e?: React.KeyboardEvent<HTMLInputElement>) => void} [props.onKeyDown] - Handler for key down events.
 */
const FormField: React.FC<FormFieldProps> = ({
  register,
  disabled = false,
  error,
  warning,
  value,
  label,
  supportingText,
  iconStyle,
  isMobile = false,
  ...rest
}) => {
  // Input State
  let inputState: InputStateStylesType;

  if (disabled) {
    inputState = "disabled";
  } else if (warning) {
    inputState = "error";
  } else if (value !== "") {
    inputState = "filled";
  } else {
    inputState = "default";
  }

  return (
    // Outer Wrapper
    <Wrapper $isMobile={isMobile} data-testid="wrapper">
      {/* Inner Input Container */}
      <InputPrimaryContainer $inputState={inputState} data-testid="primaryInputContainer">
        {/* Label */}
        <Label $inputState={inputState}>{label}</Label>
        {/* Isolated Input Styled Wrapper */}
        <StyledInputWrapper $isDisabled={disabled}>
          <input disabled={disabled} {...rest} {...register(rest.name)} />
        </StyledInputWrapper>
        {/* Left Action Icon */}
        {iconStyle && (
          <iconStyle.Icon
            title={iconStyle.title}
            width={iconStyle.width ?? "24px"}
            height={iconStyle.height ?? "24px"}
            onClick={iconStyle.onClick}
            style={iconStyle.onClick && { cursor: "pointer" }}
          />
        )}
      </InputPrimaryContainer>
      {/* Error messages */}
      {(warning || supportingText) && (
        <Messages>
          {supportingText && (
            <TextMessage $type="text" $disabled={disabled}>
              {supportingText}
            </TextMessage>
          )}
          {warning && (
            <TextMessage $type="error" $disabled={disabled}>
              {warning}
            </TextMessage>
          )}
        </Messages>
      )}
      {error && <span>{error.message}</span>}
    </Wrapper>
  );
};

export default FormField;
