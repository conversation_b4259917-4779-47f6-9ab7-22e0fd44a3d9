import styled, { css } from "styled-components";
import theme from "@talent-front-libs/ui/theme/themes";
import { BodyXsRg } from "@talent-front-libs/ui/theme/typography";
import {
  LabelStylesProps,
  PrimeryStylesWrapperProps,
  StyleInputPrimaryContainerProps,
  StyleInputPrimaryProps,
  TextMessageStylesProps,
} from "../Input/variants/InputPrimary/interfaces";

const { gray, purple, red } = theme.light.color;

const colorsInput = {
  primary: {
    backgroundColor: gray[25],
    textColor: gray[800],
    hoverBackgroundColor: gray[50],
    hoverBorderColor: gray[50],
    supportTextColor: gray[400],
    errorSupportTextColor: red[300],
    placeHolderColor: gray[800],
  },
};

const styleTypeColors = {
  default: {
    border: gray[25],
    labelColor: gray[400],
    textColor: gray[800],
    supportingTextColor: gray[400],
  },
  focused: {
    border: purple[500],
    labelColor: purple[500],
    textColor: gray[800],
    supportingTextColor: gray[400],
  },
  disabled: {
    border: gray[25],
    labelColor: gray[800],
    textColor: gray[800],
    supportingTextColor: gray[400],
  },
  error: {
    border: red[300],
    labelColor: red[300],
    textColor: gray[800],
    supportingTextColor: gray[400],
  },
  filled: {
    border: gray[25],
    labelColor: purple[500],
    textColor: gray[800],
    supportingTextColor: gray[400],
  },
};
const DisabledStyle = css`
  color: ${styleTypeColors.disabled.textColor};
  opacity: 0.5;
`;

/**
 * Wrapper Added
 */
export const Wrapper = styled.div<PrimeryStylesWrapperProps>`
  width: 100%;
  max-width: 100%;
  display: flex;
  flex-direction: column;
  gap: ${(p) => (p.$isMobile ? "8px" : "6px")};
`;

/**
 * Label
 */
export const Label = styled(BodyXsRg)<LabelStylesProps>`
  position: absolute;
  top: 8px;
  color: ${(p) => styleTypeColors[p.$inputState].labelColor};
  transition: color 0.3s ease;
  ${(p) => p.$inputState === "disabled" && DisabledStyle}
`;

/**
 * Input Container
 */
export const InputPrimaryContainer = styled.div<StyleInputPrimaryContainerProps>`
  width: 100%;
  height: 58px;
  position: relative;
  display: flex;
  align-items: center;
  flex-direction: row;
  border-radius: 100px;
  padding: 8px 24px;
  gap: 4px;
  background-color: ${colorsInput.primary.backgroundColor};
  border: 1px solid;
  border-color: ${(p) => styleTypeColors[p.$inputState].border};
  &:focus-within {
    border-color: ${styleTypeColors.focused.border};
    ${Label} {
      color: ${styleTypeColors.focused.labelColor};
    }
  }
`;

/**
 * Input Isolated Styled
 */
export const StyledInputWrapper = styled.div<StyleInputPrimaryProps>`
  width: 100%;
  background-color: transparent;
  color: ${colorsInput.primary.textColor};
  flex: 1;
  cursor: ${(p) => (p.$isDisabled ? "not-allowed" : "text")};
  font-weight: ${theme.light.fontWeight.regular};
  outline: none;
  border: none;
  font-size: 16px;
  line-height: 20px;
  margin-bottom: -19px;
  ${(p) => p.$isDisabled && DisabledStyle}
  & input {
    width: 100%;
    height: 36px;
  }
`;

export const Messages = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 3px;
  padding: 0 24px;
`;

export const TextMessage = styled(BodyXsRg)<TextMessageStylesProps>`
  color: ${(p) =>
    p?.$type === "error"
      ? colorsInput.primary.errorSupportTextColor
      : colorsInput.primary.supportTextColor};
  line-height: 14px;
  ${(p) =>
    p.$disabled &&
    ` opacity: 0.5;
  `}
`;
