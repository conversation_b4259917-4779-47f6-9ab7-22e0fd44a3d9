import styled, { css } from "styled-components";
import theme from "../../../../../theme/themes";
import { BaseAvatar } from "../../";

interface AvatarProfileContainerStyle {
  $hasLabel: boolean;
}

const hasLabelStyle = css`
  background-color: ${theme.light.color.purple[500]};
  color: ${theme.light.color.white};
`;

export const AvatarProfileContainer = styled(BaseAvatar)<AvatarProfileContainerStyle>`
  width: 56px;
  height: 56px;
  border-radius: 77px;
  background-color: ${theme.light.color.gray[50]};
  & svg {
    font-size: 27px;
  }
  ${(props) => props?.$hasLabel && hasLabelStyle}
`;
