import { BaseAvatarProps } from "./interfaces";
import { AvatarContainer } from "./styles";

/**
 * This component is used for profile, it displays the initials of the user and if there are none
 * it displays a user icon by default
 * @param label initials of the user
 * @param icon in case you do not want to use the default icon
 * @returns
 */
export const BaseAvatar = ({ children, className, testId }: BaseAvatarProps) => {
  return (
    <AvatarContainer className={className} data-testid={testId}>
      {children}
    </AvatarContainer>
  );
};
