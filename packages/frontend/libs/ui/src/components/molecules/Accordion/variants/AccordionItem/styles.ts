import styled, { css } from "styled-components";
import { BodySRg, BodySB } from "@talent-front-libs/ui/theme/typography";
import Themes from "@talent-front-libs/ui/theme/themes";

export const Container = styled.div`
  display: flex;
  flex-direction: column;
  gap: 10px;
  width: 100%;
  height: 250px;
  overflow-y: auto;
  padding-right: 10px;
`;

export const AccordionItemWrapper = styled.div`
  display: flex;
  flex-direction: column;
  margin-bottom: 10px;

  &:last-child {
    margin-bottom: 0;
  }
`;

interface HasErrorItem {
  $hasError: boolean;
}
const hasErrorlStyle = css`
  border: 1px solid ${Themes.light.color.orange[400]};
`;
export const Item = styled.div<HasErrorItem>`
  display: flex;
  flex-direction: column;
  border: 1px solid ${Themes.light.color.gray[200]};
  width: 100%;
  padding: 20px;
  min-height: 77px;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  position: relative;

  li {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  ${(props) => props?.$hasError && hasErrorlStyle}

`;

export const QuestionText = styled(BodySB)<HasErrorItem>`
  color: ${(props) => props?.$hasError ? Themes.light.color.red[300] : Themes.light.color.black};
  margin-bottom: 10px;
`;

export const AnswerText = styled(BodySRg)``;

export const StyledIconEdit = styled.div`
  width: 20px;
  height: 20px;
  cursor: pointer;
`;

export const StyledColumnItem = styled.div`
  display: flex;
  flex-direction: column;
`;

export const ErrorText = styled(BodySRg)`
  color: ${Themes.light.color.red[300]};
  margin-top: 8px;
`;
