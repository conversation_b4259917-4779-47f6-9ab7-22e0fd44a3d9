import { InputFilterProps } from "@talent-front-libs/ui/components/atoms/Input/variants/InputFilter/interfaces";
import { ButtonsModalFilterProps, ModalFilterProps } from "../../interfaces";

export interface InnerContentFilterInputProps
  extends Omit<InputFilterProps, "onChangeValue">,
    Omit<ButtonsModalFilterProps, "onUpdate"> {
  isMobile: boolean;
  isOpen: boolean;
  onSetFilterValue: (value: string) => void;
  onClose: () => void;
}

export interface ContentFilterInputProps
  extends Omit<ModalFilterProps, "children">,
    Omit<InnerContentFilterInputProps, "onUpdate"> {}

export interface FilterInputProps
  extends Omit<InputFilterProps, "onChangeValue" | "onEnterKey" | "onBlur">,
    Omit<ButtonsModalFilterProps, "onUpdate"> {
  title: string;
  label: string;
  isActive: boolean;
  isMobile: boolean;
  onSetFilterValue: (value: string) => void;
}
