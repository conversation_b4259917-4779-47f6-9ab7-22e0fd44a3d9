import styled, { css } from "styled-components";
import { InputFilter } from "@talent-front-libs/ui/components/atoms/Input/variants/InputFilter";

interface InputFilterInputStyleProps {
  $isMobile: boolean;
}

const isMobileInputStyle = css`
  width: 100%;
`;

export const InputFilterInputStyle = styled(InputFilter)<InputFilterInputStyleProps>`
  width: 252px;

  ${(props) => props.$isMobile && isMobileInputStyle}
`;

export const InnerContentFilterInputStyle = styled.div`
  width: 100%;
  height: auto;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  justify-content: center;
  gap: 24px;
`;
