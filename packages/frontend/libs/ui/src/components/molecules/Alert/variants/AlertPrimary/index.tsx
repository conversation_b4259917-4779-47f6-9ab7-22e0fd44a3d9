import { BodySRg } from "@talent-front-libs/ui/theme/typography";
import { AlertPrimaryProps } from "./interfaces";
import { AlertPrimaryStyle } from "./styles";

/**
 * @name AlertPrimary
 */
export const AlertPrimary = ({
  content,
  isScrolled,
  isMobile,
  className,
  testId,
}: AlertPrimaryProps) => {
  return (
    <AlertPrimaryStyle
      isScrolled={isScrolled}
      isMobile={isMobile}
      className={className}
      data-testid={testId}
    >
      <BodySRg as="p">{content}</BodySRg>
    </AlertPrimaryStyle>
  );
};
