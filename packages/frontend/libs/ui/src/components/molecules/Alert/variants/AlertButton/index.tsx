import { BodySSb, BodyXsRg } from "@talent-front-libs/ui/theme/typography";
import { AlertButtonProps } from "./interfaces";
import { AlertButtonStyle, ContainerTextStyle } from "./styles";
import { ButtonAlert } from "@talent-front-libs/ui/components/atoms/Button/variants/ButtonAlert";

/**
 * @name AlertButton
 */
export const AlertButton = ({
  title,
  content,
  buttonLabel,
  onButton,
  className,
  testId,
  isMobile = false,
  isScrolled,
}: AlertButtonProps) => {
  return (
    <AlertButtonStyle
      isScrolled={isScrolled}
      isMobile={isMobile}
      className={className}
      data-testid={testId}
    >
      <ContainerTextStyle>
        <BodySSb as="h5">{title}</BodySSb>
        <BodyXsRg as="p">{content}</BodyXsRg>
      </ContainerTextStyle>
      <ButtonAlert onClick={onButton} label={buttonLabel} />
    </AlertButtonStyle>
  );
};
