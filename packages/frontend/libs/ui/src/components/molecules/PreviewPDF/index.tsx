"use client";

import dynamic from 'next/dynamic';

const PDFViewer = dynamic(() => import('@react-pdf/renderer').then(mod => mod.PDFViewer), {
  ssr: false,
  loading: () => <p>Loading PDF viewer...</p>
});

export interface PdfProps {
  height: number;
  width: number;
  showToolbar?: boolean;
  children: JSX.Element;
}

/**
 * Invoice Preview Page.
 */
const PreviewPDF = ({ height, width, showToolbar, children }: PdfProps) => {
  return (
    <PDFViewer height={height} width={width} showToolbar={showToolbar} data-testid="pdf-viewer">
      {children}
    </PDFViewer>
  );
};

export default PreviewPDF;
