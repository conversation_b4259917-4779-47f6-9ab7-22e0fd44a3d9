export interface SetInSessionStorageProps {
  key: string;
  value: unknown;
}
export interface GetFromSessionStorageProps {
  key: string;
}
export interface RemoveFromSessionStorageProps {
  key: string;
}

const isClientSide = typeof window !== "undefined";

/** */
export function setInSessionStorage({ key, value }: SetInSessionStorageProps) {
  if (isClientSide) {
    sessionStorage.setItem(key, JSON.stringify(value));
  }
}

/** */
export function getFromSessionStorage({ key }: GetFromSessionStorageProps) {
  if (isClientSide) {
    try {
      return JSON.parse(sessionStorage.getItem(key) ?? "null");
    } catch {
      return undefined;
    }
  }
}

/** */
export function removeFromSessionStorage({ key }: RemoveFromSessionStorageProps) {
  if (isClientSide) {
    sessionStorage.removeItem(key);
  }
}
