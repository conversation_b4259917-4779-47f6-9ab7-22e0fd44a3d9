export interface SetInLocalStorageProps {
  key: string;
  value: unknown;
}
export interface GetFromLocalStorageProps {
  key: string;
}
export interface RemoveFromLocalStorageProps {
  key: string;
}

const isClientSide = typeof window !== "undefined";

/** */
export function setInLocalStorage({ key, value }: SetInLocalStorageProps) {
  if (isClientSide) {
    localStorage.setItem(key, JSON.stringify(value));
  }
}

/** */
export function getFromLocalStorage({ key }: GetFromLocalStorageProps) {
  if (isClientSide) {
    const value = JSON.parse(localStorage?.getItem(key) ?? "{}");

    return Object.keys(value).length !== 0 ? value : undefined;
  }
}

/**  */
export function removeFromLocalStorage({ key }: RemoveFromLocalStorageProps) {
  if (isClientSide) {
    localStorage.removeItem(key);
  }
}
