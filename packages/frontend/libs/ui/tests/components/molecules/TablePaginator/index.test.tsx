import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import { TablePaginator } from "@talent-front-libs/ui/components/molecules/TablePaginator";

/**
 * Test suite for the TablePaginator component.
 */
describe("TablePaginator component", () => {
  const mockGotoPage = jest.fn();
  const mockPreviousPage = jest.fn();
  const mockNextPage = jest.fn();

  /**
   * Helper function to render the TablePaginator component.
   * @param {number} pageCount - The total number of pages.
   * @param {number} pageIndex - The current page index.
   */
  const renderComponent = (pageCount: number, pageIndex: number) => {
    render(
      <TablePaginator
        pageCount={pageCount}
        pageIndex={pageIndex}
        gotoPage={mockGotoPage}
        previousPage={mockPreviousPage}
        nextPage={mockNextPage}
      />,
    );
  };

  /**
   * Test to verify that gotoPage is called with the correct index when page buttons are clicked.
   */
  it("calls gotoPage with correct index when page buttons are clicked", () => {
    renderComponent(5, 2);

    fireEvent.click(screen.getByTestId("pageButton-1"));
    expect(mockGotoPage).toHaveBeenCalledWith(0);

    fireEvent.click(screen.getByTestId("pageButton-5"));
    expect(mockGotoPage).toHaveBeenCalledWith(4);
  });

  /**
   * Test to verify that nextPage and previousPage are called correctly.
   */
  it("calls nextPage and previousPage correctly", () => {
    renderComponent(5, 2);

    const nextButton = screen.getByTestId("next-button");
    const prevButton = screen.getByTestId("previous-button");

    fireEvent.click(nextButton);
    expect(mockNextPage).toHaveBeenCalled();

    fireEvent.click(prevButton);
    expect(mockPreviousPage).toHaveBeenCalled();
  });

  /**
   * Test to verify that gotoPage is called with the first and last page correctly.
   */
  it("calls gotoPage with first and last page correctly", () => {
    renderComponent(5, 2);

    const firstButton = screen.getByTestId("first-button");
    const lastButton = screen.getByTestId("last-button");

    fireEvent.click(firstButton);
    expect(mockGotoPage).toHaveBeenCalledWith(0);

    fireEvent.click(lastButton);
    expect(mockGotoPage).toHaveBeenCalledWith(4);
  });
});
