
import { render, screen, waitFor } from '@testing-library/react';
import { Toast } from "@talent-front-libs/ui/components/molecules/Toast";
import "@testing-library/jest-dom";
import theme from "@talent-front-libs/ui/theme/themes"; // Import the theme to check styles

describe('Toast component', () => {
    it('renders the Toast component with a message', () => {
      render(<Toast message="Test Message" />);
      expect(screen.getByText('Test Message')).toBeInTheDocument();
    });
  
    it('applies the correct styles for "success" type', () => {
      render(<Toast message="Success Message" type="success" />);
      const toastElement = screen.getByText('Success Message');
      expect(toastElement).toHaveStyle(`background-color: ${theme.light.color.purple[500]}`);
    });
  
    it('applies the correct styles for "error" type', () => {
      render(<Toast message="Error Message" type="error" />);
      const toastElement = screen.getByText('Error Message');
      expect(toastElement).toHaveStyle(`background-color: ${theme.light.color.notification.error}`);
    });
  
    it('applies the correct styles for "info" type', () => {
      render(<Toast message="Info Message" type="info" />);
      const toastElement = screen.getByText('Info Message');
      expect(toastElement).toHaveStyle(`background-color: ${theme.light.color.gray[300]}`);
    });
  
    it('disappears after the duration', async () => {
      render(<Toast message="Timed Message" duration={1000} />);
      const toastElement = screen.getByText('Timed Message');
      expect(toastElement).toBeInTheDocument();
  
      await waitFor(() => {
        expect(toastElement).not.toBeVisible();
      }, { timeout: 1500 });
    });
  
    it('calls the onClose callback when the Toast disappears', async () => {
      const onCloseMock = jest.fn();
      render(<Toast message="Callback Message" duration={1000} onClose={onCloseMock} />);
      
      await waitFor(() => {
        expect(onCloseMock).toHaveBeenCalledTimes(1);
      }, { timeout: 1500 });
    });
  
    it('cleans up the timer when the component is unmounted', () => {
      jest.useFakeTimers();
  
      const { unmount } = render(<Toast message="Unmount Message" duration={3000} />);
      unmount();
  
      jest.runOnlyPendingTimers();
      jest.useRealTimers();
    });
  
    it('is visible immediately upon render', () => {
      render(<Toast message="Immediate Visibility" />);
      const toastElement = screen.getByText('Immediate Visibility');
      expect(toastElement).toHaveStyle('opacity: 1');
    });
  
    it('fades out when visible state is false', async () => {
      render(<Toast message="Fade Out Message" duration={1000} />);
      const toastElement = screen.getByText('Fade Out Message');
  
      await waitFor(() => {
        expect(toastElement).toHaveStyle('opacity: 0');
      }, { timeout: 1500 });
    });
  });