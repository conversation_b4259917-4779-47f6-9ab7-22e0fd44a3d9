import React from "react";
import "@testing-library/jest-dom";
import { render, screen, fireEvent } from "@testing-library/react";
import FormField from "@talent-front-libs/ui/components/atoms/FormField";

const mockRegister = jest.fn();

describe("FormField", () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it("renders the label and input field correctly", () => {
    render(
      <FormField
        name="testField"
        type="text"
        label="Test Field"
        placeholder="Enter test value"
        register={mockRegister}
      />,
    );

    const labelElement = screen.getByText("Test Field");
    const inputElement = screen.getByPlaceholderText("Enter test value");

    expect(labelElement).toBeInTheDocument();
    expect(inputElement).toBeInTheDocument();
  });

  it("displays supporting text when provided", () => {
    const supportingText = "This is a supporting text";
    render(
      <FormField
        name="testField"
        type="text"
        label="Test Field"
        supportingText={supportingText}
        register={mockRegister}
      />,
    );

    const supportingTextElement = screen.getByText(supportingText);
    expect(supportingTextElement).toBeInTheDocument();
  });

  it("displays warning message when provided", () => {
    const warningMessage = "This is a warning message";
    render(
      <FormField
        name="testField"
        type="text"
        label="Test Field"
        warning={warningMessage}
        register={mockRegister}
      />,
    );

    const warningElement = screen.getByText(warningMessage);
    expect(warningElement).toBeInTheDocument();
  });

  it("displays error message when provided", () => {
    const errorMessage = "This is an error message";
    const error = { message: errorMessage };
    render(
      <FormField
        name="testField"
        type="text"
        label="Test Field"
        error={error}
        register={mockRegister}
      />,
    );

    const errorElement = screen.getByText(errorMessage);
    expect(errorElement).toBeInTheDocument();
  });

  it("calls the register function with the correct input name", () => {
    render(<FormField name="testField" type="text" label="Test Field" register={mockRegister} />);

    expect(mockRegister).toHaveBeenCalledWith("testField");
  });

  it("disables the input field when disabled prop is true", () => {
    render(
      <FormField
        name="testField"
        type="text"
        label="Test Field"
        disabled
        register={mockRegister}
      />,
    );

    const inputElement = screen.getByRole("textbox");
    expect(inputElement).toBeDisabled();
  });

  it("handles input change", () => {
    const handleChange = jest.fn();
    render(
      <FormField
        name="testField"
        type="text"
        label="Test Field"
        onChange={handleChange}
        register={mockRegister}
      />,
    );

    const inputElement = screen.getByRole("textbox");
    fireEvent.change(inputElement, { target: { value: "test value" } });

    expect(handleChange).toHaveBeenCalledTimes(1);
  });

  it("renders the icon when iconStyle prop is provided", () => {
    const iconStyle = {
      Icon: jest.fn(() => <span data-testid="icon" />),
      title: "Icon Title",
      width: "16px",
      height: "16px",
    };

    render(
      <FormField
        name="testField"
        type="text"
        label="Test Field"
        iconStyle={iconStyle}
        register={mockRegister}
      />,
    );

    const iconElement = screen.getByTestId("icon");
    expect(iconElement).toBeInTheDocument();
  });

  it("calls the onClick handler of the icon when clicked", () => {
    const onIconClick = jest.fn();
    const iconStyle = {
      Icon: jest.fn(() => <span data-testid="icon" onClick={onIconClick} />),
      title: "Icon Title",
      width: "16px",
      height: "16px",
    };

    render(
      <FormField
        name="testField"
        type="text"
        label="Test Field"
        iconStyle={iconStyle}
        register={mockRegister}
      />,
    );

    const iconElement = screen.getByTestId("icon");
    fireEvent.click(iconElement);
    expect(onIconClick).toHaveBeenCalledTimes(1);
  });
});
