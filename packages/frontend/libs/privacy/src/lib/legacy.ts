/**
 * Encryption / Decryption General functions
 * Compatible Mode
 * Talent.com
 */

import * as crypto from "crypto";

const key = process.env.ENCRYPTION_KEY;

/**
 * General Purpose Encryption Method
 * Compatible Legacy Mode Enabled
 * @param {string} text Data to encrypt
 * @returns Encrypted Data
 */
export function encrypt(text: string) {
  try {
    // If the key is not present throw an error
    if (!key) throw new Error("\u26D4 Key is not initialized");

    // If the key is not present throw an error
    if (!text) throw new Error("There's nothing to decrypt");

    // Create Inicialization Vector
    const iv = crypto.randomBytes(16);

    // Create a decipher object
    const cipher = crypto.createCipheriv(
      "aes-256-cbc",
      Buffer.from(key, "utf-8").slice(0, 32),
      Buffer.from(iv),
    );

    // Prepare IV for encoding
    const BinaryIV = Buffer.from(iv).toString("binary");

    // Encrypt the data
    let encryptedData = cipher.update(text, "utf-8", "binary");
    encryptedData += cipher.final("binary");

    // Join and encode the data as Base64
    encryptedData = BinaryIV + encryptedData;
    encryptedData = Buffer.from(encryptedData, "binary").toString("base64");

    // Shift characters to add more chaos
    const searchArray = ["+", "/", "="];
    const replaceArray = ["-", "_", "*"];

    searchArray.forEach((search, index) => {
      const regex = new RegExp(`\\${search}`, "g");
      encryptedData = encryptedData.replace(regex, replaceArray[index]);
    });

    return `V001${encryptedData}`;
  } catch (error) {
    return "";
  }
}

/**
 * General Purpose Decryption Method
 * Compatible Legacy Mode Enabled
 * @param {string} text Data to decrypt
 * @returns Decrypted Data
 */
export function decrypt(text: string) {
  try {
    // If the key is not present throw an error
    if (!key) throw new Error("\u26D4 Key is not initialized");

    // If the key is not present throw an error
    if (!text) throw new Error("There's nothing to decrypt");

    // Separate version from ciphered text
    // const version = String(text).substring(0, 4);
    let cipheredText = String(text).substring(4, String(text).length);

    // Reverse the base64 by removing added characters
    const searchArray = ["-", "_", "*"];
    const replaceArray = ["+", "/", "="];

    searchArray.forEach((search, index) => {
      const regex = new RegExp(`\\${search}`, "g");
      cipheredText = cipheredText.replace(regex, replaceArray[index]);
    });

    // Decode the base64 string to binary
    cipheredText = Buffer.from(cipheredText, "base64").toString("binary");

    // Get the IV and convert it to Hex
    let iv = String(cipheredText).substring(0, 16);
    iv = Buffer.from(iv, "binary").toString("hex");

    // Get the ciphered text in Binary and encode it as base64
    cipheredText = String(cipheredText).substring(16, String(text).length);
    cipheredText = Buffer.from(cipheredText, "binary").toString("base64");

    // Create a decipher object
    const decipher = crypto.createDecipheriv(
      "aes-256-cbc",
      Buffer.from(key, "utf-8").slice(0, 32),
      Buffer.from(iv, "hex"),
    );

    // Decrypt the data
    let decryptedData = decipher.update(cipheredText, "base64", "utf-8");
    decryptedData += decipher.final("utf-8");

    return decryptedData;
  } catch (error) {
    return "";
  }
}