/**
 * Encryption / Decryption General functions
 * New Encryption methods
 * Talent.com
 */

import * as crypto from "crypto";

const algorithm = "aes-256-gcm";
const secretKey = process.env.CIPHER_KEY || "";

/**
 * Encrypt a text.
 * @param {string} text - String to encrypt.
 * @returns {string} The encrypted string along with the IV and auth tag.
 * @throws {Error} If the text is empty.
 */
export function encrypt(text: any): string {
  if (!text) {
    throw new Error("Empty entry to encrypt");
  }

  const key = crypto.createHash("sha256").update(secretKey, "utf8").digest();

  try {
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipheriv(algorithm, key, iv);
    let encrypted = cipher.update(text, "utf8", "hex");
    encrypted += cipher.final("hex");
    const authTag = cipher.getAuthTag().toString("hex");

    return `${iv.toString("hex")}:${encrypted}:${authTag}`;
  } catch (error) {
    throw new Error(`Encryption error: ${error}`);
  }
}

/**
 * Decrypt an encrypted text.
 * @param {string} encryptedText - The encrypted string containing the IV, encrypted data, and auth tag.
 * @returns {string} The decrypted text.
 * @throws {Error} If decryption fails.
 */
export function decrypt(encryptedText: string): string {
  const [ivHex, encryptedData, authTagHex] = encryptedText.split(":");

  if (!ivHex || !encryptedData || !authTagHex) {
    throw new Error("Invalid encrypted text format");
  }

  const key = crypto.createHash("sha256").update(secretKey, "utf8").digest();

  try {
    const iv = Buffer.from(ivHex, "hex");
    const authTag = Buffer.from(authTagHex, "hex");
    const decipher = crypto.createDecipheriv(algorithm, key, iv);
    decipher.setAuthTag(authTag);

    let decrypted = decipher.update(encryptedData, "hex", "utf8");
    decrypted += decipher.final("utf8");

    return decrypted;
  } catch (error) {
    throw new Error(`Decryption error: ${error}`);
  }
}
