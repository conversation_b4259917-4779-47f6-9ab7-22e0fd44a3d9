"use server";

import { auth } from "@talent-front-services/employers-frontend/modules/auth";
import { talentApi, talentApiLegacy, talentApiV2 } from "@talent-front-libs/ui/modules/network";
import { ISettingsAccountsFormData } from "@talent-front-services/employers-frontend/components/templates/SettingsAccountsPage/interface";

/// <reference types="../../../types.d.ts" />
interface GetBillingParams {
  id?: string;
  owner?: string;
  agency?: string;
  company?: string;
  billed?: string;
  orderBy?: string;
  limit?: number;
  from?: number;
}

interface Summary {
  spent: number;
  clicks: number;
  spentLastMonth: number;
}

interface OverviewItem {
  day: string;
  clicks: number;
  spent: number;
}

interface AccountDataApiResponse {
  status: string;
  message: string;
  statusCode: number;
  payload: {
    summary: Summary;
    costOverview: OverviewItem[];
    visitsOverview: OverviewItem[];
  };
}

/**
 *
 */
export const getAccountData = async (
  accountId: string,
): Promise<{
  totalClicks: number;
  costs: number;
  spentLastMonth: number;
  costOverview: OverviewItem[];
  visitsOverview: OverviewItem[];
}> => {
  try {
    const session = await auth();
    const userId = session?.user?.user_id;

    if (!userId) {
      return { totalClicks: 0, costs: 0, spentLastMonth: 0, costOverview: [], visitsOverview: [] };
    }

    const response: AccountDataApiResponse = await talentApi({
      url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/campaigns/analytics/${accountId}/home`,
      method: "GET",
      authPayload: { user_id: userId },
    });

    if (!response?.payload || typeof response.payload.summary !== "object") {
      return { totalClicks: 0, costs: 0, spentLastMonth: 0, costOverview: [], visitsOverview: [] };
    }

    return {
      totalClicks: response.payload.summary.clicks ?? 0,
      costs: response.payload.summary.spent ?? 0,
      spentLastMonth: response.payload.summary.spentLastMonth ?? 0,
      costOverview: response.payload.costOverview ?? [],
      visitsOverview: response.payload.visitsOverview ?? [],
    };
  } catch (error) {
    return { totalClicks: 0, costs: 0, spentLastMonth: 0, costOverview: [], visitsOverview: [] };
  }
};

/**
 * Fetch Billings based on search parameters.
 *
 * @async
 * @function getAccountsTableData
 * @param {Object} params - The parameters for the search query.
 * @param {number} params.limit - The number of campaigns to return.
 * @param {number} params.from - The page number for pagination.
 */
export const getAccountsTableData = async (params: GetBillingParams) => {
  // Remove blank and null data from paramss
  const filteredData = Object.fromEntries(
    Object.entries(params).filter(
      ([key, value]) => value !== "" && value !== "all" && value !== null,
    ),
  );

  const body: any = {
    ...filteredData,
    limit: filteredData.limit ?? 20,
    from: filteredData.from ?? 1,
    orderBy: "feedcode",
    billed: filteredData.billed ? decodeURIComponent(filteredData.billed) : "Billable",
  };

  const session = await auth();

  const data = await talentApi({
    url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/billings/search`,
    data: body,
    method: "GET",
    authPayload: {
      user_id: session?.user?.user_id,
    },
  });

  return data;
};

/**
 * Returns the spent this month value by FeedCode
 * @async
 * @function getSpentThisMonthByFeeCode
 * @param feedcode FeedCode
 */
export const getSpentThisMonthByFeeCode = async (accountId: string, feedCode: string) => {
  const session = await auth();

  const data = await talentApi({
    url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/billings/current-spent?feedcode=${feedCode}&accountId=${accountId}`,
    method: "GET",
    authPayload: {
      user_id: session?.user?.user_id,
    },
  });

  return data;
};

/**
 * Fetch Bill by ID
 *
 * @async
 * @function getBillByID
 * @param {Object} params - The parameters for the search query.
 */
export const getBillByID = async (params: any) => {
  const session = await auth();

  const data = await talentApi({
    url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/billings/${params.id}?accountId=${params.accountId}`,
    method: "GET",
    authPayload: {
      user_id: session?.user?.user_id,
    },
  });

  return data;
};

export interface SearchAccountParams {
  country?: string;
  companyLabel?: string;
  status?: string;
  limit: number;
  page: number;
  owner?: string;
  accountType?: string;
  vipAccount?: boolean;
}
/**
 * Fetches the accounts based on search parameters.
 *
 * @async
 * @function getSearchAccount
 * @param {Object} params - The parameters for the search query.
 * @param {string} [params.country] - Comma-separated list of countries.
 * @param {string} [params.companyLabel] - Label of the company.
 * @param {string} [params.status] - Status of the account.
 * @param {number} params.limit - The number of accounts to return.
 * @param {number} params.page - The page number for pagination.
 * @param {string} params.owner - The owner of the account.
 * @param {string} [params.accountType] - The type of the account.
 * @param {boolean} [params.vipAccount] - VIP account status (true/false).
 */
export const getSearchAccount = async ({
  country,
  companyLabel,
  status,
  limit = 50,
  page,
  owner,
  accountType,
  vipAccount,
}: SearchAccountParams) => {
  const params: Record<string, any> = {
    country,
    companyLabel,
    status,
    limit,
    from: (page - 1) * limit,
    owner,
    accountType,
    vipAccount: vipAccount === undefined ? undefined : vipAccount ? 1 : 0,
  };
  // Remove undefined parameters
  Object.keys(params).forEach((key) => {
    if (params[key] === undefined || params[key] === "") {
      delete params[key];
    }
  });
  const session = await auth();

  const data = await talentApi({
    url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/accounts/search`,
    data: params,
    method: "GET",
    authPayload: {
      user_id: session?.user?.user_id,
    },
  });

  return data;
};

interface FindOneAccountParams {
  id: number | string;
  relations?: string;
}

/**
 * Fetches the details of a single account.
 *
 * @async
 * @function FindOneAccount
 * @param {Object} params - The parameters for the request.
 * @param {number|string} params.id - The ID of the account.
 * @param {string} [params.relations] - Comma-separated list of relations to include in the response.
 */
export const findOneAccount = async ({ id, relations }: FindOneAccountParams) => {
  // Construct the URL with optional relations parameter
  const url = `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/accounts/${id}${
    relations ? `?relations=${relations}` : ""
  }`;

  const session = await auth();

  const data = await talentApi({
    url: url,
    method: "GET",
    authPayload: {
      user_id: session?.user?.user_id,
    },
  });

  return data;
};

/**
 * Fetch Owner Suggestions
 * @async
 * @function getAutoSuggesstOwners
 * @param query
 * @returns
 */
export const getAutoSuggesstOwners = async (query: string) => {
  const data = await talentApi({
    url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/accounts-internal/autosuggest-account-owners`,
    data: { query },
    method: "GET",
  });
  return data;
};

/**
 * Edit Billing Account
 * Edit side panel from the accounts table in the finance module
 *
 * @async
 * @function updateBillingAccount
 * @param {UpdateAccountParams} params - The parameters for updating the account.
 * @returns {Promise<any>} The response from the API.
 */
export const editBillingAccount = async (params: any) => {
  const { id, accountId, ...updateData } = params;
  const session = await auth();

  const payload = {
    talentCompany: updateData.talent_company,
    qboId: parseInt(updateData?.qbo_id, 10),
    taxId: parseInt(updateData?.tax_id, 10),
    paymentTerm: parseInt(updateData?.payment_term, 10),
    currency: updateData.currency,
    country: updateData.country,
  };

  const data = await talentApi({
    url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/billings/${id}?accountId=${accountId}`,
    data: payload,
    method: "PATCH",
    authPayload: {
      user_id: session?.user?.user_id,
    },
  });

  return data;
};

/**
 * Update Billing Account
 * Update side panel from the accounts table in the finance module
 *
 * @async
 * @function updateBillingAccount
 * @param {UpdateAccountParams} params - The parameters for updating the account.
 * @returns {Promise<any>} The response from the API.
 */
export const updateBillingAccount = async (params: any) => {
  const { id, accountId, ...updateData } = params;

  const session = await auth();

  const data = await talentApi({
    url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/billings/${id}?accountId=${accountId}`,
    data: updateData,
    method: "PATCH",
    authPayload: {
      user_id: session?.user?.user_id,
    },
  });

  return data;
};

/**
 * Create Quickbooks ID
 * Edit side panel from the accounts table in the finance module
 *
 * @async
 * @function createQboId
 * @param {QuickBooksIDparams} params - The parameters for updating the account.
 * @returns {Promise<any>} The response from the API.
 */
export const createQboId = async (params: any) => {
  const { id, accountId, ...updateData } = params;
  const session = await auth();

  const payload = {
    ...updateData,
  };

  delete payload.contact;

  const data = await talentApi({
    url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/quickbooks/customer?accountId=${accountId}`,
    data: payload,
    method: "POST",
    authPayload: {
      user_id: session?.user?.user_id,
    },
  });

  return data;
};

/**
 * Create Invoice Preview for French Accounts
 *
 * @async
 * @function createInvoicePreviewFR
 * @param {CreateAccountParams} params - The parameters for creating the account.
 * @returns {Promise<any>} The response from the API.
 */
export const createInvoicePreviewFR = async (params: any) => {
  const payload = {
    feedcode: String(params.feedCode),
    billingPeriod: String(params.billingPeriod),
    lines: parseInt(params.lines, 10),
    discount: parseFloat(params.discount),
  };

  const accountId = parseInt(params.accountId, 10);

  const session = await auth();

  const data = await talentApi({
    url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/invoice-preview/invoice-preview-fr?accountId=${accountId}`,
    data: payload,
    method: "POST",
    authPayload: {
      user_id: session?.user?.user_id,
    },
  });

  return data;
};

/**
 *
 */
export const getAccountIntegrationInfoLegacy = async ({ feedcode }: { feedcode: string }) => {
  const body = {
    feedcode,
  };

  const data = await talentApiLegacy({
    url: `${process.env.NEXT_PUBLIC_TALENT_LEGACY_API}/Feedcode/getFeedcodeIntegrationInfo`,
    data: body,
    method: "GET",
  });

  return data;
};

/**
 * FeedCode Auto Suggest
 */
export const getFeedcodeAutosugesst = async (feedCode: { value: string; limit: number }) => {
  try {
    const session = await auth();

    if (!session?.user?.user_id) {
      throw new Error("Unauthorized: No valid session");
    }

    if (feedCode?.value?.length > 2) {
      const data = await talentApi({
        url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/billings-internal/suggested-feedcode-without-qbo/${feedCode?.value}`,
        method: "GET",
        authPayload: {
          user_id: session?.user?.user_id,
        },
      });

      if (![200, 201, 204].includes(data?.statusCode)) {
        throw new Error(`Oh oh, error`);
      }

      const results = data.payload
        ? data?.payload?.suggestions?.map((item: any) => item.feedcode)
        : [];

      return results;
    }

    return [];
  } catch (error) {
    return [];
  }
};

/**
 * Get associated accounts with user
 */
// utils/getAssociatedAccounts.ts
export const getAssociatedAccounts = async (
  limit?: string | number,
  userIdParam?: string | number,
) => {
  try {
    const userId = userIdParam ?? (await auth())?.user?.user_id;

    if (!userId) {
      throw new Error("No user ID available for fetching accounts");
    }

    const { data, code } = await talentApiV2({
      url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/accounts/by-user${
        limit ? `?limit=${limit}` : ""
      }`,
      method: "GET",
      authPayload: { user_id: userId },
    });

    if (![200, 201, 204].includes(code)) {
      throw new Error(`Oh oh, error`);
    }

    return data?.payload;
  } catch (error) {
    return [];
  }
};

/**
 * Fetch the user account info for account settings page
 */
export const getAccountInfo = async (accountId: any) => {
  const session = await auth();

  if (!session?.user?.user_id) {
    throw new Error("Unauthorized: no session user_id");
  }

  const data = await talentApi({
    url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/accounts/sales-module/${accountId}/info`,
    method: "GET",
    authPayload: {
      user_id: session?.user?.user_id,
    },
  });

  return data;
};

/**
 * Updates the user account info
 */
export const updateAccountInfo = async (formData: ISettingsAccountsFormData, accountId: string) => {
  try {
    const session = await auth();

    if (!session?.user?.user_id) {
      throw new Error("Unauthorized: No valid session");
    }

    const response = await talentApiV2({
      url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/accounts/${accountId}`,
      method: "PATCH",
      authPayload: {
        user_id: session?.user?.user_id,
      },
      data: formData,
    });

    if (![200, 201, 204].includes(response?.code)) {
      throw new Error(`Oh oh, error`);
    }

    return response;
  } catch (error) {
    return {
      code: 404,
      error: "Couldn't update account info",
    };
  }
};
