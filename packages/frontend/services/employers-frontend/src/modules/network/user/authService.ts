/* istanbul ignore file */
"use server";

import { z } from "zod";
import { AuthError } from "next-auth";
import * as privacy from "libs/privacy/src";
import { signIn, signOut } from "@talent-front-services/employers-frontend/modules/auth";
import { talentApiV2 } from "@talent-front-libs/ui/modules/network";
import {
  ForgotSchema,
  ResetSchema,
} from "@talent-front-services/employers-frontend/modules/utils/schemaUtils";
import { headers } from "next/headers";
import { getUserId } from "../../auth/methods";

/**
 * Define the schema for the login form using Zod.
 */
const LoginFormSchema = z.object({
  email: z.string(),
  password: z.string(),
});

/**
 * Authenticates a user based on credentials provided through a form.
 * This function validates the form data against a predefined Zod schema.
 *
 * @param {string | undefined} prevState - The previous state of the authentication process, if any.
 * @param {FormData} formData - The form data containing user credentials.
 */
export async function authenticate(prevState: string | undefined, formData: FormData) {
  const validatedFields = LoginFormSchema.safeParse({
    email: formData.get("email"),
    password: formData.get("password"),
  });

  if (validatedFields.success) {
    try {
      const response = await talentApiV2({
        url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/auth/signin-basic-auth`,
        method: "POST",
        data: validatedFields.data,
      });
      if (response.code === 201 && response.data?.payload) {
        await signIn("credentials", {
          ...validatedFields.data,
          user_id: response.data.payload.user_id,
          user_roles: response.data.payload.user_roles,
        });
      } else {
        const errorMessage =
          "The username or password you entered is incorrect. Please try again or reset your password if you've forgotten it.";
        throw new Error(errorMessage);
      }
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error("An unexpected error occurred");
    }
  } else {
    throw new Error("Invalid form data");
  }
}

/**
 * Initiates a single sign-on (SSO) authentication process for a specified SSO type.
 *
 * @param {string} ssoType - The type of SSO provider to authenticate with.
 */
export async function ssoSignIn(ssoType: string) {
  try {
    await signIn(ssoType);
  } catch (error) {
    if (error instanceof AuthError) {
      throw new Error(error.message || "SSO Authentication failed");
    }
    throw error;
  }
}

/**
 * Logs out the current user and redirects to the login page.
 */
export async function logOut() {
  const headerList = headers();
  const host = headerList.get("host");
  const proto = host?.includes("localhost") ? "http" : "https";
  try {
    await signOut({ redirect: true, redirectTo: `${proto}://${host}/employers/login` });
  } catch (error) {
    if (error instanceof AuthError) {
      throw new Error("SSO Authentication failed");
    }
    throw error;
  }
}

/**
 * Send Forgot Password Email
 * @param formData Form Data. Only requires the email
 * @returns status
 */
export async function forgotPassword(formData: FormData) {
  const validatedFields = ForgotSchema.safeParse({
    email: formData.get("email"),
  });

  if (validatedFields.success) {
    try {
      const response = await talentApiV2({
        url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/auth/reset-password/send-email`,
        method: "POST",
        data: {
          email: validatedFields?.data?.email,
        },
      });

      if (response.code === 500 && response.data?.payload) {
        const errorMessage = "Password Reset failed";
        throw new Error(errorMessage);
      }

      return { status: "ok", code: 200 };
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error("An unexpected error occurred");
    }
  } else {
    throw new Error("Invalid form data");
  }
}

/**
 * Reset Password with Token
 * @param user_id User ID
 * @param password Plain Password
 * @returns Status Code
 */
export async function resetPassword(formData: FormData, token: string) {
  try {
    const validatedFields = ResetSchema.safeParse({
      password: formData.get("password"),
    });

    // Check it's a valid Token
    const unparsedData = privacy.cipher.decrypt(token as string);

    // Parse the Token
    const decryptedToken = JSON.parse(unparsedData);

    // Get user id
    const { user_id } = await getUserId({ email: decryptedToken?.email });

    if (!user_id) {
      throw new Error("User ID not found");
    }

    if (validatedFields.success) {
      // Encrypt the data
      const passwordEncrypted = privacy.cipher.encrypt(validatedFields?.data?.password);

      const response = await talentApiV2({
        url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/auth/reset-password`,
        method: "POST",
        data: {
          password: passwordEncrypted,
        },
        authPayload: { user_id: user_id },
      });

      if (response.code === 500 && response.data?.payload) {
        throw new Error("Password Reset failed");
      }

      return { status: "ok", code: 200 };
    } else {
      throw new Error("Invalid form data");
    }
  } catch (error) {
    throw new Error("Password Reset failed");
  }
}
