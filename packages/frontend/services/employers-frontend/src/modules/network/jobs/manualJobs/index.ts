"use server";

import { talentApiV2 } from "@talent-front-libs/ui/modules/network";
import { signPayload } from "@talent-front-libs/ui/modules/network/signPayload";
import { auth } from "@talent-front-services/employers-frontend/modules/auth";

/** trim filter data */
const trimFilterData = (data: any) => {
  const trimmedData = Object.entries(data)
    .filter(([key, value]) => value !== undefined && value !== "all" && value !== "")
    .reduce((acc, [key, value]) => ({ ...acc, [key]: value }), {});
  return trimmedData;
};

/**
 * search manually posted jobs
 * @param filters - The account ID to search for privileges.
 */
export const searchManuallyPostedJobs = async (filters: any) => {
  const session = await auth();
  // remove empty filters from the object

  const body = trimFilterData(filters);

  const data = await talentApiV2({
    url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/manual-jobs/search`,
    method: "GET",
    data: body,
    authPayload: {
      user_id: session?.user?.user_id,
    },
  });

  return data;
};

/** Get cities suggestion by country name */
export const getCitiesByCountry = async (filters: any) => {
  const session = await auth();
  const body = trimFilterData(filters);

  const data = await talentApiV2({
    url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/accounts-internal/cities-by-country`,
    method: "GET",
    data: body,
    authPayload: {
      user_id: session?.user?.user_id,
    },
  });

  return data;
};

/** Create a manual job */
export const createManualJob = async (body: any) => {
  const session = await auth();
  const payload = trimFilterData(body);
  const data = await talentApiV2({
    url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/manual-jobs`,
    method: "POST",
    data: payload,
    authPayload: {
      user_id: session?.user?.user_id,
    },
  });

  return data;
};

/** Update job */
export const updateJob = async (jobId: any, body: any) => {
  const session = await auth();
  
  body.status = body.status === "active" ? "open" : body.status;

  const payload = trimFilterData(body);

  const data = await talentApiV2({
    url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/manual-jobs/${jobId}`,
    method: "PATCH",
    data: payload,
    authPayload: {
      user_id: session?.user?.user_id,
    },
  });

  return data;
};

/** Update child job status*/
export const updateChildJobStatus = async (jobId: string, accountId: string, status: string) => {
  try {
    const session = await auth();

    if (!session) throw new Error("Unauthorized access");

    const payload = {
      jobsDataByAccount: [
        {
          accountId: accountId,
          jobs: [{ jobId: jobId }],
        },
      ],
      newStatus: status === "closed" ? "removed" : status,
      jobType: "child",
    };

    const { data, code } = await talentApiV2({
      url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/manual-jobs/jobs-status-change`,
      method: "POST",
      data: payload,
      authPayload: {
        user_id: session?.user?.user_id,
      },
    });

    if (![200, 201, 204].includes(code)) throw new Error("Failed to update status");

    return {
      code: 200,
      data,
    };
  } catch (error) {
    return {
      code: 404,
      data: {},
    };
  }
};

/** Get job by uts ID */
export const getJobById = async (jobId: string) => {
  const session = await auth();
  const data = await talentApiV2({
    url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/manual-jobs/${jobId}`,
    method: "GET",
    authPayload: {
      user_id: session?.user?.user_id,
    },
  });

  return data;
};

/** Job city expansion */
export const jobCityExpansion = async (body: any) => {
  const session = await auth();
  const data = await talentApiV2({
    url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/manual-jobs-expansion/expansion`,
    method: "POST",
    data: body,
    authPayload: {
      user_id: session?.user?.user_id,
    },
  });

  return data;
};

/**
 * Imports a file for a specific employer account.
 *
 * This function handles authentication and makes a POST request to the
 * API endpoint for importing jobs into the system, passing along the
 * necessary user credentials for authentication.
 *
 * @param {any} params - The request payload, containing the data to be imported.
 * @param {any} accountId - The unique identifier for the employer account.
 * @returns {Promise<any>} - The response data from the API call on success.
 * @throws {Error} - Throws an error if the authentication fails, the API request fails,
 *                   or any other unexpected issues occur during the process.
 */
export const importFile = async (params: any, accountId: any): Promise<any> => {
  try {
    const session = await auth();

    if (!session?.user?.user_id) {
      throw new Error("Unauthorized: No valid session");
    }

    // Append form fields
    const formData = new FormData();
    formData.append("parentId", params.get("parentId") as string);
    formData.append("country", params.get("country") as string);
    formData.append("file", params.get("file") as File);

    // Create headers
    const headers = new Headers();
    headers.append("Cache-Control", "no-store");
    headers.append(
      "Authorization",
      await signPayload({
        user_id: session.user.user_id,
      }),
    );

    const apiUrl = new URL(
      `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/manual-jobs-expansion/import/${accountId}`,
    );

    const response = await fetch(apiUrl.toString(), {
      method: "POST",
      body: formData,
      headers,
      credentials: "include",
      referrerPolicy: "no-referrer",
      cache: "no-store",
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => null);
      throw new Error(errorData?.message || `Server responded with status: ${response.status}`);
    }

    const result = await response.json();

    return {
      code: response.status,
      data: result,
    };
  } catch (error) {
    return {
      code: 404,
      data: error instanceof Error ? error.message : "Uknown error",
    };
  }
};

/** create duplicate jobs */
export const createDuplicateJobs = async (body: any) => {
  const session = await auth();
  const data = await talentApiV2({
    url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/manual-jobs-expansion/duplicate`,
    method: "POST",
    data: body,
    authPayload: {
      user_id: session?.user?.user_id,
    },
  });

  return data;
};
