/**
 * Invoice Render Route Hanlder - Talent.com
 * Used to render incoming requests from users,
 * with a token that comes from an email
 */

import { NextRequest, NextResponse } from "next/server";
import { decrypt } from "libs/privacy/src/lib/cipher";

export const dynamic = "force-dynamic";

/**
 * GET Handler
 * @param { NextRequest } request NextRequest Object
 * @returns { NextRequest } NextRequest Response
 */
export const GET = async (request: NextRequest) => {
  const url = `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/billings-internal/invoices/view-by-token`;

  try {
    // Get the token to validate the request
    const searchParams = request?.nextUrl?.searchParams;
    const token = decodeURIComponent(searchParams?.get("token") as string) || "";
    if (!token) throw new Error("Invalid Token");

    // Check the validity of the token if not throw the exception
    const base64Regex = /^[A-Za-z0-9+/=]*$/;
    const isBase64Valid = base64Regex.test(token) && token.length % 4 === 0;
    if (!isBase64Valid) throw new Error("Invalid Token");

    // Decode the base64 string, handling URL-safe encoding
    const decodedPayload = Buffer.from(token, "base64").toString("utf-8");
    if (!decodedPayload) throw new Error("Invalid Request");

    // Check the validity of the decoded payload
    const tokenFormat = /^[a-f0-9]{32}:[a-f0-9]+:[a-f0-9]{32}$/;
    const isEncryptionValid = tokenFormat.test(decodedPayload);
    if (!isEncryptionValid) throw new Error("Invalid Request");

    // Check the validity of the encryption if not throw the exception
    const decryptedToken = decrypt(decodedPayload);
    if (!decryptedToken) throw new Error("Invalid Request");

    // Read the token again now that is valid
    const headers = new Headers({
      "x-token": token,
      host: process.env.ENVIRONMENT === "dev" ? "ca.dev.talent.com" : "ca.talent.com",
    });

    // Get the PDF data
    const result = await fetch(url, {
      method: "GET",
      headers: headers,
      redirect: "follow",
      referrerPolicy: "no-referrer",
      cache: "no-store",
    });

    const pdfResponse = await result.json();

    // If there's no PDF string, redirect
    if (![200, 201, 204].includes(pdfResponse?.statusCode)) throw new Error("Invalid Invoice");

    // Check validity of string
    const base64Pdf = pdfResponse?.payload;
    const fileName = "Talent-Invoice.pdf";
    const isValidPdf = base64Regex.test(base64Pdf) && token?.length % 4 === 0;
    if (!isValidPdf) throw new Error("Invalid Invoice");

    // Decode the base64 string into a Buffer
    const bufferedPdf = Buffer.from(base64Pdf, "base64");

    // Return the PDF with headers
    return new NextResponse(bufferedPdf, {
      status: 200,
      headers: {
        "Content-Type": "application/pdf",
        "Content-Disposition": `inline; filename="${fileName}"`,
        "Content-Length": String(bufferedPdf.length),
        "X-Robots-Tag": "noindex, nofollow",
        "Cache-Control": "no-store, no-cache, must-revalidate, proxy-revalidate",
        Pragma: "no-cache",
        Expires: "0",
      },
    });
  } catch (error) {
    // If an error occurs, redirect to talent.com
    return NextResponse.redirect("https://ca.talent.com", 302);
  }
};
