import React, { ReactNode } from "react";
import CampaignStorageProvider from "@talent-front-services/employers-frontend/components/templates/NewCampaignPage/providers";
import { getCampaignInfo } from "@talent-front-services/employers-frontend/components/templates/NewCampaignPage/utils";
import { redirect } from "next/navigation";

interface LayoutProps {
  children: ReactNode;
  params: { accountId: string; campaignId: string };
}

const CAMPAIGN_ID_REGEX = /^\d+$/;

/**
 * Campaign Layout with Provider for Update
 */
export default async function Layout({ children, params }: LayoutProps) {
  const campaignId = params?.campaignId;
  const accountId = params?.accountId;

  // Validate id is only numbers
  if (!campaignId || !CAMPAIGN_ID_REGEX.test(campaignId)) {
    redirect(`/${accountId}/campaigns`);
  }

  const campaignData = await getCampaignInfo({ campaignId, accountId });

  // If the campaign does not belong to the account, redirect back.
  if (!campaignData) redirect(`/${accountId}/campaigns`);

  return (
    <CampaignStorageProvider initialValues={{ data: campaignData }}>
      {children}
    </CampaignStorageProvider>
  );
}
