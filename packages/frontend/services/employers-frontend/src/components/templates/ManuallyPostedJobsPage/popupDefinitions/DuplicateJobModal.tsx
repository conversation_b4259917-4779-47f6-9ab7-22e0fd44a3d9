"use client";
import React, { use<PERSON><PERSON>back, useEffect, useState } from "react";
import {
  CloseButton,
  ContainerInfo,
  Backdrop,
  ConfirmationBox,
  HeaderCard,
  HeaderTitle,
  TogglePrimaryStyle,
  DropdownPrimaryStyle,
  CitiesWrapper,
  CityContainer,
} from "./styles";
import { BodySRg, BodySSb, BodyLMd, BodyXxsRg } from "@talent-front-libs/ui/theme/typography";
import { Modal } from "@talent-front-libs/ui/components/atoms/Modal";
import theme from "@talent-front-libs/ui/theme/themes";
import { IDuplicatePopupProps } from "./interfaces";
import { InputPrimary } from "@talent-front-libs/ui/components/atoms/Input/variants/InputPrimary";
import { countryOptions } from "@talent-front-libs/ui/localization/allCountries";
import { AutoSuggestCityDropdown } from "@talent-front-services/employers-frontend/components/molecules/AutoSuggestCityDropdown";
import TrashIcon from "@talent-front-libs/ui/icons/line/trash.svg";
import { ButtonPrimary } from "@talent-front-libs/ui/components/atoms/Button/variants/ButtonPrimary";
import { createDuplicateJobs } from "@talent-front-services/employers-frontend/modules/network/jobs/manualJobs";
import Notification from "@talent-front-libs/ui/components/atoms/Notification";
import { NotificationProps } from "@talent-front-libs/ui/components/atoms/Notification/interfaces";
import LoadingIcon from "@talent-front-libs/ui/icons/solid/loading.svg";
import { set } from "date-fns";

const DuplicateJobModal = ({ isOpen, onClose, data }: IDuplicatePopupProps) => {
  const [formData, setFormData] = useState({
    reqid: "",
    title: "",
    url: "",
    expansion: false,
  });
  const [city, setCitiy] = useState<string>("");
  const [cities, setCities] = useState<any[]>([]);
  const [country, setCounrty] = useState<any>({ label: "", value: "" });
  const [errorMessage, setErrorMessage] = useState({
    title: "",
  });
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [notificationData, setNotificationData] = useState<NotificationProps>({
    type: "success",
    message: "",
    body: "",
  });
  const [isLoading, setIsLoading] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleCityChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCitiy(e.target.value);
  };

  const handleAddCity = useCallback(() => {
    // setCities((prevCities) => [...prevCities, query]);
    setCities((prevCities) => {
      return [
        ...prevCities,
        {
          country: country.value,
          location: [
            {
              region: city.split(",")[1],
              city: city.split(",")[0],
            },
          ],
        },
      ];
    });
    setCitiy("");
  }, [city]);

  const handleCountryChange = (value: any) => {
    setCountryChange(value);
    setFormData((prev) => ({ ...prev, country: value }));
  };

  const setCountryChange = (value: any) => {
    if (!value) {
      setCounrty({ label: undefined, value: undefined });
      return;
    }
    const countryOption = countryOptions.find((option) => option.value === value);
    if (countryOption) {
      setCounrty(countryOption);
    }
  };

  const handleToggleChange = () => {
    setFormData((prev) => ({ ...prev, expansion: !formData.expansion }));
  };

  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = "hidden"; // Lock scroll
    } else {
      document.body.style.overflow = ""; // Re-enable scroll
    }

    // Clean up when modal unmounts
    return () => {
      document.body.style.overflow = "";
    };
  }, [isOpen]);

  // remove selected city from the list
  const handleRemoveCity = (index: number) => {
    setCities((prevCities) => prevCities.filter((_, i) => i !== index));
  };

  const handleSubmit = async () => {
    setIsLoading(true);

    const trimmedFromData = Object.fromEntries(
      Object.entries(formData).filter(([_, value]) => value !== "" && value !== undefined),
    );
    const body = {
      ...trimmedFromData,
      accountId: data.accountId,
      country: data.country,
      parentId: data.parentId || data.id,
      location: cities,
    };
    if (!formData.title) {
      setErrorMessage((prev) => ({ ...prev, title: "Title is required" }));
      return;
    }

    try {
      const response = await createDuplicateJobs(body);
      if (response.code === 201) {
        setIsLoading(false);
        const data = response?.data?.payload;
        let messageBody = "";
        let message = data?.message || "Job duplicated successfully";
        if (data?.success && data?.success.length > 0) {
          messageBody = data?.success.map((item: any, index: number) => {
            return <span key={index}>{item}</span>;
          });
        }
        setNotificationData({
          type: "success",
          message: message,
          body: messageBody,
        });
        setIsSubmitted(true);
      } else {
        setIsLoading(false);
        setIsSubmitted(true);
        setNotificationData({
          type: "error",
          message: response?.data?.message || "Something went wrong",
        });
      }
    } catch (error) {
      setIsLoading(false);
      setIsSubmitted(true);
      setNotificationData({
        type: "error",
        message: "Something went wrong",
      });
    }
  };

  useEffect(() => {
    setIsLoading(false);
    setIsSubmitted(false);
    setCities([]);
    if (data) {
      setCountryChange(data.country);
      setFormData((prev) => ({ ...prev, country: data.country }));
    }
  }, [data]);

  return (
    <Modal isOpen={isOpen}>
      <Backdrop>
        <ConfirmationBox>
          <HeaderCard>
            <HeaderTitle>
              <CloseButton onClick={onClose}>
                <BodySRg color={theme.light.color.gray[400]}>x</BodySRg>
              </CloseButton>
              <BodyLMd>Quick duplication</BodyLMd>
              <BodySSb color={theme.light.color.gray[400]}>
                You may duplicate this job under another title.
              </BodySSb>
            </HeaderTitle>
          </HeaderCard>
          {isSubmitted ? (
            <Notification
              type={notificationData.type || "success"}
              message={notificationData.message}
              onClose={() => {}}
              autoDismiss={false}
              body={notificationData.body}
            />
          ) : (
            <ContainerInfo>
              <InputPrimary
                label="ReqId (Optional)"
                placeholder="Enter ReqId"
                type="text"
                name="reqid"
                onChange={(e) => handleInputChange(e)}
              />
              <InputPrimary
                label="New job title"
                placeholder="Enter new job title"
                type="text"
                name="title"
                errors={errorMessage.title}
                onChange={(e) => handleInputChange(e)}
              />
              <InputPrimary
                label="JobUrl"
                placeholder="Enter JobUrl"
                type="text"
                name="url"
                onChange={(e) => handleInputChange(e)}
              />
              <TogglePrimaryStyle
                label="Include geo expansion jobs in duplication"
                name="expansion"
                value={formData.expansion}
                onChange={handleToggleChange}
              />
              <DropdownPrimaryStyle
                label="Select country"
                options={countryOptions}
                value={country}
                onSelect={(e) => handleCountryChange(e)}
                onReset={() => setCountryChange("")}
              />
              <BodyXxsRg color={theme.light.color.gray[400]} style={{ paddingLeft: "10px" }}>
                Where do you want to repost this job?
              </BodyXxsRg>
              <AutoSuggestCityDropdown
                query={city}
                name="city"
                country={country.value}
                language="en"
                onChange={(e: any) => handleCityChange(e)}
                onAddCity={handleAddCity}
              />

              <CitiesWrapper>
                {cities.map((city: any, index: number) => (
                  <CityContainer key={index}>
                    {city.location[0].city}, {city.location[0].region}
                    <button onClick={() => handleRemoveCity(index)}>
                      <TrashIcon />
                    </button>
                  </CityContainer>
                ))}
              </CitiesWrapper>
              <ButtonPrimary disabled={isLoading} onClick={handleSubmit}>
                {isLoading ? <LoadingIcon /> : "Create"}
              </ButtonPrimary>
            </ContainerInfo>
          )}
        </ConfirmationBox>
      </Backdrop>
    </Modal>
  );
};

export default DuplicateJobModal;
