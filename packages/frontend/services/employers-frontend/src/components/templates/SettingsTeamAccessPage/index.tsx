"use client";

import React, { useMemo, useState } from "react";
import { PageTitle } from "../../../../../../libs/ui/src/components/atoms/PageTitle";
import { CardPrimary } from "@talent-front-libs/ui/components/atoms/Card/variants/CardPrimary";
import { ButtonPrimary } from "@talent-front-libs/ui/components/atoms/Button/variants/ButtonPrimary";
import PowerTable from "@talent-front-libs/ui/components/organisms/PowerTable";
import { defaultColumns } from "@talent-front-services/employers-frontend/components/templates/SettingsTeamAccessPage/tableDefinitions/TableColumns";
import { SidePanel } from "@talent-front-libs/ui/components/organisms/SidePanel";
import { useRbac } from "@talent-front-services/employers-frontend/modules/utils/Rbac";
import AddUserPanel from "./components/AddUserPanel";
import { ISettingsTeamAccessPageProps, IUser } from "./interface";
import EditUser from "./components/EditUser";
import {
  addOrRemoveUserToTeamAccess,
  getTeamAccess,
} from "@talent-front-services/employers-frontend/modules/network/user/teamAccess";
import { Toast } from "@talent-front-libs/ui/components/molecules/Toast";
import { SortingState } from "@tanstack/react-table";

/**
 * CampaignsPage component renders a page with a table of campaigns and filters.
 * @component
 */
const SettingsTeamAccessPage = ({ teamAccessList, accountId }: ISettingsTeamAccessPageProps) => {
  const { hasPermission } = useRbac();
  // Check RBAC permissions
  const canAddUser = hasPermission("TEAM_ACCESS", "CAN_ADD_USER");
  const canSeeAction = hasPermission("TEAM_ACCESS", "CAN_SEE_ACTION_DATATABLE_COLUMN");

  const [tableData, setTableData] = useState<IUser[]>(teamAccessList);
  const [isDataLoading, setIsDataLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [sorting, setSorting] = useState<SortingState>([]);
  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);
  const [isOpenAddUser, setIsOpenAddUser] = useState(false);
  const [selectedUser, setSelectedUser] = useState<IUser | null>(null);
  const [removeUserNotification, setRemoveUserNotification] = useState<{
    message: string;
    type: "success" | "error";
  } | null>(null);
  const pageData = useMemo(() => {
    const start = (page - 1) * limit;
    const end = start + limit;
    return tableData?.slice(start, end);
  }, [page, limit, tableData]);

  /**  */
  const refetchTableData = async () => {
    setIsDataLoading(true);
    try {
      const response = await getTeamAccess(accountId);
      const teamAccessList = response.data?.payload as IUser[];
      setTableData(teamAccessList);
    } catch (error) {
      setErrorMessage("Error loading data... Please refresh the page");
    }
    setIsDataLoading(false);
  };

  /** */
  const handleRemoveAccess = async (userEmail: string) => {
    const response = await addOrRemoveUserToTeamAccess(
      { userEmail, privilege: "removed" },
      accountId,
    );
    if (response.code === 201) {
      refetchTableData();
      setRemoveUserNotification({
        message: "User removed successfully",
        type: "success",
      });
    } else {
      setRemoveUserNotification({
        message: "Something went wrong",
        type: "error",
      });
    }
  };

  /**
   *
   */
  const handleEditUser = (userId: number | string) => {
    const filterUser = pageData.find((user: IUser) => user.id === userId) as IUser;
    const user = { ...filterUser };
    setSelectedUser(user);
  };

  /** */
  const handleOnEditSuccess = () => {
    refetchTableData();
  };

  /** */
  const handleAddUserSuccess = () => {
    refetchTableData();
  };

  return (
    <>
      {selectedUser && (
        <SidePanel isOpen onClose={() => setSelectedUser(null)} title={`Edit User`}>
          <EditUser user={selectedUser} onEditSuccess={handleOnEditSuccess} accountId={accountId} />
        </SidePanel>
      )}
      {isOpenAddUser && (
        <SidePanel isOpen onClose={() => setIsOpenAddUser(false)} title={`Add User`}>
          <AddUserPanel onAddSuccess={handleAddUserSuccess} accountId={accountId} />
        </SidePanel>
      )}
      <PageTitle title="Team Access Settings">
        {canAddUser && <ButtonPrimary onClick={() => setIsOpenAddUser(true)} label="Add User" />}
      </PageTitle>
      <div style={{ padding: 50 }}>
        <CardPrimary isActive={false}>
          <PowerTable
            loading={isDataLoading}
            data={pageData}
            columns={defaultColumns({
              /**
               *
               */
              handleEdit: (id: number | string) => handleEditUser(id),
              /**
               *
               */
              handleRemove: (userEmail: string) => handleRemoveAccess(userEmail),
              canSeeAction,
            })}
            limit={limit}
            page={page}
            totalResults={tableData?.length}
            changeLimit={setLimit}
            changePage={setPage}
            sorting={sorting}
            onSortingChange={setSorting}
          />
        </CardPrimary>
        {removeUserNotification && (
          <Toast
            message={removeUserNotification?.message}
            type={removeUserNotification?.type}
            onClose={() => setRemoveUserNotification(null)}
          />
        )}
        {errorMessage && (
          <Toast message={errorMessage} type="error" onClose={() => setErrorMessage("")} />
        )}
      </div>
    </>
  );
};

export default SettingsTeamAccessPage;
