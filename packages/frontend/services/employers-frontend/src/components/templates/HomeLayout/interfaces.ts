export interface AccountPerformanceProps {
  date: string;
  paid_clicks: number;
  organic_clicks: number;
  cost: number;
  conversions: number;
  conversion_rate: string;
  cost_per_conversion: string;
  currency: string;
}

export interface FiltersProps {
  dateRange: string;
}

export interface TableFilterGroupProps {
  onFilterChange: (filters: FiltersProps) => void;
}

export interface SearchCampaignPerformanceResponse {
  statusCode: number;
  payload?: {
    accounts: AccountPerformanceProps[];
    total: number;
  };
}

export interface CampaignPerformancePageProps {
  getSearchCampaignPerformance: (
    params: {
      limit: number;
      page: number;
    } & FiltersProps,
  ) => Promise<SearchCampaignPerformanceResponse>;
}

export interface RenderPanelContentProps {
  accountData: any;
}

export interface StyleTablePagination {
  $isMobile: boolean;
}