"use client";

import React, { createContext, useContext, useState, ReactNode } from "react";

// Define the shape of the context
interface StorageContextType {
  campaignValues: Record<string, any>;
  setCampaignValues: (key: string, newValue: any) => void;
}
interface CampaignStorageProviderProps {
  initialValues?: Record<string, any>;
  children: ReactNode;
}

// Create the context
const CampaignStorageContext = createContext<StorageContextType | undefined>(undefined);

/**
 * Helper function to update nested values in an object
 */
const updateNestedValue = (obj: Record<string, any>, path: string, value: any) => {
  const keys = path.split(".");
  let current = obj;
  while (keys.length > 1) {
    const key = keys.shift()!;
    if (!current[key]) current[key] = {};
    current = current[key];
  }
  const lastKey = keys.shift()!;
  if (value === "") {
    delete current[lastKey];
  } else {
    current[lastKey] = value;
  }
  return { ...obj };
};

/**
 * Provider component
 */
const CampaignStorageProvider: React.FC<CampaignStorageProviderProps> = ({
  initialValues = {},
  children,
}) => {
  const [campaignValues, setValuesState] = useState(initialValues);

  /**
   * Setting campaign Values
   */
  const setCampaignValues = (key: string, newValue: any) => {
    setValuesState((prevValues) => updateNestedValue({ ...prevValues }, key, newValue));
  };

  // Provide state and functions to children
  return (
    <CampaignStorageContext.Provider value={{ campaignValues, setCampaignValues }}>
      {children}
    </CampaignStorageContext.Provider>
  );
};

export default CampaignStorageProvider;

/**
 * Custom hook for accessing the context
 */
export const useCampaignStorageContext = (): StorageContextType => {
  const context = useContext(CampaignStorageContext);
  if (!context) {
    throw new Error("useCampaignStorageContext must be used within a CampaignStorageProvider");
  }
  return context;
};
