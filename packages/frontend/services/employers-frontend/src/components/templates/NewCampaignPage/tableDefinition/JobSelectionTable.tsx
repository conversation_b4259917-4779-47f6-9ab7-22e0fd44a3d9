"use client";

import React, { Dispatch, SetStateAction, useMemo } from "react";
import { useWatch } from "react-hook-form";
import PowerTable from "@talent-front-libs/ui/components/organisms/PowerTable";
import { defaultColumns } from "./TableColumns";
import { TableContainer } from "../style";
import { useCampaignStorageContext } from "../providers";

interface JobSelectionTableProps {
  tableData: any;
  page: number;
  limit: number;
  pageCallback: Dispatch<SetStateAction<number>>;
  limitCallback: Dispatch<SetStateAction<number>>;
  isLoading?: boolean;
}

/**
 * Main JobSelection Table
 */
export const JobSelectionTable = ({
  tableData,
  page,
  limit,
  pageCallback,
  limitCallback,
  isLoading = false,
}: JobSelectionTableProps) => {
  const data: any = tableData?.jobs ? tableData?.jobs?.filter((item: any) => item.job_title) : [];
  const totalJobs: any = tableData?.total || 0;
  const campaignType = useWatch({ name: "campaign.type" });
  const { campaignValues, setCampaignValues } = useCampaignStorageContext();

  const columns = useMemo(
    () => defaultColumns(setCampaignValues, campaignValues, campaignType),
    [campaignType, campaignValues],
  );

  return (
    <>
      <TableContainer>
        <PowerTable
          data={data}
          columns={columns}
          limit={limit}
          page={page}
          totalResults={totalJobs}
          changeLimit={limitCallback}
          changePage={pageCallback}
          loading={isLoading}
        />
      </TableContainer>
    </>
  );
};
