/**
 *
 */
export const conversionTypeOptions = (t: any) => {
  return [
    { label: "Application", value: "application" },
    { label: "Apply start", value: "applystart" },
    { label: "Qualified applicant", value: "qualified_application" },
    { label: "Hire", value: "hire" },
    { label: "Other", value: "other" },
  ];
};

export const jobSelectionTypeOptions = [
  { label: "By country", value: "by_location" },
  { label: "By Job", value: "by_jobs" },
  { label: "By Tag", value: "by_tags" },
  { label: "By Filter", value: "by_filters" },
  { label: "By all jobs", value: "by_feedcode" },
];

export const distantLocationOptions = [
  { label: "25 km", value: 25 },
  { label: "50 km", value: 50 },
  { label: "75 km", value: 75 },
  { label: "100 km", value: 100 },
];

/**
 *
 */
export const forwardApplicationOptions = (t: any) => {
  return [
    {
      label: t("subsection3-1.api"),
      value: "api",
      supportingText: t("subsubsection3-1.this_selection_forwards"),
    },
    {
      label: t("subsection3-2.email"),
      value: "email",
      supportingText: t("subsubsection3-2.enter_the_primary"),
    },
    {
      label: t("subsection3-3.none"),
      value: "none",
      supportingText: t("subsubsection3-7.applications_will_not"),
    },
  ];
};

export const budgetTypeOptions = [
  { label: "Monthly", value: "monthly" },
  { label: "Daily", value: "daily" },
];

export const campaignPaceOptions = [
  { label: "Accelerated", value: 0 },
  { label: "Paced", value: 1 },
  { label: "Paced (flexible)", value: 2 },
];

export const sponsoredOptions = [
  { label: "yes", value: 1 },
  { label: "no", value: 0 },
];

export const applyOptions = [
  { label: "Jobredirect", value: "jobredirect" },
  { label: "TalentApply", value: "talentApply" },
];
