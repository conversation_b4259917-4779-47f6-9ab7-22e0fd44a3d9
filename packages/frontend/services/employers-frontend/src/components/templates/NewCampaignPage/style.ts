import styled from "styled-components";
import Themes from "@talent-front-libs/ui/theme/themes";
import { BodyXsRg } from "@talent-front-libs/ui/theme/typography";
import { AccordionPrimary } from "@talent-front-libs/ui/components/molecules/Accordion/variants/AccordionPrimary";
import { CardContainer } from "@talent-front-libs/ui/components/atoms/Card/styles";
import { FieldHolderEdit } from "@talent-front-libs/ui/components/organisms/RenderFields/styles";
import {
  ButtonHandleItemStyle,
  HeaderItemStyle,
} from "@talent-front-libs/ui/components/molecules/Accordion/variants/AccordionPrimary/styles";

export const Container = styled.div<{
  $pad?: string;
  $flex?: boolean;
  $dir?: string;
  $gap?: number;
}>`
  padding: ${(props) => props.$pad || "0"};
  display: ${(props) => (props.$flex ? "flex" : "block")};
  gap: ${(props) => (props.$gap ? `${props.$gap}px` : "unset")};
  flex-direction: ${(props) => props.$dir || "unset"};
  width: 100%;
`;

export const TableContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 10px;
`;

export const GeneralCardContainer = styled.div`
  display: flex;
  margin-top: 20px;
  gap: 10px;
`;

export const ContainerRow = styled.div`
  display: flex;
  flex-direction: row;
  gap: 10px;
  width: 100%;
  padding: 8px 0;
`;

export const ContainerColumn = styled.div`
  display: flex;
  flex-direction: column;
  gap: 10px;
  width: 100%;
`;

export const WarningAlertText = styled.div`
  padding: 16px;
  border: 2px solid #f2994a;
  background: #fefbf8;
  border-radius: 8px;
`;

export const BannerContainer = styled(AccordionPrimary)`
  border: 1px solid ${Themes.light.color.purple["500"]};
  overflow: hidden;
  border-radius: 20px;
  padding: 0;

  ${HeaderItemStyle} {
    padding: 5px 16px;
    background-color: ${Themes.light.color.purple["500"]};
    color: ${Themes.light.color.white};

    ${ButtonHandleItemStyle} {
      color: ${Themes.light.color.white};
    }

    svg {
      color: ${Themes.light.color.white};
    }
  }
`;

export const BannerFirstSection = styled.div`
  background-color: ${Themes.light.color.purple["700"]};
  padding: 20px;
`;

export const BannerSecondSection = styled.div`
  display: flex;
  flex-direction: column;
  padding: 20px;
  gap: 20px;
`;

export const LiContainer = styled.li`
  list-style-type: disc;
  padding-left: 20px;
`;

export const PageHeader = styled.div`
  padding: 10px 16px;
`;

export const PageTitle = styled.div`
  font-size: 24px;
  font-weight: 700;
  line-height: 27px;
  margin: 20px 0;
`;

export const Card = styled.div`
  background-color: ${Themes.light.color.white};
  margin: 16px 10px;
  padding: 24px 16px;
  position: relative;
  box-shadow: ${Themes.light.shadow.md1};
  border-radius: 8px;
`;

export const CardTitle = styled.div`
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 20px;
`;

export const CardBody = styled.div`
  display: flex;

  & > div[class^="styles__TabViewContainer"] {
    width: 100%;
  }

  div[class^="styles__TabHeaderContainer"] {
    border-bottom: 2px solid ${Themes.light.color.gray[25]};
    cursor: pointer;
  }
`;

export const CardParagraph = styled(BodyXsRg)`
  color: ${Themes.light.color.gray[400]};
`;

export const FormContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  width: 100%;
  @media (min-width: 1090px) {
    ${FieldHolderEdit} {
      max-width: 368px;
      width: 100%;
      gap: 6px;
      flex: 0 0 calc(25% - 8px);
    }
  }
  & div[class^="styles__ToggleWrapper"] {
    margin: 0 0 4px 12px;
  }
`;

export const TagsBtn = styled.div`
  display: flex;
`;

export const TagsContainer = styled.div`
  display: flex;
  gap: 5px;
  ${CardContainer} {
    max-height: 350px;
  }
`;

export const BtnWrapper = styled.div`
  margin-bottom: 190px;
`;

export const StepSection = styled.div`
  display: flex;
  flex-direction: column;
  gap: 5px;
`;

export const StepsContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
`;

export const DynamicEmailContainer = styled(FieldHolderEdit)`
  width: 100%;
  @media (min-width: 1090px) {
    & {
      min-width: 368px;
    }
  }
`;

export const DynamicConversionContainer = styled(FieldHolderEdit)`
  width: 100%;
  @media (max-width: 900px) {
    & .input-group {
      display: flex;
      flex-direction: column;
    }
  }
  @media (min-width: 1090px) {
    & {
      min-width: 700px;
    }
  }
`;

export const ButtonWrapper = styled.div`
  display: flex;
  justify-content: end;
  flex-direction: row;
  gap: 8px;
  padding: 0 12px 0;
`;

export const StyledRadioGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 6px;

  div[class^="styles__ErrorMessageContainer"] > span {
    padding: 0 0 0 16px;
  }
`;

export const StyledEmailRadioGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 6px;
  padding: 0 0 0 40px;
  max-width: 500px;
`;

export const StyledSearchForm = styled.div`
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 10px;
  @media (min-width: 1090px) {
    & {
      flex-direction: row;
      max-width: 654px;
    }

    & button {
      margin: 4px 0;
    }
  }
`;

export const StyledTagContainer = styled.div`
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  width: 100%;
  gap: 10px;
  @media (min-width: 1090px) {
    & {
      max-width: 654px;
    }

    & button {
      margin: 4px 0;
    }
  }
`;

export const StyledFilterContainer = styled.div`
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 0 0 16px;
  gap: 12px;
  @media (min-width: 1090px) {
    & {
      flex-direction: row;
      flex: 0 0 calc(20% - 8px);
    }
  }
`;

export const ResultsFoundContainer = styled.div`
  display: flex;
  align-items: center;
  flex-direction: column;
  gap: 16px;
  justify-content: center;
  padding: 32px;
  width: 100%;
`;

export const Separator = styled.div`
  display: flex;
  width: 98%;
  border-bottom: 1px solid #eeeeee;
  margin: 16px auto;
`;

export const FilterTagBadge = styled.div`
  color: #222222;
  background-color: #f6f6f9;
  background-image: url(https://cdn-static.talent.com/img/others/remove_skill.png);
  background-repeat: no-repeat;
  background-size: 20px;
  background-position-x: calc(100% - 14px);
  min-height: 48px;
  height: auto;
  padding: 10px 45px 10px 18px;
  background-position-y: center;
  cursor: pointer;
  border-radius: 8px;
`;

export const FilterInputWrapper = styled.div`
  display: flex;
  flex-direction: column;

  & [class^="styles__BaseCheckboxContainerStyle-sc-"] {
    padding: 0 16px;
    gap: 4px;
  }

  & [class^="styles__BaseCheckboxContainerStyle-sc-"] > span {
    font-size: 0.75rem;
  }

  & [class^="styles__BaseCheckboxInputContainerStyle-sc-"] svg {
    width: 16px;
    height: 16px;
  }
`;
