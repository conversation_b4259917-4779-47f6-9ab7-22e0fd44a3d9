import { useEffect, useState, useMemo } from "react";
import { getPPCLimitByCountry } from "@talent-front-services/employers-frontend/modules/network/monetizationRules/server";
import { currencyList } from "@talent-front-services/employers-frontend/modules/utils/currencyUtils";

/**
 * Hook to control the changes and calculate the PPC Limits
 * @param {object} accountInfo Campaign Data Info
 * @returns {object} PPC Limits
 */
export const usePPCLimits = (accountInfo: any) => {
  const [ppcLimit, setPPCLimit] = useState<any>({ minPPC: 0.01, maxPPC: 999999 });

  useEffect(() => {
    if (!accountInfo || !accountInfo.country) return;

    const country = accountInfo.country || "ca";
    const currencyRate = parseInt(currencyList[accountInfo.accountCurrency], 10) || 1;
    /** Calculate PPC Limits */
    const calculatePPCLimits = async () => {
      try {
        const response = await getPPCLimitByCountry({ country });
        if (response && response.results && response.results[0]) {
          const { job_ppcu_less_than, set_maxppcu } = response.results[0];
          const minPPC = ((parseInt(job_ppcu_less_than, 10) * currencyRate) / 1000);
          const maxPPC = ((parseInt(set_maxppcu, 10) * currencyRate) / 1000);

          setPPCLimit({ minPPC, maxPPC });
        } 
      } catch (error) {
      }
    };

    calculatePPCLimits();
  }, [accountInfo]);

  return ppcLimit;
};

/**
 * Hook to control the changes of Campaign Objective Status
 * @param {object} accountInfo Campaign Data Info
 * @returns {boolean} Status
 */
export const useCampaignObjectiveStatus = (
  applyType: string,
  accountInfo?: any,
  campaignInfo?: any,
) => {
  const result = useMemo(() => {
    // Default status
    let isDisabled = true;

    const campaignSettings = campaignInfo?.campaignSettings;
    const accountSettings = accountInfo?.accountSettings;
    const campaignTags = campaignInfo?.campaignTag || [];
    const accountTags = accountInfo?.accountTag || [];

    const isCampaignReliableConversion = campaignSettings?.reliableConversions === 1;
    const isAccountReliableConversion = accountSettings?.reliableConversion === 1;

    const hasCampaignTag = campaignTags.some(
      (item: any) =>
        (item?.tag?.typeId === 1 && item?.status === 1) ||
        (item?.tag?.typeId === 2 && item?.status === 1),
    );

    const hasAccountTag = accountTags.some(
      (item: any) =>
        (item?.tag?.typeId === 1 && item?.status === 1) ||
        (item?.tag?.typeId === 2 && item?.status === 1),
    );

    if (
      applyType === "talentApply" ||
      (applyType === "jobredirect" &&
        ((isCampaignReliableConversion && hasCampaignTag) ||
          (!campaignInfo?.id && isAccountReliableConversion && hasAccountTag)))
    ) {
      isDisabled = false;
    }

    return isDisabled;
  }, [applyType, accountInfo, campaignInfo]);

  return result;
};

/**
 * Hook helper to search with a Delay
 * @param {object} accountInfo Campaign Data Info
 * @returns {object} PPC Limits
 */
export const useDebounce = (value: any, delay: number) => {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};
