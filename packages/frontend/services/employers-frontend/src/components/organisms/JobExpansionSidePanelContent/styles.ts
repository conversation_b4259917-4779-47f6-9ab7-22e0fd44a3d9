import theme from "@talent-front-libs/ui/theme/themes";
import styled from "styled-components";

export const Wrapper = styled.div`
  display: flex;
  padding: 20px;
  height: calc(100vh - 150px);
  overflow-y: auto;
  flex-direction: column;
  gap: 20px;
`;

export const Content = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
`;

export const FormFieldContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 10px;
`;

export const AutoSuggestWrapper = styled.div`
  display: flex;
  position: relative;
  justify-content: space-between;
  ${Content} {
    min-width: 250px;
    top: 56px;
    overflow-y: scroll;
    max-height: 400px;
  }
`;

export const StyledInputPrimaryAutosuggest = styled.div`
  display: flex;
  border: 1px solid #f0f0f3;
  border-radius: 50px;
  height: 44px;
  padding: 0px 16px;
  align-items: space-between;
  width: 90%;
`;

export const CitiesWrapper = styled.div`
  display: flex;
  flex-direction: column;
  gap: 10px;
`;

export const CityContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
  border-radius: 50px;
  background-color: ${theme.light.color.gray[50]};
  & > button {
    background-color: transparent;
    border: none;
    cursor: pointer;
    color: #ff0000;
  }
`;

export const AddButton = styled.button`
  background-color: transparent;
  border: none;
  cursor: pointer;
`;

// Import tab styles

export const TamplateWrapper = styled.div`
  display: flex;
  flex-direction: column;
  align-items: left;
  justify-content: center;
  width: 100%;
  background-color: ${theme.light.color.gray[25]};
  padding: 10px 20px;
  border-radius: 8px;
`;

export const TitleBox = styled.div`
  display: flex;
  justify-content: left;
  gap: 10px;
  align-items: center;
  width: 100%;
`;

export const DownloadBlock = styled.div`
  display: flex;
  justify-content: left;
  gap: 5px;
  align-items: center;
  width: 100%;
  padding: 10px 0px;
  a {
    background-color: transparent;
    color: ${theme.light.color.purple[500]};
  }
`;
