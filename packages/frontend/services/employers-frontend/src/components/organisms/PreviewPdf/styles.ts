import styled from "styled-components";

export const LoadingPlaceHolder = styled.div`
  height: 600px;
  background-color: gray;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
`;

export const Header = styled.div`
  width: 100%;
  display: flex;
  justify-content: space-between;
`;

export const DownloadButton = styled.button`
  display: flex;
  gap: 4px;
  & > span {
    display: flex;
    align-items: center;
  }
`;
