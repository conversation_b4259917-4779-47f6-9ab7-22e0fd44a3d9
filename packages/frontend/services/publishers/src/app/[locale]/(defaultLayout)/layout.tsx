import React from "react";
import Providers from "../../../modules/providers";
import { WebVitals } from "@talent-front-services/publishers/components/nextComponents/WebVitals";
import { cookies, headers } from "next/headers";
import { convertToNestedObject } from "@talent-front-services/publishers/i18n";
import ClientNavigation from "@talent-front-services/publishers/components/navigation/client-navigation";
import { auth } from "@talent-front-services/publishers/modules/auth/auth";

export const metadata = {
  title: "Talent.com",
  description: "Generated by create-nx-workspace",
};

type Props = {
  readonly children: React.ReactNode;
  params: any;
};

/** validate if is mobile or not using user agent */
const isMobileNext = () => {
  const userAgent = headers().get("user-agent");
  const regex =
    /(nokia|iphone|android|motorola|^mot-|softbank|foma|docomo|kddi|up\.(browser|link)|htc|dopod|blazer|netfront|helio|hosin|huawei|novarra|CoolPad|webos|techfaith|palmsource|blackberry|alcatel|amoi|ktouch|nexian|s(a|c)gh|^lge|ericsson|philips|sagem|wellcom|bunjalloo|maui|symbian|smartphone|midp|wap|phone|windows ce|iemobile|^spice|^bird|^zte-|longcos|pantech|gionee|^sie-|portalmmm|jigs browser|hiptop|^ucweb|^benq|haier|^lct|operas?mobi|opera(mini)?|320x320|240x320|176x220)/i;
  const found = userAgent?.toLowerCase().match(regex);
  return !!found;
};

const SUPPORTED_LOCALES = ["en", "fr", "es" , "de", "it", "nl", "de"]

/**
 * @returns
 */
export default async function RootLayout({ children, params }: Readonly<Props>) {  
  const isMobile = isMobileNext();
  const session = await auth();
  const cookieStore = cookies()

  // Get the cookie value
  const accountLanguageCookie = cookieStore.get("account_language")?.value;
  
  // Validate and set the locale
  let locale = params.locale; // Start with the URL locale
  
  // If cookie exists and has a valid value, use it
  if (accountLanguageCookie && SUPPORTED_LOCALES.includes(accountLanguageCookie)) {
    locale = accountLanguageCookie;
  }
  
  // Ensure we always have a valid locale
  if (!locale || !SUPPORTED_LOCALES.includes(locale)) {
    locale = 'en'; // Fallback to English
  }

  //console.log("sesion:", session)
  const messages = convertToNestedObject(
    (await import(`../../../../messages/${locale}.json`)).default,
  );
  
  // Default account data if session is undefined
  const accountData = session?.account || {id: '', name: '', productsList: [], contactName: '', accountCurrency: '', email: '', language: ''};

  return (
    <html lang={params.locale}>
    <head>
      <link rel="icon" type="image/png" href="https://cdn-static.talent.com/img/ico/favicon-32x32.png" />
    </head>
      <Providers isMobileNext={isMobile} locale={params.locale} messages={messages}>
        <body>
          <WebVitals />
          <ClientNavigation 
            userData={session?.user} 
            accountData={accountData}
          >
            {children}
          </ClientNavigation>
        </body>
      </Providers>
    </html>
  );
}