import { useState, useCallback } from "react";
import { ButtonSecondary } from "@talent-front-libs/ui/components/atoms/Button/variants/ButtonSecondary";
import { FilterContainer } from "@talent-front-services/publishers/components/templates/AccountSettings/TeamAccess/styles";
import { DropdownPrimary } from "@talent-front-libs/ui/components/molecules/Dropdown/variants/DropdownPrimary";
import { filtersProps } from "../interfaces";
import DateSelector from "@talent-front-libs/ui/components/molecules/DateSelector";
import { formatDate } from "date-fns";
import { DateSelectorWrapper, FormContainer, ButtonWrapper } from "../../styles";
import { ButtonTertiary } from "@talent-front-libs/ui/components/atoms/Button/variants/ButtonTertiary";
import { getFormattedDate } from "@talent-front-services/publishers/modules/utils/formatter";


/**
 *
 * Table Filters
 * @returns
 */
export const TableFilters = ({ onFilterChange }: { onFilterChange: any }) => {
  const [filters, setFilters] = useState<filtersProps>({
    dateRange: "",
    source: "",
    country: "",
    subId: "",
  });

  const today = new Date();
  const startDate = new Date(today.getFullYear(), today.getMonth(), 1);
  const initDate = `${getFormattedDate(startDate ?? new Date())} - ${getFormattedDate(today ?? new Date())}`;

  const [resetDateComponentTrigger, setResetDateComponentTrigger] = useState<boolean>(false);

  const mockDropDownData = [
    { value: "", label: "All" },
    { value: "option1", label: "Option 1" },
    { value: "option2", label: "Option 2" },
    { value: "option3", label: "Option 3" },
  ];

  /**
   * Date Filter handler
   */
  const handleDateChange = (dates: [Date | null, Date | null]) => {
    // Handle date change here
    const [start, end] = dates;
    const dateValue = `${formatDate(start ?? new Date(), "MM/dd/yyyy")} - ${formatDate(end ?? new Date(), "MM/dd/yyyy")}`;
    setFilters({ ...filters, dateRange: dateValue });
    console.log(dateValue);
  };

  /**
   * Date Accept handler
   */
  const handleDateAccept = (startDate: Date, endDate: Date) => {
    // Handle date acceptance here
    handleDateChange([startDate, endDate]);
  };

  const handleDropdownChange = (name: string, value: string) => {
    setFilters({ ...filters, [name]: value });
    console.log(name, value);
  };

  const handleFilterChange = useCallback(() => {
    onFilterChange(filters);
  }, [filters]);

  const handleClearFilters = () => {
    setFilters({
      dateRange: initDate,
      source: "",
      country: "",
      subId: "",
    });
    setResetDateComponentTrigger((prev: boolean) => !prev);
    onFilterChange({
      date: initDate,
    });
  };

  return (
    <FilterContainer>
      <FormContainer>
        <DateSelectorWrapper>
          <DateSelector
            key={Number(resetDateComponentTrigger)}
            onDateChange={handleDateChange}
            onAccept={handleDateAccept}
            onCancel={() => {}}
            defaultStartDate={startDate}
            defaultEndDate={today}
          />
        </DateSelectorWrapper>
        <DropdownPrimary
          label="Source"
          options={mockDropDownData}
          onSelect={(value) => handleDropdownChange("source", value)}
          onReset={() => handleDropdownChange("source", "")}
        />
        <DropdownPrimary
          label="Country"
          options={mockDropDownData}
          onSelect={(value) => handleDropdownChange("country", value)}
          onReset={() => handleDropdownChange("country", "")}
        />
        <DropdownPrimary
          label="Sub ID"
          options={mockDropDownData}
          onSelect={(value) => handleDropdownChange("subId", value)}
          onReset={() => handleDropdownChange("subId", "")}
        />
        <ButtonWrapper>
          <ButtonSecondary label="Search" onClick={handleFilterChange} />
          <ButtonTertiary label="Clear all" onClick={() => {handleClearFilters}} />
        </ButtonWrapper>
      </FormContainer>
    </FilterContainer>
  );
};
