/**
 * Next-Auth.js / Auth.js Configuration File
 * Version: 1.0.0
 * Talent.com
 */

import type { NextAuthConfig } from "next-auth";
import NextAuth, { CredentialsSignin } from "next-auth";
import {
  validateRegisterOtpCode,
  validateSignInOtpCode,
} from "@talent-front-services/jobseeker/modules/network/user/auth";
import GoogleProvider from "next-auth/providers/google";
import LinkedInProvider from "next-auth/providers/linkedin";
import CredentialsProvider from "next-auth/providers/credentials";
import { registerUserAccountSSO, migrateUserSession } from "./src/modules/network/user/auth";
import { validateLogin } from "./src/modules/network/user/signInValidation";

/** Defined for 401 errors */
export class OtpAttemptError extends CredentialsSignin {}

/** Defined for 429 and 424 errors */
export class TooManyAttempts extends CredentialsSignin {}

/** Defined for 500 and 503 errors */
export class LoginError extends CredentialsSignin {}

// Default Image
const defaultImage = "https://cdn-dynamic.talent.com/ajax/img/get-svg.php?icon=user&size=50";

// Next-Auth configuration object
const config = {
  pages: {
    signIn: "/jobs",
    signOut: "/jobs",
    error: "/jobs",
    verifyRequest: "/jobs",
  },
  providers: [
    // Setting up Google Provider
    GoogleProvider({
      clientId: process.env.AUTH_GOOGLE_ID as string,
      clientSecret: process.env.AUTH_GOOGLE_SECRET as string,
      profile(profile) {
        return {
          id: profile.sub,
          name: profile.name,
          email: profile.email,
          image: profile.picture ?? defaultImage,
          first_name: profile.given_name,
          last_name: profile.family_name,
        };
      },
      authorization: {
        params: {
          prompt: "consent",
          access_type: "offline",
          response_type: "code",
        },
      },
    }),
    // Setting up Linkedin Provider
    LinkedInProvider({
      clientId: process.env.AUTH_LINKEDIN_ID as string,
      clientSecret: process.env.AUTH_LINKEDIN_SECRET as string,
      profile(profile) {
        return {
          id: profile.sub,
          name: profile.name,
          email: profile.email,
          image: profile.picture ?? defaultImage,
        };
      },
    }),
    // Setting up Talent.com Basic Auth Provider
    CredentialsProvider({
      id: "password",
      name: "Basic Auth",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
        accountType: { label: "Account Type", type: "text" },
      },
      // Sign In / Sign up Logic
      async authorize(credentials) {
        try {
          const response: any = await validateLogin(credentials);

          const errorList = [200, 201];
          const isError = !errorList.find((error) => error === response?.statusCode);
          if (isError) throw new Error("Login failed");
          const user = { email: credentials.email as string, user_id: response?.data.user_id };
          return user;
        } catch (error) {
          console.log(`LOGIN ERROR PROVIDER: ${JSON.stringify(error)}`);
          return null;
        }
      },
    }),
    // Setting up Talent.com OTP Provider
    // The OTP strategy depends on multiple error codes
    // For security reasons NextAuth is not providing custom messages
    CredentialsProvider({
      id: "otp",
      name: "Otp",
      credentials: {
        email: { label: "Email", type: "email" },
        code: { label: "code", type: "text" },
        isAccountFound: { label: "isAccountFound", type: "text" },
      },
      // Sign In / Sign up Logic
      async authorize(credentials) {
        try {
          const { email, isAccountFound, code } = credentials;

          // Log the user in the plaform
          const result: any =
            isAccountFound === "true"
              ? await validateSignInOtpCode(email as string, code as string)
              : await validateRegisterOtpCode(email as string, code as string);

          // Error safetynet
          if (result.statusCode !== 201) throw new Error(String(result.statusCode));

          // Validate the user_id in the response
          if (!result?.data?.user_id) {
            throw new Error("400");
          }

          const userId = result?.data?.user_id;

          return {
            email: credentials.email as string,
            user_id: userId as string,
          };
        } catch (error: any) {
          // Check for known status codes
          if (error instanceof Error) {
            const errorCode = parseInt(error.message, 10);

            // OTP Attempt error
            if ([401].includes(errorCode)) throw new OtpAttemptError();

            // Too Many OTP Attempts
            if ([400, 429, 404].includes(errorCode)) throw new TooManyAttempts();
          }

          // Fatal error safetynet
          throw new LoginError();
        }
      },
    }),
    // Setting up Talent.com Basic Auth Provider
    CredentialsProvider({
      id: "migration",
      name: "User Migration",
      credentials: {
        token: { type: "text" },
        userAgent: { type: "text" },
      },
      async authorize(credentials) {
        try {
          const response: any = await migrateUserSession(credentials);

          // Check for errors
          const errorList = [200, 201];
          const isError = !errorList.find((error) => error === response.statusCode);
          if (isError) throw new Error("Login failed");

          // Build the user object
          const email = response.data?.email;
          const user_id = response.data?.user_id;
          const first_name = response.data?.first_name ?? null;
          const user = {
            user_id: user_id,
            name: first_name,
            email: email,
          };
          return user;
        } catch (error) {
          return null;
        }
      },
    }),
  ],
  callbacks: {
    async signIn({ user }) {
      return user ? true : false;
    },
    async jwt({ token, user, account }: { token: any; user: any; account: any }) {
      // Get the user id if present
      let user_id: string = user?.user_id as string;

      // SSO Account Validation
      if (account && (account.provider === "google" || account.provider === "linkedin")) {
        const response: any = await registerUserAccountSSO(
          user.email as string,
          user.first_name || user.name || ("" as string),
          user.last_name || ("" as string),
          `sso_${account.provider}`,
        );
        // Response is wrong, remove this comment once fix
        if (response.statusCode !== 200) return null;
        user_id = response?.user_id;
      }

      // When user is present, build the user token information
      if (user) token.user_id = user_id;

      // Always return the Token
      return token;
    },
    async session({ session, token }) {
      if (token.sub && session.user) {
        session.user.id = token.user_id as string;
      }

      if (session.user) {
        session.user.name = token.name;
        session.user.email = token.email as string;
      }

      return session;
    },
  },
  cookies: {
    sessionToken: {
      name: process.env.ENVIRONMENT === "prod" ? `__Secure-jobseeker.session` : `jobseeker.session`,
      options: {
        httpOnly: true,
        secure: process.env.ENVIRONMENT === "prod" ? true : false,
        sameSite: "lax",
        path: "/",
        domain: process.env.COOKIE_DOMAIN,
      },
    },
    callbackUrl: {
      name:
        process.env.ENVIRONMENT === "prod"
          ? `__Secure-jobseeker.callback-url`
          : `jobseeker.callback-url`,
      options: {
        sameSite: "lax",
        path: "/",
        secure: process.env.ENVIRONMENT === "prod" ? true : false,
      },
    },
    csrfToken: {
      name:
        process.env.ENVIRONMENT === "prod"
          ? `__Secure-jobseeker.csrf-token`
          : `jobseeker.csrf-token`,
      options: {
        httpOnly: true,
        sameSite: "lax",
        path: "/",
        secure: process.env.ENVIRONMENT === "prod" ? true : false,
      },
    },
  },
  // Session strategy and token expiration time
  session: { strategy: "jwt", maxAge: 2592000 }, // 30 days;
  // Enable debug messages in the console when having problems
  debug: process.env.ENVIRONMENT !== "prod",
  // TODO: remove before going to prod
  trustHost: true,
} satisfies NextAuthConfig;

// validate cookie domain to login in localhost
/**
 *
 */
const validation = () => {
  if (!process.env.COOKIE_DOMAIN) {
    delete config.cookies.sessionToken.options.domain;
  }
  return config;
};

// Export Next-Auth functions
export const { handlers, auth, signIn, signOut } = NextAuth(validation());
