"use server";
import { cookies } from "next/headers";
import { signIn } from "@talent-front-services/jobseeker/../auth";

/**
 *
 */
export const login = async ({ vendor, formData }: any) => {
  const data: any = {};
  try {
    data.result = await signIn(vendor);
  } catch (error) {
    // TODO
  }
  return { data: data.result ?? null, success: "Logged In" };
};

/**
 *
 */
export const setJobDetails = async (data: Record<string, any>) => {
  (await cookies()).set("jobDetails", JSON.stringify(data), { maxAge: 60 * 2 });
};