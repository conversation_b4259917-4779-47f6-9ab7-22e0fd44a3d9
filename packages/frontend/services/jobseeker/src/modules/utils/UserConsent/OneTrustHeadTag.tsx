"use client";

import { useState, useEffect } from "react";
import { usePathname, useSearchParams } from "next/navigation";
import OneTrustScript from "./OneTrustScript";
import { settings } from "@talent/utils";

/**
 * OneTrust Consent Hook
 */
export default function OneTrustHeadTag({ country, state }: { country?: string, state?: string }) {
  const [showScript, setShowScript] = useState(false);
  const pathname = usePathname();
  const searchParams = useSearchParams();

  useEffect(() => {
    /**
     * Check for changes in the subdomain
     */
    const checkSubdomain = () => {
      const countryCode = country || "ca";
      const currentState = state || "montreal";
      const isBannerVisible = settings.getBannerSettings(
        countryCode as string,
        currentState as string,
      );

      setShowScript(isBannerVisible);
    };

    checkSubdomain();
  }, [country, state, pathname, searchParams]);

  if (!showScript) {
    return null;
  }

  const domainScript = "7b428ecd-f348-424f-ac15-5830cb16dd38";

  return <OneTrustScript domainScript={domainScript} />;
}
