import Script from "next/script";

/**
 * OneTrust Script Main Component
 */
export default function OneTrustScript({ domainScript, strategy = "afterInteractive" }: any) {
  return (
    <>
      <Script
        id="onetrust-script"
        src="https://cdn.cookielaw.org/scripttemplates/otSDKStub.js"
        data-document-language="true"
        data-domain-script={domainScript}
        strategy={strategy}
        data-dlayer-name={"dataLayerOneTrust"}
      />

      <Script
        id="otwrapper"
        strategy="afterInteractive"
        type="text/javascript"
        src="https://cdn-static.talent.com/libs/js/onetrust/ot-optanon.js"
      />

      <style>{`#ot-sdk-btn-floating { display: none !important; }`}</style>
    </>
  );
}
