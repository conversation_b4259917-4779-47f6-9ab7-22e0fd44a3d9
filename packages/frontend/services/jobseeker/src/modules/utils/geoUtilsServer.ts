"use server";

import { headers } from "next/headers";
import { getLocationByIp } from "@talent-front-services/jobseeker/modules/network/geo/server";
import {
  countriesMapping,
  getCountryFromDomain,
  countryGeneralRegion,
} from "@talent-front-services/jobseeker/modules/utils/geoUtils";
import { LocationType } from "../../..";
/**
 * Get Host from headers
 */
export const getHost = () => {
  return headers().get("host");
};

/**
 *
 */
export const maskIp = () => {
  let ip = getIp();
  if (
    ip === "::1" ||
    ip?.startsWith("10.10") ||
    ip?.startsWith("10.30") ||
    ip?.startsWith("10.0")
  ) {
    ip = "************"; //montreal
    // const ip = "***********"; //miami
    // const ip = "**************"; //ny
    // let ip = '************' //montreal
  }
  // Handle IPv4 address masking
  if (ip?.includes(".")) {
    const segments = ip.split(".");
    segments[3] = "0"; // Mask the last segment
    return segments.join(".");
  }

  // Handle IPv6 address masking (mask last 4 hexes)
  if (ip?.includes(":")) {
    const segments = ip.split(":");
    segments[segments.length - 1] = "0000"; // Mask the last segment
    return segments.join(":");
  }

  return ip; // Return the original IP if not recognized
};

/**
 * Get ip from request headers
 */
export const getIp = () => {
  let ip: string | null = headers().get("x-real-ip");
  if (!ip) {
    const forwardedFor = headers().get("x-forwarded-for");
    if (Array.isArray(forwardedFor)) {
      ip = forwardedFor.at(0) || null;
    } else {
      ip = forwardedFor?.split(",").at(0) ?? "Unknown";
    }
  }
  return ip;
};

/**
 * Get Location object from endpoint using IP
 */
export const getLocation = async () => {
  let ip = getIp();

  if (
    ip === "::1" ||
    ip?.startsWith("10.10") ||
    ip?.startsWith("10.30") ||
    ip?.startsWith("10.0")
  ) {
    ip = "************"; //montreal
    // const ip = "***********"; //miami
    // const ip = "**************"; //ny
    // let ip = '************' //montreal
  }
  const host = getHost();

  let countryFromDomain: string | null = null;
  let location: any = null;

  let prefilledLocation;

  if (host) {
    countryFromDomain = getCountryFromDomain({ host });

    if (countryFromDomain === "gb") {
      countryFromDomain = "uk";
    }

    prefilledLocation = countriesMapping.find(
      (c) => c.iso === countryFromDomain?.toLowerCase(),
    )?.name;
  }

  if (ip) {
    location = await getLocationByIp({ ip });
  }

  if (
    countryFromDomain &&
    location &&
    countryFromDomain?.toLowerCase() === location?.data?.countrycode?.toLowerCase()
  ) {
    prefilledLocation = `${location?.data?.locality}, ${location?.data?.countrycode}`;
  }

  return {
    prefilledLocation,
    locationCountryIso: location?.data?.countrycode?.toLowerCase(),
    lat: location?.data?.lat,
    lon: location?.data?.lng,
    timeZone: location?.data?.time_zone,
    city: location?.data?.locality,
    zip_code: location?.data?.postal_code,
    state: location?.data?.administrative_area_level_1,
    ipLocation: ip,
    host,
  } as LocationType;
};

/**
 * Get the general region of a country
 */
const getCountryRegion = (country: string): string | undefined => {
  return countryGeneralRegion[country.toUpperCase()];
};

/**
 * Check if the country of the user's IP and the host are in the same region
 */
export const isSameCountryRegion = async (countryFromDomain: string): Promise<boolean> => {
  const userLocation = await getLocation();
  if (!countryFromDomain || !userLocation.locationCountryIso) {
    return false;
  }

  const userRegion = getCountryRegion(userLocation.locationCountryIso);
  const hostRegion = getCountryRegion(countryFromDomain);
  return userRegion === hostRegion;
};

/**
 * Get the page origin in a server component
 */
export async function getOrigin() {
  const headersList = headers();
  const host = headersList.get("host");
  const isLocal = host?.includes("localhost");
  const protocol = isLocal ? "http" : "https";
  return `${protocol}://${host}`;
}
