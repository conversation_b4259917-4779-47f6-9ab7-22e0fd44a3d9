/**
 * Returns country settings for a specific country code
 *
 * @param countryCode ISO country code
 * @returns
 */
export function getCountrySettings(countryCode: string): CountrySettings | undefined {
  countryCode = countryCode.toLowerCase();
  const country = countryArguments[countryCode];
  return country || undefined;
}

export interface CountrySettings {
  country_label: string;
  country: string;
  languages: string[];
  jobs_languages: string[];
  default_language: string;
  active: number;
  currency: string;
  salary_data: {
    currency_symbol?: string;
    currency_symbol_ar?: string;
    currency_symbol_fr?: string;
    currency_symbol_en?: string;
    currency_text?: string;
    symbol_position?: string;
    thousand_separate?: string;
    weekly_hours?: string;
    hour_min_wage?: string;
    month_median_wage?: string;
    min_salary?: string;
    max_salary?: string;
    step?: string;
    active?: number;
    active_languages?: string[];
  };
  tax_cal: {
    active: number | null;
  } | null;
  user_consent?: {
    active?: number;
    exceptions?: string[];
  };
}

export interface CountryArguments {
  [key: string]: CountrySettings;
}

export const countryArguments: CountryArguments = {
  ae: {
    country_label: "United Arab Emirates",
    country: "ae",
    languages: ["ar"],
    jobs_languages: ["ar"],
    default_language: "ar",
    active: 1,
    currency: "AED",
    salary_data: {
      currency_symbol: "د.إ ",
      symbol_position: "after",
      thousand_separate: ",",
      weekly_hours: "48",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "7500",
      max_salary: "92500",
      step: "2000",
      active: 1,
    },
    tax_cal: { active: 1 },
    user_consent: { active: 1 },
  },
  ao: {
    country_label: "Angola",
    country: "ao",
    languages: ["pt"],
    jobs_languages: ["pt"],
    default_language: "pt",
    active: 1,
    currency: "AOA",
    salary_data: {
      currency_symbol: " Kz",
      symbol_position: "after",
      thousand_separate: "",
      weekly_hours: "40",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "100000",
      max_salary: "930000",
      step: "18000",
      active: 0,
    },
    tax_cal: { active: 1 },
    user_consent: { active: 1 },
  },
  ar: {
    country_label: "Argentina",
    country: "ar",
    languages: ["es"],
    jobs_languages: ["es"],
    default_language: "es",
    active: 1,
    currency: "ARS",
    salary_data: {
      currency_symbol: "$ ",
      symbol_position: "before",
      thousand_separate: ".",
      weekly_hours: "40",
      hour_min_wage: "59.375",
      month_median_wage: "124608",
      min_salary: "40000",
      max_salary: "470000",
      step: "10000",
      active: 1,
    },
    tax_cal: { active: 1 },
  },
  at: {
    country_label: "Austria",
    country: "at",
    languages: ["de"],
    jobs_languages: ["de"],
    default_language: "de",
    active: 1,
    currency: "EUR",
    salary_data: {
      currency_symbol: "€ ",
      symbol_position: "before",
      thousand_separate: ".",
      weekly_hours: "40",
      hour_min_wage: "6.92",
      month_median_wage: "22068",
      min_salary: "1000",
      max_salary: "11750",
      step: "250",
      active: 1,
    },
    tax_cal: { active: 1 },
  },
  au: {
    country_label: "Australia",
    country: "au",
    languages: ["en"],
    jobs_languages: ["en"],
    default_language: "en",
    active: 1,
    currency: "AUD",
    salary_data: {
      currency_symbol: "$",
      symbol_position: "before",
      thousand_separate: ",",
      weekly_hours: "38",
      hour_min_wage: "17.29",
      month_median_wage: "59571",
      min_salary: "16000",
      max_salary: "190000",
      step: "4000",
      active: 1,
    },
    tax_cal: { active: 1 },
  },
  be: {
    country_label: "Belgium",
    country: "be",
    languages: ["nl", "fr"],
    jobs_languages: ["nl", "fr"],
    default_language: "nl",
    active: 1,
    currency: "EUR",
    salary_data: {
      currency_symbol: "€ ",
      symbol_position: "before",
      thousand_separate: " ",
      weekly_hours: "38",
      hour_min_wage: "9.12",
      month_median_wage: "33600",
      min_salary: "1300",
      max_salary: "14500",
      step: "500",
      active: 1,
    },
    tax_cal: { active: 1 },
  },
  bh: {
    country_label: "Bahrain",
    country: "bh",
    languages: ["ar", "en"],
    jobs_languages: ["ar", "en"],
    default_language: "ar",
    active: 1,
    currency: "BHD",
    salary_data: {
      currency_symbol: " BHD",
      currency_symbol_ar: "د.ب ",
      symbol_position: "after",
      thousand_separate: "",
      weekly_hours: "40",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "6000",
      max_salary: "68500",
      step: "1500",
      active: 0,
    },
    tax_cal: { active: 1 },
  },
  br: {
    country_label: "Brazil",
    country: "br",
    languages: ["pt"],
    jobs_languages: ["pt"],
    default_language: "pt",
    active: 1,
    currency: "BRL",
    salary_data: {
      currency_symbol: "R$",
      symbol_position: "before",
      thousand_separate: ".",
      weekly_hours: "40",
      hour_min_wage: "5.08",
      month_median_wage: "26730",
      min_salary: "4000",
      max_salary: "47000",
      step: "1000",
      active: 1,
    },
    tax_cal: { active: 1 },
  },
  ca: {
    country_label: "Canada",
    country: "ca",
    languages: ["fr", "en"],
    jobs_languages: ["fr", "en"],
    default_language: "en",
    active: 1,
    currency: "CAD",
    salary_data: {
      currency_symbol: "$",
      currency_text: "CAD",
      symbol_position: "before",
      thousand_separate: ",",
      weekly_hours: "37.5",
      hour_min_wage: "10.45",
      month_median_wage: "32790",
      min_salary: "15000",
      max_salary: "232000",
      step: "3000",
      active: 1,
    },
    tax_cal: { active: 1 },
    user_consent: { active: 1 },
  },
  ch: {
    country_label: "Switzerland",
    country: "ch",
    languages: ["fr", "de", "en", "it"],
    jobs_languages: ["fr", "de", "en", "it"],
    default_language: "de",
    active: 1,
    currency: "CHF",
    salary_data: {
      currency_symbol: "CHF ",
      symbol_position: "before",
      thousand_separate: "'",
      weekly_hours: "42.5",
      month_median_wage: "74268",
      hour_min_wage: "16.5",
      min_salary: "15000",
      max_salary: "230000",
      step: "2000",
      active: 1,
    },
    tax_cal: { active: 1 },
  },
  ci: {
    country_label: "Côte d'Ivoire",
    country: "ci",
    languages: ["fr"],
    jobs_languages: ["fr"],
    default_language: "fr",
    active: 1,
    currency: "XOF",
    salary_data: {
      currency_symbol: " CFA",
      symbol_position: "after",
      thousand_separate: "",
      weekly_hours: "40",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "100000",
      max_salary: "900000",
      step: "20000",
      active: 0,
    },
    tax_cal: { active: 1 },
  },
  cn: {
    country_label: "China",
    country: "cn",
    languages: ["cn", "en"],
    jobs_languages: ["cn", "en"],
    default_language: "cn",
    active: 1,
    currency: "CNY",
    salary_data: {
      currency_symbol: " 元",
      symbol_position: "after",
      thousand_separate: "",
      weekly_hours: "40",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "100000",
      max_salary: "1200000",
      step: "15000",
      active: 0,
    },
    tax_cal: { active: 1 },
  },
  cm: {
    country_label: "Cameroon",
    country: "cm",
    languages: ["fr", "en"],
    jobs_languages: ["fr", "en"],
    default_language: "fr",
    active: 1,
    currency: "XAF",
    salary_data: {
      currency_symbol: " FCFA",
      symbol_position: "after",
      thousand_separate: "",
      weekly_hours: "40",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "130000",
      max_salary: "1450000",
      step: "30000",
      active: 0,
    },
    tax_cal: { active: 1 },
  },
  cl: {
    country_label: "Chile",
    country: "cl",
    languages: ["es"],
    jobs_languages: ["es"],
    default_language: "es",
    active: 1,
    currency: "CLP",
    salary_data: {
      currency_symbol: "$",
      symbol_position: "before",
      thousand_separate: ".",
      weekly_hours: "45",
      month_median_wage: "4080000",
      hour_min_wage: "1725",
      min_salary: "130000",
      max_salary: "1300000",
      step: "30000",
      active: 1,
    },
    tax_cal: { active: 1 },
  },
  co: {
    country_label: "Colombia",
    country: "co",
    languages: ["es"],
    jobs_languages: ["es", "en"],
    default_language: "es",
    active: 1,
    currency: "COP",
    salary_data: {
      currency_symbol: "$",
      symbol_position: "before",
      thousand_separate: ".",
      weekly_hours: "42",
      hour_min_wage: "4882.77",
      month_median_wage: "781242",
      min_salary: "650000",
      max_salary: "9000000",
      step: "200000",
      active: 1,
    },
    tax_cal: { active: 1 },
  },
  cr: {
    country_label: "Costa Rica",
    country: "cr",
    languages: ["es"],
    jobs_languages: ["es"],
    default_language: "es",
    active: 1,
    currency: "CRC",
    salary_data: {
      currency_symbol: "₡ ",
      symbol_position: "before",
      thousand_separate: "",
      weekly_hours: "40",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "200000",
      max_salary: "2350000",
      step: "50000",
      active: 0,
    },
    tax_cal: { active: 1 },
  },
  cz: {
    country_label: "Czech Republic",
    country: "cz",
    languages: ["cs", "en"],
    jobs_languages: ["cs", "en"],
    default_language: "cs",
    active: 1,
    currency: "CZK",
    salary_data: {
      currency_symbol: " Kč",
      symbol_position: "after",
      thousand_separate: "",
      weekly_hours: "40",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "120000",
      max_salary: "900000",
      step: "20000",
      active: 1,
    },
    tax_cal: { active: 1 },
  },
  dk: {
    country_label: "Denmark",
    country: "dk",
    languages: ["da", "en"],
    jobs_languages: ["da", "en"],
    default_language: "da",
    active: 1,
    currency: "DKK",
    salary_data: {
      currency_symbol: " kr",
      symbol_position: "after",
      thousand_separate: "",
      weekly_hours: "37",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "100000",
      max_salary: "1500000",
      step: "10000",
      active: 0,
    },
    tax_cal: { active: 1 },
  },
  de: {
    country_label: "Germany",
    country: "de",
    languages: ["de"],
    jobs_languages: ["de", "en"],
    default_language: "de",
    active: 1,
    currency: "EUR",
    salary_data: {
      currency_symbol: "€ ",
      symbol_position: "before",
      thousand_separate: " ",
      weekly_hours: "40",
      month_median_wage: "21264",
      hour_min_wage: "8.5",
      min_salary: "1000",
      max_salary: "13500",
      step: "150",
      active: 1,
    },
    tax_cal: { active: 1 },
  },
  do: {
    country_label: "Dominican Republic",
    country: "do",
    languages: ["es"],
    jobs_languages: ["es"],
    default_language: "es",
    active: 0,
    currency: "DOP",
    salary_data: {
      currency_symbol: " $",
      symbol_position: "after",
      thousand_separate: " ",
      weekly_hours: "40",
      month_median_wage: "",
      hour_min_wage: "8.5",
      active: 0,
    },
    tax_cal: null,
  },
  dz: {
    country_label: "Algeria",
    country: "dz",
    languages: ["ar", "fr", "en"],
    jobs_languages: ["ar", "fr", "en"],
    default_language: "ar",
    active: 1,
    currency: "DZD",
    salary_data: {
      currency_symbol: "دج ",
      currency_symbol_fr: " DA",
      symbol_position: "after",
      thousand_separate: "",
      weekly_hours: "40",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "170000",
      max_salary: "2250000",
      step: "50000",
      active: 0,
    },
    tax_cal: { active: 1 },
  },
  ec: {
    country_label: "Ecuador",
    country: "ec",
    languages: ["es"],
    jobs_languages: ["es"],
    default_language: "es",
    active: 1,
    currency: "USD",
    salary_data: {
      currency_symbol: "$",
      symbol_position: "before",
      thousand_separate: "",
      weekly_hours: "40",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "250",
      max_salary: "3250",
      step: "70",
      active: 0,
    },
    tax_cal: { active: 1 },
  },
  eg: {
    country_label: "Egypt",
    country: "eg",
    languages: ["ar", "en"],
    jobs_languages: ["ar", "en"],
    default_language: "ar",
    active: 1,
    currency: "EGP",
    salary_data: {
      currency_symbol: "E£ ",
      currency_symbol_ar: "ج.م ",
      symbol_position: "before",
      thousand_separate: "",
      weekly_hours: "40",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "35000",
      max_salary: "410000",
      step: "10000",
      active: 0,
    },
    tax_cal: { active: 1 },
  },
  es: {
    country_label: "Spain",
    country: "es",
    languages: ["es"],
    jobs_languages: ["es"],
    default_language: "es",
    active: 1,
    currency: "EUR",
    salary_data: {
      currency_symbol: "€ ",
      symbol_position: "before",
      thousand_separate: ".",
      weekly_hours: "40",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "1200",
      max_salary: "15500",
      step: "300",
      active: 1,
    },
    tax_cal: { active: 1 },
  },
  fi: {
    country_label: "Finland",
    country: "fi",
    languages: ["fi", "en"],
    jobs_languages: ["fi", "en"],
    default_language: "fi",
    active: 1,
    currency: "EUR",
    salary_data: {
      currency_symbol: "€ ",
      symbol_position: "before",
      thousand_separate: "",
      weekly_hours: "40",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "13000",
      max_salary: "180000",
      step: "4000",
      active: 1,
      active_languages: ["en"],
    },
    tax_cal: { active: 1 },
  },
  fr: {
    country_label: "France",
    country: "fr",
    languages: ["fr"],
    jobs_languages: ["fr"],
    default_language: "fr",
    active: 1,
    currency: "EUR",
    salary_data: {
      currency_symbol: "€ ",
      symbol_position: "before",
      thousand_separate: " ",
      weekly_hours: "35",
      month_median_wage: "21264",
      hour_min_wage: "9.61",
      min_salary: "1000",
      max_salary: "13000",
      step: "250",
      active: 1,
    },
    tax_cal: { active: 1 },
  },
  uk: {
    country_label: "United Kingdom",
    country: "gb",
    languages: ["en"],
    jobs_languages: ["en"],
    default_language: "en",
    active: 1,
    currency: "GBP",
    salary_data: {
      currency_symbol: "£",
      symbol_position: "before",
      thousand_separate: ",",
      weekly_hours: "40",
      hour_min_wage: "6.7",
      month_median_wage: "27600",
      min_salary: "15000",
      max_salary: "230000",
      step: "4000",
      active: 1,
    },
    tax_cal: { active: 1 },
  },
  gb: {
    country_label: "United Kingdom of Great Britain and Northern Ireland",
    country: "gb",
    languages: ["en"],
    jobs_languages: ["en"],
    default_language: "en",
    active: 1,
    currency: "GBP",
    salary_data: {
      currency_symbol: "£",
      symbol_position: "before",
      thousand_separate: ",",
      weekly_hours: "40",
      hour_min_wage: "6.7",
      month_median_wage: "27600",
      min_salary: "15000",
      max_salary: "230000",
      step: "4000",
      active: 1,
    },
    tax_cal: { active: 1 },
  },
  gr: {
    country_label: "Greece",
    country: "gr",
    languages: ["el", "en"],
    jobs_languages: ["el", "en"],
    default_language: "el",
    active: 1,
    currency: "EUR",
    salary_data: {
      currency_symbol: "€ ",
      symbol_position: "before",
      thousand_separate: "",
      weekly_hours: "40",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "500",
      max_salary: "6750",
      step: "150",
      active: 0,
    },
    tax_cal: { active: 1 },
  },
  gh: {
    country_label: "Ghana",
    country: "gh",
    languages: ["en"],
    jobs_languages: ["en"],
    default_language: "en",
    active: 1,
    currency: "GHS",
    salary_data: {
      currency_symbol: "GH₵ ",
      symbol_position: "before",
      thousand_separate: "",
      weekly_hours: "40",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "170000",
      max_salary: "1900000",
      step: "40000",
      active: 0,
    },
    tax_cal: { active: 1 },
  },
  gt: {
    country_label: "Guatemala",
    country: "gt",
    languages: ["es"],
    jobs_languages: ["es"],
    default_language: "es",
    active: 1,
    currency: "GTQ",
    salary_data: {
      currency_symbol: "Q ",
      symbol_position: "before",
      thousand_separate: "",
      weekly_hours: "40",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "170000",
      max_salary: "2250000",
      step: "50000",
      active: 0,
    },
    tax_cal: { active: 1 },
  },
  hk: {
    country_label: "Hong Kong",
    country: "hk",
    languages: ["en", "zh", "cn"],
    jobs_languages: ["en", "zh", "cn"],
    default_language: "zh",
    active: 1,
    currency: "HKD",
    salary_data: {
      currency_symbol: "$ ",
      symbol_position: "before",
      thousand_separate: "",
      weekly_hours: "40",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "100000",
      max_salary: "1400000",
      step: "20000",
      active: 1,
      active_languages: ["en"],
    },
    tax_cal: { active: 1 },
  },
  hu: {
    country_label: "Hungary",
    country: "hu",
    languages: ["hu", "en"],
    jobs_languages: ["hu", "en"],
    default_language: "hu",
    active: 1,
    currency: "HUF",
    salary_data: {
      currency_symbol: "Ft ",
      symbol_position: "before",
      thousand_separate: "",
      weekly_hours: "40",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "15000",
      max_salary: "210000",
      step: "4000",
      active: 1,
      active_languages: ["en"],
    },
    tax_cal: { active: 1 },
  },
  id: {
    country_label: "Indonesia",
    country: "id",
    languages: ["id", "en"],
    jobs_languages: ["id", "en"],
    default_language: "id",
    active: 1,
    currency: "IDR",
    salary_data: {
      currency_symbol: "Rp. ",
      symbol_position: "before",
      thousand_separate: "",
      weekly_hours: "40",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "17000000",
      max_salary: "*********",
      step: "4000000",
      active: 0,
    },
    tax_cal: { active: 1 },
  },
  ie: {
    country_label: "Ireland",
    country: "ie",
    languages: ["en"],
    jobs_languages: ["en"],
    default_language: "en",
    active: 1,
    currency: "EUR",
    salary_data: {
      currency_symbol: "€ ",
      symbol_position: "before",
      thousand_separate: " ",
      weekly_hours: "39",
      hour_min_wage: "9.15",
      month_median_wage: "19728",
      min_salary: "1400",
      max_salary: "18200",
      step: "400",
      active: 1,
    },
    tax_cal: { active: 1 },
  },
  il: {
    country_label: "Israel",
    country: "il",
    languages: ["he", "ar", "en"],
    jobs_languages: ["he", "ar", "en"],
    default_language: "he",
    active: 1,
    currency: "ILS",
    salary_data: {
      currency_symbol: "₪ ",
      symbol_position: "before",
      thousand_separate: "",
      weekly_hours: "40",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "15000",
      max_salary: "225000",
      step: "5000",
      active: 0,
    },
    tax_cal: { active: 1 },
  },
  in: {
    country_label: "India",
    country: "in",
    languages: ["en"],
    jobs_languages: ["en"],
    default_language: "en",
    active: 1,
    currency: "INR",
    salary_data: {
      currency_symbol: "₹ ",
      symbol_position: "before",
      thousand_separate: "",
      weekly_hours: "48",
      hour_min_wage: "100",
      month_median_wage: "100000",
      min_salary: "170000",
      max_salary: "2250000",
      step: "50000",
      active: 1,
    },
    tax_cal: { active: 1 },
  },
  it: {
    country_label: "Italy",
    country: "it",
    languages: ["it"],
    jobs_languages: ["it"],
    default_language: "it",
    active: 1,
    currency: "EUR",
    salary_data: {
      currency_symbol: "€ ",
      symbol_position: "before",
      thousand_separate: " ",
      weekly_hours: "40",
      month_median_wage: "16032",
      hour_min_wage: "6",
      min_salary: "1400",
      max_salary: "22200",
      step: "200",
      active: 1,
    },
    tax_cal: { active: 1 },
  },
  jp: {
    country_label: "Japan",
    country: "jp",
    languages: ["ja", "en"],
    jobs_languages: ["ja", "en"],
    default_language: "ja",
    active: 1,
    currency: "JPY",
    salary_data: {
      currency_symbol: " 円",
      symbol_position: "after",
      thousand_separate: "",
      weekly_hours: "40",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "2000000",
      max_salary: "26000000",
      step: "400000",
      active: 1,
      active_languages: ["en"],
    },
    tax_cal: { active: 1 },
  },
  ke: {
    country_label: "Kenya",
    country: "ke",
    languages: ["en"],
    jobs_languages: ["en"],
    default_language: "en",
    active: 1,
    currency: "KES",
    salary_data: {
      currency_symbol: "KSh ",
      symbol_position: "before",
      thousand_separate: "",
      weekly_hours: "40",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "170000",
      max_salary: "2250000",
      step: "50000",
      active: 0,
    },
    tax_cal: { active: 1 },
  },
  kr: {
    country_label: "South Korea",
    country: "kr",
    languages: ["ko", "en"],
    jobs_languages: ["ko", "en"],
    default_language: "ko",
    active: 1,
    currency: "KRW",
    salary_data: {
      currency_symbol: " 원",
      symbol_position: "after",
      thousand_separate: "",
      weekly_hours: "40",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "18000000",
      max_salary: "*********",
      step: "5000000",
      active: 0,
    },
    tax_cal: { active: 1 },
  },
  kw: {
    country_label: "Kuwait",
    country: "kw",
    languages: ["ar", "en"],
    jobs_languages: ["ar", "en"],
    default_language: "ar",
    active: 1,
    currency: "KWD",
    salary_data: {
      currency_symbol: " KWD",
      currency_symbol_ar: "د.ك. ",
      symbol_position: "after",
      thousand_separate: "",
      weekly_hours: "40",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "3000",
      max_salary: "45000",
      step: "1000",
      active: 0,
    },
    tax_cal: { active: 1 },
  },
  kz: {
    country_label: "Kazakhstan",
    country: "kz",
    languages: ["ru", "en"],
    jobs_languages: ["ru", "en"],
    default_language: "ru",
    active: 1,
    currency: "KZT",
    salary_data: {
      currency_symbol: " ₸",
      symbol_position: "after",
      thousand_separate: "",
      weekly_hours: "40",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "50000",
      max_salary: "500000",
      step: "10000",
      active: 0,
    },
    tax_cal: { active: 1 },
  },
  lb: {
    country_label: "Lebanon",
    country: "lb",
    languages: ["ar", "en", "fr"],
    jobs_languages: ["ar", "en", "fr"],
    default_language: "ar",
    active: 1,
    currency: "LBP",
    salary_data: {
      currency_symbol: " ل.ل.‎",
      currency_symbol_fr: " LL",
      currency_symbol_en: " LL",
      symbol_position: "after",
      thousand_separate: "",
      weekly_hours: "40",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "150000",
      max_salary: "4000000",
      step: "100000",
      active: 0,
    },
    tax_cal: { active: 1 },
  },
  lu: {
    country_label: "Luxembourg",
    country: "lu",
    languages: ["de", "fr", "en"],
    jobs_languages: ["de", "fr", "en"],
    default_language: "fr",
    active: 1,
    currency: "EUR",
    salary_data: {
      currency_symbol: "€ ",
      symbol_position: "before",
      thousand_separate: ",",
      weekly_hours: "40",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "15000",
      max_salary: "400000",
      step: "10000",
      active: 1,
    },
    tax_cal: { active: 1 },
  },
  ma: {
    country_label: "Morocco",
    country: "ma",
    languages: ["ar", "fr", "en"],
    jobs_languages: ["ar", "fr", "en"],
    default_language: "fr",
    active: 1,
    currency: "MAD",
    salary_data: {
      currency_symbol: " MAD",
      currency_symbol_ar: "درهم ",
      symbol_position: "after",
      thousand_separate: "",
      weekly_hours: "40",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "15000",
      max_salary: "400000",
      step: "10000",
      active: 0,
    },
    tax_cal: { active: 1 },
  },
  mx: {
    country_label: "Mexico",
    country: "mx",
    languages: ["es"],
    jobs_languages: ["es"],
    default_language: "es",
    active: 1,
    currency: "MXN",
    salary_data: {
      currency_symbol: "$ ",
      symbol_position: "before",
      thousand_separate: ",",
      weekly_hours: "48",
      hour_min_wage: "7.61",
      month_median_wage: "26244",
      min_salary: "3000",
      max_salary: "45000",
      step: "1000",
      active: 1,
    },
    tax_cal: { active: 1 },
  },
  my: {
    country_label: "Malaysia",
    country: "my",
    languages: ["ms", "en"],
    jobs_languages: ["ms", "en"],
    default_language: "ms",
    active: 1,
    currency: "MYR",
    salary_data: {
      currency_symbol: "RM ",
      symbol_position: "before",
      thousand_separate: "",
      weekly_hours: "45",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "23000",
      max_salary: "280000",
      step: "6000",
      active: 1,
      active_languages: ["en"],
    },
    tax_cal: { active: 1 },
  },
  mz: {
    country_label: "Mozambique",
    country: "mz",
    languages: ["pt"],
    jobs_languages: ["pt"],
    default_language: "pt",
    active: 1,
    currency: "MZN",
    salary_data: {
      currency_symbol: " MTn",
      symbol_position: "after",
      thousand_separate: "",
      weekly_hours: "40",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "170000",
      max_salary: "2250000",
      step: "50000",
      active: 0,
    },
    tax_cal: { active: 1 },
  },
  ng: {
    country_label: "Nigeria",
    country: "ng",
    languages: ["en"],
    jobs_languages: ["en"],
    default_language: "en",
    active: 1,
    currency: "NGN",
    salary_data: {
      currency_symbol: "₦ ",
      symbol_position: "before",
      thousand_separate: "",
      weekly_hours: "40",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "50000",
      max_salary: "500000",
      step: "10000",
      active: 0,
    },
    tax_cal: { active: 1 },
  },
  nl: {
    country_label: "Netherlands",
    country: "nl",
    languages: ["nl"],
    jobs_languages: ["nl"],
    default_language: "nl",
    active: 1,
    currency: "EUR",
    salary_data: {
      currency_symbol: "€ ",
      symbol_position: "before",
      thousand_separate: ".",
      weekly_hours: "38",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "13000",
      max_salary: "180000",
      step: "4000",
      active: 1,
    },
    tax_cal: { active: 1 },
  },
  no: {
    country_label: "Norway",
    country: "no",
    languages: ["no", "en"],
    jobs_languages: ["no", "en"],
    default_language: "no",
    active: 1,
    currency: "NOK",
    salary_data: {
      currency_symbol: " kr",
      symbol_position: "after",
      thousand_separate: "",
      weekly_hours: "40",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "170000",
      max_salary: "4000000",
      step: "100000",
      active: 0,
    },
    tax_cal: { active: 1 },
  },
  nz: {
    country_label: "New Zealand",
    country: "nz",
    languages: ["en"],
    jobs_languages: ["en"],
    default_language: "en",
    active: 1,
    currency: "NZD",
    salary_data: {
      currency_symbol: "$",
      symbol_position: "before",
      thousand_separate: "",
      weekly_hours: "40",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "15000",
      max_salary: "400000",
      step: "10000",
      active: 1,
    },
    tax_cal: { active: 1 },
  },
  om: {
    country_label: "Oman",
    country: "om",
    languages: ["ar", "en"],
    jobs_languages: ["ar", "en"],
    default_language: "ar",
    active: 1,
    currency: "OMR",
    salary_data: {
      currency_symbol: " OMR",
      currency_symbol_ar: "	ر.ع",
      symbol_position: "after",
      thousand_separate: "",
      weekly_hours: "40",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "5000",
      max_salary: "68500",
      step: "1500",
      active: 0,
    },
    tax_cal: { active: 1 },
  },
  pa: {
    country_label: "Panama",
    country: "pa",
    languages: ["es"],
    jobs_languages: ["es"],
    default_language: "es",
    active: 1,
    currency: "PAB",
    salary_data: {
      currency_symbol: "B/. ",
      symbol_position: "before",
      thousand_separate: "",
      weekly_hours: "40",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "700",
      max_salary: "9000",
      step: "200",
      active: 0,
    },
    tax_cal: { active: 1 },
  },
  pe: {
    country_label: "Peru",
    country: "pe",
    languages: ["es"],
    jobs_languages: ["es"],
    default_language: "es",
    active: 1,
    currency: "PEN",
    salary_data: {
      currency_symbol: "S/ ",
      symbol_position: "before",
      thousand_separate: "",
      weekly_hours: "40",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "700",
      max_salary: "9000",
      step: "200",
      active: 0,
    },
    tax_cal: { active: 1 },
  },
  ph: {
    country_label: "Philippines",
    country: "ph",
    languages: ["en"],
    jobs_languages: ["en"],
    default_language: "en",
    active: 1,
    currency: "PHP",
    salary_data: {
      currency_symbol: "₱ ",
      symbol_position: "before",
      thousand_separate: "",
      weekly_hours: "48",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "130000",
      max_salary: "1780000",
      step: "40000",
      active: 1,
    },
    tax_cal: { active: 1 },
  },
  pk: {
    country_label: "Pakistan",
    country: "pk",
    languages: ["en"],
    jobs_languages: ["en"],
    default_language: "en",
    active: 1,
    currency: "PKR",
    salary_data: {
      currency_symbol: "₨ ",
      symbol_position: "before",
      thousand_separate: "",
      weekly_hours: "40",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "7000",
      max_salary: "75000",
      step: "1000",
      active: 0,
    },
    tax_cal: { active: 1 },
  },
  pl: {
    country_label: "Poland",
    country: "pl",
    languages: ["pl", "en"],
    jobs_languages: ["pl", "en"],
    default_language: "pl",
    active: 1,
    currency: "PLN",
    salary_data: {
      currency_symbol: " zł",
      symbol_position: "after",
      thousand_separate: " ",
      weekly_hours: "40",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "15000",
      max_salary: "250000",
      step: "5000",
      active: 1,
    },
    tax_cal: { active: 1 },
  },
  pt: {
    country_label: "Portugal",
    country: "pt",
    languages: ["pt"],
    jobs_languages: ["pt"],
    default_language: "pt",
    active: 1,
    currency: "EUR",
    salary_data: {
      currency_symbol: "€ ",
      symbol_position: "before",
      thousand_separate: "",
      weekly_hours: "40",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "700",
      max_salary: "9000",
      step: "200",
      active: 1,
    },
    tax_cal: { active: 1 },
  },
  pr: {
    country_label: "Puerto Rico",
    country: "pr",
    languages: ["es"],
    jobs_languages: ["es"],
    default_language: "es",
    active: 1,
    currency: "USD",
    salary_data: {
      currency_symbol: "$",
      symbol_position: "before",
      thousand_separate: "",
      weekly_hours: "40",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "10000",
      max_salary: "150000",
      step: "3000",
      active: 1,
      active_languages: ["es"],
    },
    tax_cal: { active: 1 },
  },
  qa: {
    country_label: "Qatar",
    country: "qa",
    languages: ["ar", "en"],
    jobs_languages: ["ar", "en"],
    default_language: "ar",
    active: 1,
    currency: "QAR",
    salary_data: {
      currency_symbol: " QAR",
      currency_symbol_ar: " ر. ق",
      symbol_position: "after",
      thousand_separate: "",
      weekly_hours: "48",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "170000",
      max_salary: "2100000",
      step: "40000",
      active: 0,
    },
    tax_cal: { active: 1 },
  },
  ro: {
    country_label: "Romania",
    country: "ro",
    languages: ["ro", "en"],
    jobs_languages: ["ro", "en"],
    default_language: "ro",
    active: 1,
    currency: "RON",
    salary_data: {
      currency_symbol: " lei",
      symbol_position: "after",
      thousand_separate: "",
      weekly_hours: "40",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "13000",
      max_salary: "160000",
      step: "3000",
      active: 0,
    },
    tax_cal: { active: 1 },
  },
  ru: {
    country_label: "Russian Federation",
    country: "ru",
    languages: ["ru", "en"],
    jobs_languages: ["ru", "en"],
    default_language: "ru",
    active: 1,
    currency: "RUB",
    salary_data: {
      currency_symbol: " ₽",
      symbol_position: "after",
      thousand_separate: "",
      weekly_hours: "",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "250000",
      max_salary: "3150000",
      step: "60000",
      active: 0,
    },
    tax_cal: { active: 1 },
  },
  sa: {
    country_label: "Saudi Arabia",
    country: "sa",
    languages: ["ar", "en"],
    jobs_languages: ["ar", "en"],
    default_language: "ar",
    active: 1,
    currency: "SAR",
    salary_data: {
      currency_symbol: " SAR",
      currency_symbol_ar: "ر. س ",
      symbol_position: "after",
      thousand_separate: "",
      weekly_hours: "48",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "40000",
      max_salary: "540000",
      step: "10000",
      active: 1,
    },
    tax_cal: { active: 1 },
  },
  se: {
    country_label: "Sweden",
    country: "se",
    languages: ["sv", "en"],
    jobs_languages: ["sv", "en"],
    default_language: "sv",
    active: 1,
    currency: "SEK",
    salary_data: {
      currency_symbol: " kr",
      symbol_position: "after",
      thousand_separate: " ",
      weekly_hours: "40",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "13000",
      max_salary: "160000",
      step: "3000",
      active: 1,
    },
    tax_cal: { active: 1 },
  },
  sg: {
    country_label: "Singapore",
    country: "sg",
    languages: ["en"],
    jobs_languages: ["en"],
    default_language: "en",
    active: 1,
    currency: "SGD",
    salary_data: {
      currency_symbol: "S$",
      symbol_position: "before",
      thousand_separate: ",",
      weekly_hours: "44",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "2300",
      max_salary: "27100",
      step: "500",
      active: 1,
    },
    tax_cal: { active: 1 },
  },
  sn: {
    country_label: "Senegal",
    country: "sn",
    languages: ["fr"],
    jobs_languages: ["fr"],
    default_language: "fr",
    active: 1,
    currency: "XOF",
    salary_data: {
      currency_symbol: " CFA",
      symbol_position: "after",
      thousand_separate: "",
      weekly_hours: "40",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "170000",
      max_salary: "2500000",
      step: "50000",
      active: 0,
    },
    tax_cal: { active: 1 },
  },
  tr: {
    country_label: "Turkey",
    country: "tr",
    languages: ["tr", "en"],
    jobs_languages: ["tr", "en"],
    default_language: "tr",
    active: 1,
    currency: "TRY",
    salary_data: {
      currency_symbol: " TL",
      symbol_position: "after",
      thousand_separate: "",
      weekly_hours: "40",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "10000",
      max_salary: "150000",
      step: "3000",
      active: 0,
    },
    tax_cal: { active: 1 },
  },
  th: {
    country_label: "Thailand",
    country: "th",
    languages: ["th", "en"],
    jobs_languages: ["th", "en"],
    default_language: "th",
    active: 1,
    currency: "THB",
    salary_data: {
      currency_symbol: " บาท",
      symbol_position: "after",
      thousand_separate: "",
      weekly_hours: "40",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "40000",
      max_salary: "520000",
      step: "10000",
      active: 0,
    },
    tax_cal: { active: 1 },
  },
  tn: {
    country_label: "Tunisia",
    country: "tn",
    languages: ["ar", "fr", "en"],
    jobs_languages: ["ar", "fr", "en"],
    default_language: "ar",
    active: 1,
    currency: "TND",
    salary_data: {
      currency_symbol: "د.ت",
      currency_symbol_fr: " DT",
      symbol_position: "after",
      thousand_separate: "",
      weekly_hours: "40",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "170000",
      max_salary: "2100000",
      step: "40000",
      active: 0,
    },
    tax_cal: { active: 1 },
  },
  tw: {
    country_label: "Taiwan",
    country: "tw",
    languages: ["zh", "en"],
    jobs_languages: ["zh", "en"],
    default_language: "zh",
    active: 1,
    currency: "TWD",
    salary_data: {
      currency_symbol: "$ ",
      symbol_position: "before",
      thousand_separate: "",
      weekly_hours: "40",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "400000",
      max_salary: "5200000",
      step: "100000",
      active: 0,
    },
    tax_cal: { active: 1 },
  },
  ua: {
    country_label: "Ukraine",
    country: "ua",
    languages: ["ru", "uk", "en"],
    jobs_languages: ["ru", "uk", "en"],
    default_language: "uk",
    active: 1,
    currency: "UAH",
    salary_data: {
      currency_symbol: " грн",
      symbol_position: "after",
      thousand_separate: "",
      weekly_hours: "40",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "15000",
      max_salary: "210000",
      step: "4000",
      active: 0,
    },
    tax_cal: { active: 1 },
  },
  sca: {
    country_label: "United States of America",
    country: "us",
    languages: ["en", "es"],
    jobs_languages: ["en", "es"],
    default_language: "en",
    active: 1,
    currency: "USD",
    salary_data: {
      currency_symbol: "$",
      symbol_position: "before",
      thousand_separate: ",",
      weekly_hours: "37.5",
      hour_min_wage: "7.25",
      month_median_wage: "28851",
      min_salary: "18000",
      max_salary: "335000",
      step: "2000",
      active: 1,
    },
    tax_cal: { active: 1 },
  },
  us: {
    country_label: "United States of America",
    country: "us",
    languages: ["en", "es"],
    jobs_languages: ["en", "es"],
    default_language: "en",
    active: 1,
    currency: "USD",
    salary_data: {
      currency_symbol: "$",
      symbol_position: "before",
      thousand_separate: ",",
      weekly_hours: "40",
      hour_min_wage: "7.25",
      month_median_wage: "28851",
      min_salary: "18000",
      max_salary: "335000",
      step: "2000",
      active: 1,
    },
    tax_cal: { active: 1 },
    user_consent: { active: 1 },
  },
  uy: {
    country_label: "Uruguay",
    country: "uy",
    languages: ["es"],
    jobs_languages: ["es"],
    default_language: "es",
    active: 1,
    currency: "UYU",
    salary_data: {
      currency_symbol: "$ ",
      symbol_position: "before",
      thousand_separate: "",
      weekly_hours: "40",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "13000",
      max_salary: "180000",
      step: "4000",
      active: 0,
    },
    tax_cal: { active: 1 },
  },
  ug: {
    country_label: "Uganda",
    country: "ug",
    languages: ["en"],
    jobs_languages: ["en"],
    default_language: "en",
    active: 1,
    currency: "UGX",
    salary_data: {
      currency_symbol: "USh ",
      symbol_position: "before",
      thousand_separate: "",
      weekly_hours: "40",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "300000",
      max_salary: "4500000",
      step: "100000",
      active: 0,
    },
    tax_cal: { active: 1 },
  },
  ve: {
    country_label: "Venezuela",
    country: "ve",
    languages: ["es"],
    jobs_languages: ["es"],
    default_language: "es",
    active: 1,
    currency: "VEF",
    salary_data: {
      currency_symbol: " Bs",
      symbol_position: "after",
      thousand_separate: ".",
      weekly_hours: "40",
      hour_min_wage: "86.83",
      month_median_wage: "303504",
      min_salary: "650000",
      max_salary: "9000000",
      step: "200000",
      active: 0,
    },
    tax_cal: { active: 1 },
  },
  vn: {
    country_label: "Vietnam",
    country: "vn",
    languages: ["vi", "en"],
    jobs_languages: ["vi", "en"],
    default_language: "vi",
    active: 1,
    currency: "VND",
    salary_data: {
      currency_symbol: " ₫",
      symbol_position: "after",
      thousand_separate: "",
      weekly_hours: "40",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "15000000",
      max_salary: "*********",
      step: "4000000",
      active: 1,
      active_languages: ["en"],
    },
    tax_cal: { active: 1 },
  },
  za: {
    country_label: "South Africa",
    country: "za",
    languages: ["en"],
    jobs_languages: ["en"],
    default_language: "en",
    active: 1,
    currency: "ZAR",
    salary_data: {
      currency_symbol: "R ",
      symbol_position: "before",
      thousand_separate: " ",
      weekly_hours: "40",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "7000",
      max_salary: "90000",
      step: "2000",
      active: 1,
    },
    tax_cal: { active: 1 },
  },
  zm: {
    country_label: "Zambia",
    country: "zm",
    languages: ["en"],
    jobs_languages: ["en"],
    default_language: "en",
    active: 1,
    currency: "ZMW",
    salary_data: {
      currency_symbol: "K ",
      symbol_position: "before",
      thousand_separate: "",
      weekly_hours: "40",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "30000",
      max_salary: "450000",
      step: "10000",
      active: 0,
    },
    tax_cal: { active: 1 },
  },
};
