"use client";

import { useServerInsertedHTML } from "next/navigation";
import { useState } from "react";
import { ServerStyleSheet, StyleSheetManager } from "styled-components";
import { FavouriteProvider } from "./providers/FavouriteProvider";
import { IsMobileProvider } from "@talent-front-services/jobseeker/modules/providers/IsMobileProvider";
import { GlobalStylesProvider } from "./providers/GlobalStylesProvider";
import { NextIntlClientProvider } from "next-intl";
import { FilterProvider } from "@talent-front-services/jobseeker/modules/providers/FiltersProvider/provider";
import { SessionProvider } from "next-auth/react";
import { PopupProvider } from "./providers/PopupProvider";
import { UserProvider } from "./providers/UserProvider";
import { SnackbarProvider } from "./providers/SnackbarProvider";
import { LocationProvider } from "./providers/LocationProvider";
import { EventProvider } from "./providers/EventProvider";
import { DefaultLanguageProvider } from "./providers/DefaultLanguage";

/**
 * Authentication Provivers
 * @param {*} param0
 * @returns
 */
export default function Providers({
  children,
  isMobileNext,
  locale,
  messages,
  prefilledLocation,
  userData,
  location,
  ip,
  userAppliedJobs,
  userJobAlertData,
  session,
  botScore,
  referer,
  isDefaultLanguage
}: {
  children: React.ReactNode;
  isMobileNext: boolean;
  locale: string;
  messages: any;
  prefilledLocation?: string;
  userData?: any;
  location?: any;
  ip: any;
  userAppliedJobs?: any;
  userJobAlertData?: any;
  session?: any;
  botScore?: any;
  referer?: any;
  isDefaultLanguage: boolean;
}) {
  return (
    <SessionProvider session={session}>
      <NextIntlClientProvider messages={messages} locale={locale} timeZone={"America/Toronto"}>
        <LocationProvider location={location} ip={ip}>
          <UserProvider
            userData={userData}
            userAppliedJobs={userAppliedJobs}
            userJobAlertData={userJobAlertData}
          >
            <DefaultLanguageProvider isDefaultLanguage={isDefaultLanguage} locale={locale}>
              <EventProvider serverbotScore={botScore} referer={referer}>
                <FavouriteProvider>
                  <PopupProvider>
                    <StyledComponentsRegistry>
                      <IsMobileProvider isMobileNext={isMobileNext}>
                        <FilterProvider prefilledLocation={prefilledLocation}>
                          <GlobalStylesProvider>
                            <SnackbarProvider>{children}</SnackbarProvider>
                          </GlobalStylesProvider>
                        </FilterProvider>
                      </IsMobileProvider>
                    </StyledComponentsRegistry>
                  </PopupProvider>
                </FavouriteProvider>
              </EventProvider>
            </DefaultLanguageProvider>
          </UserProvider>
        </LocationProvider>
      </NextIntlClientProvider>
    </SessionProvider>
  );
}

/**
 * Provider Styled Component
 */
export function StyledComponentsRegistry({ children }: { children: React.ReactNode }) {
  // Only create stylesheet once with lazy initial state
  // x-ref: https://reactjs.org/docs/hooks-reference.html#lazy-initial-state
  const [styledComponentsStyleSheet] = useState(() => new ServerStyleSheet());

  useServerInsertedHTML(() => {
    const styles = styledComponentsStyleSheet.getStyleElement();
    styledComponentsStyleSheet.instance.clearTag();
    return <>{styles}</>;
  });

  if (typeof window !== "undefined") return <>{children}</>;

  return (
    <StyleSheetManager sheet={styledComponentsStyleSheet.instance}>{children}</StyleSheetManager>
  );
}
