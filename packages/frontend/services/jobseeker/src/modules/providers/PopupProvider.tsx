/**
 * Modal Provider - Talent.com
 */

"use client";

import React, { createContext, useContext, useState } from "react";
import { set } from "react-hook-form";

// Define the type for the state and the function
type PopupContextType = {
  modal: string;
  values: any;
  modalProps?: any;
  showModal: (value: string, modalProps?: any) => void;
  closeModal: () => void;
  setNewValue: (key: string, value: string | boolean | any) => void;
  setModalProps: (arg: any) => void;
  jobAlertData: any;
  setJobAlertData: any
};

// Context for Modals
const PopupContext = createContext<PopupContextType>({
  modal: "",
  values: {},
  // eslint-disable-next-line jsdoc/require-jsdoc
  showModal: () => {},
  // eslint-disable-next-line jsdoc/require-jsdoc
  closeModal: () => {},
  // eslint-disable-next-line jsdoc/require-jsdoc
  setNewValue: () => {},
  // eslint-disable-next-line jsdoc/require-jsdoc
  setModalProps: () => {},
  jobAlertData: false,
  setJobAlertData: () => {},
});

/**
 * Provider for Modals
 * @param {Object} children content to be rendered
 * @returns React component
 */
export const PopupProvider = ({ children }: { children: React.ReactNode }) => {
  const [modal, setModal] = useState<string>("");
  const [values, setData] = useState<any>({});
  const [modalProps, setModalProps] = useState<any>();
  const [jobAlertData, setJobAlertData] = useState<any>(false);

  /**
   * Function to open the login modal
   * @param {string} value - Name of the modal or step to open
   * @returns void
   */
  const showModal = (value: string, modalProps: any): void => {
    if (modalProps) {
      setModalProps(modalProps);
    }

    if (value === "SignInJobalert") {
      setNewValue("isJobAlert", true);
    }
    setModal(value);

    document.body.style.overflow = "hidden";
  };

  /**
   * Function to close the login modal
   * @returns void
   */
  const closeModal = (): void => {
    setJobAlertData(null)
    if (values?.showSkip?.jobId) {
      window.open(`/view?id=${values?.showSkip?.jobId}`, "_blank");
      sessionStorage.setItem("skipLogin", "true");
      setNewValue("showSkip", false);
    }
    setModal("");
    setModalProps(undefined);
    document.body.style.overflow = "unset";

    // Remove callbackUrl from the URL without reloading
    const newSearchParams = new URLSearchParams(window.location.search);
    if (newSearchParams.get('callbackUrl')) {
      newSearchParams.delete("callbackUrl"); // Remove the param

      const newUrl =
        window.location.pathname +
        (newSearchParams.toString() ? `?${newSearchParams.toString()}` : "");

      // ✅ Update the URL without reloading the page
      window.history.replaceState(null, "", newUrl);
    }
    if (values.isJobAlert) {
      sessionStorage.setItem("isSignInClosed", "true");
      setNewValue("isJobAlert", false);
    }
  };

  /**
   * Function to set data from the Popup
   * @param {string} key - Name of the modal or step to open
   * @param {string} value - Name of the modal or step to open
   * @returns void
   */
  const setNewValue = (key: string, value: string | boolean): void => {
    setData((prevValues: any) => ({ ...prevValues, [key]: value }));
  };

  return (
    <PopupContext.Provider
      value={{ modal, values, modalProps, showModal, closeModal, setNewValue, setModalProps, jobAlertData, setJobAlertData }}
    >
      {children}
    </PopupContext.Provider>
  );
};

/**
 * Hook to retrieve popupContext values
 * @param {string} modal Name of the Modal
 * @param {function} showModal Opens the Modal
 * @param {function} closeModal Closes the Modal
 * @returns Popup context
 */
export const usePopupContext = () => useContext(PopupContext);
