"use server";

import {
  signIn,
  Too<PERSON><PERSON><PERSON>ttempts,
  OtpAttemptError,
} from "packages/frontend/services/jobseeker/auth";

/**
 * This function is meant to be used in client side login handlers
 * This functions is the only function to be used to Sign in or Sign Up users
 * This functions is the only method to be used to handle custom errors properly
 */
export async function signInModule(
  email: string,
  code: string,
  isAccountFound: boolean,
): Promise<any | { code: number; error: string }> {
  try {
    // Call the OTP Custom provider
    await signIn("otp", {
      email: email as string,
      code: code as string,
      isAccountFound: isAccountFound as boolean,
      redirect: false,
    });

    return { statusCode: 201 };
  } catch (error: any) {
    // Default error
    let customError = { statusCode: 404 };

    if (error.cause.err instanceof OtpAttemptError) {
      customError = { statusCode: 401 };
    }

    if (error.cause.err instanceof TooManyAttempts) {
      customError = { statusCode: 429 };
    }

    return customError;
  }
}
