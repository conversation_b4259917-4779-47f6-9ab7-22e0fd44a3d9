"use client";

import { MainPopupContainer } from "..";
import { MainPopupContainerProps } from "../interfaces";
import AccountIcon from "../../../../../../../libs/ui/src/icons/brand/createAccount.svg";
import { useTranslations } from "next-intl";
import { MainHeader } from "../components/MainHeader";
import { usePopupContext } from "@talent-front-services/jobseeker/modules/providers/PopupProvider";
import { StyledLegalParagraph, StyledPasswordForm } from "./styles";
import { FormProvider, SubmitHandler, useForm } from "react-hook-form";
import { ButtonPrimary } from "@talent-front-libs/ui/components/atoms/Button/variants/ButtonPrimary";
import { PasswordFormFields, PasswordFormSchema } from "@talent-front-services/jobseeker/modules/utils/schemaUtils";
import { zodResolver } from "@hookform/resolvers/zod";
import { BodySSb } from "@talent-front-libs/ui/theme/typography";
import { Link } from "@talent-front-services/jobseeker/navigation";
import { PasswordInput } from "@talent-front-services/jobseeker/components/molecules/InputPasswordControlled";
import { useSnackbar } from "@talent-front-services/jobseeker/modules/providers/SnackbarProvider";
import { useLocationContext } from "@talent-front-services/jobseeker/modules/providers/LocationProvider";
import { registerAccount } from "@talent-front-services/jobseeker/modules/network/user/auth";
import { getSocStorageData } from "@talent-front-services/jobseeker/modules/hooks/useEventData";

/**
 * OTP (One Time Password) - Sign In Popup
 */
export const CreatePassword = ({ isMobile, skipJobAlertHandler }: MainPopupContainerProps) => {
  const t = useTranslations("modal_account_creation");
  const { showModal, values, setNewValue, jobAlertData } = usePopupContext();

  const {ip} = useLocationContext()

  const createLabel = t.raw("create_an_account.create_your_account");
  const passwordLabel = t.raw("otp.password");
  const continueLabel = t.raw("landing_modal.continue");
  const snackbar = useSnackbar();


  const passwordFormSchemaTranslated = PasswordFormSchema(t)

  const formMethods = useForm<PasswordFormFields>({ resolver: zodResolver(passwordFormSchemaTranslated), mode: "onChange" });
  const { formState: { errors, isSubmitting, dirtyFields } } = formMethods;

  const handleBackClick = () => {
    if (jobAlertData) {
      showModal("SignInJobalert", jobAlertData);
      return
    }
    showModal("SignIn");
  };

  const onSubmit: SubmitHandler<PasswordFormFields> = async (data: PasswordFormFields) => {
    try {
      const email = values?.email;

      if (!email) throw new globalThis.Error("No Email");
      if (!data.password) throw new globalThis.Error("No Password");

      setNewValue("password", data.password as string);
      const socData = await getSocStorageData();
      const response: any = await registerAccount(email, data?.password, ip, {}, socData);

      if (response?.statusCode === 200 || response?.statusCode === 201) showModal("ValidateOtpCode");

      if (response?.statusCode === 503 || !response?.statusCode) snackbar.error();

    } catch (error) {
      snackbar.error()
      return false;
    }
  };

  const subtitle = () => (
    <>
      {t.rich("create_an_account.create_password_for", { email: <BodySSb>{values?.email}</BodySSb> as any })}
    </>
  )

  return (
    <MainPopupContainer isMobile={isMobile} onBack={handleBackClick} skipJobAlertHandler={skipJobAlertHandler}>

      <MainHeader 
        title={createLabel}
        subtitle={subtitle()}
        icon={<AccountIcon width={40} height={40} />}
      />

      <StyledPasswordForm>
        <FormProvider {...formMethods}>
          <PasswordInput errors={errors.password} isDirty={dirtyFields.password} label={passwordLabel} />
          <ButtonPrimary
            label={continueLabel}
            type={"button"}
            size={"large"}
            onClick={formMethods.handleSubmit(onSubmit)}
            disabled={isSubmitting || !dirtyFields.password || !!errors.password} 
          />
        </FormProvider>
      </StyledPasswordForm>

      <StyledLegalParagraph>
        {t.rich("landing_modal.by_continuing_i_agree", { 
          link1: (chunks) => <Link href="/terms-of-service">{chunks}</Link>,
          link2: (chunks) => <Link href="/privacy-policy">{chunks}</Link>,
          link3: (chunks) => <Link href="/cookie-policy">{chunks}</Link>,
        })}
      </StyledLegalParagraph>

    </MainPopupContainer>
  );
};
