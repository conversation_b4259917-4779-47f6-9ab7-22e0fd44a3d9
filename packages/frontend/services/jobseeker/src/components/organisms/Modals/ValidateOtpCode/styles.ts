import { BodyLSb, BodySRg, BodyXsRg } from "@talent-front-libs/ui/theme/typography";
import styled from "styled-components";
import theme from "../../../../../../../libs/ui/src/theme/themes";

export const NotificationErrorContainer = styled.div`
  display: block;
  position: absolute;
  top: 10px;
  z-index: 5;
  max-width: 100%;

  div {
    color: ${theme.light.color.white};
    text-decoration: none;
    background-color: ${theme.light.color.purple[600]};
    height: 72px;
    border: none;
    font-size: 14px;
  }
`;