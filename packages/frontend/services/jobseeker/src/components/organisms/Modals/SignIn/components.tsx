"use client";

import React from "react";
import { usePopupContext } from "@talent-front-services/jobseeker/modules/providers/PopupProvider";
import { ButtonPrimary } from "../../../../../../../libs/ui/src/components/atoms/Button/variants/ButtonPrimary/index";
import TalentLogo from "../../../../../../../libs/ui/src/icons/brand/talentIsotype.svg";
import JobAlertBell from "../../../../../../../libs/ui/src/icons/brand/jobAlertBell.svg";
import { ButtonSocialLogin } from "@talent-front-services/jobseeker/components/molecules/ButtonSocialLogin";
import {
  StyledSubHeading,
  StyledTextSeparator,
  StyledSeparator,
  StyledSignInForm,
  StyledBodyText,
  StyledHeading,
  HeaderContainerStyle,
  SkipJobAlertContainerStyle,
  TitleJobAlert,
} from "./styles";
import {
  Email<PERSON>ormFields,
  EmailFormSchema,
} from "@talent-front-services/jobseeker/modules/utils/schemaUtils";
import { FormProvider, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { InputPrimaryControlled } from "@talent-front-libs/ui/components/atoms/Input/variants/InputPrimaryControlled";
import { BodyLSb, BodySMd, HeaderSSb } from "@talent-front-libs/ui/theme/typography";
import theme from "@talent-front-libs/ui/theme/themes";
import {
  checkUserAccountStatus,
  registerAccount,
  sendSignInOtpCode,
} from "../../../../modules/network/user/auth";
import { getCountryFromDomain } from "../../../../modules/utils/geoUtils";
import { useTranslations } from "next-intl";
import { useSnackbar } from "@talent-front-services/jobseeker/modules/providers/SnackbarProvider";
import { useLocationContext } from "@talent-front-services/jobseeker/modules/providers/LocationProvider";
import { capitalizeFirstLetter } from "@talent-front-libs/ui/modules/utils/textUtils";
import { getSocStorageData } from "@talent-front-services/jobseeker/modules/hooks/useEventData";
import { cleanAllHTMLTags } from "@talent-front-services/jobseeker/modules/utils/jobDescriptionUtils";

/**
 * Text Header
 */
export const MainHeader: React.FC = () => {
  const t = useTranslations("modal_account_creation");

  return (
    <HeaderContainerStyle>
      <div data-testid="svg-logo">
        <TalentLogo width={40} height={40} />
      </div>
      <StyledHeading>
        {t.rich("landing_modal.your_job_search", {
          /** **/
          link: (chunks) => <HeaderSSb color={theme.light.color.purple[500]}>{chunks}</HeaderSSb>,
        })}
      </StyledHeading>
      <StyledSubHeading>{t.raw("landing_modal.create_an_account")}</StyledSubHeading>
    </HeaderContainerStyle>
  );
};

interface IJobDetails {
  title: string;
  location: string;
}
/**
 *
 */
export const MainHeaderJobAlert: React.FC<{ jobDetails: IJobDetails }> = ({ jobDetails }) => {
  const t = useTranslations("modal_account_creation");
  const jobTitle =  cleanAllHTMLTags(` ${capitalizeFirstLetter(jobDetails.title)} • ${capitalizeFirstLetter(jobDetails.location)}`);

  return (
    <HeaderContainerStyle>
      <div data-testid="svg-logo">
        <JobAlertBell width={40} height={40} />
      </div>
      <TitleJobAlert as="h3">
        {t.rich("job_alerts.do_you_want", {
          jobtitle: jobTitle,
          /** */
          wrapper: (chunks) => (
            <>
              <br />
              <BodyLSb color={theme.light.color.purple[500]}>{chunks}</BodyLSb>
            </>
          ),
        })}
      </TitleJobAlert>
      <StyledSubHeading>{t("landing_modal.create_an_account")}</StyledSubHeading>
    </HeaderContainerStyle>
  );
};

interface SkipJobAlertFlowProps {
  skipJobAlertHandler: () => void;
}

/**
 *
 */
export const SkipJobAlertFlow: React.FC<SkipJobAlertFlowProps> = ({ skipJobAlertHandler }) => {
  const t = useTranslations("modal_account_creation");

  return (
    <SkipJobAlertContainerStyle onClick={skipJobAlertHandler}>
      <BodySMd color={theme.light.color.purple[500]}>{t("job_alert.no_thanks_i")}</BodySMd>
    </SkipJobAlertContainerStyle>
  );
};

/**
 * Text Separator
 */
export const TextSeparator: React.FC = () => {
  const t = useTranslations("modal_account_creation");
  return (
    <StyledTextSeparator>
      <StyledSeparator />
      <StyledBodyText>{t("landing_modal.or")}</StyledBodyText>
      <StyledSeparator />
    </StyledTextSeparator>
  );
};

/**
 * Basic Auth Sign In Form
 */
export const SocialLoginForm: React.FC<{ buttonClickCallBack?: () => void }> = ({
  buttonClickCallBack,
}) => {
  const t = useTranslations("modal_account_creation");
  // This function sets the URI cookie and triggers the callback
  /**
   *
   */
  const handleClick = () => {
    //Cookie used to know the uri on the server side it will be deleted on packages/frontend/services/jobseeker/src/components/organisms/Modals/LoginRouter/index.tsx
    const currentUrl = new URL(window.location.href);
    document.cookie = `uri=${encodeURIComponent(currentUrl.href)}; path=/`;

    if (buttonClickCallBack) {
      buttonClickCallBack();
    }
  };
  return (
    <>
      <ButtonSocialLogin
        vendor={"google"}
        label={t.raw("otp.continue_with_google")}
        clickCallBack={() => handleClick()}
      />
      <ButtonSocialLogin
        vendor={"linkedin"}
        label={t.raw("otp.continue_with_linkedin")}
        clickCallBack={() => handleClick()}
      />
    </>
  );
};

/**
 * Basic Auth Sign In Form
 */
export const CredentialsForm: React.FC = () => {
  const { showModal, setNewValue } = usePopupContext();
  const t = useTranslations("modal_account_creation");
  const continueLabel = t.raw("landing_modal.continue");

  const { ip } = useLocationContext();
  const snackbar = useSnackbar();
  const EmailFormSchemaTranslated = EmailFormSchema(t);
  const formMethods = useForm<any>({ resolver: zodResolver(EmailFormSchemaTranslated) });

  /** */
  const onSubmit = async (data: EmailFormFields) => {
    try {
      // Set working values
      let route = "ValidateOtpSignIn";

      const countrySubdomain = getCountryFromDomain({ host: window.location.host });
      const topCountries = ["BR", "CA", "CH", "DE", "FR", "ES", "IN", "IT", "NL", "UK", "US", "GB"];
      const isTopTierCountry = !!topCountries.find(
        (country) => country === countrySubdomain.toUpperCase(),
      );

      const accountData: any = await checkUserAccountStatus(data?.email as string);

      if (
        accountData?.statusCode === 500 ||
        accountData?.statusCode === 503 ||
        !accountData?.statusCode
      ) {
        snackbar.error();
        return;
      }

      // Flag to know if the account exists and other flag values used in other steps
      const isAccountFound = accountData?.statusCode === 200;
      setNewValue("isAccountFound", isAccountFound);
      setNewValue("accountType", accountData?.data?.accountType);
      setNewValue("isTopTierCountry", isTopTierCountry);

      // Validate if the account has a password
      const isPasswordAccount =
        accountData?.data?.accountType === "password" ||
        accountData?.data?.accountType === "old_password";

      if (isAccountFound) {
        route = isPasswordAccount ? "ValidatePassword" : "ValidateOtpSignIn";

        if (!isPasswordAccount) {
          await sendSignInOtpCode(data?.email as string);
        }
      } else {
        route = isTopTierCountry ? "CreatePassword" : "ValidateOtpSignIn";

        if (!isTopTierCountry) {
          const socData = await getSocStorageData();
          await registerAccount(data?.email as string, "", ip, "", socData);
        }
      }

      setNewValue("email", data.email as string);
      showModal(route);
    } catch (error) {
      snackbar.error();
      return false;
    }
  };

  return (
    <StyledSignInForm>
      <FormProvider {...formMethods}>
        <InputPrimaryControlled name="email" type="email" label={t.raw("otp.email")} />
        <ButtonPrimary
          fullWidth={true}
          label={continueLabel}
          type={"button"}
          onClick={formMethods.handleSubmit(onSubmit)}
          size={"small"}
          disabled={formMethods.formState.isSubmitting}
        />
      </FormProvider>
    </StyledSignInForm>
  );
};
