import styled from "styled-components";
import theme from "../../../../../../../libs/ui/src/theme/themes";
import { BodySRg, BodyXLSb, BodyXsRg, HeaderSSb } from "@talent-front-libs/ui/theme/typography";

export const CloseIconStyle = styled.div`
  background-color: ${theme.light.color.gray[150]};
  border-radius: 50px;
  color: ${theme.light.color.gray[400]};
  cursor: pointer;
  height: 32px;
  position: absolute;
  right: 16px;
  top: 16px;
  width: 32px;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background-color: ${theme.light.color.gray[200]};
  }
`;

export const StyledSubHeading = styled(BodySRg)`
  text-align: center;
  color: ${theme.light.color.gray[400]};
  padding: 0 10px 16px;
  margin: 0 auto;
  max-width: 368px;
`;

export const StyledHeading = styled(HeaderSSb)`
  text-align: center;
  color: ${theme.light.color.black};
`;

export const StyledTextSeparator = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 16px auto;
  width: 100%;
`;

export const StyledSeparator = styled.div`
  background-color: ${theme.light.color.gray[150]};
  height: 1px;
  width: 100%;
`;
export const StyledBodyText = styled(BodyXsRg)`
  color: ${theme.light.color.gray[400]};
  padding: 0 24px;
`;

export const StyledSignInForm = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
  margin: 0 auto;
  width: 100%;

  & > form {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 16px;
    margin: 0 auto;
    width: 100%;
  }

  button {
    width: 100%;
  }

  input {
    font-size: 14px;
  }
`;

export const HeaderContainerStyle = styled.header`
  margin-top: 32px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
`;

export const SkipJobAlertContainerStyle = styled.div`
  margin: 14px;
  display: flex;
  flex-direction: column;
  align-items: center; 
  width: 100%;
  height: 26px;
  cursor: pointer;
`;
export const StyledLegalParagraph = styled(BodyXsRg)`
  color: ${theme.light.color.gray[400]};
  line-height: 18px;
  padding: 16px 0 0;
  text-align: center;

  a {
    color: ${theme.light.color.purple[500]};
    text-decoration: none;
  }

  a:hover {
    text-decoration: underline;
  }
`;

export const TitleJobAlert = styled(BodyXLSb)`
  color: ${theme.light.color.purple[800]};
  text-align: center;
  font-size: 18px;

  & > span {
    color: ${theme.light.color.purple[500]};
  }
`;
