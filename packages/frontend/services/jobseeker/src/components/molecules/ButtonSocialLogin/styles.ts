import styled from "styled-components";
import theme from "@talent-front-libs/ui/theme/themes";
import { BaseButton } from "@talent-front-libs/ui/components/atoms/Button";
import { BodySMd } from "@talent-front-libs/ui/theme/typography";

export const ButtonSocialStyle = styled(BaseButton)`
  width: 100%;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  cursor: pointer;
  gap: 16px;
  padding: 4px 56px 4px 24px;
  border-radius: 30px;
  border: 1px solid ${theme.light.color.gray[200]};
  color: ${theme.light.color.black};
  background-color: ${theme.light.color.white};
  &:hover {
    background-color: ${theme.light.color.gray[25]};
  }
  &:disabled {
    background-color: ${theme.light.color.gray[200]};
    border-color: ${theme.light.color.gray[200]};
  }
  & > span {
    flex: 1;
  }
`;

export const Span = styled(BodySMd)`
  color: ${theme.light.color.gray[400]};
  word-break: normal;
`;

export const LoadingIconStyle = styled.span`
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  text-align: center;
  text-decoration: none;
`;