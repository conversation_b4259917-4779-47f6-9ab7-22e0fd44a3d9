"use client";

import { useTranslations } from "next-intl";
import { useFilterContext } from "@talent-front-services/jobseeker/modules/providers/FiltersProvider";
import { OptionListDropdownType } from "@talent-front-libs/ui/components/molecules/ListDropdown/interfaces";
import {
  useGetSearchEventData,
  useSendEvent,
} from "@talent-front-services/jobseeker/modules/hooks/useEventData";
import { dataFilters } from "../../data";
import { FilterDropdownSerpProps } from "../FilterDropdownSerp/interfaces";
import { FilterDropdownMobileSerpProps } from "./interfaces";
import { FilterModalMobile } from "@talent-front-libs/ui/components/molecules/Filter/variants/FilterModalMobile";
/**
 * @component FilterDropdownMobileSerp
 */
export const FilterDropdownMobileSerp = ({ filterName }: FilterDropdownMobileSerpProps) => {
  const t = useTranslations("serp_jobs");
  const { filters, filtersCache } = useFilterContext();
  const searchData = useGetSearchEventData();
  const { sendEvents } = useSendEvent();
  const { name, options, defaultOption, title, anyOption } = dataFilters.find(
    (filter) => filter.name === filterName,
  ) as FilterDropdownSerpProps;

  /**** Options Filter - Translation ****/
  const translationLabel = {
    /** date */
    date: (option: OptionListDropdownType) =>
      t(option.label, { number: option?.value === "1" ? "24" : option.value }),
    /** job_type */
    job_type: (option: OptionListDropdownType) => t(option.label),
  };

  const filterOptions = options?.map((option: OptionListDropdownType) => {
    return { label: translationLabel[name](option), value: option.value };
  });

  const currentValue = filterOptions?.find((op) => op.value === filters.get(filterName));

  let filterDefaultOption = defaultOption && {
    label: translationLabel[name](defaultOption),
    value: defaultOption.value,
  };

  if (anyOption) {
    filterOptions.unshift({ label: t(anyOption.label), value: anyOption.value });
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    filterDefaultOption = anyOption && {
      label: translationLabel[filterName](anyOption),
      value: anyOption.value,
    };
  }

  const selectedOption = currentValue ?? filterDefaultOption;

  /** Remove Filter */
  const handleRemove = () => {
    filtersCache.current.remove([name, "id", "p"], true);
    sendEvents(
      {
        event_type: "filter_cleared",
        filter_name: name,
        filter_value_cleared: currentValue?.value,
      },
      {},
      searchData,
    );
  };

  /** Handle Filter */
  const handleSelect = ({ value }: OptionListDropdownType) => {
    if (!value) return;

    if (value === "any") {
      handleRemove();
      return;
    }

    filtersCache.current.remove(["id", "p"]);
    filtersCache.current.set({ name, value }, true);
    sendEvents(
      {
        event_type: "filter_applied",
        filter_name: name,
        filter_value: value,
      },
      {},
      searchData,
    );
  };

  const filterTitle =
    selectedOption && selectedOption.value !== "any" ? selectedOption.label : t(title);
  const isActive = selectedOption && selectedOption.value !== "any";

  return (
    <FilterModalMobile
      buttonType="buttonFilter"
      title={t(title)}
      buttonLabel={filterTitle}
      options={filterOptions}
      onReset={handleRemove}
      modalButtonLabel={{
        reset: "Reset" /* TO-DO: add translation */,
        update: "Update" /* TO-DO: add translation */,
      }}
      value={selectedOption as OptionListDropdownType}
      onSelect={handleSelect}
      isActive={isActive}
    />
  );
};
