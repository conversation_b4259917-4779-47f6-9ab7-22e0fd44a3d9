export interface JobData {
  system_legacy_id: string;
  IsPPC: boolean;
  ApplyType: string;
  id: string;
  source_title: string;
  enrich_company_name: string;
  source_location: string;
  logo: string;
  datefound?: string;
  geo_remote?: string;
  job_type?: string;
  source_jobtype?: string;
  salary_match?: string;
  source_jobdesc_html: string;
  system_apply_type?: string;
  enrich_jobdesc_html: string;
  source_jobdesc_text: string;
  enrich_date_posted?: string;
  source_date_posted: string;
  enrich_geo_country?: string;
  system_feedcode: string;
  system_date_found: string;
  enrich_jobtype: string[];
  enrich_geo_remote?: string;
  enrich_salary_min?: string;
  enrich_salary_max?: string;
  enrich_salary_type?: string;
  enrich_salary_currency?: string;
  enrich_salary_avg?: string;
  userApply?: boolean;
}

export interface JobDescriptionCardProps {
  jobData: JobData;
  isSameCountryRegion: boolean;
  onApplyClick?: (url?: any) => void;
}

export interface JobDescriptionHeaderProps {
  jobData: JobData;
  isMobile: boolean;
}
export interface JobDescriptionProps {
  jobData: JobData;
  isMobile: boolean;
}
export interface JobDescriptionDetailsProps {
  jobData: JobData;
}

export interface JobDescriptionContainerStyleProps {
  $isMobile: boolean;
}
