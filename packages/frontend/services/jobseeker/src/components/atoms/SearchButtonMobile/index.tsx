import theme from "@talent-front-libs/ui/theme/themes";
import { InputSearchProps } from "./interfaces";
import {
  ButtonCleanInputStyle,
  IconContainerStyle,
  ButtonContainerStyle,
  SearchTextContainerStyle,
  KeywordInputStyle,
  KeywordPlaceHolderStyle,
} from "./styles";
import IconClean from "../../../icons/solid/close.svg";

/**
 *
 */
export const SearchButtonMobile = ({
  value,
  setUserInput,
  onBackButtonClick,
  label,
  icon: Icon,
  testid,
}: InputSearchProps) => {
  /**
   *
   */
  const onClickHandler = () => {
    onBackButtonClick(true);
  };

  /** */
  const handleCleanInput = (e: React.MouseEvent<HTMLButtonElement, MouseEvent>) => {
    e.persist();
    setUserInput("");
  };

  return (
    <ButtonContainerStyle data-testid={testid}>
      <IconContainerStyle>
        <Icon color={theme.light.color.gray[800]} data-testid={"search-mobile-button-icon"} />
      </IconContainerStyle>
      <SearchTextContainerStyle onClick={onClickHandler} data-testid={"search-button-label"}>
        {value === "" ? (
          <KeywordPlaceHolderStyle>{label}</KeywordPlaceHolderStyle>
        ) : (
          <KeywordInputStyle>{value}</KeywordInputStyle>
        )}
      </SearchTextContainerStyle>
      {value !== "" && (
        <ButtonCleanInputStyle onClick={handleCleanInput}>
          <IconClean
            width="8px"
            height="8px"
            color={theme.light.color.gray[400]}
            data-testid={"search-button-clear-icon"}
          />
        </ButtonCleanInputStyle>
      )}
    </ButtonContainerStyle>
  );
};
