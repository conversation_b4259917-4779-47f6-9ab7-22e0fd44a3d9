"use client";

import theme from "@talent-front-libs/ui/theme/themes";
import { InputAutoCompleteProps } from "./interfaces";
import { useFilterContext } from "@talent-front-services/jobseeker/modules/providers/FiltersProvider";
import { ButtonCleanInputStyle } from "../inputSearch/styles";
import IconClean from "../../../icons/solid/close.svg";
import {
  InputPrimaryContainer,
  Label,
} from "@talent-front-libs/ui/components/atoms/Input/variants/InputPrimary/styles";
import { StyledInputPrimaryAutosuggest } from "./styles";
import {
  getAutosuggestKeyword,
  getAutosuggestLocation,
} from "@talent-front-services/jobseeker/modules/network/autosuggest/server";
/**
 *
 */
export const InputAutoComplete = ({
  name,
  value,
  onChangeValue,
  onSubmit,
  label,
  placeholder,
  disabled = false,
}: InputAutoCompleteProps) => {
  const { filtersCache } = useFilterContext();
  const inputState = disabled ? "disabled" : value ? "filled" : "default";

  /** */
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    onChangeValue(value);
    filtersCache.current.set({ name, value });
  };

  /** */
  const handleCleanInput = (e: React.MouseEvent<HTMLButtonElement, MouseEvent>) => {
    e.persist();
    onChangeValue("");
    filtersCache.current.remove(name);
  };

  /** */
  const handleKeyPress = (e?: React.KeyboardEvent<HTMLInputElement | HTMLDivElement>) => {
    if (e?.key === "Enter") {
      onSubmit?.(e);
    }
  };

  /** */
  const formatOptions = (items: any) => {
    if (name === "k") {
      return items.map((v: any) => v.keyword);
    } else {
      return items.map((v: any) => `${v.city}, ${v.region}`);
    }
  };

  return (
    <InputPrimaryContainer $inputState={inputState}>
      <Label $inputState={inputState}>{label}</Label>
      <StyledInputPrimaryAutosuggest
        placeholder={placeholder}
        type="text"
        label={label}
        inputMode="text"
        name={name}
        value={value}
        onChange={handleChange}
        onKeyDown={handleKeyPress}
        getterFunction={name === "k" ? getAutosuggestKeyword : getAutosuggestLocation}
        formatOptions={formatOptions}
        disabled={disabled}
      />
      {value !== "" && (
        <ButtonCleanInputStyle onClick={handleCleanInput}>
          <IconClean width="10px" height="10px" color={theme.light.color.gray[400]} />
        </ButtonCleanInputStyle>
      )}
    </InputPrimaryContainer>
  );
};
