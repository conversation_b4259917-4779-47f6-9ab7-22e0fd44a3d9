import theme from "@talent-front-libs/ui/theme/themes";
import styled from "styled-components";
import { MobileStylesProps } from "./interfaces";

export const SectionWrapper = styled.section<MobileStylesProps>`
  width: 100%;
  height: auto;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: ${(p) => (p.$isMobile ? "12px 8px" : "20px 0 40px")};
  background-color: ${theme.light.color.gray[25]};
  overflow: auto;
  flex-direction: column;
  gap: 24px;
  margin: 0;
`;
export const ContentWrapper = styled.div`
  width: 100%;
  max-width: 900px;
  height: 90%;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  z-index: 2;
  gap: 16px;
  padding: 0;
  margin: 0;
`;

export const MobileSearchBarContainer = styled.div`
display: flex;
flex-direction: column;
alignItems: start; 
gap: 8px; 
width: 100%
`;

export const MobileSearchBarStyle = styled.div`
display: flex;
padding: 12px 0 20px 0;
justify-content: center;
align-items: center;
align-self: stretch;
border-radius: 8px;
background: #FFF;
`;
