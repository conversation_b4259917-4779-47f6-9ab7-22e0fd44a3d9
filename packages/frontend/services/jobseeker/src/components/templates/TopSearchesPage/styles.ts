import styled from "styled-components";
import { MobileStylesProps } from "./interfaces";

export const SectionWrapper = styled.section<MobileStylesProps>`
      max-width: 900px;
      margin: 65px 15px;
      margin: ${(p) => (p.$isMobile ? "65px 15px" : "65px auto")};
`;

export const TopSearchesTitleHeaderStyle = styled.section`
`;

export const TitleUnderlyingStyle = styled.div`
  border-top: 1px solid #E1DCE2;
  margin-top: 20px;
  padding-top: 20px;
`;

export const TopSearchesTitleStyle = styled.div`
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  line-height: 30px;
  color: #30183f;
`;