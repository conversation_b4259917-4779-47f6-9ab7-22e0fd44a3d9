export const jsonCodePixel = `<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Implementation of the Talent.com pixel using JavaScript</title>
</head>

<body>

    <!--This is how to make a pixel get triggered via an onclick button via JS. You need to add the onClick=”” function in your HTML code. 
        This demo uses a stand-alone button, but we encourage you to integrate the function into your existing HTML. -->

    <!-- If you do not provide an applyStep function parameter, it will default to application -->
    <button onclick="triggerTalentPixelSync()">Trigger</button>

    <!-- You can however, manually pass in different applySteps at different points in the application process-->
    <button onclick="triggerTalentPixelSync('apply_start')">Track Application Start</button>
    <button onclick="triggerTalentPixelSync('apply_submit')">Track Application Submit</button>

    <script>

        /** 
         * Synchronously create an image element and append it to the body of your page 
         * once the element is appended to your page, the request will trigger the pixel 
         * @param applyStep optional parameter to trigger pixel at different stages of the application process 
         */

        function triggerTalentPixelSync(applyStep = "application") {

            // the pixel will be wrapped inside of a paragraph element 
            let talentPixelWrapper = document.createElement("p");

            // the pixel itself is just an image element 
            let talentPixel = document.createElement("IMG");

            // the URL provided by your talent account representative 

            // ensure that the URL contains the GET parameter "tracker" with your company name/alias 
            let providedURL = "https://www.talent.com/tracker/img-pixel.php?tracker=COMPANY_TRACKER";

            // the final pixel url should also contain some indication of the applicationStep 
            let pixelURL = providedURL + "&apply_step=" + applyStep;

            // set the target link 
            talentPixel.setAttribute("src", pixelURL);

            // append the image element to the paragraph element 
            talentPixelWrapper.appendChild(talentPixel);

            // append the pixel either to the body element of your page or another element 
            document.body.appendChild(talentPixelWrapper);

        }
    </script>
</body>

</html>`;