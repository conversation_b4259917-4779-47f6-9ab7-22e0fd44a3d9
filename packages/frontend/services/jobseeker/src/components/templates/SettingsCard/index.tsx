"use client";

import { useState, useEffect } from "react";
import { <PERSON>er<PERSON>b, BodySRg, BodySMd } from "@talent-front-libs/ui/theme/typography";
import {
  CardMainNotifications,
  MainWrapNotifications,
  FlexColumnContainer,
  Divider,
} from "./styles";
import { SettingsCardContainer } from "../../molecules/SettingsCard";
import { TopActions, MainHeader, ChangePassword } from "./components";
import { signOut } from "next-auth/react";
import { SettingsCardProps } from "./interfaces";
import { useSendEvent } from "@talent-front-services/jobseeker/modules/hooks/useEventData";
import { useTranslations } from "next-intl";
import { useSnackbar } from "@talent-front-services/jobseeker/modules/providers/SnackbarProvider";
import { deleteAccount } from "@talent-front-services/jobseeker/modules/network/user/account";
import { setUISelectionRaw } from "@testing-library/user-event/dist/types/document/UI";
import { useUserContext } from "@talent-front-services/jobseeker/modules/providers/UserProvider";

/**
 *
 */
export const SettingsCard = ({
  isMobile,
  userData,
  accountType,
  eventMetaData,
}: SettingsCardProps) => {
  const [activeTab, setActiveTab] = useState<"MAINSCREEN" | "PASSWORD" | "DELETE">("MAINSCREEN");
  const [disableDeleteButton, setDisableDeleteButton] = useState<boolean>(false);

  const t = useTranslations("profile_settings");
  const s = useTranslations("serp_jobs");
  const { sendEvents } = useSendEvent();
  const { setUser } = useUserContext();
  const Snackbar = useSnackbar();

  useEffect(() => {
    const startTime = performance.now();
    const endTime = performance.now();
    const timeLoaded = endTime - startTime;
    const loadTime = Math.round(timeLoaded);
    sendEvents(
      {
        event_type: "page_viewed",
        pageloadtime_millisec: loadTime,
      },
      eventMetaData,
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  /**
   * Handle the click on the "Delete account" button in the second container
   */
  const handleConfirmDeleteAccountClick = async () => {
    setDisableDeleteButton(true)
    const summitDeleteAccount = await deleteAccount();

    if (summitDeleteAccount?.result === "ok") {
      setUser(null);
      await signOut({ callbackUrl: "/jobs?d=1" });
    } else {
      setActiveTab("MAINSCREEN");
      setDisableDeleteButton(false)
      Snackbar.show({ message: s("error_messages.we_re_experiencing") });
    }
  };

  /**
   *
   */
  const handleOnChangeSuccess: () => void = () => {
    setActiveTab("MAINSCREEN");
    Snackbar.show({ message: t("pop_up_message.password_changed") });
  };

  return (
    <MainWrapNotifications>
      <CardMainNotifications isMobile={isMobile} isActive={false}>
        {activeTab === "MAINSCREEN" && (
          <FlexColumnContainer>
            <HeaderSSb>{t("header_1.settings")}</HeaderSSb>
            <BodySRg>
              <BodySMd>{t("header_2.email", { email: userData?.email ?? "" })}</BodySMd>{" "}
              {/* Display the email from the session */}
            </BodySRg>
            {accountType !== "otp" && (
              <>
                <SettingsCardContainer
                  title={t("cta.manage_password")}
                  onClick={() => setActiveTab("PASSWORD")}
                  mode="manage"
                />
                <Divider />
              </>
            )}
            <SettingsCardContainer
              title={t("cta.delete_account")}
              onClick={() => setActiveTab("DELETE")}
              mode="delete"
            />
          </FlexColumnContainer>
        )}
        {activeTab === "DELETE" && (
          <FlexColumnContainer>
            <TopActions onClose={() => setActiveTab("MAINSCREEN")} />
            <MainHeader onDeleteClick={handleConfirmDeleteAccountClick} t={t} disableButton={disableDeleteButton} />
          </FlexColumnContainer>
        )}
        {activeTab === "PASSWORD" && (
          <FlexColumnContainer>
            <TopActions onClose={() => setActiveTab("MAINSCREEN")} />
            <ChangePassword onChangeSuccess={handleOnChangeSuccess} />
          </FlexColumnContainer>
        )}
      </CardMainNotifications>
    </MainWrapNotifications>
  );
};
