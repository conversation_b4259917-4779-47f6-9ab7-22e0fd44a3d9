import styled from "styled-components";
import theme from "@talent-front-libs/ui/theme/themes";
import { CardPrimary } from "@talent-front-libs/ui/components/atoms/Card/variants/CardPrimary";
import { CheckStylesProps } from "./interfaces";
import { IconWrapperColorStylesProps } from "./interfaces";

export const MainWrap = styled.div`
  display: flex;
  padding: 80px 0;
  background-color: ${theme.light.color.gray[25]};
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
`;

export const StyledContainer = styled(CardPrimary)`
  display: flex;
  flex: unset;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  max-height: inherit;
  overflow: auto;
  cursor: unset;
  max-width: 480px;
  padding: 40px;
  &:hover,
  &:focus {
    box-shadow: none;
    outline: none;
  }
  ${(props) =>
    props?.isMobile &&
    `
    padding:24px 16px 40px;
    width: 100%;
  `}
`;

export const StyledHeading = styled.div`
  color: ${theme.light.color.black};
  font-size: 20px;
  font-weight: 600;
  line-height: 30px;
  text-align: center;

  span {
    color: ${theme.light.color.purple[500]};
    font-size: inherit;
    font-weight: inherit;
    line-height: inherit;
  }
`;

export const StyledSubHeading = styled.div`
  text-align: center;
  color: ${theme.light.color.gray[400]};
  font-size: 14px;
  line-height: 22.4px;
  padding: 0 10px 16px;
  margin: 0 auto;
  max-width: 368px;

  & b {
    font-weight: 600;
  }
`;

export const StyledPasswordForm = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
  margin: 0 auto;
  width: 100%;

  & > form {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 16px;
    margin: 0 auto;
    width: 100%;
  }

  button {
    width: 100%;
  }

  input {
    font-size: 14px;
  }
`;

export const StyledLegalParagraph = styled.p`
  color: ${theme.light.color.gray[400]};
  font-size: 12px;
  line-height: 18px;
  padding: 16px 0 0;
  text-align: center;

  a {
    color: ${theme.light.color.purple[500]};
    text-decoration: none;
  }

  a:hover {
    text-decoration: underline;
  }
`;

/**
 * Error Group Styling
 */
export const Container = styled.div`
  display: flex;
  flex-direction: row;
  gap: 8px;
`;

export const Text = styled.p<CheckStylesProps>`
  color: ${(p) => (p.$check ? theme.light.color.gray[400] : theme.light.color.red[300])};
  text-align: right;
  line-height: 18px;
  font-size: 12px;
`;

export const GroupContainer = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  gap: 8px;
  width: 100%;
  padding: 0 0 0 24px;
`;

export const WrapperColor = styled.div<IconWrapperColorStylesProps>`
  display: flex;
  justify-content: center;
  align-items: center;
  & svg {
    width: ${(p) => `${p?.$width}px` ?? "auto"};
    height: ${(p) => `${p?.$height}px` ?? "auto"};
    flex: 1;
  }

  ${(p) =>
    `
        & svg > * { 
            fill: ${p.$color === "green" ? theme.light.color.green[300] : theme.light.color.red[300]};
            width: auto;
            height: 100%;
        }
    `}
`;
