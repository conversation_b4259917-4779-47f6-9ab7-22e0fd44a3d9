import React from "react";
import {
  TaxDetailsContainer,
  TaxInfo,
  WithholdingHeader,
  Divider,
  WithholdingDataContainer,
} from "../../styles";
import { BodySSb, BodySRg, BodyLSb, BodyMSb } from "@talent-front-libs/ui/theme/typography";
import { IWithholdingProps } from "../../interfaces";
import TaxPieChart from "@talent-front-services/jobseeker/components/organisms/TaxCalculatorChart";
import theme from "@talent-front-libs/ui/theme/themes";
import { HeaderDivider, WithholdingWrapper } from "./styles";

/**
 *
 */
const WithholdingCard = ({ taxinfo, salary, chartData, t }: IWithholdingProps) => {
  // If salary has a space between the currency and the amount, it will replace the space with an un-breakable space
  const cleanSalary = salary?.at(1) === " " ? salary.replace(" ", "\u00A0") : `\u00A0${salary}`;
  //

  if (taxinfo === undefined) {
    return <></>;
  }
  return (
    <WithholdingWrapper>
      <WithholdingHeader>
        <BodyLSb color={theme.light.color.purple[800]}>{t("withholding_box.withholding")}</BodyLSb>
      </WithholdingHeader>
      <HeaderDivider />
      <WithholdingDataContainer>
        <TaxDetailsContainer>
          <TaxInfo>
            <BodySSb color={theme.light.color.gray[800]}>{t("withholding_box.salary")}</BodySSb>
            <BodySSb color={theme.light.color.gray[800]}>{cleanSalary ?? ""}</BodySSb>
          </TaxInfo>
          {Array.from(taxinfo.entries()).map(([key, value]) => {
            // If value has a space between the currency and the amount, it will replace the space with an un-breakable space
            const cleanValue =
              value?.at(1) === " " ? value.replace(" ", "\u00A0") : `\u00A0${value}`;
            //

            if (key === "total_tax") {
              return (
                <>
                  <Divider />
                  <TaxInfo>
                    <BodySSb color={theme.light.color.gray[800]}>
                      {t(`withholding_box.${key}`)}
                    </BodySSb>
                    <BodySSb color={theme.light.color.gray[800]}>{`\u00A0-${cleanValue}`}</BodySSb>
                  </TaxInfo>
                </>
              );
            }
            if (key === "net_pay") {
              return (
                <>
                  <TaxInfo>
                    <BodyMSb color={theme.light.color.gray[800]}>
                      {t(`withholding_box.${key}`)}
                    </BodyMSb>
                    <BodyMSb color={theme.light.color.gray[800]}>{`\u00A0*${cleanValue}`}</BodyMSb>
                  </TaxInfo>
                  <Divider />
                </>
              );
            }
            return (
              <TaxInfo key={`${key}-${value}`}>
                <BodySRg color={theme.light.color.gray[400]}>{t(`withholding_box.${key}`)}</BodySRg>
                <BodySRg
                  color={theme.light.color.gray[800]}
                >{`${key === "marginal_tax_rate" || key === "average_tax_rate" ? `${cleanValue}` : `\u00A0-${cleanValue}`}`}</BodySRg>
              </TaxInfo>
            );
          })}
        </TaxDetailsContainer>
        <TaxPieChart
          netTaxPercentage={chartData?.total_tax_per}
          netPayPercentage={chartData?.net_pay_per}
          rotate={chartData?.rotate}
        />
      </WithholdingDataContainer>
    </WithholdingWrapper>
  );
};

export default WithholdingCard;
