import { BaseCard } from "@talent-front-libs/ui/components/atoms/Card";
import theme from "@talent-front-libs/ui/theme/themes";
import { BodyLSb, BodySRg } from "@talent-front-libs/ui/theme/typography";
import { isMobileStyle } from "@talent-front-services/jobseeker/modules/styles";
import styled, { css } from "styled-components";

export const mobileStyleCardContainer = isMobileStyle(css`
  width: 100%;
`);

export const CardSummary = styled(BaseCard)`
  width: 700px;
  position: relative;
  display: flex;
  flex-direction: column;
  border-radius: 8px;
  padding: 24px;
  gap: 24px;
  background-color: ${theme.light.color.white};

  ${mobileStyleCardContainer}
`;

export const Title = styled(BodyLSb)`
  color: ${theme.light.color.purple[800]};
  line-height: 27px;
  padding: 0 0 24px 0;
  border-bottom: 1px solid ${theme.light.color.gray[150]};
`;

export const TextContent = styled(BodySRg).attrs((props) => ({
  ...props,
  as: "p",
}))`
  color: ${theme.light.color.gray[400]};
  line-height: 21px;

  & > strong {
    font-weight: ${theme.light.fontWeight.bold};
  }
`;
