"use server";

/**
 * Use this file to declare server actions for apply processes
 */

import { headers } from "next/headers";
import {
  generateUrlForFileUpload,
  uploadFileToS3,
} from "@talent-front-services/jobseeker/modules/network/user/uploadFileToS3";
import { getS3BucketByHost } from "@talent-front-services/jobseeker/modules/utils/geoUtils";
import { uploadToUserFileDatabase } from "@talent-front-services/jobseeker/modules/network/user/file";
import { hash } from "../../../../../../../../../libs/privacy/src/lib/cipher";

/**
 * Helper function to generate the file key
 */
const generateFileKey = (userId: string, fileName: string, bucket: string) => {
  const extension = fileName.split(".").pop() || "";
  const hashedFileName = hash(fileName);
  return `${bucket.endsWith("eu") ? "s3_eu" : "s3_us"}_${userId}_${hashedFileName}.${extension}`;
};

/**
 * Helper function to upload file to the user file database
 */
const saveFileToDatabase = async (
  userId: string,
  fileName: string,
  fileKey: string,
  extension: string,
) => {
  return await uploadToUserFileDatabase({
    user_id: userId,
    file_name: fileName,
    s3_file_id: fileKey,
    file_extention: extension,
  });
};

/**
 * Upload file when user becomes a talent user and now we got the userid
 */
export const uploadUserFile = async (userId: string, formData: any, fileName: string) => {
  try {
    // Prepared the data
    const headersList = await headers();
    const host = headersList.get("host") || "undefined";
    const bucket = await getS3BucketByHost({ host });
    const fileKey = generateFileKey(userId, fileName, bucket);
    const fileExtension = fileName.split(".").pop() || "";
    const dataForUrl = { bucket: "", file_key: fileKey };

    // Generate upload URL
    const {
      data: { url },
    } = await generateUrlForFileUpload({ data: dataForUrl });

    // Upload the file to S3
    await uploadFileToS3({ file: formData, url });

    // Save file details to database
    const userFileDb = await saveFileToDatabase(userId, fileName, fileKey, fileExtension);

    return { id: userFileDb.id, file_name: fileName };
  } catch (e: any) {
    throw new Error(`Error uploading the file`);
  }
};
