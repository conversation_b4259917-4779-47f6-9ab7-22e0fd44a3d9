import { QuestionTabsInterface } from "@talent-front-services/jobseeker/components/templates/Apply/interfaces";
import {
  AdditionalContent,
  FooterContent,
  TabContainer,
} from "@talent-front-services/jobseeker/components/templates/Apply/styles";
import { ApplyQuestionSelector } from "@talent-front-services/jobseeker/components/organisms/ApplyQuestionSelector";
import { ButtonPrimary } from "@talent-front-libs/ui/components/atoms/Button/variants/ButtonPrimary";
import { Link } from "@talent-front-services/jobseeker/navigation";
import { useTranslations } from "next-intl";

/** Tab to display on editing question from summary */
export const QuestionsTabs = ({
  groupedQuestions,
  activeTab,
  handleNextStep,
  handleBack,
}: QuestionTabsInterface & { handleBack: () => void }) => {

  const t = useTranslations("apply_talent_apply")

  return (
    <div>
      {groupedQuestions.map((questionsGroup: any, index: number) => {
        const keyQuestion = `${index}-${questionsGroup.id}}`;

        return (
          <TabContainer
            $display={activeTab === groupedQuestions.indexOf(questionsGroup) + 1 ? "flex" : "none"}
            key={keyQuestion}
          >
            <ApplyQuestionSelector questionsGroup={questionsGroup} isHierarchical={false} />
            <ButtonPrimary
              label={t("cta.continue")}
              type={"button"}
              onClick={handleNextStep}
              data-testid="login-continue-button"
            />
            <AdditionalContent>
              { t.rich("footer.by_continuing_i", {
                link1: (chunks) => <Link href="/terms-of-service">{chunks}</Link>,
                link2: (chunks) => <Link href="/privacy-policy">{chunks}</Link>,
                link3: (chunks) => <Link href="/cookie-policy">{chunks}</Link>
              })}
            </AdditionalContent>
            <FooterContent>
              {t("footer.questions")}{" "}{t.rich("footer.visit_our_help", {link: (chunks) => <Link href="https://helpcenter.talent.com/hc/">{chunks}</Link>})}
            </FooterContent>
          </TabContainer>
        );
      })}
    </div>
  );
};
