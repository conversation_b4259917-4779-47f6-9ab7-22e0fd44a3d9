import { HeaderSB, BodyMB, BodyMRg, BodySRg } from "@talent-front-libs/ui/theme/typography";
import { Logo } from "@talent-front-services/jobseeker/components/organisms/Navbar";
import {
  BulletItem,
  BulletItemSecondary,
  BulletList,
  Card,
  CenterWrapper,
  IndentWrapper,
  MainContainer,
  TableContainer,
  Table,
} from "../styles";
import theme from "@talent-front-libs/ui/theme/themes";
import { tableDataApply, tableReturnedFileApply } from "./jsonData";
/**
 * component for pixel documentation
 */
export const RecApiApplyDocumentation = () => {
  return (
    <MainContainer>
      <Logo width="100%" height="100%" />
      <HeaderSB>REC API - APPLY DATA</HeaderSB>
      <Card>
        <BodyMRg>Talent.com Reconciliation API Apply Documentation</BodyMRg>
        <br />
        <BodyMB>What is the Talent.com Reconciliation API - Apply?</BodyMB>
        <BodyMRg>
          The Reconciliation - API Apply service allows access to apply data without needing to log
          in to our portal. There are two display formats offered: json (default), and xml.
        </BodyMRg>
        <br />
        <BodyMB>Setting up the Talent.com Reconciliation API Apply</BodyMB>
        <BodyMRg>
          Your IT, or Product department will need to configure the Reconciliation API Apply.
        </BodyMRg>
        <BodyMRg>
          The data is provided inside an array. The response needs to be scanned for the{" "}
          {`"result"`}
          field to see if there is a success or fail. If the {`"result"`} provides a success, the
          file is ready. If the {`"result"`} has failed, an error message will be provided in a
          description field. The format of the response will match the output (json or xml).
        </BodyMRg>
        <br />
        <BodyMRg>For example (json):</BodyMRg>

        <BulletList>
          <BulletItem>
            <BodyMRg> {`"status" :{"result":"success"}`}</BodyMRg>
          </BulletItem>

          <BulletItem>
            <BodyMRg>{`"status" :{"result":"fail","description":"Error message"}`}</BodyMRg>
          </BulletItem>
        </BulletList>
        <br />
        <BodyMB>Sample: </BodyMB>
        <CenterWrapper>
          <BodyMRg>
            <IndentWrapper data-testid="text-url">
              {`https://reconciliation.talent.com/api/reconciliation/reconciliation-apply.php?key=talentdemo&format=json&dateFrom=**********&dateTo=**********`}
            </IndentWrapper>
          </BodyMRg>
        </CenterWrapper>
        <br />
        <CenterWrapper>
          <BodyMB>**The key= parameter will be provided by the account management team**</BodyMB>
        </CenterWrapper>
        <CenterWrapper>
          <BodyMB color={theme.light.color.red[500]}>
            **If you are a user of the Talent.com Rec API, you may use the same key**
          </BodyMB>
        </CenterWrapper>
        <br />
        <BodyMRg>
          The service will generate data according to parameters above or by default.
        </BodyMRg>
        <br />
        <BodyMB>Limits and Important Information:</BodyMB>
        <br />
        <BulletList>
          <BulletItem>
            <BodyMRg>
              The API can only be pinged once per minute. If this is exceeded, the API will throw an
              error message saying something went wrong. This is to ensure that servers can handle
              the requests that are being received and provide accurate and up to date data.
            </BodyMRg>
          </BulletItem>

          <BulletList>
            <BulletItemSecondary>
              <BodyMRg>Sample error:</BodyMRg>
              <br />
              <BodyMB>
                Too many requests. You have sent too many request in a given amount of time.
              </BodyMB>
            </BulletItemSecondary>
          </BulletList>
          <BulletItem>
            <BodyMRg>
              Calls can be made for up to 40 days of data. For requests going back more than 40
              days, a report will need to be requested from the account management team.
            </BodyMRg>
          </BulletItem>
          <BulletItem>
            <BodyMRg>
              Data cannot be called in real time. The earliest available hour range is the current
              time (Eastern time), minus two hours (floored).
            </BodyMRg>
          </BulletItem>
          <BulletItem>
            <BodyMRg>
              The Reconciliation API Apply relies on eventual consistency. There may be adjustments
              in the case of rare data inconsistencies, or, to remove unbillable events.
            </BodyMRg>
          </BulletItem>
          <BulletItem>
            <BodyMRg>
              Apply data can be linked in post-processing to an optional parameter in the Talent.com
              Rec API. Additional documentation can be viewed <IndentWrapper>here.</IndentWrapper>
              <BulletList>
                <BulletItemSecondary>
                  <BodyMRg>
                    It is possible to have applies that do not relate to an ID from the
                    Reconciliation API. This can happen if user behaviour alters the tracking
                    parameter in the redirect.
                  </BodyMRg>
                </BulletItemSecondary>
              </BulletList>
            </BodyMRg>
          </BulletItem>
        </BulletList>
        <br />
        <Card>
          <BodyMB color={theme.light.primaryColor}>Parameters</BodyMB>
          <br />
          <TableContainer>
            <Table className="table-seven-columns">
              <thead>
                <tr>
                  <th>Parameter</th>
                  <th>Format</th>
                  <th>Default</th>
                  <th>Required</th>
                  <th>Options</th>
                  <th>Context</th>
                  <th>Example Request</th>
                </tr>
              </thead>
              <tbody>
                {tableDataApply.map((row, index) => (
                  <tr key={`apply-${row.parameter}`}>
                    <td>{row.parameter}</td>
                    <td>{row.format}</td>
                    <td>{row.default}</td>
                    <td>{row.required}</td>
                    <td>{row.options}</td>
                    <td>{row.context}</td>
                    <td>{row.exampleRequest}</td>
                  </tr>
                ))}
              </tbody>
            </Table>
          </TableContainer>
        </Card>
        <br />
        <Card>
          <BodyMB color={theme.light.primaryColor}>Returned Fields</BodyMB>
          <br />
          <TableContainer>
            <Table className="table-three-columns">
              <thead>
                <tr>
                  <th>Returned Field</th>
                  <th>Context</th>
                  <th>Example Response</th>
                </tr>
              </thead>
              <tbody>
                {tableReturnedFileApply.map((row) => (
                  <tr key={row.returnedField}>
                    <td>{row.returnedField}</td>
                    <td>{row.context}</td>
                    <td>{row.exampleResponse}</td>
                  </tr>
                ))}
              </tbody>
            </Table>
          </TableContainer>
        </Card>
      </Card>

      <footer>
        <CenterWrapper>
          <BodySRg color={theme.light.color.gray[300]}>Last updated October 2024</BodySRg>
        </CenterWrapper>
      </footer>
    </MainContainer>
  );
};
