"use server";

import { NextRequest, NextResponse } from "next/server";
import { revalidatePath } from "next/cache";
import { cookies } from "next/headers";
import { signIn } from "../../../../auth";
import * as privacy from "../../../../../../../../libs/privacy/src";

type UserCookie = {
  name: string;
  value: string;
};

/**
 * This Handler Migrates the user from the previous Session (PHP)
 * to the new Session (Next-Auth)
 */
export async function GET(request: NextRequest) {
  // Calculate Host (In dockers, the host is 0.0.0.0)
  const host = request.headers.get("host");
  const proto = request.nextUrl.origin?.includes("localhost") ? "http" : "https";
  const origin = `${proto}://${host}`;
  // Create the response to redirect the user
  const { searchParams } = new URL(request.nextUrl);

  let redirectUri = searchParams.get("redirectUri") ?? "/";
  redirectUri = atob(redirectUri);

  const url = new URL(redirectUri, origin);
  const response = NextResponse.redirect(url);

  // To migrate the user session, check for valid cookies first
  const cookieStore = cookies();
  const userCookie: UserCookie | null = cookieStore.get("user-token") ?? null;

  // Start the migration process ONLY when the cookie is present
  try {
    if (userCookie) {
      // Decrypt the user token from the cookie
      const sessionData: any = privacy.legacy.decrypt(userCookie?.value as string);
      const token = JSON.parse(sessionData)?.token ?? null;
      const userAgent = request.headers.get("user-agent");

      // Start Sign In Process
      if (token) await signIn("migration", { token: token, userAgent: userAgent, redirect: false });
    }
  } catch (error) {
    console.log("Migration not processed");
  }

  // Remove the previous cookie from the user (both or it won't work)
  response.cookies.set("user-token", "", {
    expires: new Date(0),
    path: "/",
    domain: ".talent.com",
  });

  revalidatePath(redirectUri, "layout");

  return response;
}
