import { handlers } from "../../../../../auth";
import { NextRequest } from "next/server";

/**
 * Trust the Origin to avoid Callbacks with 0.0.0.0 in the Hostname
 * @param { NextRequest } req NextRequest Object
 * @returns { NextRequest } NextRequest Response
 */
const reqWithTrustedOrigin = (req: NextRequest): NextRequest => {
  const host = req.headers.get("x-forwarded-host");
  const proto = host?.includes("localhost") ? "http" : "https";
  if (!host) {
    console.warn("Missing x-forwarded-proto or x-forwarded-host headers.");
    return req;
  }
  const envOrigin = `${proto}://${host}`;
  const { href, origin } = req.nextUrl;
  return new NextRequest(href.replace(origin, envOrigin), req);
};

/**
 * GET Handler
 * @param { NextRequest } req NextRequest Object
 * @returns { NextRequest } NextRequest Response
 */
export const GET = (req: NextRequest) => {
  return handlers.GET(reqWithTrustedOrigin(req));
};

/**
 * POST Handler
 * @param { NextRequest } req NextRequest Object
 * @returns { NextRequest } NextRequest Response
 */
export const POST = (req: NextRequest) => {
  return handlers.POST(reqWithTrustedOrigin(req));
};
