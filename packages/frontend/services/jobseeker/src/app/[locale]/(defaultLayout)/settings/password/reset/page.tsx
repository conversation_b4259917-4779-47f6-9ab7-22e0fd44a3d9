import React from "react";
import { redirect } from "next/navigation";
import { PasswordResetPage } from "@talent-front-services/jobseeker/components/templates/PasswordResetPage";
import * as privacy from "libs/privacy/src";

/**
 * Password Reset Page
 */
export default async function PasswordReset({
  searchParams,
}: {
  searchParams: { [key: string]: string | string[] | undefined }
}) {

  const encodedToken = searchParams?.token as string;
  const hexRegex = /^[A-Za-z0-9+/]+={0,2}$/;
  let email, token = "";

  try {
    if (!hexRegex.test(encodedToken)) throw new Error("Invalid token");
    const decodedToken = atob(encodedToken);
    const data = JSON.parse(decodedToken);
    const expiration = data?.expiration;
    const currentTime = Date.now();
    // Check expiration time
    if (!expiration || (expiration < currentTime)) throw new Error("Invalid token");
    email = privacy.cipher.decrypt(data?.email);
    token = data?.token;
  } catch (error) {
    redirect("/");
  }

  return (
    <PasswordResetPage email={email} token={token}/>
  );
}
