"use server";

import { Metada<PERSON>, ResolvingMetadata } from "next";
import JobDescription from "@talent-front-services/jobseeker/components/templates/JobDescriptionPage";
import { getJobsJobMeta } from "@talent-front-services/jobseeker/modules/network/jobs/server";
import {
  isSameCountryRegion,
  getHost,
} from "@talent-front-services/jobseeker/modules/utils/geoUtilsServer";
import { TranslateMetadata } from "./translations";
import { getLocale, getTranslations } from "next-intl/server";
import { getCountryFromDomain } from "@talent-front-services/jobseeker/modules/utils/geoUtils";
import {
  getMetaRobots,
  transformSearchParams,
} from "@talent-front-services/jobseeker/modules/utils/metaRobots";
import { notFound } from "next/navigation";
import { getCanonicalUrl } from "@talent-front-services/jobseeker/modules/seo/metadata/utilsCanonicalUrl";
import { buildMetaDataSEO, getRelatedAndPopularSearches, getR<PERSON>ted<PERSON><PERSON><PERSON>, ScripLdJobPostingBreadcrumb, viewPageValidations } from "@talent-front-services/jobseeker/modules/utils/viewPageUtils";

type Props = {
  params: { locale: string };
  searchParams: { [key: string]: string };
};

/**
 *
 */
export async function generateMetadata(
  { params, searchParams }: Props,
  parent: ResolvingMetadata,
): Promise<Metadata> {

  const meta: { indexability: { index: boolean; follow: boolean }; canonical: string } = {
    indexability: { index: false, follow: false },
    canonical: "",
  };

  if (!searchParams.id) {
    console.log("VIEW METADATA ERROR  error=NOT_JOB_ID");
    notFound();
  }

  const t = await getTranslations({ locale: params.locale, namespace: "job_description" });
  const jobs = await getJobsJobMeta({ ids: [searchParams.id], allowDistilledTitle: true });
  const jobData = jobs?.jobs[0];
  if (!jobData) {
    notFound();
  }

  meta.indexability = await getMetaRobots("view", {
    googleIndexed: jobData?.google_indexed,
  });

  const mt = TranslateMetadata(jobData, t);

  const canonicalUrl = getCanonicalUrl({
    pathname: "/view",
    params,
    searchParams: transformSearchParams(searchParams),
  });

  return {
    title: mt?.title,
    description: mt?.description,
    robots: meta.indexability,
    openGraph: {
      url: canonicalUrl,
      type: "website",
      siteName: "Talent.com",
      images: [
        {
          url: "https://cdn-static.talent.com/img/others/talent_meta_img.png",
          secureUrl: "https://cdn-static.talent.com/img/others/talent_meta_img.png",
        },
      ],
    },
    alternates: {
      canonical: canonicalUrl,
    },
  };
}

/**
 * @returns
 */
export default async function View({
  searchParams,
  params,
}: {
  readonly searchParams: any;
  params: { locale: string };
}) {

  const meta: { indexability: { index: boolean; follow: boolean }; canonical: string } = {
    indexability: { index: false, follow: false },
    canonical: "",
  };

  if (!searchParams.id) {
    console.log("VIEW ERROR error=NOT_JOB_ID");
    notFound();
  }

  const [lang, jobData] = await Promise.all([
    getLocale(),
    getJobsJobMeta({ ids: [searchParams.id], source: "VIEW", allowDistilledTitle: true }),
  ]);

  const job = jobData?.jobs?.[0];
  const country: string = getCountryFromDomain({ host: getHost() ?? "us" });

  const validations = viewPageValidations({job, searchParams, country, jobData});

  if (!validations) {
    notFound();
  }

  meta.indexability = await getMetaRobots("view", {
    googleIndexed: job?.google_indexed,
  });

  const geoFence = await isSameCountryRegion(country);

  const relatedJobs = await getRelatedJobs({job, country});

  const { jobPostingJSON, eventMetaData, breadCrumbData, jsonLdScript, jsonLdBreadCrumb } = await buildMetaDataSEO({params, searchParams, job, lang, country, indexability: meta.indexability.index});

  const { popularSearchData, relatedSearches } = await getRelatedAndPopularSearches({meta, job, country, locale: params.locale, searchParams });

  return (
    <>
      <ScripLdJobPostingBreadcrumb
        jsonLdScript={jsonLdScript}
        jobPostingJSON={jobPostingJSON}
        jsonLdBreadCrumb={jsonLdBreadCrumb}
      />
      <JobDescription
        jobs={relatedJobs?.jobs}
        job={job}
        isSameCountryRegion={geoFence}
        eventMetaData={eventMetaData}
        breadCrumbData={breadCrumbData}
        country={country}
        relatedSearchesData={relatedSearches}
        popularSearchData={popularSearchData}
      />
    </>
  );
}
