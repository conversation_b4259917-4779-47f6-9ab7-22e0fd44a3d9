{"homepage.header_1.your_job_search_starts_here": "<strong> 채용 공고 검색 </strong> 이 여기에서 시작됩니다", "homepage.header_2.search_from": "<strong> 4천만 개 이상의 채용 공고 </strong> 중 원하는 공고에 단 몇 초 만에 지원하십시오.", "homepage.search_box.what": "검색", "homepage.search_box.job_title": "채용 공고", "homepage.search_box.where": "어디에서?", "homepage.search_box.location": "위치", "homepage.search_box.find_jobs": "채용 공고 검색", "homepage.middle_section.create_an_acount": "계정을 만들고 <strong> 딱 맞는 채용 공고를 </strong> 찾으세요.", "homepage.middle_section.create_my_account": "게정 생성하기", "homepage.middle_section.sign_in": "로그인", "homepage.organization_schema.as_a_leading": "선도적인 구직 플랫폼인 Talent.com은 77개국 1백만 개 기업에서 3천만 개 이상의 일자리를 제공합니다.\n \n또한 새로운 채용 기회를 직접 받은 편지함으로 전달하는 맞춤형 채용 알림 등 구직자의 취업을 지원하는 다양한 솔루션을 제공합니다. 또한 이 사이트를 통해 이해하기 어려운 급여 및 세금 세부 정보를 파악하는 데 도움을 받을 수도 있습니다. 전 세계 모든 구직자가 Talent.com에서 제공하는 서비스를 경험해 보시기 바랍니다.", "footer.talent.blog": "블로그", "footer.browse.browse_jobs": "잡 검색", "footer.browse.by_location": "위치별", "footer.talent.work_at_talent": "Talent.com에서 일하세요", "footer.job_seekers.career_advice": "진로 조언", "footer.talent.cookie_settings": "쿠키 설정", "footer.talent.more_countries": "더 많은 국가", "footer.job_seekers.search_salary": "급여 검색", "footer.employers.enterprise": "기업", "footer.talent.help_center": "지원 센터", "footer.job_seekers.for_job_seekers": "구직자 용", "footer.job_seekers.search_jobs": "채용 정보 검색", "footer.talent.talent_com": "Talent.com", "footer.employers.for_employers": "고용주", "footer.employers.job_posting_policy": "채용 공고 정책", "footer.talent.personal_data_request": "개인 데이터 요청", "footer.employers.publisher_programs": "출판사 프로그램", "footer.job_seekers.salary_converter": "급여 변환기", "footer.job_seekers.tax_calculator": "세금 계산기", "footer.browse.top_searches": "인기 검색어", "footer.talent.tos_cookies_pp": "<a href=\"{link}\">서비스 약관, </a> <a href=\"{link}\">개인 정보 보호정책 및 </a> <a href=\"{link}\">쿠키 정책</a>", "footer.talent.legal_notice": "법적 고지", "footer.talent.accessibility": "접근성", "nav_bar.bar.search_job": "잡 검색하기", "nav_bar.bar.search_salary": "연봉 검색", "nav_bar.bar.sign_in": "로그인", "nav_bar.bar.tax_calculator": "세금 계산기", "nav_bar.bar.for_employers": "고용주", "banners_legal.cta.ok": "좋아요", "banners_legal.header_1.we_have_updated": "당사의 <a href=\"{link}\">서비스 약관</a>, <a href=\"{link}\">개인 정보 보호정책</a> 및 <a href=\"{link}\">쿠키 정책</a>을 업데이트했습니다 . 당사의 서비스를 계속 이용하기 전에 주의 깊게 읽어 보시기 바랍니다.", "apply_talent_apply.header_1.add_your_contact": "연락처 정보를 추가하세요", "apply_talent_apply.topbar.back": "뒤로", "apply_talent_apply.review.by_applying_i": "지원함으로써 Talent.com의 <a href=\"%1$s\">서비스 약관,</a> <a href=\"%1$s\">개인정보 보호정책</a> 및 <a href=\"%1$s\">쿠키 정책</a>에 동의합니다 . 또한 본인의 지원서가 고용주에게 공유되며 Talent.com 및 고용주의 서비스 약관 및 개인정보 보호정책에 의한 처리에 동의합니다.", "apply_talent_apply.footer.by_continuing_i": "계속함으로써 Talent.com의 <a href=\"%1$s\">서비스 약관,</a> <a href=\"%1$s\">개인정보 보호정책</a> 및 <a href=\"%1$s\">쿠키 정책</a>에 동의합니다 .", "apply_talent_apply.topbar.exit": "나가기", "apply_talent_apply.final_screen.good_job": "성공적으로 완료되었습니다!", "apply_talent_apply.review.contact_information": "연락처 정보", "apply_talent_apply.cta.continue": "계속", "apply_talent_apply.resume.delete": "삭제", "apply_talent_apply.form.email": "이메일", "apply_talent_apply.form.your_first_name": "내 이름", "apply_talent_apply.form.your_last_name": "내 성", "apply_talent_apply.exit_screen.exit": "출구", "apply_talent_apply.final_screen.finish": "끝", "apply_talent_apply.form.first_name": "이름", "apply_talent_apply.exit_screen.hold_on_you": "기다리다! 지원서를 완료하지 않았습니다. 나중을 위해 이 작업을 저장할 수 있지만 업로드된 파일은 저장되지 않습니다.", "apply_talent_apply.error_message.incorrect_phone_number": "유효하지 않은 전화 번호", "apply_talent_apply.job_card.job_description": "직무 기술서", "apply_talent_apply.form.last_name": "성", "apply_talent_apply.exit_screen.exit_this_job": "이 입사 지원서를 종료하시겠습니까?", "apply_talent_apply.resume.max_file_size": "최대 파일 사이즈: 2MB(.pdf, .doc, .docx, .txt, .rtf, .odt)", "apply_talent_apply.review.n_a": "N/A", "http_errors_410.cta.go_back_to": "사이트로 돌아가기", "http_errors_410.cta.search_jobs": "잡 검색하기", "http_errors_410.h1.this_page_has": "이 페이지는 이동되었거나 더 이상 존재하지 않습니다.", "http_errors_410.h2.try_going_back": "사이트로 돌아가 보세요!", "http_errors_404.cta.go_back_to": "사이트로 돌아가기", "http_errors_404.cta.search_jobs": "잡 검색하기", "http_errors_404.h1.this_page_has": "이 페이지는 이동되었거나 더 이상 존재하지 않습니다.", "http_errors_404.h2.try_going_back": "사이트로 돌아가 보세요!"}