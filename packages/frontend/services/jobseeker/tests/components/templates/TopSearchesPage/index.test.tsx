import "__mocks__/resizeObserver.mock";
import "__mocks__/useRouter.mock";
import mockNextProviders from "__mocks__/nextProviders.mock";
jest.mock("@talent-front-services/jobseeker/modules/network/seo");
jest.mock("@talent-front-services/jobseeker/navigation");

import React from "react";
import "@testing-library/jest-dom";
import TopSearchesPage from "@talent-front-services/jobseeker/components/templates/TopSearchesPage";

const dataTopSearchesMock = {
  desktop: [
    [
      {
        link: "https://ca.talent.com/jobs/k-%2420-an-hour-l-montreal-qc",
        name: "$20 an hour",
      },
      {
        link: "https://ca.talent.com/jobs/k-3d-animator-l-montreal-qc",
        name: "3d animator",
      },
      {
        link: "https://ca.talent.com/jobs/k-3d-designer-l-montreal-qc",
        name: "3d designer",
      },
    ],
    [
      {
        link: "https://ca.talent.com/jobs/k-3d-modeler-l-montreal-qc",
        name: "3d modeler",
      },
      {
        link: "https://ca.talent.com/jobs/k-abap-developer-l-montreal-qc",
        name: "Abap developer",
      },
      {
        link: "https://ca.talent.com/jobs/k-academic-administrator-l-montreal-qc",
        name: "Academic administrator",
      },
    ],
    [
      {
        link: "https://ca.talent.com/jobs/k-academic-coordinator-l-montreal-qc",
        name: "Academic coordinator",
      },
      {
        link: "https://ca.talent.com/jobs/k-academic-dean-l-montreal-qc",
        name: "Academic dean",
      },
      {
        link: "https://ca.talent.com/jobs/k-account-analyst-l-montreal-qc",
        name: "Account analyst",
      },
    ],
    [
      {
        link: "https://ca.talent.com/jobs/k-account-assistant-l-montreal-qc",
        name: "Account assistant",
      },
    ],
  ],
  mobile: [
    [
      {
        link: "https://ca.talent.com/jobs/k-%2420-an-hour-l-montreal-qc",
        name: "$20 an hour",
      },
      {
        link: "https://ca.talent.com/jobs/k-3d-animator-l-montreal-qc",
        name: "3d animator",
      },
      {
        link: "https://ca.talent.com/jobs/k-3d-designer-l-montreal-qc",
        name: "3d designer",
      },
      {
        link: "https://ca.talent.com/jobs/k-3d-modeler-l-montreal-qc",
        name: "3d modeler",
      },
      {
        link: "https://ca.talent.com/jobs/k-abap-developer-l-montreal-qc",
        name: "Abap developer",
      },
      {
        link: "https://ca.talent.com/jobs/k-academic-administrator-l-montreal-qc",
        name: "Academic administrator",
      },
      {
        link: "https://ca.talent.com/jobs/k-academic-coordinator-l-montreal-qc",
        name: "Academic coordinator",
      },
      {
        link: "https://ca.talent.com/jobs/k-academic-dean-l-montreal-qc",
        name: "Academic dean",
      },
      {
        link: "https://ca.talent.com/jobs/k-account-analyst-l-montreal-qc",
        name: "Account analyst",
      },
      {
        link: "https://ca.talent.com/jobs/k-account-assistant-l-montreal-qc",
        name: "Account assistant",
      },
    ],
  ],
};

afterAll(() => {
  jest.clearAllMocks();
});

/**
 * TopSearchesPage
 */
const renderTopSearchesPage = mockNextProviders(
  <TopSearchesPage dataTopSearches={dataTopSearchesMock} title="Title" eventMetaData={undefined} />,
);

describe("TopSearchesPage", () => {
  it("should render successfully | Desktop", async () => {
    const { baseElement } = renderTopSearchesPage({
      isMobile: false,
    });
    expect(baseElement).toBeInTheDocument();
  });

  it("should render successfully | Mobile", async () => {
    const { baseElement } = renderTopSearchesPage({ isMobile: true });
    expect(baseElement).toBeInTheDocument();
  });
});
