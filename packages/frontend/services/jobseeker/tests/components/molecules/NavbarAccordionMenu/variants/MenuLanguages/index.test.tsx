import "__mocks__/nextLink.mock";
import { act, fireEvent, render, screen } from "@testing-library/react";
import "@testing-library/jest-dom";
import { MenuLanguages } from "@talent-front-services/jobseeker/components/molecules/NavbarAccordionMenu/variants/MenuLanguages";

jest.mock("next/navigation", () => ({
  /** */
  usePathname: () => "/",
  /** */
  useRouter: () => ({
    back: jest.fn(),
    forward: jest.fn(),
    refresh: jest.fn(),
    push: jest.fn(),
    prefetch: jest.fn(),
    replace: jest.fn(),
  }),
  /** */
  useParams: () => ({ locale: "en" }),
  /** */
  useSelectedLayoutSegment: () => ({ locale: "en" }),
}));

const mockProps = {
  actualLocale: "en",
  options: [
    {
      langCode: "en",
      url: "/jobs",
      label: "English",
    },
    {
      langCode: "fr",
      url: "/jobs",
      label: "Français",
    },
    {
      langCode: "es",
      url: "/jobs",
      label: "Español",
    },
  ],
  onClick: jest.fn(),
};

afterEach(() => {
  jest.clearAllMocks();
});

describe("MenuLanguages", () => {
  it("should be rendered", async () => {
    render(<MenuLanguages {...mockProps} />);

    const btnMenu = screen.getByText("English");
    expect(btnMenu).toBeInTheDocument();

    act(() => {
      fireEvent.click(btnMenu);
    });

    const btnFr = await screen.findByText("Français");
    expect(btnFr).toBeInTheDocument();

    act(() => {
      fireEvent.click(btnFr);
    });

    expect(mockProps.onClick).toHaveBeenCalled();
  });

  it("should be rendered and have 'es' in the locales", async () => {
    render(<MenuLanguages {...mockProps} />);

    const btnMenu = screen.getByText("English");
    expect(btnMenu).toBeInTheDocument();

    act(() => {
      fireEvent.click(btnMenu);
    });

    const btnEs = await screen.findByText("Español");
    expect(btnEs).toBeInTheDocument();
    act(() => {
      fireEvent.click(btnEs);
    });
    expect(mockProps.onClick).toHaveBeenCalled();
  });

  it("should be rendered and handles the menu", async () => {
    render(<MenuLanguages {...mockProps} />);

    const btnMenu = screen.getByText("English");
    expect(btnMenu).toBeInTheDocument();

    act(() => {
      fireEvent.click(btnMenu);
    });

    const btnEs = await screen.findByText("Español");
    expect(btnEs).toBeInTheDocument();

    act(() => {
      fireEvent.click(btnMenu);
    });

    const btnEsNull = screen.queryByText("Español");
    expect(btnEsNull).toBeNull();
  });

  it("should be rendered and closed when the user clicks outside the menu", async () => {
    render(<MenuLanguages {...mockProps} />);

    const btnMenu = screen.getByText("English");
    expect(btnMenu).toBeInTheDocument();

    act(() => {
      fireEvent.click(btnMenu);
    });

    const btnEs = await screen.findByText("Español");
    expect(btnEs).toBeInTheDocument();

    const menu = await screen.findByTestId("menu-languages");
    expect(menu).toBeInTheDocument();

    act(() => {
      fireEvent.blur(menu);
    });
  });
});
