import React from "react";
import "@testing-library/jest-dom";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { AccordionConvert } from "@talent-front-services/jobseeker/components/molecules/ConvertAccordion";
import { useTranslations } from "next-intl";
import { useRouter } from "../../../../src/navigation";
import { getCountrySettings } from "@talent-front-services/jobseeker/modules/utils/init";
import { getDefaultRegions } from "@talent-front-services/jobseeker/modules/utils/countryRegions";
import { convertToStandardFormat } from "@talent-front-services/jobseeker/modules/utils/converterUtils";

// Mocks
jest.mock("next-intl", () => ({
  useTranslations: jest.fn(),
}));

jest.mock("../../../../src/navigation", () => ({
  useRouter: jest.fn(),
}));

jest.mock("@talent-front-services/jobseeker/modules/utils/init", () => ({
  //  getDefaultRegions: jest.fn(),
  getCountrySettings: jest.fn(),
}));

jest.mock("@talent-front-services/jobseeker/modules/utils/converterUtils", () => ({
  convertToStandardFormat: jest.fn(),
}));

// Mock ResizeObserver before tests
beforeAll(() => {
  global.ResizeObserver = class {
    observe() {}
    unobserve() {}
    disconnect() {}
  };
});

describe("AccordionConvert Component", () => {
  const mockRouterPush = jest.fn();
  const mockTranslations = {
    "frequency_per.per_year": "per year",
    "frequency_per.per_month": "per month",
    "frequency_per.per_year": "per two weeks",
    "frequency_per.per_month": "per week",
    "frequency_per.per_year": "per day",
    "frequency_per.per_month": "per hour",
    "table3_people_also_ask.how_much_tax": "How much tax for {salary} {currency} {frequency_per}",
  };

  beforeEach(() => {
    (useTranslations as jest.Mock).mockReturnValue((key: string) => mockTranslations[key] || key);
    (useRouter as jest.Mock).mockReturnValue({ push: mockRouterPush });
    /* (getDefaultRegions as jest.Mock).mockImplementation((country) => {
      if (country === "us") {
        return ["Region1", "Region2", "Region3"];
      } else if (country === "ca") {
        return ["RegionA", "RegionB", "RegionC"];
      } else {
        return ["DefaultRegion1", "DefaultRegion2"];
      }
    }); */
    (getCountrySettings as jest.Mock).mockReturnValue({
      salary_data: { currency_symbol: "$" },
    });
    (convertToStandardFormat as jest.Mock).mockReturnValue("50,000");
  });

  afterEach(() => {
    jest.clearAllMocks();
  });
  describe("AccordionConvert Component", () => {
    it("should render with default props", () => {
      render(<AccordionConvert />);
      expect(screen.getByTestId("item-1")).toBeInTheDocument();
    });

    it("should render with provided props", () => {
      render(<AccordionConvert country="" salaryTax="" frequency="" />);
      expect(screen.getByTestId("item-1")).toBeInTheDocument();
    });
  });

  test("renders 'How much tax for' text in accordion header", async () => {
    render(<AccordionConvert country="US" salaryTax="50000" frequency="year" />);

    const accordion = await screen.findByTestId("item-1");
    expect(accordion).toBeInTheDocument();

    const accordionButton = screen.getByTestId("btn-item-1");
    fireEvent.click(accordionButton);

    const content = await screen.findByTestId("content-item-1");
    expect(content).toBeInTheDocument();
  });

  test("renders 'How much tax for' text in accordion header for month", async () => {
    render(<AccordionConvert country="US" salaryTax="5000" frequency="month" />);

    const accordion = await screen.findByTestId("item-1");
    expect(accordion).toBeInTheDocument();

    const accordionButton = screen.getByTestId("btn-item-1");
    fireEvent.click(accordionButton);

    const content = await screen.findByTestId("content-item-1");
    expect(content).toBeInTheDocument();
  });
  test("renders 'How much tax for' text in accordion header for two week", async () => {
    render(<AccordionConvert country="US" salaryTax="2500" frequency="biweekly" />);

    const accordion = await screen.findByTestId("item-1");
    expect(accordion).toBeInTheDocument();

    const accordionButton = screen.getByTestId("btn-item-1");
    fireEvent.click(accordionButton);

    const content = await screen.findByTestId("content-item-1");
    expect(content).toBeInTheDocument();
  });
  test("renders 'How much tax for' text in accordion header for week", async () => {
    render(<AccordionConvert country="US" salaryTax="1000" frequency="week" />);

    const accordion = await screen.findByTestId("item-1");
    expect(accordion).toBeInTheDocument();

    const accordionButton = screen.getByTestId("btn-item-1");
    fireEvent.click(accordionButton);

    const content = await screen.findByTestId("content-item-1");
    expect(content).toBeInTheDocument();
  });
  test("renders 'How much tax for' text in accordion header for day", async () => {
    render(<AccordionConvert country="US" salaryTax="100" frequency="day" />);

    const accordion = await screen.findByTestId("item-1");
    expect(accordion).toBeInTheDocument();

    const accordionButton = screen.getByTestId("btn-item-1");
    fireEvent.click(accordionButton);

    const content = await screen.findByTestId("content-item-1");
    expect(content).toBeInTheDocument();
  });
  test("renders 'How much tax for' text in accordion header for hour", async () => {
    render(<AccordionConvert country="US" salaryTax="25" frequency="hour" />);

    const accordion = await screen.findByTestId("item-1");
    expect(accordion).toBeInTheDocument();

    const accordionButton = screen.getByTestId("btn-item-1");
    fireEvent.click(accordionButton);

    const content = await screen.findByTestId("content-item-1");
    expect(content).toBeInTheDocument();
  });
  test("renders 'How much tax for' text in accordion header for default", async () => {
    render(<AccordionConvert country="US" salaryTax="50000" frequency="other" />);

    const accordion = await screen.findByTestId("item-1");
    expect(accordion).toBeInTheDocument();

    const accordionButton = screen.getByTestId("btn-item-1");
    fireEvent.click(accordionButton);

    const content = await screen.findByTestId("content-item-1");
    expect(content).toBeInTheDocument();
  });
  test("renders 'How much tax for' text in accordion header for US", async () => {
    const result = getDefaultRegions("US");

    render(<AccordionConvert country="us" salaryTax="50000" frequency="year" />);

    const accordion = await screen.findByTestId("item-1");
    expect(accordion).toBeInTheDocument();

    const accordionButton = screen.getByTestId("btn-item-1");
    fireEvent.click(accordionButton);

    const firstItem = screen.getByTestId("item-accordion-0");
    expect(firstItem).toBeInTheDocument();
    fireEvent.click(firstItem);

    expect(mockRouterPush).toHaveBeenCalledWith("/tax-calculator/Alabama-50000");
  });
});
