import React from "react";
import { render, screen } from "@testing-library/react";
import "@testing-library/jest-dom";
import { ConfirmResetPassword } from "@talent-front-services/jobseeker/components/organisms/Modals/ConfirmResetPassword";
import {
  TopActions,
  MainHeader,
} from "@talent-front-services/jobseeker/components/organisms/Modals/ConfirmResetPassword/components";

jest.mock(
  "@talent-front-services/jobseeker/components/organisms/Modals/ConfirmResetPassword/components",
  () => ({
    TopActions: jest.fn(() => <div data-testid="mocked-top-actions" />),
    MainHeader: jest.fn(() => <div data-testid="mocked-main-header" />),
  }),
);

describe("ConfirmResetPassword", () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should render the MainPopupContainer", () => {
    render(<ConfirmResetPassword isMobile={false} isOpen={true} />);
  });

  it("should render the TopActions component", () => {
    render(<ConfirmResetPassword isMobile={false} isOpen={true} />);
    const topActions = screen.getByTestId("mocked-top-actions");

    expect(topActions).toBeInTheDocument();
    expect(TopActions).toHaveBeenCalledTimes(1);
  });

  it("should render the MainHeader component", () => {
    render(<ConfirmResetPassword isMobile={false} isOpen={true} />);
    const mainHeader = screen.getByTestId("mocked-main-header");

    expect(mainHeader).toBeInTheDocument();
    expect(MainHeader).toHaveBeenCalledTimes(1);
  });

  it("should render correctly for mobile", () => {
    render(<ConfirmResetPassword isMobile={true} isOpen={true} />);
    const container = screen.getByTestId("mocked-top-actions");

    expect(container).toBeTruthy();
  });
});
