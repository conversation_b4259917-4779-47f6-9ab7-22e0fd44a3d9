import { render, fireEvent } from "@testing-library/react";
import "jest-styled-components";
import "@testing-library/jest-dom";
import {
  MainHeader,
  SuccessClose,
} from "../../../../../src/components/organisms/Modals/PasswordUpdateSuccess/components";

import { usePopupContext } from "@talent-front-services/jobseeker/modules/providers/PopupProvider";

jest.mock("@talent-front-services/jobseeker/modules/providers/PopupProvider", () => ({
  usePopupContext: jest.fn(),
}));

describe("MainHeader", () => {
  it("renders correctly", () => {
    const { getByText } = render(<MainHeader />);
    expect(getByText("Password updated")).toBeInTheDocument();
    expect(
      getByText("You can now access your account using your new credentials."),
    ).toBeInTheDocument();
  });
});

describe("SuccessClose", () => {
  it("renders correctly", () => {
    // Mocking usePopupContext
    const mockCloseModal = jest.fn();
    (usePopupContext as jest.Mock).mockReturnValue({ closeModal: mockCloseModal });

    const { getByText } = render(<SuccessClose />);
    expect(getByText("Continue")).toBeInTheDocument();
  });

  it("calls closeModal when the button is clicked", () => {
    // Mocking usePopupContext
    const mockCloseModal = jest.fn();
    (usePopupContext as jest.Mock).mockReturnValue({ closeModal: mockCloseModal });

    const { getByText } = render(<SuccessClose />);
    const continueButton = getByText("Continue");
    fireEvent.click(continueButton);
  });
});
