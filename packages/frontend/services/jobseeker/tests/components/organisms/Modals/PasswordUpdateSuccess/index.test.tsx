import React from "react";
import { render } from "@testing-library/react";
import "@testing-library/jest-dom";
import "jest-styled-components";
import { PasswordUpdateSuccess } from "@talent-front-services/jobseeker/components/organisms/Modals/PasswordUpdateSuccess";
import { MainPopupContainerProps } from "@talent-front-services/jobseeker/components/organisms/Modals/interfaces";

// Mocking components used in PasswordUpdateSuccess
jest.mock("@talent-front-services/jobseeker/components/organisms/Modals", () => ({
  /**
   *
   */
  MainPopupContainer: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="MainPopupContainer">{children}</div>
  ),
}));

jest.mock(
  "@talent-front-services/jobseeker/components/organisms/Modals/PasswordUpdateSuccess/components",
  () => ({
    /**
     *
     */
    MainHeader: () => <div data-testid="MainHeader">MainHeader</div>,
    /**
     *
     */
    SuccessClose: () => <div data-testid="SuccessClose">SuccessClose</div>,
  }),
);

describe("PasswordUpdateSuccess", () => {
  const props: MainPopupContainerProps = {
    isMobile: false,
    isOpen: true,
  };

  it("renders correctly on desktop", () => {
    const { getByTestId } = render(<PasswordUpdateSuccess {...props} />);
    expect(getByTestId("MainPopupContainer")).toBeInTheDocument();
    expect(getByTestId("MainHeader")).toBeInTheDocument();
    expect(getByTestId("SuccessClose")).toBeInTheDocument();
  });

  it("renders correctly on mobile", () => {
    const mobileProps = { ...props, isMobile: true };
    const { getByTestId } = render(<PasswordUpdateSuccess {...mobileProps} />);
    expect(getByTestId("MainPopupContainer")).toBeInTheDocument();
    expect(getByTestId("MainHeader")).toBeInTheDocument();
    expect(getByTestId("SuccessClose")).toBeInTheDocument();
  });
});
