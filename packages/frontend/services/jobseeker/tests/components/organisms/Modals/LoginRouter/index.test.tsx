import React from "react";
import { render, screen } from "@testing-library/react";
import "@testing-library/jest-dom";
import { LoginRouter } from "@talent-front-services/jobseeker/components/organisms/Modals/LoginRouter";
import * as PopupContext from "@talent-front-services/jobseeker/modules/providers/PopupProvider";
import * as IsMobileContext from "@talent-front-services/jobseeker/modules/providers/IsMobileProvider";

// Mock all the components that might be rendered
jest.mock("@talent-front-services/jobseeker/components/organisms/Modals/SignIn", () => ({
  /**
   *
   */
  SignIn: () => <div>SignIn</div>,
}));
jest.mock("@talent-front-services/jobseeker/components/organisms/Modals/CreatePassword", () => ({
  /**
   *
   */
  CreatePassword: () => <div>CreatePassword</div>,
}));
jest.mock("@talent-front-services/jobseeker/components/organisms/Modals/ValidateOtpCode", () => ({
  /**
   *
   */
  ValidateOtpCode: () => <div>ValidateOtpCode</div>,
}));
jest.mock("@talent-front-services/jobseeker/components/organisms/Modals/ValidateOtpSignIn", () => ({
  /**
   *
   */
  ValidateOtpSignIn: () => <div>ValidateOtpSignIn</div>,
}));
jest.mock("@talent-front-services/jobseeker/components/organisms/Modals/ValidatePassword", () => ({
  /**
   *
   */
  ValidatePassword: () => <div>ValidatePassword</div>,
}));
jest.mock(
  "@talent-front-services/jobseeker/components/organisms/Modals/CreateAccountSuccess",
  () => ({
    /**
     *
     */
    CreateAccountSuccess: () => <div>CreateAccountSuccess</div>,
  }),
);
jest.mock(
  "@talent-front-services/jobseeker/components/organisms/Modals/CreatePasswordSuccess",
  () => ({
    /**
     *
     */
    CreatePasswordSuccess: () => <div>CreatePasswordSuccess</div>,
  }),
);
jest.mock(
  "@talent-front-services/jobseeker/components/organisms/Modals/SecureYourAccountStep",
  () => ({
    /**
     *
     */
    SecureYourAccountStep: () => <div>SecureYourAccountStep</div>,
  }),
);
jest.mock(
  "@talent-front-services/jobseeker/components/organisms/Modals/ForgotPasswordStep",
  () => ({
    /**
     *
     */
    ForgotPasswordStep: () => <div>ForgotPasswordStep</div>,
  }),
);
jest.mock(
  "@talent-front-services/jobseeker/components/organisms/Modals/ConfirmResetPassword",
  () => ({
    /**
     *
     */
    ConfirmResetPassword: () => <div>ConfirmResetPassword</div>,
  }),
);
jest.mock("@talent-front-services/jobseeker/components/organisms/Modals/ResetPasswordStep", () => ({
  /**
   *
   */
  ResetPasswordStep: () => <div>ResetPasswordStep</div>,
}));
jest.mock(
  "@talent-front-services/jobseeker/components/organisms/Modals/PasswordUpdateSuccess",
  () => ({
    /**
     *
     */
    PasswordUpdateSuccess: () => <div>PasswordUpdateSuccess</div>,
  }),
);

describe("Login Router", () => {
  const mockUsePopupContext = jest.spyOn(PopupContext, "usePopupContext");
  const mockUseIsMobileContext = jest.spyOn(IsMobileContext, "useIsMobileContext");

  // Mock the full PopupContextType
  const mockPopupContext: any = {
    modal: "",
    values: {},
    showModal: jest.fn(),
    closeModal: jest.fn(),
    setNewValue: jest.fn(),
  };

  beforeEach(() => {
    mockUseIsMobileContext.mockReturnValue(false);
    mockPopupContext.modal = "";
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  test.each([
    ["SignIn", "SignIn"],
    ["CreatePassword", "CreatePassword"],
    ["ValidateOtpCode", "ValidateOtpCode"],
    ["ValidateOtpSignIn", "ValidateOtpSignIn"],
    ["ValidatePassword", "ValidatePassword"],
    ["CreateAccountSuccess", "CreateAccountSuccess"],
    ["CreatePasswordSuccess", "CreatePasswordSuccess"],
    ["SecureYourAccountStep", "SecureYourAccountStep"],
    ["ForgotPasswordStep", "ForgotPasswordStep"],
    ["ConfirmResetPassword", "ConfirmResetPassword"],
    ["ResetPasswordStep", "ResetPasswordStep"],
    ["PasswordUpdateSuccess", "PasswordUpdateSuccess"],
  ])("renders %s component when modal is %s", (modal, expectedComponent) => {
    mockPopupContext.modal = modal;
    mockUsePopupContext.mockReturnValue(mockPopupContext);

    render(<LoginRouter />);

    expect(screen.getByText(expectedComponent)).toBeInTheDocument();
  });

  test("renders null when modal is unknown", () => {
    mockPopupContext.modal = "UnknownModal";
    mockUsePopupContext.mockReturnValue(mockPopupContext);

    const { container } = render(<LoginRouter />);

    expect(container.firstChild).toBeNull();
  });

  test("passes isMobile prop correctly", () => {
    mockPopupContext.modal = "SignIn";
    mockUsePopupContext.mockReturnValue(mockPopupContext);
    mockUseIsMobileContext.mockReturnValue(true);

    render(<LoginRouter />);

    expect(screen.getByText("SignIn")).toBeInTheDocument();
  });
});
