/**
 * @jest-environment node
 */

import middleware from "../src/middleware";
import { NextResponse, NextRequest } from "next/server";


jest.mock("@talent-front-services/jobseeker/modules/utils/geoUtilsServer", () => {
  return {
    /**
     *
     */
    getIp: () => {
      return "************";
    },
    /**
     *
     */
    getLocation: () => {
      return {
        prefilledLocation: "canada"
      };
    },
  };
});

describe("Middleware", () => {
  const redirectSpy = jest.spyOn(NextResponse, "redirect");

  it("should remove locale", async () => {
    const req = new NextRequest(new Request("http://ca.localhost:3000/en"), {});

    req.headers.set("host", "ca.localhost:3000");

    await middleware(req);

    expect(redirectSpy).toHaveBeenCalledTimes(2);
    expect(redirectSpy).toHaveBeenCalledWith(new URL("/", req.url));
  });
});

describe("Middleware 2", () => {
  const redirectSpy2 = jest.spyOn(NextResponse, "redirect");

  it("should remove locale keeping params", async () => {
    const req = new NextRequest(new Request("http://ca.localhost:3000/en/view?id=123"), {});

    req.headers.set("host", "ca.localhost:3000");

    await middleware(req);

    expect(redirectSpy2).toHaveBeenCalledTimes(4);
    expect(redirectSpy2).toHaveBeenCalledWith(new URL("/view?id=123", req.url));
  });
});

describe("Middleware 3", () => {
  const redirectSpy3 = jest.spyOn(NextResponse, "redirect");

  it("should go directly", async () => {
    redirectSpy3.mockReset();
    const req = new NextRequest(new Request("http://ca.localhost:3000"), {});

    req.headers.set("host", "ca.localhost:3000");

    await middleware(req);

    expect(redirectSpy3).toHaveBeenCalled();
  });
});
describe("Middleware 5", () => {
  const redirectSpy5 = jest.spyOn(NextResponse, "redirect");

  it("should go directly", async () => {
    redirectSpy5.mockReset();
    const req = new NextRequest(new Request("http://ca.localhost:3000"), {});

    req.headers.set("host", "ca.localhost:3000");
    req.cookies.set("preferred_language", "fr");

    await middleware(req);

    expect(redirectSpy5).toHaveBeenCalled();
  });
});

describe("Middleware 4", () => {
  const redirectSpy4 = jest.spyOn(NextResponse, "redirect");

  it("should go directly", async () => {
    redirectSpy4.mockReset();
    const req = new NextRequest(new Request("http://ca.localhost:3000/jobs"), {});

    await middleware(req);

    expect(redirectSpy4).toHaveBeenCalledWith(
      new URL("http://ca.localhost:3000/jobs?l=canada", req.url),
    );
  });
});
