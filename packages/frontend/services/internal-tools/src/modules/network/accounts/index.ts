"use server";

import { auth } from "@talent-front-services/internal-tools/modules/auth";
import { talentApi, talentApiLegacy, talentApiV2 } from "@talent-front-libs/ui/modules/network";
import { Tag } from "../employers/server";
import {
  decryptDealEmails,
  decryptEmail,
  decryptOwnerEmails,
} from "@talent-front-services/internal-tools/utils/decryptEmail";
import { format } from "date-fns";

/// <reference types="../../../types.d.ts" />
/**
 *
 */
export const getAccountData = async ({
  startPeriod,
  endPeriod,
  accountOwnerEmail,
}: {
  startPeriod: string;
  endPeriod: string;
  accountOwnerEmail: string;
}): Promise<{
  budgetMonth: number;
  spentMonth: number;
  spentToday: number;
  spentVsLastMonth: string;
  spentVsLastMonthPercentage: string;
}> => {
  try {
    const session = await auth();
    const userId = session?.user?.user_id;

    if (!userId) {
      return {
        budgetMonth: 0,
        spentMonth: 0,
        spentToday: 0,
        spentVsLastMonth: "0",
        spentVsLastMonthPercentage: "0",
      };
    }

    const params = new URLSearchParams({
      limit: "10",
      sortType: "DESC",
      sortColumn: "costConv",
      startPeriod,
      endPeriod,
      accountOwnerEmail,
    }).toString();

    const response = await talentApi({
      url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/accounts/search-dashboard?${params}`,
      method: "GET",
      authPayload: { user_id: userId },
    });

    if (!response?.payload) {
      console.warn("getAccountData: Invalid or missing API response.");
      return {
        budgetMonth: 0,
        spentMonth: 0,
        spentToday: 0,
        spentVsLastMonth: "0",
        spentVsLastMonthPercentage: "0",
      };
    }

    const { budgetMonth, spentMonth, spentToday, spentVsLastMonth, spentVsLastMonthPercentage } =
      response.payload;

    return {
      budgetMonth: budgetMonth ?? 0,
      spentMonth: spentMonth ?? 0,
      spentToday: spentToday ?? 0,
      spentVsLastMonth: spentVsLastMonth ?? "0",
      spentVsLastMonthPercentage: spentVsLastMonthPercentage ?? "0",
    };
  } catch (error) {
    return {
      budgetMonth: 0,
      spentMonth: 0,
      spentToday: 0,
      spentVsLastMonth: "0",
      spentVsLastMonthPercentage: "0",
    };
  }
};

export const getOwnerSuggestions = async (query: string): Promise<string[]> => {
  try {
    const session = await auth();
    const userId = session?.user?.user_id;

    if (!userId || !query) return [];

    const response = await talentApi({
      url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/accounts-internal/autosuggest-account-owners?query=${query}`,
      method: "GET",
      authPayload: { user_id: userId },
    });

    if (response?.status === "ok" && Array.isArray(response?.payload)) {
      return response.payload;
    }

    return [];
  } catch (err: any) {
    const code = err?.response?.status;
    const message = err?.response?.data?.message || err?.message;



    return [];
  }
};

/**
 *
 */
export const getSalesModuleTags = async (accountId: number): Promise<{ tags: string[] }> => {
  try {
    const session = await auth();
    const userId = session?.user?.user_id;

    if (!userId) {
      return { tags: [] };
    }

    const response = await talentApi({
      url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/tags/sales-module/account/${accountId}/accountId?status=true`,
      method: "GET",
      authPayload: { user_id: userId },
    });

    if (!response?.payload || !Array.isArray(response.payload)) {
      return { tags: [] };
    }

    return { tags: response.payload };
  } catch (error) {
    return { tags: [] };
  }
};

/**
 *
 */
export const getAccountSettings = async (accountId: number) => {
  try {
    const session = await auth();
    const userId = session?.user?.user_id;

    if (!userId) {
      return { payload: {} };
    }

    const response = await talentApi({
      url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/accounts/sales-module/settings/${accountId}/info`,
      method: "GET",
      authPayload: { user_id: userId },
    });

    if (!response?.payload) {
      return { payload: {} };
    }

    return response.payload;
  } catch (error) {
    return { payload: {} };
  }
};

interface GetBillingParams {
  id?: string;
  owner?: string;
  agency?: string;
  company?: string;
  billed?: string;
  orderBy?: string;
  limit?: number;
  from?: number;
}

/**
 * Fetch Billings based on search parameters.
 *
 * @async
 * @function getAccountsTableData
 * @param {Object} params - The parameters for the search query.
 * @param {number} params.limit - The number of campaigns to return.
 * @param {number} params.from - The page number for pagination.
 */
export const getAccountsTableData = async (params: GetBillingParams) => {
  // Remove blank and null data from paramss
  const filteredData = Object.fromEntries(
    Object.entries(params).filter(
      ([key, value]) => value !== "" && value !== "all" && value !== null,
    ),
  );

  const body: any = {
    ...filteredData,
    limit: filteredData.limit ?? 20,
    from: filteredData.from ?? 1,
    orderBy: "feedcode",
    billed: filteredData.Billed ?? "Billable",
  };

  const session = await auth();

  const data = await talentApi({
    url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/billings/search`,
    data: body,
    method: "GET",
    authPayload: {
      user_id: session?.user?.user_id,
    },
  });

  return data;
};

/**
 * Returns the spent this month value by FeedCode
 * @async
 * @function getSpentThisMonthByFeeCode
 * @param feedcode FeedCode
 */
export const getSpentThisMonthByFeeCode = async (feedCode: string) => {
  const session = await auth();

  const data = await talentApi({
    url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/billings/current-spent?feedcode=${feedCode}`,
    method: "GET",
    authPayload: {
      user_id: session?.user?.user_id,
    },
  });

  return data;
};

/**
 * Fetch Bill by ID
 *
 * @async
 * @function getBillByID
 * @param {Object} params - The parameters for the search query.
 */
export const getBillByID = async (params: GetBillingParams) => {
  const session = await auth();

  const data = await talentApi({
    url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/billings/${params.id}`,
    method: "GET",
    authPayload: {
      user_id: session?.user?.user_id,
    },
  });

  return data;
};

export interface SearchAccountParams {
  country?: string;
  search?: string;
  status?: string;
  limit: number;
  page: number;
  owner?: string;
  qaStatus?: string;
  accountType?: string;
  vipAccount?: boolean;
  startDate?: Date;
  endDate?: Date;
  isAutoCampaign?: boolean;
}
/**
 * Fetches the accounts based on search parameters.
 *
 * @async
 * @function getSearchAccount
 * @param {Object} params - The parameters for the search query.
 * @param {string} [params.country] - Comma-separated list of countries.
 * @param {string} [params.companyLabel] - Label of the company.
 * @param {string} [params.status] - Status of the account.
 * @param {number} params.limit - The number of accounts to return.
 * @param {number} params.page - The page number for pagination.
 * @param {string} params.owner - The owner of the account.
 * @param {string} [params.accountType] - The type of the account.
 * @param {boolean} [params.vipAccount] - VIP account status (true/false).
 */
export const getSearchAccount = async ({
  country,
  search,
  status,
  limit = 50,
  page,
  owner,
  accountType,
  qaStatus,
  vipAccount,
  startDate,
  endDate,
  isAutoCampaign,
}: SearchAccountParams) => {
  const dateRange =
    startDate && endDate
      ? `${format(startDate, "yyyy-MM-dd")} - ${format(endDate, "yyyy-MM-dd")}`
      : undefined;

  const params: Record<string, any> = {
    country,
    search,
    status,
    limit,
    from: (page - 1) * limit,
    owner,
    accountType,
    qaStatus,
    vipAccount: vipAccount === undefined ? undefined : vipAccount ? 1 : 0,
    dateRange,
    isAutoCampaign: isAutoCampaign === undefined ? undefined : isAutoCampaign ? 1 : 0,
  };

  Object.keys(params).forEach((key) => {
    if (params[key] === undefined || params[key] === "") delete params[key];
  });

  const session = await auth();
  if (!session?.user?.user_id) {
    throw new Error("Unauthorized: No valid session");
  }

  const response = await talentApi({
    url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/accounts/search`,
    data: params,
    method: "GET",
    authPayload: { user_id: session.user.user_id },
  });

  // Throw on any non-OK code
  if (![200, 201, 204].includes(response.statusCode)) {
    throw new Error(`API error: status ${response.statusCode}`);
  }

  const payload = response.payload;
  const normalizedAccounts = (payload.accounts ?? []).map((acct: any) => ({
    ...acct,
    created: format(new Date(acct.created as string), "yyyy-MM-dd"),
    accountsSpentBudget: acct.accountsBudget?.spentBudget ?? "0.00",
    accountsBudget: (acct.accountsBudget?.budget ?? 0).toString(),
  }));

  return {
    ...response,
    payload: {
      ...payload,
      accounts: normalizedAccounts,
    },
  };
};

interface FindOneAccountParams {
  id: number | string;
  relations?: string;
}

/**
 * Fetches the details of a single account and decrypts any encrypted emails.
 *
 * @async
 * @function findOneAccount
 * @param {Object} params - The parameters for the request.
 * @param {number|string} params.id - The ID of the account.
 * @param {string} [params.relations] - Comma-separated list of relations to include in the response.
 */
export const findOneAccount = async ({ id, relations }: FindOneAccountParams) => {
  const url = `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/accounts/${id}${
    relations ? `?relations=${relations}` : ""
  }`;

  const session = await auth();
  const data = await talentApi({
    url,
    method: "GET",
    authPayload: { user_id: session?.user?.user_id },
  });

  if (data?.payload) {
    const payload = data.payload;

    if (payload.contactEmail) {
      payload.contactEmail = decryptEmail(payload.contactEmail);
    }

    if (Array.isArray(payload.accountsOwners)) {
      payload.accountsOwners = decryptOwnerEmails(payload.accountsOwners);
    }

    if (payload.deal) {
      payload.deal = decryptDealEmails(payload.deal);
    }
  }

  return data;
};

/**
 * Fetches the tags based on type ID and status.
 *
 * @async
 * @function fetchTags
 * @param {number} typeId - The type ID for the tag.
 * @param {number} status - The status of the tag, default is 1.
 */
const TAG_TYPES = [
  { id: 1, key: "pixelTag", category: "FeatureTags" },
  { id: 2, key: "postbackTag", category: "FeatureTags" },
  { id: 3, key: "reconciliationApiTag", category: "FeatureTags" },
  { id: 4, key: "reconciliationSftpTag", category: "FeatureTags" },
  { id: 5, key: "agency", category: "AccountEditTags" },
  { id: 10, key: "subidSourceTag", category: "FeatureTags" },
  { id: 8, key: "performanceOverview", category: "SettingsTags" },
  { id: 7, key: "autoCampaignAPI", category: "SettingsTags" },
] as const;
interface SuggestedTags {
  pixelTag: {};
  postbackTag: {};
  reconciliationApiTag: {};
  reconciliationSftpTag: {};
  subidSourceTag: {};
}

interface SettingsTags {
  performanceOverview: {};
  autoCampaignAPI: {};
}
interface AccountEditTags {
  agency: {};
}
export const getTags = async (typeId: number, status: number = 1) => {
  const params: Record<string, any> = {
    status,
    typeId,
  };
  const session = await auth();
  const data = await talentApi({
    url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/tags/search`,
    data: params,
    method: "GET",
    authPayload: {
      user_id: session?.user?.user_id,
    },
  });
  return data;
};

/**
 * Fetches tags based on an optional category filter.
 *
 * @async
 * @function getAllTags
 * @param {string} [category] - Optional category to filter tags by ('FeatureTags', 'SettingsTags', or 'AccountEditTags')
 * @returns {Promise<any>} Object containing requested tag categories
 */
export const getAllTags = async (category?: "FeatureTags" | "SettingsTags" | "AccountEditTags"): Promise<any> => {
  // Filter tag types based on the provided category (if any)
  const filteredTagTypes = category 
    ? TAG_TYPES.filter(tag => tag.category === category)
    : TAG_TYPES;

  // Only fetch the filtered tags
  const tagPromises = filteredTagTypes.map(({ id }) => getTags(id));
  const results = await Promise.all(tagPromises);

  // Initialize with empty objects for all categories we need
  const initialTags: {
    FeatureTags?: SuggestedTags;
    SettingsTags?: SettingsTags;
    AccountEditTags?: AccountEditTags;
  } = {};

  // Only initialize the categories we need
  if (!category || category === "FeatureTags") {
    initialTags.FeatureTags = {
      pixelTag: {},
      postbackTag: {},
      reconciliationApiTag: {},
      reconciliationSftpTag: {},
      subidSourceTag: {},
    };
  }
  
  if (!category || category === "SettingsTags") {
    initialTags.SettingsTags = {
      performanceOverview: {},
      autoCampaignAPI: {},
    };
  }
  
  if (!category || category === "AccountEditTags") {
    initialTags.AccountEditTags = {
      agency: {},
    };
  }

  // Populate the tags
  return results.reduce((acc, result, index) => {
    if (result.status === "ok") {
      const { key, category: tagCategory } = filteredTagTypes[index];
      const tags = result.payload.tags.map((tag: Tag) => ({
        label: tag.name,
        value: String(tag.id ?? ""),
      }));

      if (tagCategory === "FeatureTags" && acc.FeatureTags && key in acc.FeatureTags) {
        (acc.FeatureTags as any)[key] = tags;
      } else if (tagCategory === "SettingsTags" && acc.SettingsTags && key in acc.SettingsTags) {
        (acc.SettingsTags as any)[key] = tags;
      } else if (tagCategory === "AccountEditTags" && acc.AccountEditTags && key in acc.AccountEditTags) {
        (acc.AccountEditTags as any)[key] = tags;
      }
    }
    return acc;
  }, initialTags);
};
/**
 * Fetch Owner Suggestions
 * @async
 * @function getAutoSuggesstOwners
 * @param query
 * @returns
 */
export const getAutoSuggesstOwners = async (query: string) => {
  const session = await auth();
  const data = await talentApi({
    url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/accounts-internal/autosuggest-account-owners`,
    data: { query },
    method: "GET",
    authPayload: {
      user_id: session?.user?.user_id,
    },
  });
  return data;
};
interface User {
  name?: string;
  email?: string;
  type?: string;
}

type UserArray = User[];
export interface UpdateAccountParams {
  id: string | number;
  account?: {
    country?: string;
    language?: string;
    status?: string;
    accountType?: string;
    companyLabel?: string;
    contactEmail?: string;
    contactName?: string;
    leadType?: string;
    accountCurrency?: string;
    contactHsId?: string | null;
    forceRedirection?: string;
    autoCampaignApiTagName?: string;
    hubspotDealId?: null | number;
  };
  user?: UserArray;
  billing?: {
    name?: string;
    address?: string;
    city?: string;
    region?: string;
    postalCode?: string;
    invoiceOption?: number;
    paymentTerm?: number;
    country?: string;
    currency?: string;
    segment?: string;
    language?: string;
    group?: string;
    contact?: string;
    email?: string;
    phoneNumber?: string;
    discount?: number;
    nextDiscountApply?: string;
    discountStartDate?: string;
    discontEndDate?: string;
    reason?: string;
    poNumber1?: string;
    poStartDate?: string;
    poEndDate?: string;
  };
  settings?: {
    reliableConversion?: boolean;
    frontLoadedStatus?: boolean;
    requiresQa?: boolean;
    multiStepTracking?: boolean;
    vipAccount?: boolean;
    status24hCommitment?: boolean;
    endOfMonthExposure?: boolean;
    spikeManager?: boolean;
    smartBidding?: boolean;
    sendBudgetEmail?: boolean;
    externalConversionData?: boolean;
    ppcUrlParameter?: boolean;
    cem?: boolean;
    cctModifier?: number;
  };
  tags?: {
    postback?: string;
    reconciliation?: string;
  };
  reliableConversion?: boolean;
}

/**
 * Updates an account with the provided data.
 *
 * @async
 * @function updateAccount
 * @param {UpdateAccountParams} params - The parameters for updating the account.
 * @returns {Promise<any>} The response from the API.
 */
export const updateAccount = async (params: UpdateAccountParams) => {
  const { id, ...updateData } = params;
  const session = await auth();
  const data = await talentApiV2({
    url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/accounts/${id}`,
    data: updateData,
    method: "PATCH",
    authPayload: {
      user_id: session?.user?.user_id,
    },
  });
  return data;
};

/**
 * Edit Billing Account
 * Edit side panel from the accounts table in the finance module
 *
 * @async
 * @function updateBillingAccount
 * @param {UpdateAccountParams} params - The parameters for updating the account.
 * @returns {Promise<any>} The response from the API.
 */
export const editBillingAccount = async (params: any) => {
  const { id, ...updateData } = params;
  const session = await auth();

  const data = await talentApi({
    url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/billings/${id}`,
    data: updateData,
    method: "PATCH",
    authPayload: {
      user_id: session?.user?.user_id,
    },
  });

  return data;
};

/**
 *
 */
export const getAccountIntegrationInfoLegacy = async ({ feedcode }: { feedcode: string }) => {
  const body = {
    feedcode,
  };

  const data = await talentApiLegacy({
    url: `${process.env.NEXT_PUBLIC_TALENT_LEGACY_API}/Feedcode/getFeedcodeIntegrationInfo`,
    data: body,
    method: "GET",
  });

  return data;
};

/**
 * Updates the tags and related settings for a specific account
 *
 * @async
 * @function updateAccountTags
 * @param {string | number} id - The account ID
 * @param {Object} params - The tag update parameters
 * @returns {Promise<any>} The response from the API
 */
export const updateAccountTags = async (
  id: string | number,
  params: {
    Postback?: string;
    Reconciliation?: string;
    ReconciliationSFTP?: string;
    SubidSource?: string;
    webhookTagId?: number;
    multistepTraking?: "yes" | "no";
    conversionTypesNoMultistep?: string;
    stepsMain?: string;
    conversionTypesMain?: string;
    steps?: Array<{
      step: string;
      conversionType: string;
    }>;
    flagUpdateConversionTypes?: number;
    conversionTypeAccountLevel?: string;
    updateCampaignsObjective?: string;
  },
) => {
  const session = await auth();
  const data = await talentApiV2({
    url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/accounts/${id}/update-account-tags`,
    data: params,
    method: "PATCH",
    authPayload: {
      user_id: session?.user?.user_id,
    },
  });

  return data;
};

interface AccountOwner {
  email: string;
  type: "account_sales_owner" | "account_cs_owner" | "account_second_sales_owner";
}

/**
 * Updates the owners of a specific account
 *
 * @async
 * @function updateAccountOwners
 * @param {string | number} accountId - The ID of the account to update
 * @param {AccountOwner[]} owners - Array of owner objects containing email and type
 * @returns {Promise<any>} The response from the API
 */
export const updateAccountOwners = async (accountId: string | number, owners: AccountOwner[]) => {
  const session = await auth();

  const data = await talentApiV2({
    url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/accounts/${accountId}/update-owners`,
    data: {
      user: owners,
    },
    method: "PATCH",
    authPayload: {
      user_id: session?.user?.user_id,
    },
  });
  return data;
};

export interface AccountSettingsPatch {
  performanceOverview?: string;
  cpcColumn?: boolean;
  accountType?: string;
  status24hCommitment?: boolean;
  frontLoadedStatus?: boolean;
  endOfMonthExposure?: boolean;
  spikeManager?: boolean;
  smartBidding?: boolean;
  sendBudgetEmail?: boolean;
  acProfitProtection?: boolean;
  externalConversionData?: boolean;
  reliableConversion?: boolean;
  ppcUrlParameter?: string | null;
  ppcUrlFormatValue?: string | null;
  cem?: boolean;
  cctModifier?: number;
  isFullyAutoCampaign?: boolean;
  autoCampaignApiTagName?: string;
  multiStepTracking?: boolean;
}

export const updateAccountSetting = async (
  accountId: string | number,
  settings: AccountSettingsPatch,
): Promise<any> => {
  try {
    const session = await auth();
    const userId = session?.user?.user_id;
    if (!userId) {
      throw new Error("Unauthorized");
    }
    const response = await talentApiV2({
      url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/accounts/sales-module/settings/${accountId}`,
      method: "PATCH",
      authPayload: { user_id: userId },
      data: settings,
    });
    return response;
  } catch (error) {
    throw error;
  }
};

export interface SearchAccountParamsv2 {
  country?: string | string[];
  search?: string;
  accountStatus?: string;
  limit: number;
  page: number;
  accountOwnerEmail?: string;
  qaStatus?: string;
  accountType?: string | string[];
  isVip?: boolean;
  startPeriod?: string;
  endPeriod?: string;
  isAutoCampaign?: boolean;
  sortType?: string;
  sortColumn?: string;
}

export const getSearchAccountV2 = async ({
  country,
  search,
  accountStatus,
  limit = 50,
  page,
  accountOwnerEmail,
  accountType,
  qaStatus,
  isVip,
  startPeriod,
  endPeriod,
  isAutoCampaign,
  sortType,
  sortColumn,
}: SearchAccountParamsv2) => {
  // build query params - using URLSearchParams for proper parameter handling
  const searchParams = new URLSearchParams();

  // Helper to add parameters, handling arrays properly
  const addParam = (key: string, value: any) => {
    if (value === undefined || value === "") return;

    if (Array.isArray(value)) {
      // For arrays, add multiple entries with the same key
      value.forEach((item) => {
        if (item) searchParams.append(key, item);
      });
    } else {
      searchParams.append(key, String(value));
    }
  };

  // Add all parameters
  addParam("limit", limit);
  addParam("offset", (page - 1) * limit);
  addParam("search", search);
  addParam("accountStatus", accountStatus);
  addParam("accountOwnerEmail", accountOwnerEmail);
  addParam("qaStatus", qaStatus);
  addParam("country", country);
  addParam("accountType", accountType);
  addParam("sortType", sortType);
  addParam("sortColumn", sortColumn);

  // Handle boolean parameters
  if (isVip) searchParams.append("isVip", "1");
  if (isAutoCampaign) searchParams.append("isAutoCampaign", "1");

  // Add accountPanel parameter
  searchParams.append("accountPanel", "true");

  // Format and add date parameters
  if (startPeriod) {
    searchParams.append("startPeriod", format(startPeriod, "yyyy-MM-dd"));
  }

  if (endPeriod) {
    searchParams.append("endPeriod", format(endPeriod, "yyyy-MM-dd"));
  }

  const session = await auth();
  if (!session?.user?.user_id) {
    throw new Error("Unauthorized: No valid session");
  }

  // call the new endpoint with the search params
  const response = await talentApi({
    url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/accounts/search-dashboard?${searchParams.toString()}`,
    method: "GET",
    authPayload: { user_id: session.user.user_id },
  });

  if (![200, 201, 204].includes(response.statusCode)) {
    throw new Error(`API error: status ${response.statusCode}`);
  }

  const { payload } = response;

  // normalize each account
  const normalized: [] = (payload.data ?? []).map((acct: any) => ({
    id: acct.accountId,
    feedcode: acct.feedcode,
    companyLabel: acct.accountName,
    country: acct.country,
    accountsBudget: acct.budget?.toString() ?? "0",
    accountsSpentBudget: acct.budgetSpent?.toString() ?? "0",
    campaignCount: acct.campaignNumber,
    created: format(new Date(acct.dateCreated), "yyyy-MM-dd"),
    accountType: acct.type,
    isVip: acct.vip,
    accountCurrency: acct.currency,
    spentToday: acct.spentToday,
    costPerConv: acct.costConv,
    spendVs: acct.spendVs,
  }));

  return {
    ...response,
    payload: {
      // pagination
      total: payload.total,
      limit: payload.limit,
      offset: payload.offset,
      // summary metrics
      budgetMonth: payload.budgetMonth,
      spentMonth: payload.spentMonth,
      spentLastMonth: payload.spentLastMonth,
      spentToday: payload.spentToday,
      spentVsLastMonth: payload.spentVsLastMonth,
      // data rows
      accounts: normalized,
    },
  };
};
