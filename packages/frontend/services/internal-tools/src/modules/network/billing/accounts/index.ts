"use server";

import { auth } from "@talent-front-services/internal-tools/modules/auth";
import { talentApi, talentApiLegacy } from "@talent-front-libs/ui/modules/network";
import { SortingState } from "@tanstack/react-table";

/// <reference types="../../../types.d.ts" />
interface GetBillingParams {
  id?: string;
  owner?: string;
  agency?: string;
  company?: string;
  billed?: string;
  orderBy?: string;
  limit?: number;
  from?: number;
  sorting: SortingState;
}

/**
 * Fetch Billings based on search parameters.
 *
 * @async
 * @function getAccountsTableData
 * @param {Object} params - The parameters for the search query.
 * @param {number} params.limit - The number of campaigns to return.
 * @param {number} params.from - The page number for pagination.
 */
export const getAccountsTableData = async (params: GetBillingParams) => {
  // Remove blank and null data from paramss
  const { sorting, ...filteredData } = Object.fromEntries(
    Object.entries(params).filter(
      ([key, value]) => value !== "" && value !== "all" && value !== null,
    ),
  );
  const sortingParams = sorting.length
    ? { orderBy: sorting[0].id, order: sorting[0].desc ? "DESC" : "ASC" }
    : {};

  const body: any = {
    ...filteredData,
    limit: filteredData.limit ?? 20,
    from: filteredData.from ?? 1,
    // orderBy: "feedcode",
    billed: filteredData.billed ? decodeURIComponent(filteredData.billed) : "Billable",
    ...sortingParams,
  };

  const session = await auth();

  const data = await talentApi({
    url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/billings/search`,
    data: body,
    method: "GET",
    authPayload: {
      user_id: session?.user?.user_id,
    },
  });

  return data;
};

/**
 * Returns the spent this month value by FeedCode
 * @async
 * @function getSpentThisMonthByFeeCode
 * @param feedcode FeedCode
 */
export const getSpentThisMonthByFeeCode = async (accountId: string, feedCode: string) => {
  const session = await auth();

  const data = await talentApi({
    url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/billings/current-spent?feedcode=${feedCode}&accountId=${accountId}`,
    method: "GET",
    authPayload: {
      user_id: session?.user?.user_id,
    },
  });

  return data;
};

/**
 * Fetch Bill by ID
 *
 * @async
 * @function getBillByID
 * @param {Object} params - The parameters for the search query.
 */
export const getBillByID = async (params: any) => {
  try {
    const session = await auth();

    if (!session?.user?.user_id) {
      throw new Error("Unauthorized: No valid session");
    }

    const data = await talentApi({
      url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/billings/${params.id}?accountId=${params.accountId}`,
      method: "GET",
      authPayload: {
        user_id: session?.user?.user_id,
      },
    });

    if (![200, 201, 204].includes(data?.statusCode)) {
      throw new Error(`Oh oh, error`);
    }

    data.payload.qboId = data.payload.quickbooksId;
    data.payload.group = data.payload.billingGroup;
    delete data.payload.quickbooksId;
    delete data.payload.billingGroup;

    return data;
  } catch (error) {
    return {
      statusCode: 404,
      data: [],
    };
  }
};

export interface SearchAccountParams {
  country?: string;
  companyLabel?: string;
  status?: string;
  limit: number;
  page: number;
  owner?: string;
  accountType?: string;
  vipAccount?: boolean;
}
/**
 * Fetches the accounts based on search parameters.
 *
 * @async
 * @function getSearchAccount
 * @param {Object} params - The parameters for the search query.
 * @param {string} [params.country] - Comma-separated list of countries.
 * @param {string} [params.companyLabel] - Label of the company.
 * @param {string} [params.status] - Status of the account.
 * @param {number} params.limit - The number of accounts to return.
 * @param {number} params.page - The page number for pagination.
 * @param {string} params.owner - The owner of the account.
 * @param {string} [params.accountType] - The type of the account.
 * @param {boolean} [params.vipAccount] - VIP account status (true/false).
 */
export const getSearchAccount = async ({
  country,
  companyLabel,
  status,
  limit = 50,
  page,
  owner,
  accountType,
  vipAccount,
}: SearchAccountParams) => {
  const params: Record<string, any> = {
    country,
    companyLabel,
    status,
    limit,
    from: (page - 1) * limit,
    owner,
    accountType,
    vipAccount: vipAccount === undefined ? undefined : vipAccount ? 1 : 0,
  };
  // Remove undefined parameters
  Object.keys(params).forEach((key) => {
    if (params[key] === undefined || params[key] === "") {
      delete params[key];
    }
  });
  const session = await auth();

  const data = await talentApi({
    url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/accounts/search`,
    data: params,
    method: "GET",
    authPayload: {
      user_id: session?.user?.user_id,
    },
  });

  return data;
};

interface FindOneAccountParams {
  id: number | string;
  relations?: string;
}

/**
 * Fetches the details of a single account.
 *
 * @async
 * @function FindOneAccount
 * @param {Object} params - The parameters for the request.
 * @param {number|string} params.id - The ID of the account.
 * @param {string} [params.relations] - Comma-separated list of relations to include in the response.
 */
export const findOneAccount = async ({ id, relations }: FindOneAccountParams) => {
  // Construct the URL with optional relations parameter
  const url = `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/accounts/${id}${
    relations ? `?relations=${relations}` : ""
  }`;

  const session = await auth();

  const data = await talentApi({
    url: url,
    method: "GET",
    authPayload: {
      user_id: session?.user?.user_id,
    },
  });

  return data;
};

/**
 * Fetch Owner Suggestions
 * @async
 * @function getAutoSuggesstOwners
 * @param query
 * @returns
 */
export const getAutoSuggesstOwners = async (query: string) => {
  const data = await talentApi({
    url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/accounts-internal/autosuggest-account-owners`,
    data: { query },
    method: "GET",
  });
  return data;
};

/**
 * Edit Billing Account
 * Edit side panel from the accounts table in the finance module
 *
 * @async
 * @function updateBillingAccount
 * @param {UpdateAccountParams} params - The parameters for updating the account.
 * @returns {Promise<any>} The response from the API.
 */
export const editBillingAccount = async (params: any) => {
  const { id, accountId } = params;
  const session = await auth();

  const payload = {
    talentCompany: params.talentCompany,
    qboId: parseInt(params?.qboId, 10),
    taxId: parseInt(params?.tax, 10),
    paymentTerm: parseInt(params?.paymentTerm, 10),
    currency: params.currency,
    country: params.country,
    netsuiteId: params.netsuite_id,
  };

  const data = await talentApi({
    url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/billings/${id}?accountId=${accountId}`,
    data: payload,
    method: "PATCH",
    authPayload: {
      user_id: session?.user?.user_id,
    },
  });

  return data;
};

/**
 * Update Billing Account
 * Update side panel from the accounts table in the finance module
 *
 * @async
 * @function updateBillingAccount
 * @param {UpdateAccountParams} params - The parameters for updating the account.
 * @returns {Promise<any>} The response from the API.
 */
export const updateBillingAccount = async (params: any) => {
  const { id, accountId, ...updateData } = params;

  const session = await auth();

  const data = await talentApi({
    url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/billings/update-billing-values?accountId=${accountId}`,
    data: updateData,
    method: "POST",
    authPayload: {
      user_id: session?.user?.user_id,
    },
  });

  return data;
};

/**
 * Create Quickbooks ID
 * Edit side panel from the accounts table in the finance module
 *
 * @async
 * @function createQboId
 * @param {QuickBooksIDparams} params - The parameters for updating the account.
 * @returns {Promise<any>} The response from the API.
 */
export const createQboId = async (params: any) => {
  const { id, accountId, ...updateData } = params;
  const session = await auth();

  const payload = {
    ...updateData,
  };

  delete payload.contact;

  const data = await talentApi({
    url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/quickbooks/customer?accountId=${accountId}`,
    data: payload,
    method: "POST",
    authPayload: {
      user_id: session?.user?.user_id,
    },
  });

  return data;
};

/**
 * Create Invoice Preview for French Accounts
 *
 * @async
 * @function createInvoicePreviewFR
 * @param {CreateAccountParams} params - The parameters for creating the account.
 * @returns {Promise<any>} The response from the API.
 */
export const createInvoicePreviewFR = async (params: any) => {
  const payload = {
    feedcode: String(params.feedCode),
    billingPeriod: String(params.billingPeriod),
    lines: parseInt(params.lines, 10),
    discount: parseFloat(params.discount),
  };

  const accountId = parseInt(params.accountId, 10);

  const session = await auth();

  const data = await talentApi({
    url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/invoice-preview/invoice-preview-fr?accountId=${accountId}`,
    data: payload,
    method: "POST",
    authPayload: {
      user_id: session?.user?.user_id,
    },
  });

  return data;
};

/**
 *
 */
export const getAccountIntegrationInfoLegacy = async ({ feedcode }: { feedcode: string }) => {
  const body = {
    feedcode,
  };

  const data = await talentApiLegacy({
    url: `${process.env.NEXT_PUBLIC_TALENT_LEGACY_API}/Feedcode/getFeedcodeIntegrationInfo`,
    data: body,
    method: "GET",
  });

  return data;
};

/**
 * FeedCode Auto Suggest
 */
export const getFeedcodeAutosugesst = async (
  feedCode: { value: string; limit: number },
  format = "suggestions",
) => {
  try {
    const session = await auth();

    if (!session?.user?.user_id) {
      throw new Error("Unauthorized: No valid session");
    }

    if (feedCode?.value?.length > 2) {
      const data = await talentApi({
        url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/billings-internal/suggested-feedcode-without-qbo/${feedCode?.value}`,
        method: "GET",
        authPayload: {
          user_id: session?.user?.user_id,
        },
      });

      if (![200, 201, 204].includes(data?.statusCode)) {
        throw new Error(`Oh oh, error`);
      }

      const results = data.payload
        ? data?.payload?.suggestions?.map((item: any) => {
            return format === "suggestions" ? item.feedcode : item;
          })
        : [];

      return results;
    }

    return [];
  } catch (error) {
    return [];
  }
};

/**
 * Export all data to CSV.
 *
 * @async
 * @function getExportableAccountsTable
 */
export const getExportableAccountsTable = async (params: any) => {
  try {
    const session = await auth();

    if (!session?.user?.user_id) {
      throw new Error("Unauthorized: No valid session");
    }

    // Remove blank and null data from paramss
    const filteredData = Object.fromEntries(
      Object.entries(params).filter(
        ([_, value]) => value !== "" && value !== "all" && value !== null,
      ),
    );

    const body: any = {
      ...filteredData,
      orderBy: "feedcode",
      export: true,
    };

    const data = await talentApi({
      url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/billings/search`,
      data: body,
      method: "GET",
      authPayload: {
        user_id: session?.user?.user_id,
      },
    });

    if (![200, 201, 204].includes(data?.statusCode)) {
      throw new Error(`Oh oh, error`);
    }

    return {
      statusCode: 200,
      data: data?.payload?.billings || [],
    };
  } catch (error) {
    return {
      statusCode: 404,
      data: [],
    };
  }
};

/**
 * Export all data to CSV.
 *
 * @async
 * @function getExportableInvoicesTable
 */
export const getExportableInvoicesTable = async (params: any) => {
  try {
    const session = await auth();

    if (!session?.user?.user_id) {
      throw new Error("Unauthorized: No valid session");
    }

    // Remove blank and null data from paramss
    const filteredData = Object.fromEntries(
      Object.entries(params).filter(
        ([_, value]) => value !== "" && value !== "all" && value !== null,
      ),
    );

    const body: any = {
      ...filteredData,
      export: true,
    };

    const searchParams = new URLSearchParams(body as Record<string, string>).toString();

    const data = await talentApi({
      url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/invoices/search?${searchParams}`,
      method: "GET",
      authPayload: {
        user_id: session?.user?.user_id,
      },
    });

    if (![200, 201, 204].includes(data?.statusCode)) {
      throw new Error(`Oh oh, error`);
    }

    return {
      statusCode: 200,
      data: data?.payload?.invoices || [],
    };
  } catch (error) {
    return {
      statusCode: 404,
      data: [],
    };
  }
};

/**
 * Fetch Billing Groups for autosuggest (by partial fee/group text).
 *
 * @async
 * @function getBillingListGroup
 * @param {string | { value: string; limit: number }} query
 * @returns {Promise<string[]>} Array of matching group names.
 */
export const getBillingListGroup = async (
  query: string | { value: string; limit: number },
): Promise<string[]> => {
  // normalize query into text
  const text = typeof query === "string" ? query : query.value;

  // only hit the API for 3+ chars
  if (text.length < 3) return [];

  try {
    const session = await auth();
    if (!session?.user?.user_id) throw new Error("Unauthorized");

    const res = await talentApi({
      url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/billings/list-groups?query=${encodeURIComponent(
        text,
      )}`,
      method: "GET",
      authPayload: { user_id: session.user.user_id },
    });

    if (![200, 201, 204].includes(res.statusCode)) {
      console.warn("list-groups returned status", res.statusCode);
      return [];
    }

    return Array.isArray(res.payload?.list) ? res.payload.list.map((item: any) => item.group) : [];
  } catch (err) {
    return [];
  }
};
interface GetBillingDiscountParams {
  accountId: string;
  billingGroup: string; // e.g. "adecco-it-ppc"
}

/**
 * Fetches discount information for a given account and company.
 *
 * @async
 * @function getBillingDiscount
 * @param {Object} params
 * @param {string} params.accountId - The ID of the account.
 * @param {string} params.billingGroup - The billing Group slug to hit in the path.
 * @returns {Promise<any>} The discount data.
 */
export const getBillingDiscount = async ({ accountId, billingGroup }: GetBillingDiscountParams) => {
  if (!accountId || !billingGroup) {
    throw new Error("Both accountId and billingGroup are required");
  }

  const session = await auth();
  if (!session?.user?.user_id) {
    throw new Error("Unauthorized: No valid session");
  }

  const url = `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/billings/discount/${encodeURIComponent(
    billingGroup,
  )}`;

  const response = await talentApi({
    url,
    method: "GET",
    data: { accountId },
    authPayload: {
      user_id: session.user.user_id,
    },
  });

  if (![200, 201, 204].includes(response?.statusCode)) {
    throw new Error(`Unexpected status code: ${response?.statusCode}`);
  }

  return response.payload;
};
