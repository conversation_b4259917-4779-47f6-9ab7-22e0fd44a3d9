"use server";

import { auth } from "@talent-front-services/internal-tools/modules/auth";
import { talentApi } from "@talent-front-libs/ui/modules/network";
import { signPayload } from "@talent-front-libs/ui/modules/network/signPayload";
import { formatDate } from "date-fns";

/// <reference types="../../../types.d.ts" />
interface GetInvoiceParams {
  id?: number;
  accountId?: number;
  limit?: number;
  status?: string;
  accountType?: string;
  accountsOwner?: string;
  from?: number;
}

/**
 * Fetch Invoices based on search parameters.
 *
 * @async
 * @function getInvoicesDataTable
 * @param {Object} params - The parameters for the search query.
 * @param {number} params.limit - The number of campaigns to return.
 * @param {number} params.from - The page number for pagination.
 */
export const getInvoicesDataTable = async (params: GetInvoiceParams) => {
  try {
    const session = await auth();

    if (!session?.user?.user_id) {
      throw new Error("Unauthorized: No valid session");
    }

    // Remove blank and null data from paramss
    const filteredData = Object.fromEntries(
      Object.entries(params).filter(
        ([_, value]) => value !== "" && value !== "all" && value !== null,
      ),
    );

    const today = new Date();
    const previousFirstDayOfMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
    // Get the first day of the current month
    const firstDayOfCurrentMonth = new Date(today.getFullYear(), today.getMonth(), 1);

    // Set the last day of the previous month by going back one day from the first of the current month
    const lastDayOfPreviousMonth = new Date(firstDayOfCurrentMonth);
    lastDayOfPreviousMonth.setDate(firstDayOfCurrentMonth.getDate() - 1);

    if (!filteredData?.startDate)
      filteredData.startDate = `${formatDate(previousFirstDayOfMonth, "yyyy-MM-dd").replace(/\//g, "-")}`;
    if (!filteredData?.endDate)
      filteredData.endDate = `${formatDate(lastDayOfPreviousMonth, "yyyy-MM-dd").replace(/\//g, "-")}`;

    const body = {
      ...filteredData,
      limit: filteredData.limit || 50,
      from: filteredData?.from - 1 || 0,
    };

    const data = await talentApi({
      url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/invoices/search`,
      data: body,
      method: "GET",
      authPayload: {
        user_id: session?.user?.user_id,
      },
    });

    if (![200, 201, 204].includes(data?.statusCode)) {
      throw new Error(`Oh oh, error`);
    }

    return data;
  } catch (error) {
    return {
      statusCode: 404,
      data: error instanceof Error ? error.message : "Uknown error",
    };
  }
};

/**
 * Payment status for Invoice
 * @async
 * @function getPaymentStatus
 * @param accountId Account ID
 * @param invoiceId Invoice ID
 */
export const getPaymentStatus = async (invoiceId: string | number, accountId: string | number) => {
  try {
    const session = await auth();

    if (!session?.user?.user_id) {
      throw new Error("Unauthorized: No valid session");
    }

    const data = await talentApi({
      url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/invoices/payment-status?invoiceId=${invoiceId}&accountId=${accountId}`,
      method: "GET",
      authPayload: {
        user_id: session?.user?.user_id,
      },
    });

    if (![200, 201, 204].includes(data?.statusCode)) {
      throw new Error(`Oh oh, error`);
    }

    return data;
  } catch (error) {
    return {
      statusCode: 404,
      data: error instanceof Error ? error.message : "Uknown error",
    };
  }
};

/**
 * Qbo status for Invoice
 * @async
 * @function getQboStatus
 * @param accountId Account ID
 * @param invoiceId Invoice ID
 */
export const getQboStatus = async (invoiceId: string | number, accountId: string | number) => {
  try {
    const session = await auth();

    if (!session?.user?.user_id) {
      throw new Error("Unauthorized: No valid session");
    }

    const data = await talentApi({
      url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/invoices/compare-amount?invoiceId=${invoiceId}&accountId=${accountId}`,
      method: "GET",
      authPayload: {
        user_id: session?.user?.user_id,
      },
    });

    if (![200, 201, 204].includes(data?.statusCode)) {
      throw new Error(`Oh oh, error`);
    }

    return data;
  } catch (error) {
    return {
      statusCode: 404,
      data: error instanceof Error ? error.message : "Uknown error",
    };
  }
};

/**
 * Fetch Invoice based on ID.
 *
 * @async
 * @function getInvoiceByID
 * @param {string} id - The parameters for the search query.
 */
export const getPreviewByID = async (id?: string, accountId?: string) => {
  try {
    const session = await auth();

    if (!session?.user?.user_id) {
      throw new Error("Unauthorized: No valid session");
    }

    const data = await talentApi({
      url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/invoice-preview/${id}?accountId=${accountId}`,
      method: "GET",
      authPayload: {
        user_id: session?.user?.user_id,
      },
    });

    if (![200, 201, 204].includes(data?.statusCode)) {
      throw new Error(`Oh oh, error`);
    }

    return data;
  } catch (error) {
    return {
      statusCode: 404,
      data: error instanceof Error ? error.message : "Uknown error",
    };
  }
};

/**
 * Fetch Invoice based on ID.
 *
 * @async
 * @function getInvoiceByID
 * @param {string} id - The parameters for the search query.
 */
export const getInvoiceByID = async (id: string, accountId: string) => {
  try {
    const session = await auth();

    if (!session?.user?.user_id) {
      throw new Error("Unauthorized: No valid session");
    }

    const data = await talentApi({
      url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/invoices/invoice/${id}/render?accountId=${accountId}`,
      method: "GET",
      authPayload: {
        user_id: session?.user?.user_id,
      },
    });

    if (![200, 201, 204].includes(data?.statusCode)) {
      throw new Error(`Oh oh, error`);
    }

    return data;
  } catch (error) {
    return {
      statusCode: 404,
      data: error instanceof Error ? error.message : "Uknown error",
    };
  }
};

/**
 * Fetch Edit Invoice Side Panel based on ID.
 *
 * @async
 * @function getEditInvoiceSidePanelByID
 * @param {string} id - The parameters for the search query.
 */
export const getEditInvoiceSidePanelByID = async (id: string, accountId: string) => {
  try {
    const session = await auth();

    if (!session?.user?.user_id) {
      throw new Error("Unauthorized: No valid session");
    }

    const data = await talentApi({
      url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/invoices/${id}/descriptionOverwrite?accountId=${accountId}`,
      method: "GET",
      authPayload: {
        user_id: session?.user?.user_id,
      },
    });

    if (![200, 201, 204].includes(data?.statusCode)) {
      throw new Error(`Oh oh, error`);
    }

    return data;
  } catch (error) {
    return {
      statusCode: 404,
      data: error instanceof Error ? error.message : "Uknown error",
    };
  }
};

/**
 * Fetch Edit Invoice Side Panel based on ID.
 *
 * @async
 * @function postUpdateInvoiceSidePanelByID
 * @param {string} id - The parameters for the search query.
 */
export const postUpdateInvoiceSidePanelByID = async (invoiceData: any) => {
  try {
    const session = await auth();

    if (!session?.user?.user_id) {
      throw new Error("Unauthorized: No valid session");
    }

    // Remove blank and null data from paramss
    const filteredData: any = Object.fromEntries(
      Object.entries(invoiceData).filter(
        ([_, value]) => value !== "" && value !== "all" && value !== null,
      ),
    );

    const body = {
      lines: filteredData.lines,
      docNumber: parseInt(filteredData?.docNumber, 10),
      billingPeriod: filteredData.billingPeriod,
      txnId: parseInt(filteredData.txnId, 10),
      note: filteredData?.note || null,
      block: filteredData.block === "yes" ? 1 : 0,
    };

    const data = await talentApi({
      url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/invoices/${invoiceData.invoiceId}?accountId=${invoiceData.accountId}`,
      data: body,
      method: "PATCH",
      authPayload: {
        user_id: session?.user?.user_id,
      },
    });

    if (![200, 201, 204].includes(data?.statusCode)) {
      throw new Error(`Oh oh, error`);
    }

    return data;
  } catch (error) {
    return {
      statusCode: 404,
      data: error instanceof Error ? error.message : "Uknown error",
    };
  }
};

/**
 * Generate new invoice based on the Preview ID
 *
 * @async
 * @function generateInvoiceByID
 * @param {string} id - Preview ID to generate invoice
 */
export const generateInvoiceByID = async (identifier: string, accountId: string) => {
  try {
    const session = await auth();

    if (!session?.user?.user_id) {
      throw new Error("Unauthorized: No valid session");
    }

    const decodedIdentifier = decodeURIComponent(identifier);

    const data = await talentApi({
      url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/invoices/generate-invoice`,
      method: "POST",
      data: { identifier: decodedIdentifier, accountId: accountId },
      authPayload: {
        user_id: session?.user?.user_id,
      },
    });

    if (![200, 201, 204].includes(data?.statusCode)) {
      throw new Error(`Oh oh, error`);
    }

    return data;
  } catch (error) {
    return {
      statusCode: 404,
      data: error instanceof Error ? error.message : "Uknown error",
    };
  }
};

/**
 * Generate new invoice based on the Preview ID
 *
 * @async
 * @function getDownloadableInvoice
 */
export const getDownloadableInvoice = async (params: any) => {
  try {
    const session = await auth();

    if (!session?.user?.user_id) {
      throw new Error("Unauthorized: No valid session");
    }

    // Remove blank and null data from params
    const filteredData = Object.fromEntries(
      Object.entries(params).filter(([key, value]) => value !== "" && value !== null),
    );

    const body = { ...filteredData };

    const data = await talentApi({
      url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/invoices/invoice-render`,
      method: "GET",
      data: body,
      authPayload: {
        user_id: session?.user?.user_id,
      },
    });

    if (![200, 201, 204].includes(data?.statusCode)) {
      throw new Error(`Oh oh, error`);
    }

    return data;
  } catch (error) {
    return {
      statusCode: 404,
      data: error instanceof Error ? error.message : "Uknown error",
    };
  }
};

/**
 * Generate new invoice based on the Preview ID
 *
 * @async
 * @function getDownloadableInvoice
 */
export const saveInvoicePreviewComment = async (params: any) => {
  try {
    const session = await auth();

    if (!session?.user?.user_id) {
      throw new Error("Unauthorized: No valid session");
    }

    // Remove blank and null data from params
    const filteredData = Object.fromEntries(
      Object.entries(params).filter(([key, value]) => value !== "" && value !== null),
    );

    const body = { ...filteredData };
    delete body.id;
    delete body.accountId;

    const endodedId = encodeURIComponent(params.id);

    const data = await talentApi({
      url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/invoice-preview/comments/${endodedId}?accountId=${params.accountId}`,
      method: "PATCH",
      data: body,
      authPayload: {
        user_id: session?.user?.user_id,
      },
    });

    if (![200, 201, 204].includes(data?.statusCode)) {
      throw new Error(`Oh oh, error`);
    }

    return data;
  } catch (error) {
    return {
      statusCode: 404,
      data: error instanceof Error ? error.message : "Uknown error",
    };
  }
};

/**
 * Generate new invoice based on the Preview ID
 *
 * @async
 * @function saveGeneralSidePanel
 */
export const saveGeneralSidePanel = async (params: any) => {
  try {
    const session = await auth();

    if (!session?.user?.user_id) {
      throw new Error("Unauthorized: No valid session");
    }

    const body = { ...params };
    delete body.id;
    delete body.accountId;

    const endodedId = encodeURIComponent(params.id);

    const data = await talentApi({
      url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/invoice-preview/general/${endodedId}?accountId=${params.accountId}`,
      method: "PATCH",
      data: body,
      authPayload: {
        user_id: session?.user?.user_id,
      },
    });

    if (![200, 201, 204].includes(data?.statusCode)) {
      throw new Error(`Oh oh, error`);
    }

    return data;
  } catch (error) {
    return {
      statusCode: 404,
      data: error instanceof Error ? error.message : "Uknown error",
    };
  }
};

/**
 * Generate a credit based on the Preview ID
 *
 * @async
 * @function saveCreditSidePanel
 * @param {CreditParams} params - The credit parameters
 * @returns The server response
 */
export const saveCreditSidePanel = async (params: FormData) => {
  try {
    const session = await auth();

    if (!session?.user?.user_id) {
      throw new Error("Unauthorized: No valid session");
    }

    const accountId = params.get("accountId") as string;
    const id = encodeURIComponent(params.get("id") as string);

    // Append form fields
    const formData = new FormData();
    formData.append("accountId", accountId);
    formData.append("amount", params.get("amount") as string);
    formData.append("reason", params.get("reason") as string);
    formData.append("salesManager", params.get("sales") as string);

    // Append optional fields if they exist
    if (params.get("comments")) {
      formData.append("comments", params.get("comments") as string);
    }

    // Append the file if it exists
    if (params.get("file")) {
      formData.append("file", params.get("file") as File);
    }

    // Create headers
    const headers = new Headers();
    headers.append("Cache-Control", "no-store");
    headers.append(
      "Authorization",
      await signPayload({
        user_id: session.user.user_id,
      }),
    );

    const apiUrl = new URL(
      `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/invoice-preview/credit-record/${id}`,
    );

    apiUrl.searchParams.append("accountId", accountId);

    const response = await fetch(apiUrl.toString(), {
      method: "PATCH",
      body: formData,
      headers,
      credentials: "include",
      referrerPolicy: "no-referrer",
      cache: "no-store",
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => null);
      throw new Error(errorData?.message || `Server responded with status: ${response.status}`);
    }

    const result = await response.json();

    return {
      statusCode: response.status,
      data: result,
    };
  } catch (error) {
    return {
      statusCode: 404,
      data: error instanceof Error ? error.message : "Uknown error",
    };
  }
};

/**
 * Account Tab from Invoice Preview
 *
 * @async
 * @function saveAccountSidePanel
 */
export const saveAccountSidePanel = async (params: any) => {
  try {
    const session = await auth();

    if (!session?.user?.user_id) {
      throw new Error("Unauthorized: No valid session");
    }

    const body = { ...params, showClicks: params?.showClicks === "1" ? true : false };
    delete body.id;
    delete body.accountId;

    const encodedId = encodeURIComponent(params.id);

    const data = await talentApi({
      url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/invoice-preview/accounts/${encodedId}?accountId=${params.accountId}`,
      method: "PATCH",
      data: body,
      authPayload: {
        user_id: session?.user?.user_id,
      },
    });

    if (![200, 201, 204].includes(data?.statusCode)) {
      throw new Error(`Oh oh, error`);
    }

    return data;
  } catch (error) {
    return {
      statusCode: 404,
      data: error instanceof Error ? error.message : "Uknown error",
    };
  }
};

/**
 * Returns the JSON from a payment receipt
 *
 * @async
 * @function getPaymentReceipt
 */
export const getPaymentReceipt = async (params: any) => {
  try {
    const session = await auth();

    if (!session?.user?.user_id) {
      throw new Error("Unauthorized: No valid session");
    }

    // Remove blank and null data from params
    const filteredData = Object.fromEntries(
      Object.entries(params).filter(([_, value]) => value !== "" && value !== null),
    );

    const data = await talentApi({
      url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/quickbooks/payment/${filteredData.documentId}?company=${filteredData.company}`,
      method: "GET",
      authPayload: {
        user_id: session?.user?.user_id,
      },
    });

    if (![200, 201, 204].includes(data?.statusCode)) {
      throw new Error(`Oh oh, error`);
    }

    return data;
  } catch (error) {
    return {
      statusCode: 404,
      data: error instanceof Error ? error.message : "Uknown error",
    };
  }
};

/**
 * Returns the JSON from a credit memo
 *
 * @async
 * @function getCreditMemo
 */
export const getCreditMemo = async (params: any) => {
  try {
    const session = await auth();

    if (!session?.user?.user_id) {
      throw new Error("Unauthorized: No valid session");
    }

    // Remove blank and null data from params
    const filteredData = Object.fromEntries(
      Object.entries(params).filter(([_, value]) => value !== "" && value !== null),
    );

    const data = await talentApi({
      url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/quickbooks/credit-memo/${filteredData.documentId}?company=${filteredData.company}`,
      method: "GET",
      authPayload: {
        user_id: session?.user?.user_id,
      },
    });

    if (![200, 201, 204].includes(data?.statusCode)) {
      throw new Error(`Oh oh, error`);
    }

    return data;
  } catch (error) {
    return {
      statusCode: 404,
      data: error instanceof Error ? error.message : "Uknown error",
    };
  }
};

/**
 * Send an invoice email based on the Invoice ID and Account ID.
 *
 * @async
 * @function getCreditMemo
 */
export const sendInvoiceEmail = async (accountId: string, invoiceId: string) => {
  try {
    const session = await auth();

    if (!session?.user?.user_id) {
      throw new Error("Unauthorized: No valid session");
    }

    const body = {
      accountId: parseInt(accountId, 10),
      invoiceId: parseInt(invoiceId, 10),
    };

    const data = await talentApi({
      url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/invoices/send-invoice-email`,
      method: "POST",
      data: body,
      authPayload: {
        user_id: session?.user?.user_id,
      },
    });

    if (![200, 201, 204].includes(data?.statusCode)) {
      throw new Error(`Oh oh, error`);
    }

    return { statusCode: 200 };
  } catch (error) {
    return {
      statusCode: 404,
      data: "Cannot send invoice email",
    };
  }
};
