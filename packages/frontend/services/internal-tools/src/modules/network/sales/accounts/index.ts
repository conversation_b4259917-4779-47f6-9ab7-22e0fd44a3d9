"use server";

import { auth } from "@talent-front-services/internal-tools/modules/auth";
import { talentApiV2 } from "@talent-front-libs/ui/modules/network";
import { decrypt } from "libs/privacy/src/lib/cipher";
import { safeDecrypt } from "@talent-front-services/internal-tools/utils/decryptEmail";

/// <reference types="../../../types.d.ts" />
// interface GetAccountInfoParams {
//   id?: string;
//   owner?: string;
//   agency?: string;
//   company?: string;
//   billed?: string;
//   orderBy?: string;
//   limit?: number;
//   from?: number;
// }

/**
 * Fetch Account information for the Side Panel
 *
 * @async
 * @function getAccountInfoSp
 * @param {Object} params - The parameters for the search query.
 */
export const getAccountInfoSp = async (accountId: number) => {
  const session = await auth();
  if (!session?.user?.user_id) {
    throw new Error("Unauthorized: no session user_id");
  }

  const response = await talentApiV2({
    url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/accounts/sales-module/${accountId}/info`,
    data: { ignore_cache: true },
    method: "GET",
    authPayload: { user_id: session.user.user_id },
  });

  const { data, code } = response;
  if (!data) {
    throw new Error("getAccountInfoSp: response missing data");
  }
  if (![200, 201, 204].includes(code) || data.statusCode !== 200) {
    throw new Error(`API error (${data.statusCode}): ${data.message}`);
  }

  // unwrap the payload
  const account = data.payload;

  // decrypt fields
  account.contactEmail = safeDecrypt(account.contactEmail);
  account.billingEmail = safeDecrypt(account.billingEmail);
  account.billingContact = safeDecrypt(account.billingContact);
  account.billingPhoneNumber = safeDecrypt(account.billingPhoneNumber);
  account.billingAddress = safeDecrypt((account as any).billingAddress);
  account.salesOwner = safeDecrypt(account.salesOwner);
  account.csOwner = safeDecrypt(account.csOwner);
  account.secondarySalesOwner = safeDecrypt(account.secondarySalesOwner);

  return account;
};
/**
 * Refresh HubSpot Deal by ID
 *
 * @async
 * @function refreshHubspotDealById
 * @param {string} dealId - Hubspot Deal ID.
 */
export const refreshHubspotDealById = async (dealId: string) => {
  try {
    const session = await auth();

    if (!session?.user?.user_id) {
      throw new Error("Unauthorized: No valid session");
    }

    const response = await talentApiV2({
      url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/deals/${dealId}`,
      method: "PATCH",
      authPayload: {
        user_id: session?.user?.user_id,
      },
    });

    const { code } = await response;

    if (![200, 201, 204].includes(code)) {
      throw new Error(`Oh oh, error`);
    }
  } catch (error) {
    return {
      code: 404,
      error: "Couldn't refresh the HS Deal",
    };
  }
};

export interface SearchJobsDataParams {
  feedcode?: string;
  status?: string;
  search?: string;
  accountOwner?: string;
  limit: number;
  page: number;
}
/* =====================  Generic API wrapper  ===================== */

export interface ManualJobsApiResponse {
  status: string; // "ok"
  message: string; // "successful response"
  statusCode: number; // 200, 4xx, 5xx, …
  payload: ManualJobsPayload;
}

/* =====================  Payload level  ===================== */

export interface ManualJobsPayload {
  manualJobs: ManualJob[];
  totalJobs: number;
}

/* =====================  Core data model  ===================== */

// In your network file, update or ensure ManualJob interface can have parentId
export interface ManualJob {
  id: number; // This will be used as jobId in the request
  accountId: number;
  title: string;
  description: string;
  country: string;
  region: string;
  city: string;
  location: string;
  companyLabel: string;
  dateCreated: string;
  status: ManualJobStatus;
  indexationID: string | null;
  url: string | null;
  createdBy: number;
  updatedBy: number | null; // Assuming it can be null
  reqid: string | null;
  account: ManualJobAccount;
  createdByEmail: string | null; // Assuming it can be null
  enterpost_id?: string; // from your example data
  parentId?: number | string | null; // Add parentId if it can exist on a job
}
export interface ManualJobAccount {
  companyLabel: string;
  feedcode: string;
}
export type ManualJobStatus = "draft" | "stand_by" | "published" | "archived" | string; // fallback `string` keeps the type open-ended

/* -------- What the helper itself will return -------------- */
export interface GetEnterpostJobsDataSuccess {
  code: 200;
  data: ManualJobsPayload; // { manualJobs: ManualJob[]; totalJobs: number }
}

export interface GetEnterpostJobsDataFailure {
  code: number; // 4xx / 5xx…
  data: ManualJobsPayload; // always same shape; empty on failure
}

export type GetEnterpostJobsDataResponse =
  | GetEnterpostJobsDataSuccess
  | GetEnterpostJobsDataFailure;

/* =========================================================
   Main helper
   ========================================================= */
export const getEnterpostJobsData = async ({
  feedcode,
  status,
  search,
  accountOwner,
  limit = 50,
  page,
}: SearchJobsDataParams): Promise<GetEnterpostJobsDataResponse> => {
  try {
    /* -------- Build & clean query params ------------------ */
    const queryParams: Record<string, string | number> = {
      ...(feedcode && { feedcode }),
      ...(search && { search }),
      ...(status && { status }),
      ...(accountOwner && { accountOwner }),
      limit,
      from: (page - 1) * limit, // paginate
    };

    /* -------- Authentication ------------------------------ */
    const session = await auth();
    if (!session?.user?.user_id) {
      throw new Error("Unauthorized: No valid session");
    }

    /* -------- Call the API -------------------------------- */
    const { data, code } = (await talentApiV2({
      url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/manual-jobs/search`,
      method: "GET",
      data: queryParams,
      authPayload: { user_id: session.user.user_id },
    })) as { data: ManualJobsApiResponse; code: number };

    /* -------- Basic success guard ------------------------- */
    if (![200, 201, 204].includes(code)) {
      throw new Error("Unexpected response code");
    }

    return {
      code: 200,
      data: data.payload,
    };
  } catch (error) {
    /* -------- Normalise failure shape --------------------- */
    return {
      code: 404,
      data: {
        manualJobs: [],
        totalJobs: 0,
      },
    };
  }
};

/**
 * @async
 * @function getAccountData
 * @accountId account id to get data
 */
export const getAccountData = async (accountId: number) => {
  try {
    const session = await auth();

    if (!session?.user?.user_id) {
      throw new Error("Unauthorized: No valid session");
    }

    const response = await talentApiV2({
      url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/accounts/sales-module/${accountId}/info`,
      method: "GET",
      authPayload: {
        user_id: session?.user?.user_id,
      },
    });

    const { data, code } = response;

    if (![200, 201, 204].includes(code)) {
      throw new Error(`Oh oh, error`);
    }

    if (!data) {
      throw new Error("No data");
    }

    return data;
  } catch (error) {
    return {
      code: 404,
      error: "Couldn't fetch account data",
    };
  }
};

export type DataByAccountType = any;

// Helper interfaces for the new function's request body
interface JobIdItem {
  jobId: number;
}

interface JobsByAccountItem {
  accountId: number;
  jobs: JobIdItem[];
}

export interface JobQAStatusChangePayload {
  newStatus: "active" | "reject" | string; // Using string for flexibility
  jobType: "parent" | "child";
  jobsDataByAccount: JobsByAccountItem[];
}

// Interface for the function's response (optional, but good practice)
export interface JobQAStatusChangeResponse {
  success: boolean;
  message?: string;
  code?: number; // HTTP status code from API response or error code
}

// Add this new function to your network services file

/**
 * Sends a request to change the status of one or more jobs for QA.
 *
 * @async
 * @function jobQAStatusChange
 * @param {ManualJob[]} selectedJobs - An array of job objects to update.
 * @param {"active" | "reject"} newStatus - The new status for the jobs.
 * @returns {Promise<JobQAStatusChangeResponse>} A promise that resolves to an object indicating success or failure.
 */
export const jobQAStatusChange = async (
  selectedJobs: ManualJob[],
  newStatus: "active" | "reject",
): Promise<JobQAStatusChangeResponse> => {
  if (!selectedJobs || selectedJobs.length === 0) {
    return { success: false, message: "No jobs selected for status change." };
  }

  try {
    /* -------- Authentication ------------------------------ */
    const session = await auth();
    if (!session?.user?.user_id) {
      // Not throwing an error directly, but returning a failure response
      // This allows the caller in the React component to handle it more gracefully (e.g., show a message)
      return { success: false, message: "Unauthorized: No valid session.", code: 401 };
    }

    /* -------- Determine jobType ------------------------- */
    // If any selected job has a 'parentId', the jobType is "child". Otherwise, "parent".
    const hasParentId = selectedJobs.some(
      (job) => job.parentId !== undefined && job.parentId !== null && job.parentId !== "",
    );
    const jobType = hasParentId ? "child" : "parent";

    /* -------- Transform selectedJobs to jobsDataByAccount structure --- */
    const jobsByAccountMap = new Map<number, JobIdItem[]>();

    for (const job of selectedJobs) {
      if (!jobsByAccountMap.has(job.accountId)) {
        jobsByAccountMap.set(job.accountId, []);
      }
      jobsByAccountMap.get(job.accountId)?.push({ jobId: job.id });
    }

    const jobsDataByAccount: JobsByAccountItem[] = Array.from(jobsByAccountMap.entries()).map(
      ([accountId, jobs]) => ({
        accountId,
        jobs,
      }),
    );

    /* -------- Construct the request body ---------------- */
    const requestBody: JobQAStatusChangePayload = {
      newStatus,
      jobType,
      jobsDataByAccount,
    };

    /* -------- Call the API -------------------------------- */
    const response = await talentApiV2({
      url: `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/manual-jobs/jobs-status-change`,
      method: "POST",
      data: requestBody,
      authPayload: {
        user_id: session.user.user_id,
      },
    });
    const { code } = response; 

    if (![200, 201, 202, 204].includes(code)) {
      // 202 Accepted is also a common success for async operations
      // Log the detailed error for server-side debugging
      return {
        success: false,
        message: `Failed to change job status. API returned code ${code}.`,
        code: code,
      };
    }

    return {
      success: true,
      message: "Job status changed successfully.",
      code: code,
    };
  } catch (error) {
    return {
      success: false,
      message: "Failed to update please try again later",
      code: 404,
    };
  }
};
