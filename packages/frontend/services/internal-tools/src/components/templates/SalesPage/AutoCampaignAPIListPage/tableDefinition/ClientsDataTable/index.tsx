import React, { useCallback, useEffect, useMemo, useState } from "react";
import PowerTable from "@talent-front-libs/ui/components/organisms/PowerTable";
import {
  runningCampaignsTableColumns,
  pausedCampaignsTableColumns,
  actionsCampaignsTableColumns,
} from "./tableDefinition/TableColumns";
import {
  getClientCampaignsTablesv1,
  ClientCampaignsTablesParamsv1,
} from "@talent-front-services/internal-tools/modules/network/externalCampaign";
import { BodySMd, HeaderXsSb } from "@talent-front-libs/ui/theme/typography";
import { Divider, TableSection, TableContainer } from "./styles";
import { TableFilterGroup } from "./tableDefinition/TableFilter";
import { ColumnDef } from "@tanstack/react-table";

/** ----------------------------------
 * Types & constants
 * ---------------------------------- */

type TableType = "running" | "paused" | "entries";

interface GenericTableState<T> {
  data: T[];
  total: number;
  page: number;
  pageSize: number;
  loading: boolean;
}

const DEFAULT_PAGE_SIZE = 50;

/** ----------------------------------
 * Component
 * ---------------------------------- */

const ClientDataTable: React.FC = () => {
  // Shared filters and flag indicating a search has been submitted
  const [filters, setFilters] = useState<Partial<ClientCampaignsTablesParamsv1>>({});
  const [searchInitiated, setSearchInitiated] = useState(false);

  // Per‑table state kept simple with useState
  const [tables, setTables] = useState<Record<TableType, GenericTableState<any>>>(() => ({
    running: { data: [], total: 0, page: 1, pageSize: DEFAULT_PAGE_SIZE, loading: false },
    paused: { data: [], total: 0, page: 1, pageSize: DEFAULT_PAGE_SIZE, loading: false },
    entries: { data: [], total: 0, page: 1, pageSize: DEFAULT_PAGE_SIZE, loading: false },
  }));

  /** ----------------------------
   * Fetch helper (pure)
   * ---------------------------- */
  const fetchTable = useCallback(
    async (
      table: TableType,
      page: number,
      pageSize: number,
      activeFilters: Partial<ClientCampaignsTablesParamsv1>,
    ) => {
      setTables((prev) => ({
        ...prev,
        [table]: { ...prev[table], loading: true },
      }));

      try {
        const params: ClientCampaignsTablesParamsv1 = {
          ...activeFilters,
          table,
          limit: pageSize,
          from: (page - 1) * pageSize,
        } as ClientCampaignsTablesParamsv1;

        const res = await getClientCampaignsTablesv1(params);
        const payload = res.payload ?? { campaigns: [], total: 0 };

        setTables((prev) => ({
          ...prev,
          [table]: {
            ...prev[table],
            data: payload.campaigns ?? [],
            total: Number(payload.total) ?? 0,
            loading: false,
          },
        }));
      } catch (err) {
        setTables((prev) => ({ ...prev, [table]: { ...prev[table], loading: false } }));
      }
    },
    [],
  );

  /** ----------------------------
   * Effects per table — fire only after user submits a search.
   * ---------------------------- */
  useEffect(() => {
    if (!searchInitiated) return;
    fetchTable("running", tables.running.page, tables.running.pageSize, filters);
  }, [searchInitiated, tables.running.page, tables.running.pageSize, filters, fetchTable]);

  useEffect(() => {
    if (!searchInitiated) return;
    fetchTable("paused", tables.paused.page, tables.paused.pageSize, filters);
  }, [searchInitiated, tables.paused.page, tables.paused.pageSize, filters, fetchTable]);

  useEffect(() => {
    if (!searchInitiated) return;
    fetchTable("entries", tables.entries.page, tables.entries.pageSize, filters);
  }, [searchInitiated, tables.entries.page, tables.entries.pageSize, filters, fetchTable]);

  /** ----------------------------
   * Search handler — only updates state.
   * Fetch happens via the effects above.
   * ---------------------------- */
  const handleSearch = useCallback((rawFilters: Record<string, unknown>) => {
    const cleaned = Object.fromEntries(
      Object.entries(rawFilters).filter(([, v]) => v !== "" && v !== undefined && v !== null),
    );
    setFilters(cleaned);
    setSearchInitiated(true);
    // reset all tables to page 1 – effects will trigger a single fetch each
    setTables((prev) => {
      const updated: typeof prev = { ...prev };
      (Object.keys(prev) as TableType[]).forEach((tbl) => {
        updated[tbl] = { ...prev[tbl], page: 1 };
      });
      return updated;
    });
  }, []);

  /** ----------------------------
   * Pagination & page‑size handlers — only mutate state.
   * ---------------------------- */
  const updatePage = useCallback((tbl: TableType, newPage: number) => {
    setTables((prev) => ({
      ...prev,
      [tbl]: { ...prev[tbl], page: newPage },
    }));
  }, []);

  const updatePageSize = useCallback((tbl: TableType, newSize: number) => {
    setTables((prev) => ({
      ...prev,
      [tbl]: { ...prev[tbl], pageSize: newSize, page: 1 },
    }));
  }, []);

  /** ----------------------------
   * Column memoisation
   * ---------------------------- */
  const runningColumns = useMemo(() => runningCampaignsTableColumns(), []);
  const pausedColumns = useMemo(() => pausedCampaignsTableColumns(), []);
  const entriesColumns = useMemo(() => actionsCampaignsTableColumns(), []);
  const isLoading = tables.running.loading || tables.paused.loading || tables.entries.loading;

  /** ----------------------------
   * Render helper (generic)
   * ---------------------------- */
  const renderSection = (tbl: TableType, title: string, columns: ColumnDef<any>[]) => {
    const { data, total, page, pageSize, loading } = tables[tbl];

    if (!searchInitiated) return null; // nothing before first search
    if (total === 0) return null;

    return (
      <TableSection key={tbl}>
        <HeaderXsSb>{title}</HeaderXsSb>
        <PowerTable
          data={data}
          columns={columns}
          limit={pageSize}
          page={page}
          totalResults={total}
          changeLimit={(val) => updatePageSize(tbl, Number(val))}
          changePage={(p) => updatePage(tbl, Number(p))}
          loading={loading}
        />
      </TableSection>
    );
  };

  /** ----------------------------
   * Compose sections + dividers
   * ---------------------------- */
  const sections: JSX.Element[] = [];

  const entriesSection = renderSection(
    "entries",
    "Campaign Actions (Non‑Existing)",
    entriesColumns,
  );
  if (entriesSection) sections.push(entriesSection);

  const runningSection = renderSection("running", "Running Campaigns", runningColumns);
  if (runningSection) {
    if (sections.length) sections.push(<Divider key="div1" />);
    sections.push(runningSection);
  }

  const pausedSection = renderSection("paused", "Paused Campaigns", pausedColumns);
  if (pausedSection) {
    if (sections.length) sections.push(<Divider key="div2" />);
    sections.push(pausedSection);
  }

  /** ----------------------------
   * JSX
   * ---------------------------- */
  return (
    <>
      <TableFilterGroup onSearch={handleSearch} loading={isLoading} />

      <TableContainer>
        {searchInitiated ? (
          sections.length ? (
            sections
          ) : (
            <div>No campaigns found for the given criteria.</div>
          )
        ) : null}
      </TableContainer>
    </>
  );
};

export default ClientDataTable;
