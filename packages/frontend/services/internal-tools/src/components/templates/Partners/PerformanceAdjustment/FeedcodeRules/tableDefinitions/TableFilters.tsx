import { InputFilter } from "@talent-front-libs/ui/components/atoms/Input/variants/InputFilter";
import { useState, useCallback } from "react";
import SearchIcon from "@talent-front-libs/ui/icons/line/searchIcon.svg";
import { ButtonPrimary } from "@talent-front-libs/ui/components/atoms/Button/variants/ButtonPrimary";
import { FilterContainer, FormContainer } from "../../ChannelRules/styles";
import { DropdownPrimary } from "@talent-front-libs/ui/components/molecules/Dropdown/variants/DropdownPrimary";
import { rulesStatusOptions } from "@talent-front-services/internal-tools/utils/dropDownOptions";
import { FieldLabel, FilterInputWrapper } from "./styles";
import { Tooltip } from "@talent-front-libs/ui/components/atoms/Tooltip";
import TooltipIcon from "@talent-front-libs/ui/icons/line/questionCircle.svg";


/**
 * Table Filters
 * @returns
 */
export const TableFilters = ({ onFilterChange }: { onFilterChange: any }) => {
  const [filters, setFilters] = useState({
    search: "",
    feedcode: "",
    country: "",
    status: "",
  });

  // Fix 1: Add filters to dependency array
  const handleInputChange = useCallback((name: string, value: string) => {
    setFilters(prevFilters => ({
      ...prevFilters,
      [name]: value,
    }));
  }, []); // No dependencies needed with the function updater pattern

  const handleFilterChange = useCallback(() => {
    onFilterChange(filters);
  }, [filters, onFilterChange]);

  // Fix 2: Handle the dropdown selection correctly
  const handleStatusSelect = useCallback((option: any) => {
    // Extract the value from the option object
    const value = option || "";
    handleInputChange("status", value);
  }, [handleInputChange]);

  const handleStatusReset = useCallback(() => {
    handleInputChange("status", "-");
  }, [handleInputChange]);

  return (
    <FilterContainer>
      <FilterInputWrapper>
        <FieldLabel htmlFor="search">Search</FieldLabel>
        <InputFilter
          placeholder="job_country:ca  job_feedcode:neuvoo*"
          name="search"
          value={filters.search}
          onChangeValue={(value) => handleInputChange("search", value)}
          onEnterKey={handleFilterChange}
          icon={SearchIcon}
          iconProps={{ title: "Search", width: "15", height: "15", color: "gray" }}
          isDisabled={false}
          autoFocus={false}
        />
      </FilterInputWrapper>
      <FormContainer>
        <FilterInputWrapper>
          <FieldLabel htmlFor="feedcode">
            {" "}
            <Tooltip
              trigger="onClick"
              text={`To access the UI to disable rules, please enter an exact feedcode, as well as a value in the country field.`}
              placement="top"
            >
              <TooltipIcon width="18px" height="18px" color={"gray"} title="Information" />
            </Tooltip>
            Search an exact feedcode
          </FieldLabel>
          <InputFilter
            name="feedcode"
            placeholder="Search an exact feedcode"
            value={filters.feedcode}
            onChangeValue={(value) => handleInputChange("feedcode", value)}
            onEnterKey={handleFilterChange}
            icon={SearchIcon}
            iconProps={{ title: "Search", width: "15", height: "15", color: "gray" }}
          />
        </FilterInputWrapper>
        <FilterInputWrapper>
          <FieldLabel htmlFor="country">Country (ISO code) or * for all countries</FieldLabel>
          <InputFilter
            name="country"
            placeholder="Country (ISO code)"
            value={filters.country}
            onChangeValue={(value) => handleInputChange("country", value)}
            onEnterKey={handleFilterChange}
            icon={SearchIcon}
            iconProps={{ title: "Search", width: "15", height: "15", color: "gray" }}
          />
        </FilterInputWrapper>
        <FilterInputWrapper>
          <FieldLabel htmlFor="status">Status</FieldLabel>
          <DropdownPrimary
            label="Status"
            options={rulesStatusOptions}
            onSelect={handleStatusSelect}
            onReset={handleStatusReset}
          />
        </FilterInputWrapper>
        <FilterInputWrapper>
          <ButtonPrimary label="Search" onClick={handleFilterChange} />
        </FilterInputWrapper>
      </FormContainer>
    </FilterContainer>
  );
};