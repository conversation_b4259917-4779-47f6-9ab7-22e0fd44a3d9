"use client";

import React, { useState, useEffect, useRef } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter, useSearchParams } from "next/navigation";
import MainContainer from "@talent-front-libs/ui/components/atoms/MainContainer";
import {
  <PERSON>Header,
  PageTitle,
  Card,
  FlexContainer,
  Title,
  PurpleTitle,
  BtnTitleMessage,
  DateMessage,
  CommentMessage,
  StatusMessage,
} from "./styles";
import { ButtonSecondary } from "@talent-front-libs/ui/components/atoms/Button/variants/ButtonSecondary";
import { BodyXsMd } from "@talent-front-libs/ui/theme/typography";
import { SidePanel } from "@talent-front-libs/ui/components/organisms/SidePanel";
import InvoicePreviewComponent from "@talent-front-services/internal-tools/components/organisms/Billing/Invoices/Preview/InvoicePreviewComponent";
import { InvoicePreviewSidePanel } from "@talent-front-services/internal-tools/components/organisms/Billing/Invoices/Preview/InvoicePreviewSidePanel/index";
import {
  generateInvoiceByID,
  getPreviewByID,
  saveInvoicePreviewComment,
} from "@talent-front-services/internal-tools/modules/network/billing/invoices";
import CommentTextArea from "./CommentTextArea";
import { useSession } from "next-auth/react";
import { loadMessages } from "@talent-front-services/internal-tools/utils/translations";

/**
 * Translate description to a readable format.
 */
const transleDescription = (
  description: string,
  labels: { ppc: string; clicks: string; credit: string },
) => {
  const contentArray = description.split(" ");

  const translations = contentArray.map((item: string) => {
    if (item === "ppc_campaign,") {
      return `${labels.ppc},`;
    }
    if (item === "billed_clicks") {
      return labels.clicks;
    }

    if (item === "credit") {
      return labels.credit;
    }

    return item;
  });

  return translations.join(" ").replace(/,,/g, ",");
};

/**
 * Comment List
 */
const CommentList: React.FC<any> = ({ comments }: { comments: any }) => {
  // Safely parse the comments if available
  const commentList = comments?.invoiceComments ? JSON.parse(comments.invoiceComments) : [];

  return (
    <div style={{ maxHeight: "600px", overflowY: "auto" }}>
      {commentList.length > 0 ? (
        commentList.map((item: any, index: number) => {
          const date = item?.date
            ? new Date(item.date).toISOString().slice(0, 16).replace("T", " ")
            : "";
          return (
            <Card key={index} style={{ display: "flex", flexDirection: "column" }}>
              <FlexContainer $flexdirection="row" $gap="8">
                <FlexContainer $flexdirection="column" $gap="8">
                  <PurpleTitle>{item?.name}</PurpleTitle>
                  <DateMessage>{date}</DateMessage>
                  <CommentMessage>{item?.comment}</CommentMessage>
                </FlexContainer>
                <StatusMessage>{item?.status?.toUpperCase()}</StatusMessage>
              </FlexContainer>
            </Card>
          );
        })
      ) : (
        <></>
      )}
    </div>
  );
};

// Memoize the PDF component
const MemoizedInvoicePreview = React.memo(InvoicePreviewComponent);

/**
 * Invoice Preview Page.
 */
const InvoicePreviewPage = ({ invoiceDetails }: any): React.JSX.Element => {
  const params = useParams();
  const searchParams = useSearchParams();
  const router = useRouter();
  const [isPanelOpen, setIsPanelOpen] = useState(false);
  const [isGeneratedDisabled, setIsGeneratedDisabled] = useState(true);
  const [isRequestHelpDisabled, setRequestHelpDisabled] = useState(true);
  const [invoiceData, setInvoiceData] = useState<any | null>(invoiceDetails);
  const { data: session } = useSession();
  const isRoleFinance = session?.user?.role === "finance";
  const textareaRef = useRef<{ resetField: () => void }>(null);

  const [t, setTranslations] = useState<any>(null);

  useEffect(() => {
    (async () => {
      const translations = await loadMessages(invoiceData?.language ?? "en");
      setTranslations(translations);
    })();
  }, []);

  const descriptionLabels = {
    ppc: t?.["employers_billing_invoices.ppc_campaign"],
    clicks: t?.["employers_billing_invoices.clicks"],
    credit: t?.["employers_billing_invoices.credit"],
  };

  /**
   * Generate Invoice from Preview
   */
  const handleGenerateInvoice = async () => {
    setIsGeneratedDisabled(true);
    const accountID = searchParams.get("accountId");
    const response = await generateInvoiceByID(params?.id as string, accountID as string);
    if (![200, 201, 204].includes(response?.statusCode)) setIsGeneratedDisabled(false);
    if ([200, 201, 204].includes(response?.statusCode))
      router.push(`/billing/invoices/view/${response?.payload?.invoiceId}?accountId=${accountID}`);
  };

  /**
   * Validate current Invoice information
   */
  useEffect(() => {
    if (invoiceData.dataFront?.comments?.invoiceStatus !== "accepted") {
      setIsGeneratedDisabled(false);
      setRequestHelpDisabled(false);
    }
  }, [invoiceData]);

  /** Comment Text Area */
  const [commentText, setCommentText] = useState("");

  useEffect(() => {
    setRequestHelpDisabled(!(commentText?.length > 0));
  }, [commentText]);

  /** Comment Text Change */
  const handleTextChange = (text: any) => {
    setCommentText(text);
  };

  /** Comment Submit Handler */
  const commentSubmitHandler = async () => {
    setIsGeneratedDisabled(true);
    setRequestHelpDisabled(true);

    const accountId = searchParams.get("accountId");
    const payload = {
      id: invoiceData?.previewId || "",
      accountId: accountId || "",
      comment: commentText.toString().trim() || "",
    };

    await saveInvoicePreviewComment(payload);
    setIsGeneratedDisabled(false);
    setRequestHelpDisabled(false);

    setCommentText("");
    textareaRef.current?.resetField();
    const invoiceNewData = await getPreviewByID(
      invoiceData?.previewId as string,
      invoiceData.dataFront.accountId as string,
    );

    setInvoiceData(invoiceNewData.payload);
    router.refresh();
  };

  return (
    <MainContainer>
      <PageHeader>
        <PageTitle>Invoice Preview</PageTitle>
      </PageHeader>

      <SidePanel isOpen={isPanelOpen} onClose={() => setIsPanelOpen(false)} title="Invoice Preview">
        <InvoicePreviewSidePanel invoiceData={invoiceData} />
      </SidePanel>

      <FlexContainer $flexdirection={"row"} $gap={"8"}>
        <FlexContainer $flexdirection={"column"} $flex={"1"}>
          <Card style={{ display: "flex", flexDirection: "column" }}>
            <div style={{ padding: "0 0 16px", borderBottom: "2px solid #F6F6F9" }}>
              <FlexContainer
                $flexdirection={"row"}
                $justifycontent={"space-between"}
                $alignitems={"center"}
              >
                <FlexContainer $flexdirection={"column"}>
                  <Title>Comments:</Title>
                </FlexContainer>
                <FlexContainer $flexdirection={"column"} $alignitems={"flex-end"}>
                  {/* <Link href={`/sales/accounts?feedcode=${invoiceData?.dataFront?.feedcode}`}>
                    <ButtonSecondary label="Edit Acount" size="small" />
                  </Link> */}
                </FlexContainer>
              </FlexContainer>
            </div>
            <div
              style={{
                display: "flex",
                flexDirection: "column",
                gap: 12,
                padding: "16px 0 0",
              }}
            >
              <Title>Notes:</Title>
              <div>
                <CommentTextArea name="comment" onTextChange={handleTextChange} ref={textareaRef} />
              </div>
              <div
                style={{
                  display: "flex",
                  flexDirection: "row",
                  justifyContent: "flex-end",
                  alignItems: "center",
                  gap: 6,
                }}
              >
                <ButtonSecondary
                  type={"button"}
                  label={isRoleFinance ? "Generate" : "Accept"}
                  size="small"
                  onClick={handleGenerateInvoice}
                  disabled={isGeneratedDisabled}
                />
                {!isRoleFinance && (
                  <ButtonSecondary
                    label="Request Help"
                    size="small"
                    onClick={commentSubmitHandler}
                    disabled={isRequestHelpDisabled}
                  />
                )}
              </div>
            </div>
          </Card>
          <CommentList comments={invoiceData?.dataFront?.comments} />
          {/* Invoice Notes */}
          {!!invoiceData?.dataFront?.infoBilled &&
            invoiceData?.dataFront?.infoBilled?.length > 0 && (
              <Card
                style={{
                  display: "flex",
                  flexDirection: "column",
                  gap: 16,
                  maxHeight: "900px",
                  overflow: "hidden",
                  overflowY: "scroll",
                  padding: "24px 16px",
                }}
              >
                {invoiceData?.dataFront?.infoBilled.map(
                  ({ description, billed, mBilled, fBilled }: any, index: any) => {
                    return (
                      <Title
                        style={{ display: "flex", flexDirection: "column" }}
                        key={`notes-${index}`}
                      >
                        <BodyXsMd>
                          Description: {transleDescription(description, descriptionLabels)}
                        </BodyXsMd>
                        <BodyXsMd>Billed: {billed}</BodyXsMd>
                        {Boolean(fBilled) && <BodyXsMd>Rounded billed: {fBilled}</BodyXsMd>}
                        {Boolean(mBilled) && <BodyXsMd>Billed modified by AR: {mBilled}</BodyXsMd>}
                      </Title>
                    );
                  },
                )}
              </Card>
            )}
        </FlexContainer>
        <div style={{ display: "flex", flexDirection: "column", flex: 2 }}>
          <Card style={{ display: "flex", flexDirection: "column" }}>
            <Title style={{ padding: "0 0 16px", borderBottom: "2px solid #F6F6F9" }}>
              Feedcode: {`${invoiceData?.dataFront?.feedcode || "---"}`}
            </Title>
            <div
              style={{
                padding: "16px 0 0",
                display: "flex",
                flexDirection: "row",
                justifyContent: "space-between",
              }}
            >
              <div style={{ display: "flex", flexDirection: "column", gap: 6 }}>
                <Title>Address: {invoiceData?.dataFront?.emailToSend ?? "---"}</Title>
                <Title>
                  Subject: Talent Invoice {`#${invoiceData?.dataFront.invoiceNumber || ""}`}
                </Title>
              </div>
              <Title style={{ display: "flex", flexDirection: "column" }}>
                <div>Status: Pending</div>
              </Title>
            </div>
          </Card>
          <Card style={{ display: "flex", flexDirection: "column", flex: 1 }}>
            <div style={{ padding: "0 0 16px", borderBottom: "2px solid #F6F6F9" }}>
              <div
                style={{
                  display: "flex",
                  flexDirection: "row",
                  justifyContent: "space-between",
                }}
              >
                <div style={{ display: "flex", flexDirection: "column" }}>
                  <PurpleTitle>Invoice</PurpleTitle>
                </div>
                <div style={{ display: "flex", flexDirection: "column" }}>
                  <BtnTitleMessage onClick={() => setIsPanelOpen(true)}>Edit</BtnTitleMessage>
                </div>
              </div>
            </div>
            <div style={{ display: "flex", justifyContent: "center", padding: "24px 0 0" }}>
              <MemoizedInvoicePreview invoiceData={invoiceData?.dataFront} />
            </div>
          </Card>
        </div>
      </FlexContainer>
    </MainContainer>
  );
};

export default InvoicePreviewPage;
