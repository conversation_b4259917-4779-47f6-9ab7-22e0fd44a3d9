/**
 * Invoices Table Key Mapper
 */
export const invoicesKeyMap = {
  qboId: "Qbo ID",
  billId: "Bill ID",
  feedcode: "Feedcode",
  description: "Description",
  group: "Billing Group",
  company: "Company",
  billed: "Billed",
  currency: "Currency",
  tax: "Tax",
  account_owner: "Account Owner",
  status: "Status",
  billingName: "Customer Name",
  invoice_date: "Invoice Date",
  due_date: "Due Date",
  vat: "VAT",
  cost_centre: "Cost Centre",
  agency: "Agency",
  discount_percentage: "Discount %",
  payment_terms: "Payment Terms",
  po_number: " PO Number",
  language: "Billing Language",
  note_to_client: "Note to Client",
  subtotal: "Sub-Total",
  discount: "Discount",
  ht: "HT",
  tva: "TVA",
  total: "Total",
};

/**
 * Transforms the data object from the API to Match the KeyMap
 * @param {Object} data - The payload object to transform.
 * @returns {Object} - The transformed object with new key names.
 */
export const transformInvoiceTableData = async (data: any) => {
  return data.map((item: any) => {
    return {
      accountId: item.accountId,
      invoiceId: item.invoiceId,
      billId: item.billId,
      qboId: item.qboId,
      feedcode: item.feedcode,
      group: item.group,
      company: item.company,
      billed: item.billed,
      currency: item.currency,
      tax: item.tax,
      accountOwner: item.accountOwner,
      status: item.status,
      payment_status: item.payment_status,
      qbo_status: item.qbo_status,
      highlighting: item.highlighting,
    };
  });
};
