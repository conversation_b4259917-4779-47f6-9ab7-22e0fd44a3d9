import styled from "styled-components";
import theme from "@talent-front-libs/ui/theme/themes";

export const Card = styled.div`
  background-color: ${theme.light.color.white};
  margin: 10px;
  padding: 24px 16px;
  position: relative;
  box-shadow: ${theme.light.shadow.md1};
  border-radius: 8px;
`;

export const PageHeader = styled.div`
  padding: 10px 15px;
`;

export const PageTitle = styled.div`
  font-size: 24px;
  font-weight: 700;
  line-height: 27px;
  margin: 20px 0;
`;

// Define the interface for props
interface FlexContainerProps {
  $alignitems?: string;
  $flex?: string;
  $flexdirection?: string;
  $gap?: string;
  $justifycontent?: string;
  $cursor?: string;
}

export const FlexContainer = styled.div<FlexContainerProps>`
  display: flex;
  ${({ $flexdirection }) => $flexdirection && `flex-direction: ${$flexdirection};`}
  ${({ $flex }) => $flex && `flex: ${$flex};`}
  ${({ $gap }) => $gap && `gap: ${$gap};`}
  ${({ $justifycontent }) => $justifycontent && `justify-content: ${$justifycontent};`}
  ${({ $alignitems }) => $alignitems && `align-items: ${$alignitems};`}
`;

export const Title = styled.span`
  font-size: 14px;
  color: ${theme.light.color.black[600]};
`;

export const PurpleTitle = styled.span`
  display: flex;
  flex: 1;
  font-size: 14px;
  color: ${theme.light.color.purple[600]};
  font-weight: 500;
`;

export const BtnTitleMessage = styled.span`
  display: flex;
  flex: 1;
  font-size: 14px;
  color: ${theme.light.color.purple[600]};
  font-weight: 500;
  cursor: pointer;
`;

export const DateMessage = styled.span`
  display: flex;
  flex: 1;
  font-size: 12px;
  color: ${theme.light.color.gray[400]};
`;

export const CommentMessage = styled.span`
  display: flex;
  flex: 1;
  font-size: 12px;
  color: ${theme.light.color.black[300]};
`;

export const StatusMessage = styled.span`
  display: flex;
  flex: 1;
  font-size: 12px;
  color: ${theme.light.color.green[300]};
  justify-content: flex-end;
`;