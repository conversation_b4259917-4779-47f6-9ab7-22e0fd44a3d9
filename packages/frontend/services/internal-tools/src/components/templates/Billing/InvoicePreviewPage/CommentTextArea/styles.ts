import styled from "styled-components";
import theme from "@talent-front-libs/ui/theme/themes";
import { BodyXsRg } from "@talent-front-libs/ui/theme/typography";

const { gray, red } = theme.light.color;
const colorsInput = {
  primary: {
    backgroundColor: gray[25],
    textColor: gray[800],
    hoverBackgroundColor: gray[50],
    hoverBorderColor: gray[50],
    supportTextColor: gray[400],
    errorSupportTextColor: red[300],
    placeHolderColor: gray[250],
  },
};

export const CommentArea = styled.textarea`
  background-color: #fff;
  padding: 20px;
  border: 1px solid #676767;
  box-sizing: border-box;
  border-radius: 10px;
  width: 100%;
  max-width: 100%;
  -webkit-appearance: none;
  display: block;
  overflow: hidden;
  resize: vertical;
  min-height: 40px;
  line-height: 20px;
`;

export const TextMessage = styled(BodyXsRg)<any>`
  color: ${(p) =>
    p?.$type === "error"
      ? colorsInput.primary.errorSupportTextColor
      : colorsInput.primary.supportTextColor};
  line-height: 14px;
  ${(p) =>
    p.$disabled &&
    ` opacity: 0.5;
`}
`;

export const Messages = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 3px;
  padding: 1px 24px;
`;
