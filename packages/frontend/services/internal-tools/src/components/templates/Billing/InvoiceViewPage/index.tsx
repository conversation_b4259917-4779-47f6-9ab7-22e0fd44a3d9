"use client";

import React, { useState, useEffect } from "react";
import Link from "next/link";
import MainContainer from "@talent-front-libs/ui/components/atoms/MainContainer";
import {
  PageHeader,
  PageTitle,
  Card,
  ActionButton,
  Icon,
  FlexContainer,
  PurpleTitle,
  DateMessage,
  CommentMessage,
  StatusMessage,
  Title,
} from "./styles";
import DownloadIcon from "@talent-front-libs/ui/icons/line/download.svg";
import { SidePanel } from "@talent-front-libs/ui/components/organisms/SidePanel";
import { InvoiceViewSidePanel } from "@talent-front-services/internal-tools/components/organisms/Billing/Invoices/View/InvoiceViewSidePanel/index";
import { ButtonSecondary } from "@talent-front-libs/ui/components/atoms/Button/variants/ButtonSecondary";
import DisplayInvoiceIframe from "./DisplayInvoiceIframe";
import { useRbac } from "@talent-front-services/internal-tools/utils/Rbac";

/**
 * Comment List
 */
const CommentList: React.FC<any> = ({
  comments,
  isCommentVisible,
}: {
  comments: any;
  isCommentVisible: any;
}) => {
  // Validate comments visibility
  if (!isCommentVisible) return null;

  return (
    <FlexContainer $flexdirection={"column"} $flex={"1"}>
      <Card style={{ display: "flex", flexDirection: "column" }}>
        <div style={{ padding: "0 0 16px", borderBottom: "2px solid #F6F6F9" }}>
          <FlexContainer
            $flexdirection={"row"}
            $justifycontent={"space-between"}
            $alignitems={"center"}
          >
            <FlexContainer $flexdirection={"column"}>
              <Title>Comments:</Title>
            </FlexContainer>
          </FlexContainer>
        </div>
        <div style={{ maxHeight: "600px", overflowY: "auto" }}>
          {comments.length > 0 ? (
            comments.map((item: any, index: number) => {
              const date = item?.date
                ? new Date(item.date).toISOString().slice(0, 16).replace("T", " ")
                : "";
              return (
                <>
                  <div
                    key={index}
                    style={{ display: "flex", flexDirection: "column", padding: "16px 8px 0" }}
                  >
                    <FlexContainer $flexdirection="row" $gap="8">
                      <FlexContainer $flexdirection="column" $gap="8">
                        <PurpleTitle>{item?.name}</PurpleTitle>
                        <DateMessage>{date}</DateMessage>
                        <CommentMessage>{item?.comment}</CommentMessage>
                      </FlexContainer>
                      <StatusMessage>{item?.status?.toUpperCase()}</StatusMessage>
                    </FlexContainer>
                  </div>
                </>
              );
            })
          ) : (
            <></>
          )}
        </div>
      </Card>
    </FlexContainer>
  );
};

/**
 * Invoice View Page.
 */
const InvoiceViewPage = ({ invoiceDetails, sidePanelData }: any): React.JSX.Element => {
  const [isPanelOpen, setIsPanelOpen] = useState(false);
  const [invoiceImage, setInvoiceImage] = useState("");
  const [invoiceData] = useState<any | null>(invoiceDetails);
  const { hasPermission } = useRbac();

  const userCanEditInvoice = hasPermission("BILLINGS_INVOICES", "CAN_EDIT_INVOICES");

  useEffect(() => {
    if (invoiceData?.base64) setInvoiceImage(invoiceData?.base64);
  }, [invoiceData]);

  return (
    <MainContainer>
      <PageHeader>
        <PageTitle>Invoice {`#${invoiceData?.invoiceNumber || ""}`}</PageTitle>
        <Link href={"/billing/invoices"}>
          <ButtonSecondary label="Back to invoices" size="small" />
        </Link>
      </PageHeader>

      <SidePanel
        isOpen={isPanelOpen}
        onClose={() => setIsPanelOpen(false)}
        title={`Invoice - ${invoiceData?.invoiceNumber || ""}`}
      >
        <InvoiceViewSidePanel sidePanelData={sidePanelData} />
      </SidePanel>

      <FlexContainer $flexdirection={"row"} $gap={"8"}>
        <CommentList comments={invoiceData?.comments} isCommentVisible={userCanEditInvoice} />
        <div style={{ display: "flex", flexDirection: "column", flex: 2 }}>
          <Card style={{ display: "flex", flexDirection: "column" }}>
            <Title style={{ padding: "0 0 16px", borderBottom: "2px solid #F6F6F9" }}>
              Feedcode: {`${invoiceData?.feedcode || "---"}`}
            </Title>
            <div
              style={{
                padding: "16px 0 0",
                display: "flex",
                flexDirection: "row",
                justifyContent: "space-between",
              }}
            >
              <div style={{ display: "flex", flexDirection: "column", gap: 6 }}>
                <Title>Address: {invoiceData?.emailToSend ?? "---"}</Title>
                <Title>Subject: Facture de Talent {`#${invoiceData?.invoiceNumber || ""}`}</Title>
              </div>
              <Title style={{ display: "flex", flexDirection: "column" }}>
                <div>Status: Pending</div>
              </Title>
            </div>
          </Card>
          <Card style={{ display: "flex", flexDirection: "column", flex: 1 }}>
            <div style={{ padding: "0 0 16px", borderBottom: "2px solid #F6F6F9" }}>
              <div
                style={{
                  display: "flex",
                  flexDirection: "row",
                  justifyContent: "space-between",
                }}
              >
                <div style={{ display: "flex", flexDirection: "column" }}>
                  <PurpleTitle>Invoice</PurpleTitle>
                </div>
                <div style={{ display: "flex", flexDirection: "column" }}>
                  <div style={{ display: "flex", flexDirection: "row", gap: 8 }}>
                    <>
                      {userCanEditInvoice && (
                        <>
                          <ActionButton>
                            <span onClick={() => setIsPanelOpen(true)}>
                              <PurpleTitle>Edit</PurpleTitle>
                            </span>
                          </ActionButton>
                          <span>|</span>
                        </>
                      )}
                      <a
                        href={`data:application/pdf;base64,${invoiceData?.base64}`}
                        download="invoice.pdf"
                      >
                        <ActionButton>
                          <PurpleTitle>Download</PurpleTitle>
                          <Icon>
                            <DownloadIcon width="22px" height="22px" title="Download" />
                          </Icon>
                        </ActionButton>
                      </a>
                    </>
                  </div>
                </div>
              </div>
            </div>
            <div style={{ display: "flex", justifyContent: "center", padding: "24px 0 0" }}>
              {invoiceImage && <DisplayInvoiceIframe base64={invoiceImage} />}
            </div>
          </Card>
        </div>
      </FlexContainer>
    </MainContainer>
  );
};

export default InvoiceViewPage;
