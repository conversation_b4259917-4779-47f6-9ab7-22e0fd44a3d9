/**
 * Accounts Table Key Mapper
 */
export const accountsKeyMap = {
  netsuite_id: "Net ID",
  qboId: "Qbo ID",
  feedcode: "Feedcode",
  agency_name: "Agency Name",
  account_owner: "Account Owner",
  group: "Billing Group",
  talent_company: "Company",
  currency: "Currency",
  tax: "Tax",
  spent_last_month: "Spent last month",
  spent_this_month: "Spent this month",
  status: "Status",
  yaypay_link: "Payment Link",
};

/**
 * Transforms the data object from the API to Match the KeyMap
 * @param {Object} data - The payload object to transform.
 * @returns {Object} - The transformed object with new key names.
 */
export const transformAccountsTableData = async (data: any) => {
  return data.map((item: any) => {
    return {
      id: item.id,
      account_id: item.accountId,
      invoice_option: item.invoiceOption,
      campaign_id: item?.campaignID,
      invoice_id: item?.invoiceId,
      qboId: item.qboId,
      feedcode: item.feedcodeColumn,
      agency_name: item.agencyName,
      account_owner: item.accountOwner,
      group: item.group,
      talent_company: item.talentCompany,
      currency: item.currency,
      tax: item.tax,
      spent_last_month: item.spentLastMonth,
      spent_this_month: "",
      status: item.status,
      yaypay_link: item.yaypayLink,
      preview_identifier: item.previewIdentifier,
      highlighting: item.highlighting,
      netsuite_id: item.netsuiteId,
    };
  });
};
