"use client";

import { forwardRef, useEffect, useImperativeHandle } from "react";
import { useForm, Controller } from "react-hook-form";
import { CommentArea } from "./styles";

type CommentTextAreaProps = {
  name: string;
  onTextChange?: (value: string) => void;
};

/**
 * Text Area Component
 */
export const CommentTextArea = forwardRef(({ name, onTextChange }: CommentTextAreaProps, ref) => {
  const { control, watch, reset } = useForm({
    defaultValues: {
      [name]: "",
    },
  });

  const value = watch(name);

  useEffect(() => {
    if (onTextChange) onTextChange(value);
  }, [value, onTextChange]);

  // Exponer método reset al padre
  useImperativeHandle(ref, () => ({
    /**  */
    resetField: () => {
      reset({ [name]: "" });
    },
  }));

  return (
    <Controller
      name={name}
      control={control}
      render={({ field }) => (
        <CommentArea {...field} placeholder="Type something..." rows={4} cols={50} />
      )}
    />
  );
});

CommentTextArea.displayName = "CommentTextArea";

export default CommentTextArea;
