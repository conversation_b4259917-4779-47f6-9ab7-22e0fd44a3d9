"use client";

import React from "react";
import dynamic from "next/dynamic";
import { Page, Text, View, Document, StyleSheet, Image } from "@react-pdf/renderer";
import PreviewPDF from "@talent-front-libs/ui/components/molecules/PreviewPDF";

/** This needs to be imported Dynamically, otherwise it will not work */
const PDFDownloadLink = dynamic(
  () => import("@react-pdf/renderer").then((mod) => mod.PDFDownloadLink),
  { ssr: false },
);

// Document Settings
const settings = {
  title: "Talent.com | Receipt",
  author: "Talent.com",
  subject: "Receipt",
  keywords: "Receipt",
  creator: "Talent.com",
  producer: "Talent.com",
  language: "en",
  // eslint-disable-next-line jsdoc/require-jsdoc
  onRender: () => { },
};

// Create styles
const styles = StyleSheet.create({
  page: {
    flexDirection: "column",
    backgroundColor: "white",
  },
  header: {
    margin: 0,
    padding: 0,
    flexGrow: 1,
    backgroundColor: "#691f74",
    maxHeight: "12mm",
  },
  row: {
    flexDirection: "row",
    margin: 0,
  },
  col12: {
    flexGrow: 1,
    display: "flex",
    flexDirection: "column",
    margin: 0,
    width: "100%",
  },
  col8: {
    flexGrow: 4,
    display: "flex",
    flexDirection: "column",
    justifyContent: "flex-start",
    margin: 0,
    maxWidth: 330,
    width: "100%",
  },
  col2: {
    flexGrow: 1,
    display: "flex",
    flexDirection: "column",
    justifyContent: "flex-start",
    alignItems: "flex-start",
    margin: 0,
    maxWidth: 200,
    width: "100%",
  },
  title: {
    margin: 0,
  },
  subtitle: {
    textAlign: "left",
    margin: 0,
    color: "#241037",
    fontSize: "10px",
    lineHeight: 1.5,
    fontWeight: 700,
  },
  paragraph: {
    textAlign: "left",
    margin: 0,
    color: "gray",
    fontSize: "9px",
    lineHeight: 1.5,
  },
  item: {
    display: "flex",
    flexDirection: "column",
    margin: 0,
    color: "gray",
    fontSize: "8px",
    lineHeight: 1.5,
    width: "100%",
  },
  footer: {
    margin: 0,
    padding: 0,
    flexGrow: 1,
    backgroundColor: "#691f74",
    maxHeight: "12mm",
  },
});

/**
 * Helper function for Table definition
 */
const DescriptionTable = ({ data }: any) => {
  // Purple Top and Bottom separator
  const PurpleSeparator: JSX.Element = (
    <View style={{ borderTop: "1px solid #241037", maxHeight: "1px", width: "100%" }} />
  );

  // Grey line separator
  const GraySeparator: JSX.Element = (
    <View style={{ borderTop: "1px solid #D8D8D8", maxHeight: "1px", width: "100%" }} />
  );

  return (
    <>
      <View
        style={{
          display: "flex",
          flexDirection: "row",
          alignItems: "center",
          height: "20px",
        }}
      >
        <Text style={[styles.item, { maxWidth: "125px", color: "#241037" }]}>
          NUMERO DE FACTURE
        </Text>
        <Text style={[styles.item, { maxWidth: "100px", textAlign: "center", color: "#241037" }]}>
          DATE DE LA FACTURE
        </Text>
        <Text style={[styles.item, { maxWidth: "80px", textAlign: "center", color: "#241037" }]}>
          DATE LIMITE
        </Text>
        <Text style={[styles.item, { maxWidth: "80px", textAlign: "center", color: "#241037" }]}>
          MONTE INITIAL
        </Text>
        <Text style={[styles.item, { maxWidth: "70px", textAlign: "right", color: "#241037" }]}>
          EQUILIBRE
        </Text>
        <Text style={[styles.item, { maxWidth: "80px", textAlign: "right", color: "#241037" }]}>
          PAIMENT
        </Text>
      </View>
      {PurpleSeparator}
      <View style={{ width: "100%" }}>
        {data.map(
          (
            { Balance, DueDate, InvoiceDate, InvoiceNumber, OriginalAmount, Payment }: any,
            index: number,
          ) => {
            return (
              <div key={`line-${index}`}>
                {index > 0 && GraySeparator}
                <View
                  style={{
                    display: "flex",
                    flexDirection: "row",
                    alignItems: "center",
                    height: "20px",
                  }}
                >
                  <Text style={[styles.item, { maxWidth: "125px" }]}>{InvoiceNumber}</Text>

                  <Text style={[styles.item, { maxWidth: "100px", textAlign: "center" }]}>
                    {InvoiceDate}
                  </Text>
                  <Text style={[styles.item, { maxWidth: "80px", textAlign: "center" }]}>
                    {DueDate}
                  </Text>
                  <Text style={[styles.item, { maxWidth: "80px", textAlign: "center" }]}>
                    {OriginalAmount}
                  </Text>
                  <Text style={[styles.item, { maxWidth: "70px", textAlign: "right" }]}>
                    {Balance}
                  </Text>
                  <Text style={[styles.item, { maxWidth: "80px", textAlign: "right" }]}>
                    {Payment}
                  </Text>
                </View>
              </div>
            );
          },
        )}
      </View>
      {PurpleSeparator}
    </>
  );
};

/**
 * Invoice Preview Template
 */
export const ReceiptTemplate = ({ invoiceData }: any) => {
  if (!invoiceData) return <></>;
  //const date = new Date(invoiceData?.date).toLocaleDateString() || "";
  return (
    <Document {...settings}>
      {invoiceData.content &&
        invoiceData.content.map((content: any, index: number) => {
          const isLastIndex = index === invoiceData.content.length - 1;
          return (
            <Page size="A4" key={`page_${index}`} style={styles.page}>
              <View style={styles.header} fixed />
              <View style={[styles.row, { padding: "30 30 0" }]} fixed>
                <View style={styles.col8}>
                  <Text style={{ color: "gray", fontSize: "10px" }}>
                    {invoiceData?.invoiceNumber || ""}
                  </Text>
                  <Text style={{ color: "#241037", fontSize: "20px" }}>Payment Receipt</Text>
                </View>
                <View style={[styles.col2, { paddingTop: 8 }]}>
                  {/* eslint-disable-next-line jsx-a11y/alt-text */}
                  <Image
                    src="https://cdn-static.talent.com/img/common/talent_logo_purple_220.png"
                    style={{ width: 120 }}
                  />
                </View>
              </View>

              {/* Date and Billed From */}
              <View style={[styles.row, { padding: "20 30 0" }]}>
                <View style={styles.col8}>
                  <Text style={styles.subtitle}>Date de Facturation:</Text>
                  <Text style={styles.paragraph}>{invoiceData?.PaymentDate}</Text>
                </View>
                <View style={styles.col2}>
                  <Text style={styles.subtitle}>{invoiceData?.companyLabel}</Text>
                  {invoiceData?.companyInfo &&
                    invoiceData?.companyInfo.map((item: any, index: any) => {
                      return (
                        <Text key={`index-${index}`} style={styles.paragraph}>
                          {item}
                        </Text>
                      );
                    })}
                </View>
              </View>

              {/* Billed To */}
              <View style={[styles.row, { padding: "10 30 0" }]}>
                <View style={styles.col8}>
                  <Text style={styles.subtitle}>Reçu de:</Text>
                  <Text style={styles.paragraph}>{invoiceData?.ReceivedFrom?.company}</Text>
                  <Text style={styles.paragraph}>{invoiceData?.ReceivedFrom?.name}</Text>
                  {invoiceData?.ReceivedFrom?.address &&
                    invoiceData?.ReceivedFrom?.address.map((item: any, index: any) => {
                      return (
                        <Text key={`address-${index}`} style={styles.paragraph}>
                          {item}
                        </Text>
                      );
                    })}
                </View>
                <View style={styles.col2} />
              </View>

              {/* Content */}
              <View style={[styles.row]} fixed>
                <View style={[styles.col12, { padding: "10 30 0" }]}>
                  <DescriptionTable data={content} />
                </View>
              </View>

              {/* Banking Info, Notes, Total */}
              <View style={[styles.col12, { padding: "0 30 10" }]} fixed>
                {isLastIndex && (
                  <>
                    <View style={[styles.row]} fixed>
                      <View
                        style={[
                          styles.col12,
                          {
                            alignItems: "center",
                            borderTop: "1px solid #D8D8D8",
                            padding: "5px 0 0",
                          },
                        ]}
                      />
                    </View>
                    <View style={[styles.row, { flex: "1", padding: "5 0 0" }]} fixed>
                      <View style={[styles.col12, { padding: "0 12px 0 0" }]}></View>
                      <View style={[styles.col12, { gap: "6px" }]}>
                        {/* Sub-Total */}
                        {invoiceData?.subtotal && (
                          <View
                            key={`subtotal`}
                            style={[
                              styles.row,
                              { borderBottom: "1px solid #D8D8D8", padding: "0 0 4px" },
                            ]}
                          >
                            <Text style={[styles.subtitle, { flex: 1, textAlign: "right" }]}>
                              SOUS-TOTAL:
                            </Text>
                            <Text style={[styles.paragraph, { flex: 1, textAlign: "right" }]}>
                              {invoiceData?.subtotal as string}
                            </Text>
                          </View>
                        )}
                        {/* Rebate */}
                        {invoiceData?.rebate && (
                          <View
                            key={`rebate`}
                            style={[
                              styles.row,
                              { borderBottom: "1px solid #D8D8D8", padding: "0 0 4px" },
                            ]}
                          >
                            <Text style={[styles.subtitle, { flex: 1, textAlign: "right" }]}>
                              RABAIS:
                            </Text>
                            <Text style={[styles.paragraph, { flex: 1, textAlign: "right" }]}>
                              {invoiceData?.rebate as string}
                            </Text>
                          </View>
                        )}
                        {/* Taxes */}
                        {invoiceData?.taxes &&
                          invoiceData?.taxes.map((item: any, index: number) => {
                            return (
                              <View
                                key={`tax-${index}`}
                                style={[
                                  styles.row,
                                  { borderBottom: "1px solid #D8D8D8", padding: "0 0 4px" },
                                ]}
                              >
                                <Text style={[styles.subtitle, { flex: 1, textAlign: "right" }]}>
                                  {item.key}:
                                </Text>
                                <Text style={[styles.paragraph, { flex: 1, textAlign: "right" }]}>
                                  {item.value as string}
                                </Text>
                              </View>
                            );
                          })}
                        {/* Total */}
                        {invoiceData?.total &&
                          Object.entries(invoiceData?.total).map(([key, value]) => (
                            <View
                              key={`total-${key}`}
                              style={[
                                styles.row,
                                { borderBottom: "1px solid #D8D8D8", padding: "0 0 4px" },
                              ]}
                            >
                              <Text style={[styles.subtitle, { flex: 1, textAlign: "right" }]}>
                                {key}:
                              </Text>
                              <Text style={[styles.paragraph, { flex: 1, textAlign: "right" }]}>
                                {value as string}
                              </Text>
                            </View>
                          ))}
                      </View>
                    </View>
                  </>
                )}
              </View>

              {/* Footer */}
              <View
                style={[
                  styles.footer,
                  { display: "flex", justifyContent: "center", alignItems: "center" },
                ]}
                fixed
              >
                <Text
                  style={{ fontSize: "10px", color: "#FFFFFF" }}
                  render={({ pageNumber, totalPages }) => `${pageNumber} / ${totalPages}`}
                  fixed
                />
              </View>
            </Page>
          );
        })}
    </Document>
  );
};

/**
 * Invoice Preview Page.
 */
const PaymentReceiptComponent = ({ invoiceData }: any) => {
  return (
    <PreviewPDF height={938} width={684} showToolbar={false}>
      <ReceiptTemplate invoiceData={invoiceData} />
    </PreviewPDF>
  );
};

/** Styled download invoice button */
export const DownloadPaymentReceiptButton = async ({
  invoiceData,
  fileName = "document.pdf",
}: any) => {
  return (
    <PDFDownloadLink document={<ReceiptTemplate invoiceData={invoiceData} />} fileName={fileName}>
      {({ loading }) => (loading ? "Loading..." : "Download")}
    </PDFDownloadLink>
  );
};

export default PaymentReceiptComponent;
