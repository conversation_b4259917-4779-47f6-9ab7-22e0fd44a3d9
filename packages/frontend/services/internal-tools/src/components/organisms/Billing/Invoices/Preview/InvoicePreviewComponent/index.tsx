"use client";

import React, { useEffect, useState } from "react";
import dynamic from "next/dynamic";
import { Page, Text, View, Document, StyleSheet, Image } from "@react-pdf/renderer";
import { loadMessages } from "@talent-front-services/internal-tools/utils/translations";
import { countriesMapping } from "@talent-front-services/internal-tools/utils/geoUtils";

/** This needs to be imported Dynamically, otherwise it will not work */
const PDFDownloadLink = dynamic(() =>
  import("@react-pdf/renderer").then((mod) => mod.PDFDownloadLink),
);

const PDFViewer = dynamic(() => import("@react-pdf/renderer").then((mod) => mod.PDFViewer), {
  ssr: false,
});

// Document Settings
const settings = {
  title: "Invoice Preview",
  author: "Talent.com",
  subject: "Invoice",
  keywords: "Invoice",
  creator: "Talent.com",
  producer: "Talent.com",
  language: "en",
  // eslint-disable-next-line jsdoc/require-jsdoc
  onRender: () => { },
};

// Create styles
const styles = StyleSheet.create({
  page: {
    flexDirection: "column",
    backgroundColor: "white",
  },
  header: {
    margin: 0,
    padding: 0,
    flexGrow: 1,
    backgroundColor: "#691f74",
    maxHeight: "12mm",
  },
  row: {
    flexDirection: "row",
    margin: 0,
  },
  col12: {
    flexGrow: 1,
    display: "flex",
    flexDirection: "column",
    margin: 0,
    width: "100%",
  },
  col8: {
    flexGrow: 4,
    display: "flex",
    flexDirection: "column",
    justifyContent: "flex-start",
    margin: 0,
    maxWidth: 330,
    width: "100%",
  },
  col2: {
    flexGrow: 1,
    display: "flex",
    flexDirection: "column",
    justifyContent: "flex-start",
    alignItems: "flex-start",
    margin: 0,
    maxWidth: 200,
    width: "100%",
  },
  title: {
    margin: 0,
  },
  subtitle: {
    textAlign: "left",
    margin: 0,
    color: "#241037",
    fontSize: "10px",
    lineHeight: 1.5,
    fontWeight: 700,
  },
  paragraph: {
    textAlign: "left",
    margin: 0,
    color: "gray",
    fontSize: "9px",
    lineHeight: 1.5,
  },
  item: {
    display: "flex",
    flexDirection: "column",
    margin: 0,
    color: "gray",
    fontSize: "8px",
    lineHeight: 1.5,
    width: "100%",
  },
  footer: {
    margin: 0,
    padding: 0,
    flexGrow: 1,
    backgroundColor: "#691f74",
    maxHeight: "12mm",
  },
});

/** */
const translationsActivity = (
  activity: string,
  labels: { ppc: string; clicks: string; credit: string },
) => {
  const contentArray = activity.split(" ");

  const translations = contentArray.map((item: string) => {
    if (item === "ppc_campaign,") {
      return `${labels.ppc},`;
    }
    if (item === "billed_clicks") {
      return labels.clicks;
    }

    if (item === "credit") {
      return labels.credit;
    }

    return item;
  });

  return translations.join(" ").replace(/,,/g, ",");
};

/**
 * Helper function for Table definition
 */
const DescriptionTable = ({ data, labels }: any) => {
  // Purple Top and Bottom separator
  const PurpleSeparator: JSX.Element = (
    <View style={{ borderTop: "1px solid #241037", maxHeight: "1px", width: "100%" }} />
  );

  // Grey line separator
  const GraySeparator: JSX.Element = (
    <View style={{ borderTop: "1px solid #D8D8D8", maxHeight: "1px", width: "100%" }} />
  );

  return (
    <>
      <View
        style={{
          display: "flex",
          flexDirection: "row",
          alignItems: "center",
          height: "20px",
        }}
      >
        <Text style={[styles.item, { maxWidth: "320px", color: "#241037" }]}>
          {labels?.activity}
        </Text>
        <Text style={[styles.item, { maxWidth: "50px", textAlign: "center", color: "#241037" }]}>
          {labels?.qty}
        </Text>
        <Text style={[styles.item, { maxWidth: "80px", textAlign: "center", color: "#241037" }]}>
          {labels?.rate}
        </Text>
        <Text style={[styles.item, { maxWidth: "82px", textAlign: "right", color: "#241037" }]}>
          {labels?.amount}
        </Text>
      </View>
      {PurpleSeparator}
      <View style={{ width: "100%" }}>
        {data?.page?.map(({ activity, qty, rate, amount }: any, index: number) => {
          return (
            <View key={`line-${index}`}>
              {index > 0 && GraySeparator}
              <View
                style={{
                  display: "flex",
                  flexDirection: "row",
                  alignItems: "center",
                  height: "20px",
                }}
              >
                <Text style={[styles.item, { maxWidth: "330px" }]}>
                  {translationsActivity(activity, labels.activityLabels)}
                </Text>
                <Text style={[styles.item, { maxWidth: "40px", textAlign: "center" }]}>{qty}</Text>
                <Text style={[styles.item, { maxWidth: "80px", textAlign: "center" }]}>{rate}</Text>
                <Text style={[styles.item, { maxWidth: "82px", textAlign: "right" }]}>
                  {amount}
                </Text>
              </View>
            </View>
          );
        })}
      </View>
      {PurpleSeparator}
    </>
  );
};

/**
 * Invoice Preview Template
 */
export const InvoiceTemplateComponent = ({ invoiceData }: any) => {
  const [t, setTranslations] = useState<any>(null);

  // Load translations based on the invoice language
  const invoiceCountry =
    countriesMapping.find(
      (item: any) => item?.iso === invoiceData?.invoiceTo?.addressDetails?.country.toLowerCase(),
    )?.name ?? "";

  useEffect(() => {
    (async () => {
      const translations = await loadMessages(invoiceData?.language ?? "en");
      setTranslations(translations);
    })();
  }, []);

  const labelsDescriptionTable = {
    activity: t?.["employers_billing_invoices.description"],
    qty: t?.["employers_billing_invoices.quantity"],
    rate: t?.["employers_billing_invoices.rate"],
    amount: t?.["employers_billing_invoices.amount"],
    activityLabels: {
      ppc: t?.["employers_billing_invoices.ppc_campaign"],
      clicks: t?.["employers_billing_invoices.clicks"],
      credit: t?.["employers_billing_invoices.credit"],
    },
  };

  if (!invoiceData || !t) return <></>;

  let date = "";

  if (invoiceData?.date) {
    const [year, month, day] = invoiceData.date.split("-").map(Number);
    date = new Date(Date.UTC(year, month - 1, day)).toLocaleDateString("en-US", {
      timeZone: "UTC",
      year: "numeric",
      month: "numeric",
      day: "numeric",
    });
  }

  const isDELanguage = invoiceData?.language === "de";

  return (
    <Document {...settings} pageMode="fullScreen">
      {invoiceData.content &&
        invoiceData.content.map((content: any, index: number) => {
          const isLastIndex = index === invoiceData.content.length - 1;
          const isFirstIndex = index === 0;
          return (
            <Page size="A4" key={`page_${index}`} style={styles.page}>
              {/* Begin Header - Logo and Invoice Number */}
              <View style={styles.header} fixed />
              <View style={[styles.row, { padding: "30 30 0" }]} fixed>
                <View style={styles.col8}>
                  <Text style={{ color: "gray", fontSize: "10px" }}>
                    {t?.["employers_billing_invoices.preview"] ?? "PREVIEW"}
                  </Text>
                  <Text style={{ color: "#241037", fontSize: "20px" }}>
                    {t?.["employers_billing_invoices.invoice"] ?? "Facture"}
                  </Text>
                </View>
                <View style={[styles.col2, { padding: "10px 0" }]}>
                  {/* eslint-disable-next-line jsx-a11y/alt-text */}
                  <Image
                    src="https://cdn-static.talent.com/img/common/talent_logo_purple_220.png"
                    style={{ width: 120 }}
                  />
                </View>
              </View>
              {/* End Header - Logo and Invoice Number */}
              {/* Start Billed To data, dates, Invoice To */}
              {isFirstIndex && (
                <>
                  {/* Begin Date and Billed From */}
                  <View style={[styles.row, { padding: "20 30 0", minHeight: "100px" }]}>
                    {/* Date */}
                    <View style={styles.col8}>
                      <Text style={styles.subtitle}>
                        {t["employers_billing_invoices.invoice_date"]}
                      </Text>
                      <Text style={styles.paragraph}>{date}</Text>
                      <Text style={styles.paragraph}>
                        {`${t["employers_billing_invoices.payment_terms"]} ${t[`employers_billing_invoices.${invoiceData?.terms}`]}`}
                      </Text>
                      <Text style={styles.paragraph}>
                        {`${t["employers_billing_invoices.billing_period"]} ${invoiceData?.invoicePeriod}`}
                      </Text>
                      {/* PO Numbers */}
                      {invoiceData?.poNumbers && invoiceData?.poNumbers.length > 0 && (
                        <Text style={styles.subtitle}>PO #:</Text>
                      )}
                      {invoiceData?.poNumbers &&
                        invoiceData?.poNumbers.map((item: any, index: any) => {
                          const poNumberKey = Object.keys(item)[0];
                          return (
                            <Text key={`ponumber-${index}`} style={styles.paragraph}>
                              {item[poNumberKey]}
                            </Text>
                          );
                        })}
                    </View>
                    {/* Billed From */}
                    <View style={styles.col2}>
                      <Text style={styles.subtitle}>{invoiceData?.neuvooCompanyLabel}</Text>
                      {invoiceData?.neuvooInfo &&
                        invoiceData?.neuvooInfo.map((item: any, index: any) => {
                          return (
                            <Text key={`index-${index}`} style={styles.paragraph}>
                              {item}
                            </Text>
                          );
                        })}
                    </View>
                  </View>
                  {/* End Date and Billed From */}
                  {/* Begin Note Message */}
                  <View style={[styles.row, { padding: "10 30 0" }]}>
                    <View style={styles.col8}>
                      <Text style={styles.subtitle}>
                        {t["employers_billing_invoices.clients_have_30"]}
                      </Text>
                    </View>
                    <View style={styles.col2} />
                  </View>
                  {/* End Note Message */}
                  {/* Begin Billed To */}
                  <View style={[styles.row, { padding: "10 30 0" }]}>
                    <View style={styles.col8}>
                      <Text style={styles.subtitle}>
                        {t["employers_billing_invoices.invoice_to"]}
                      </Text>
                      <Text style={styles.paragraph}>{invoiceData?.invoiceTo?.company}</Text>
                      <Text style={styles.paragraph}>
                        {invoiceData?.invoiceTo?.addressDetails?.address}
                      </Text>
                      <Text style={styles.paragraph}>
                        {invoiceData?.invoiceTo?.addressDetails?.city}
                      </Text>
                      <Text style={styles.paragraph}>
                        {invoiceData?.invoiceTo?.addressDetails?.region}
                      </Text>
                      <Text style={styles.paragraph}>
                        {invoiceData?.invoiceTo?.addressDetails?.postalCode}
                      </Text>
                      <Text style={styles.paragraph}>{invoiceCountry}</Text>
                    </View>
                    {/* Dummy Column */}
                    <View style={styles.col2} />
                  </View>
                  {/* End Billed To */}
                </>
              )}
              {/* End Billed To data, dates, Invoice To */}
              {/* Begin Rendering Content */}
              <View style={[styles.col12]} fixed>
                <View style={[styles.col12, { padding: "10 30 0" }]}>
                  <DescriptionTable data={content} labels={labelsDescriptionTable} />
                </View>
              </View>
              {/* End Rendering Content */}
              {/* Begin total  */}
              {/* Banking Info, Notes, Total */}
              <View>
                <View style={[styles.col12, { padding: "0 30 8" }]} fixed>
                  {isLastIndex && (
                    <>
                      {/* Separator */}
                      <View style={[styles.row]}>
                        <View
                          style={[
                            styles.col12,
                            {
                              alignItems: "center",
                              borderTop: "1px solid #D8D8D8",
                              padding: "5px 0 0",
                            },
                          ]}
                        />
                      </View>
                      <View style={[styles.row]}>
                        {/* (Left Column) */}
                        {/* Banking Info */}
                        <View style={[styles.col12, { padding: "0 12px 0 0", gap: 10 }]}>
                          <View style={[styles.col12]}>
                            <View style={[styles.col12, { gap: 4 }]}>
                              {invoiceData?.noteToClient && (
                                <Text style={[styles.paragraph]}>{invoiceData?.noteToClient}</Text>
                              )}
                              <Text style={styles.subtitle}>
                                {`${t?.["employers_billing_invoices.please_include_the"]} <EMAIL>`}
                              </Text>
                              <View
                                style={{
                                  display: "flex",
                                  flexDirection: "row",
                                  alignItems: "center",
                                  gap: 6,
                                }}
                              >
                                {/* eslint-disable-next-line jsx-a11y/alt-text */}
                                <Image
                                  src="https://cdn-static.talent.com/img/tax-calculator/input__salary-icon.png"
                                  style={{ width: "14px", height: "14px" }}
                                />
                                <Text style={[styles.subtitle, { padding: "3px 0 0" }]}>
                                  {t?.["employers_billing_invoices.click_here_to"]}
                                </Text>
                              </View>
                            </View>
                          </View>
                          <View style={[styles.col12]}>
                            <View style={[styles.col12]}>
                              {invoiceData.neuvooBanking &&
                                invoiceData.neuvooBanking.map((item: any, index: any) => {
                                  return (
                                    <Text key={`banking-${index}`} style={styles.paragraph}>
                                      {item}
                                    </Text>
                                  );
                                })}
                            </View>
                            {isDELanguage && (
                              <View style={[styles.col12]}>
                                <Text style={[styles.subtitle, { padding: "3px 0 0" }]}>
                                  Steuerschuldnerschaft des leistungsempfängers
                                </Text>
                              </View>
                            )}
                          </View>
                        </View>
                        {/* (Right Column) */}
                        <View style={[styles.col12, { gap: "6px" }]}>
                          {/* Sub Total */}
                          {invoiceData?.subtotal && (
                            <View
                              style={[
                                styles.row,
                                { borderBottom: "1px solid #D8D8D8", padding: "0 0 4px" },
                              ]}
                            >
                              <Text style={[styles.subtitle, { flex: 1, textAlign: "right" }]}>
                                {t?.["employers_billing_invoices.subtotal"]}
                              </Text>
                              <Text style={[styles.paragraph, { flex: 1, textAlign: "right" }]}>
                                {invoiceData?.subtotal}
                              </Text>
                            </View>
                          )}
                          {/* Rebate */}
                          {invoiceData?.rebate &&
                            Object.entries(invoiceData?.rebate).map(([key, value]) => (
                              <View
                                key={`rebate-${key}`}
                                style={[
                                  styles.row,
                                  { borderBottom: "1px solid #D8D8D8", padding: "0 0 4px" },
                                ]}
                              >
                                <Text style={[styles.subtitle, { flex: 1, textAlign: "right" }]}>
                                  {t?.["employers_billing_invoices.DISCOUNT"]} ({key}):
                                </Text>
                                <Text style={[styles.paragraph, { flex: 1, textAlign: "right" }]}>
                                  {value as string}
                                </Text>
                              </View>
                            ))}
                          {/* HT */}
                          {invoiceData?.ht && (
                            <View
                              style={[
                                styles.row,
                                { borderBottom: "1px solid #D8D8D8", padding: "0 0 4px" },
                              ]}
                            >
                              <Text style={[styles.subtitle, { flex: 1, textAlign: "right" }]}>
                                HT:
                              </Text>
                              <Text style={[styles.paragraph, { flex: 1, textAlign: "right" }]}>
                                {invoiceData?.ht}
                              </Text>
                            </View>
                          )}
                          {/* Taxes */}
                          {invoiceData?.taxes &&
                            invoiceData?.taxes.map((item: any, index: any) => {
                              return (
                                <View
                                  key={`taxes-${index}`}
                                  style={[
                                    styles.row,
                                    { borderBottom: "1px solid #D8D8D8", padding: "0 0 4px" },
                                  ]}
                                >
                                  <Text style={[styles.subtitle, { flex: 1, textAlign: "right" }]}>
                                    {item?.key}:
                                  </Text>
                                  <Text style={[styles.paragraph, { flex: 1, textAlign: "right" }]}>
                                    {item?.value as string}
                                  </Text>
                                </View>
                              );
                            })}
                          {/* Total */}
                          {invoiceData?.total &&
                            Object.entries(invoiceData?.total).map(([key, value]) => (
                              <View
                                key={`total-${key}`}
                                style={[
                                  styles.row,
                                  { borderBottom: "1px solid #D8D8D8", padding: "0 0 4px" },
                                ]}
                              >
                                <Text style={[styles.subtitle, { flex: 1, textAlign: "right" }]}>
                                  {t?.["employers_billing_invoices.total"]}
                                </Text>
                                <Text style={[styles.paragraph, { flex: 1, textAlign: "right" }]}>
                                  {value as string}
                                </Text>
                              </View>
                            ))}
                          {/* Amount Received */}
                          {invoiceData?.amountReceived && (
                            <View
                              style={[
                                styles.row,
                                { borderBottom: "1px solid #D8D8D8", padding: "0 0 4px" },
                              ]}
                            >
                              <Text style={[styles.subtitle, { flex: 1, textAlign: "right" }]}>
                                {t?.["employers_billing_invoices.amount_received"]}:
                              </Text>
                              <Text style={[styles.paragraph, { flex: 1, textAlign: "right" }]}>
                                {invoiceData?.amountReceived}
                              </Text>
                            </View>
                          )}
                          {/* Balance due */}
                          {invoiceData?.balanceDue && (
                            <View
                              style={[
                                styles.row,
                                { borderBottom: "1px solid #D8D8D8", padding: "0 0 4px" },
                              ]}
                            >
                              <Text style={[styles.subtitle, { flex: 1, textAlign: "right" }]}>
                                {t?.["employers_billing_invoices.balance_due"]}
                              </Text>
                              <Text style={[styles.paragraph, { flex: 1, textAlign: "right" }]}>
                                {invoiceData?.balanceDue}
                              </Text>
                            </View>
                          )}
                        </View>
                      </View>
                    </>
                  )}
                </View>
              </View>
              {/* Disclaimer */}
              {invoiceData?.legal && invoiceData?.legal?.length > 0 && (
                <View style={[styles.row, { padding: "0 30 0" }]} fixed>
                  <View
                    style={[
                      styles.col12,
                      { alignItems: "center", borderTop: "1px solid #D8D8D8", padding: "5px 0 0" },
                    ]}
                  >
                    {invoiceData?.legal.map((item: any, index: number) => {
                      return (
                        <Text
                          key={`legal-${index}`}
                          style={[styles.paragraph, { fontSize: "8px" }]}
                        >
                          {item}
                        </Text>
                      );
                    })}
                  </View>
                </View>
              )}
              {/* Footer */}
              <View
                style={[
                  styles.footer,
                  { display: "flex", justifyContent: "center", alignItems: "center" },
                ]}
                fixed
              >
                <Text
                  style={{ fontSize: "10px", color: "#FFFFFF" }}
                  render={({ pageNumber, totalPages }) => `${pageNumber} / ${totalPages}`}
                  fixed
                />
              </View>
            </Page>
          );
        })}
    </Document>
  );
};

/**
 * Invoice Preview Page.
 */
const InvoicePreviewComponent = ({ invoiceData }: any) => {
  return (
    <PDFViewer height={938} width={684} showToolbar={false}>
      <InvoiceTemplateComponent invoiceData={invoiceData} />
    </PDFViewer>
  );
};

/** Unstyled download invoice button */
export const DownloadInvoiceButton = async ({ invoiceData, fileName = "document.pdf" }: any) => {
  return (
    <PDFDownloadLink
      document={<InvoiceTemplateComponent invoiceData={invoiceData} />}
      fileName={fileName}
    >
      {({ loading }): any => {
        return <span>{loading ? "Loading..." : "Download"}</span>;
      }}
    </PDFDownloadLink>
  );
};

export default InvoicePreviewComponent;
