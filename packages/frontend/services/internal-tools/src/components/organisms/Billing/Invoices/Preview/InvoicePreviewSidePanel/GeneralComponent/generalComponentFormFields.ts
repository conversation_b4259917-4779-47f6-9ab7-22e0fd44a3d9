import { RenderFieldsFormdProps } from "@talent-front-libs/ui/components/organisms/RenderFields/interfaces";

const defaultFormFieldsTop: RenderFieldsFormdProps[] = [
  {
    type: "input",
    key: "billingPoNumber1",
    label: "Billing PO Number 1",
    inputType: "text",
    placeholder: "ex: 3340003456",
    required: false,
  },
  {
    type: "input",
    key: "billingPoNumber2",
    label: "Billing PO Number 2",
    inputType: "text",
    placeholder: "ex: 3340003456",
    required: false,
  },
  {
    type: "input",
    key: "billingPoNumber3",
    label: "Billing PO Number 3",
    inputType: "text",
    placeholder: "ex: 3340003456",
    required: false,
  },
  {
    type: "emptySpace",
    key: "emptySpace-1",
    label: "",
    inputType: "text",
    placeholder: "",
    required: false,
  },
];

const defaultFormFieldsBottom: RenderFieldsFormdProps[] = [
  {
    type: "input",
    key: "billingDiscount",
    label: "Billing Discount",
    inputType: "number",
    placeholder: "0",
    min: "0",
    step: ".01",
    required: false,
    description: "Discount that will be made to the invoice",
  },
  {
    type: "textarea",
    key: "billingNoteClient",
    label: "Note to client",
    inputType: "textarea",
    placeholder: "Message here",
    required: false,
    register: true,
  },
];

/**
 * Gets the overwrite fields for each line item in the invoice data.
 */
const getOverWriteFields = (
  invoiceData: any,
  canEditInvoice: boolean,
): RenderFieldsFormdProps[] => {
  const fields = invoiceData?.dataFront?.Line || [];

  // Group items by their key (e.g., "afterquery*1", "afterquery*2")
  const groupedItems: Record<string, any[]> = {};

  // Fields iteration
  fields.forEach((item: any) => {
    const key = Object.keys(item)[0];
    if (!groupedItems[key]) {
      groupedItems[key] = [];
    }
    groupedItems[key].push(item[key]);
  });

  // Group process
  return Object.entries(groupedItems).flatMap(([key, items], index) => {
    const mainLine = items.find((item) => item.DetailType === "SalesItemLineDetail");

    if (!mainLine) return [];

    const resultFields: RenderFieldsFormdProps[] = [
      {
        type: "input",
        key: `${key}-overwrite-${index}`,
        label: "Description Overwrite",
        inputType: "text",
        placeholder: "Overwrite Doc Description",
        required: false,
        description: `Description Overwrite of the FeedCode ${key}`,
        value:
          mainLine?.descriptionOverwrite ??
          invoiceData?.dataFront?.infoBilled[index]?.description ??
          "",
      },
    ];

    if (canEditInvoice) {
      resultFields.push({
        type: "input",
        key: `${key}-spent-${index}`,
        label: "Spent",
        inputType: "text",
        placeholder: "ex: 000.00",
        required: false,
        description: `Spent of the FeedCode ${key}`,
        value:
          invoiceData?.dataFront?.infoBilled[index]?.mBilled ||
          invoiceData?.dataFront?.infoBilled[index]?.fBilled ||
          "",
      });
    }

    return resultFields;
  });
};

/**
 * Combines default form fields with overwrite fields based on invoice data.
 */
export const getFormFields = (invoiceData: any, canEditInvoice: any): RenderFieldsFormdProps[] => {
  const overwriteFormFields = getOverWriteFields(invoiceData, canEditInvoice);

  const filteredDefaultFieldsBottom = defaultFormFieldsBottom.filter((item: any) =>
    !canEditInvoice ? item.key !== "billingDiscount" : item,
  );

  // Combine the fields in the desired order
  return [...defaultFormFieldsTop, ...overwriteFormFields, ...filteredDefaultFieldsBottom];
};
