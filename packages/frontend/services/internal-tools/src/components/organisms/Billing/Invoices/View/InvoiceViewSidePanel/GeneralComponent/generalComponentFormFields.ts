import { RenderFieldsFormdProps } from "@talent-front-libs/ui/components/organisms/RenderFields/interfaces";

const defaultFormFields: RenderFieldsFormdProps[] = [
  {
    type: "dropdown",
    key: "block",
    label: "Lock Invoice",
    required: true,
    options: [
      { label: "No", value: "no" },
      { label: "Yes", value: "yes" },
    ],
  },
  {
    type: "input",
    key: "docNumber",
    label: "Doc Number",
    inputType: "text",
    placeholder: "0",
    required: false,
  },
  {
    type: "input",
    key: "billingPeriod",
    label: "Billing Period",
    inputType: "text",
    placeholder: "2025-01",
    required: false,
  },
  {
    type: "input",
    key: "txnId",
    label: "TxnID",
    inputType: "text",
    placeholder: "0",
    required: false,
  },
  {
    type: "textarea",
    key: "note",
    label: "Note to client",
    inputType: "textarea",
    placeholder: "Message here",
    required: false,
    register: true,
  },
];

/**
 * Gets the overwrite fields for each line item in the invoice data.
 */
const getOverWriteFields = (invoiceData: any) => {
  // Invoice overwrite fields
  const fields = invoiceData?.lines || [];
  return fields.flatMap((item: any, index: any) => {
    return [
      {
        type: "input",
        key: `${item.fieldName}-overwrite-${index}`,
        label: "Description Overwrite",
        inputType: "text",
        placeholder: `${item.value}`,
        required: false,
        description: `Description overwrite ${item.description}`,
      },
    ];
  });
};

/**
 * Combines default form fields with overwrite fields based on invoice data.
 */
export const getFormFields = (invoiceData: any): RenderFieldsFormdProps[] => {
  const overwriteFormFields = getOverWriteFields(invoiceData);

  // Combine the fields in the desired order
  return [...overwriteFormFields, ...defaultFormFields];
};
