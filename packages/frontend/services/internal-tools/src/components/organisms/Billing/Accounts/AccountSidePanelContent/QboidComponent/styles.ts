import { BodyXsMd } from "@talent-front-libs/ui/theme/typography";
import styled from "styled-components";

export const Wrapper = styled.div`
  display: flex;
  padding: 0 0 50px;
  height: calc(100% - 16px);
  flex-direction: column;
  overflow: hidden;
`;

export const Content = styled.div`
  display: grid;
  grid-template-columns: minmax(100px, 1fr);
  gap: 10px;
  padding: 0 0 24px 0;
`;

export const FieldHolder = styled.div`
  display: flex;
  flex-direction: column;
  gap: 5px;
  margin-bottom: 10px;
`;

export const Info = styled(BodyXsMd)`
  padding: 1px 24px;
`;
