import { InputPrimaryContainer, StyledInputPrimary } from "@talent-front-libs/ui/components/atoms/Input/variants/InputPrimary/styles";
import { DropdownSelectInputStyle } from "@talent-front-libs/ui/components/molecules/Dropdown/variants/DropdownSelectInput/styles";
import { BodySMd, BodyXxsRg } from "@talent-front-libs/ui/theme/typography";
import styled from "styled-components";


export const StyledInputWrapper = styled.div`
  display: flex;
  flex-direction: column;
  gap: 10px;
`;

export const SupportText = styled(BodyXxsRg)`
  padding: 1px 24px;
`;

export const SubmitButtonWrapper = styled.div`
  display: flex;
  flex: 1;
  width: 100%;
  grid-column: 1 / -1;
  justify-content: flex-end;
  height: 200px;
`;
