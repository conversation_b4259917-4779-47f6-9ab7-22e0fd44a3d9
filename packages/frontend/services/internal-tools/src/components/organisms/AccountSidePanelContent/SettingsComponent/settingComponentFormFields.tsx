import { RenderFieldsFormdProps } from "@talent-front-libs/ui/components/organisms/RenderFields/interfaces";

export const analyticsFields: RenderFieldsFormdProps[] = [
  {
    key: "performanceOverview",
    type: "searchableDropdown",
    label: "Performance Overview",
    placeholder: "Performance Overview",
    required: false,
  },
  {
    key: "showCPCInfo",
    type: "toggle",
    label: "Show CPC info",
    value: false,
    tooltipInfo:
      "Show CPC information in the Employer Dashboard (Campaign CPC and Avg. CPC columns).",
  },
];

export const accountManagerFields: RenderFieldsFormdProps[] = [
  {
    key: "clientCanPostJobs",
    type: "text",
    content: "Client can post jobs without a feed",
    label: "Client can post jobs without a feed",
  },
  {
    key: "accountType",
    type: "dropdown",
    label: "Account type",
    options: [
      { label: "Enterpost", value: "enterpost" },
      { label: "Enterprise", value: "enterprise" },
    ],
    required: true,
  },
  {
    key: "manualJobsRequireQA",
    type: "toggle",
    label: "Manual jobs require QA",
    value: true,
    tooltipInfo:
      "If manual jobs are active on an account (Enterpost), the need for QA by the CS assigned to the account can be disabled.",
  },
];

export const campaignManagementFields: RenderFieldsFormdProps[] = [
  {
    key: "activateBudgetCPCCommitment",
    type: "toggle",
    label: "Activate 24h Budget & CPC Commitment - BETA",
    value: false,
    tooltipInfo:
      "Take the CPC & Budget sent to our API & XML publishers into consideration for 24h, even if the client changes them.",
  },
  {
    key: "activateFrontLoadedBudget",
    type: "toggle",
    label: "Activate Front Loaded Budget",
    value: true,
    tooltipInfo:
      "Automatically adjust daily allowance of monthly paced campaigns to take advantage of the natural traffic pattern of Talent.com, i.e. increase daily allowance when we have most of our traffic, and inversely.",
  },
  {
    key: "endOfMonthExposureManager",
    type: "toggle",
    label: "End Of Month Exposure Manager - BETA",
    value: false,
    tooltipInfo:
      "Adjust automatically all the campaign exposure at the end of the month to avoid CADC because of monthly budget reach.",
  },
  {
    key: "spikeManager",
    type: "toggle",
    label: "Spike Manager",
    value: true,
    tooltipInfo: "Algorithm that will automatically detect and stop spikes of traffic",
  },
  {
    key: "smartBidding",
    type: "toggle",
    label: "Smart Bidding - BETA",
    value: false,
    tooltipInfo:
      "Algorithm that automatically adjusts bids according to traffic performance and CPA target",
  },
  {
    key: "sendEmailBudgetReached",
    type: "toggle",
    label: "Send email budget reached",
    value: false,
    tooltipInfo:
      "Emails will be sent to admin clients when their accelerated campaign budgets are fully spent. If there are two or less business days remaining in the month, then an email will not be sent.",
  },
  {
    key: "autoCampaignAPI",
    type: "searchableDropdown",
    label: "Auto Campaign API",
    placeholder: "Auto Campaign API",
    required: false,
  },
  {
    key: "activateAutoCampaignAPI",
    type: "toggle",
    label: "Activate Auto Campaign profitability measures",
    value: false,
    tooltipInfo:
      "XML Partner traffic will be shut down when the days left in the campaign is lower than 1, and activated again when the campaign is reactivated.",
  },

  {
    key: "createCampaignsAutomatically",
    type: "toggle",
    label: "Create Campaigns Automatically",
    value: false,
    tooltipInfo:
      "By turning this toggle on, you’re accepting that campaigns will be created automatically without validation needed. This will only work if there’s a CPC coming from the client’s feed or the API.",
  },
];

export const trackingFields: RenderFieldsFormdProps[] = [
  {
    key: "reliableConversionsTag",
    type: "toggle",
    label: "Reliable conversions Tag",
    value: true,
    tooltipInfo:
      "Indicates that the conversion signals we are receiving from the client (postback or pixel) are aligned with what the client sees in their back-end.",
  },
  {
    key: "conversionDataExternalDashboard",
    type: "toggle",
    label: "Conversion Data taken from External Dashboard",
    value: false,
    tooltipInfo:
      "Identifies a campaign receiving conversion data from a clients external dashboard. As a result, this campaign is not added to Talent’s pricing features that manage performance based on conversion data.",
  },
  {
    key: "addBilledPPC",
    type: "toggle",
    label: "Add billed PPC to the click URL",
    value: false,
    tooltipInfo:
      "This delivers the billed CPC information on the click URL for the client to know exactly how much was billed for that click.",
  },
  {
    type: "input",
    key: "URLName",
    label: "URL Name",
    inputType: "text",
    placeholder: "URLName",
  },
  {
    type: "radioGroup",
    key: "PPCFormat",
    label: "PPC Format",
    options: [
      { value: "cents", label: "Cents (e.g. $1.00=100)" },
      { value: "dividedBy100", label: "Divided by 100 (e.g. $1.00 = 1.00)" },
    ],
  },
];

export const campaignExposureFields: RenderFieldsFormdProps[] = [
  {
    key: "campaignExposureManager",
    type: "toggle",
    label: "Campaign Exposure Manager 2.0 - BETA",
    value: true,
    tooltipInfo:
      "Automatically manages sources exposures to maximize conversions, spend & minimize CADC",
  },
  {
    key: "campaignConversionTargetModifier",
    type: "input",
    inputType: "number",
    step: ".01",
    label: "Campaign Conversion Target Modifier",
  },
  {
    key: "modifierValueExplanation",
    type: "text",
    label: "Modifier Value Explanation",
    content:
      "The modifier must have a value between 0.01 and 2 (e.g., 0.50). If the value is set to 1, the modifier won’t affect this campaign. If the value is set to 0.50 and the CPA is 10, then the new CPA will be 5.",
  },
];

export const sections = [
  { key: "Analytics", title: "Analytics", fields: analyticsFields },
  { key: "AccountManager", title: "Account Manager", fields: accountManagerFields },
  { key: "CampaignManagement", title: "Campaign Management", fields: campaignManagementFields },
  { key: "Tracking", title: "Tracking", fields: trackingFields },
  { key: "CampaignExposure", title: "Campaign Exposure Manager", fields: campaignExposureFields },
];
