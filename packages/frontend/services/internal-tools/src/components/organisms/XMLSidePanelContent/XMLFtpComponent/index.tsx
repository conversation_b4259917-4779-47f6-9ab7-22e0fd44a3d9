import { ButtonPrimary } from "@talent-front-libs/ui/components/atoms/Button/variants/ButtonPrimary";
import { useEffect, useState } from "react";
import { useForm, FormProvider } from "react-hook-form";
import { formConfiguration } from "./formConfiguration";
import { Wrapper, Content } from "../styles";
import { RenderFields } from "@talent-front-libs/ui/components/organisms/RenderFields";
import { updateProduct } from "@talent-front-services/internal-tools/modules/network/publishers/accounts";
import { NotificationProps } from "@talent-front-libs/ui/components/atoms/Notification/interfaces";
import Notification from "@talent-front-libs/ui/components/atoms/Notification";
import { Product, XMLProduct, FeedVariable } from "@talent-front-services/internal-tools/components/templates/Partners/Products/interfaces";

const XMLFtpComponent = ({ 
  xmlFeedData, 
  productData,
  isCreating 
}: { 
  xmlFeedData: XMLProduct | undefined, 
  productData: Product | undefined,
  isCreating: boolean 
}): JSX.Element => {
  const [notificationData, setNotificationData] = useState<NotificationProps>({
    message: "",
    type: "success",
  });
  const getInitialValues = () => {
    if (isCreating) {
      return {
        ftp_enable: "0", // Default disabled
        ftp_host: "",
        ftp_user: "",
        ftp_password: "",
        ftp_port: "21", // Default FTP port
        ftp_method: "FTP", // Default method
      };
    }
    
    return {
      ftp_enable: xmlFeedData?.ftp?.ftp_enable ?? "",
      ftp_host: xmlFeedData?.ftp?.ftp_host ?? "",
      ftp_user: xmlFeedData?.ftp?.ftp_user ?? "",
      ftp_password: xmlFeedData?.ftp?.ftp_password ?? "",
      ftp_port: xmlFeedData?.ftp?.ftp_port ?? "",
      ftp_method: xmlFeedData?.ftp?.ftp_method ?? "",
    };
  };
  
  const initialValues = getInitialValues();
  const methods = useForm({
    mode: "all",
    defaultValues: initialValues,
  });

  const { reset, handleSubmit, getValues } = methods;

  useEffect(() => {
    reset(initialValues);
  }, [xmlFeedData, reset]);

  /**
   * Handles form submission.
   *
   * @param {any} data - The form data.
   */
  const onSubmit = async () => {
    const formData = {
      ...getValues(),
    };
  
    const config = {
      ...xmlFeedData,
      ftp: formData
    }
    const updatedData = {
      config: config,
      product: productData,
    };
    
    let response: any;
    try {
      if (xmlFeedData?.id) {
        response = await updateProduct("xml", xmlFeedData.id, updatedData);
        if (response.code === 204) {
          setNotificationData({
            message: "Rule updated successfully",
            type: "success",
          });
        } else {
          setNotificationData({ message: `Error saving rule : ${response.error}`, type: "error" });
        }
      }
    } catch (error) {
    }
  };

  return (
    <FormProvider {...methods}>
      <Wrapper>
        <Content>
          {formConfiguration.map((field) => RenderFields(field))}
          <div>
            <ButtonPrimary type="button" label="Submit" onClick={handleSubmit(onSubmit)} />
          </div>
        </Content>
        {notificationData.message && (
          <Notification
            key={notificationData.message}
            message={notificationData.message}
            type={notificationData.type}
            autoDismiss={false}
          />
        )}
      </Wrapper>
    </FormProvider>
  );
};

export default XMLFtpComponent;
