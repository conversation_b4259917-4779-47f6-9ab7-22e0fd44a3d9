/**
 * Download Invoice Credit Records - Talent.com
 * Used to download credit records attached to invoices
 */

import { NextResponse, type NextRequest } from "next/server";
import { decrypt } from "libs/privacy/src/lib/cipher";
import { auth } from "@talent-front-services/internal-tools/modules/auth";
import { signPayload } from "@talent-front-libs/ui/modules/network/signPayload";

export const dynamic = "force-dynamic";

const mimeTypeMap: Record<string, string> = {
  pdf: "application/pdf",
  docx: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
  doc: "application/msword",
  xls: "application/vnd.ms-excel",
  xlsx: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  png: "image/png",
  jpg: "image/jpeg",
  jpeg: "image/jpeg",
};

/**
 * GET Handler
 * @param { params } params Slug from URL
 * @returns { NextRequest } NextRequest Response
 */
export const GET = async (request: NextRequest, { params }: { params: { token: string } }) => {
  try {
    // Validate authentication
    const session = await auth();
    if (!session?.user?.user_id) throw new Error("No Session Available");

    // Get the token to validate the request
    const token = params?.token;
    if (!token) throw new Error("Invalid Token");

    // Check the validity of the token if not throw the exception
    const base64Regex = /^[A-Za-z0-9+/=]*$/;
    const isBase64Valid = base64Regex.test(token) && token.length % 4 === 0;
    if (!isBase64Valid) throw new Error("Invalid Token");

    // Decode the base64 string, handling URL-safe encoding
    const decodedPayload = Buffer.from(token, "base64").toString("utf-8");
    if (!decodedPayload) throw new Error("Invalid Request 2");

    // Check the validity of the decoded payload
    const tokenFormat = /^[a-f0-9]{32}:[a-f0-9]+:[a-f0-9]{32}$/;
    const isEncryptionValid = tokenFormat.test(decodedPayload);
    if (!isEncryptionValid) throw new Error("Invalid Request");

    // Check the validity of the encryption if not throw the exception
    const decryptedToken = decrypt(decodedPayload);
    if (!decryptedToken) throw new Error("Invalid Request");

    const url = `${process.env.NEXT_PUBLIC_TALENT_EMPLOYERS}/v1/credit-records/download?file=${token}`;

    // Set headers
    const signedPayload = await signPayload({
      user_id: session?.user?.user_id,
    });

    const headers = new Headers({
      Host: process.env.ENVIRONMENT === "dev" ? "ca.dev.talent.com" : "ca.talent.com",
      Authorization: signedPayload,
    });

    // Get the PDF data
    const result = await fetch(url, {
      method: "GET",
      headers: headers,
      redirect: "follow",
      referrerPolicy: "no-referrer",
      cache: "no-store",
    });

    const docResponse = await result.json();

    // If there's no PDF string, redirect
    if (![200, 201, 204].includes(docResponse?.statusCode)) throw new Error("Invalid Invoice");

    // Check validity of string
    const base64Pdf = docResponse?.payload;
    const fileName = base64Pdf?.fileName;
    const fileExt = fileName?.split(".")[1];
    const isValidPdf = base64Regex.test(base64Pdf?.body) && token?.length % 4 === 0;
    if (!isValidPdf) throw new Error("Invalid Invoice");

    // Decode the base64 string into a Buffer
    const bufferedPdf = Buffer.from(base64Pdf?.body, "base64");

    // MIME Types
    const mimeType = mimeTypeMap[fileExt] || "application/octet-stream";

    // Return the PDF with headers
    return new NextResponse(bufferedPdf, {
      status: 200,
      headers: {
        "Content-Type": mimeType,
        "Content-Disposition": `attachement; filename="${fileName}"`,
        "Content-Length": String(bufferedPdf.length),
        "X-Robots-Tag": "noindex, nofollow",
        "Cache-Control": "no-store, no-cache, must-revalidate, proxy-revalidate",
        Pragma: "no-cache",
        Expires: "0",
      },
    });
  } catch (error: any) {
    // If an error occurs, redirect to talent.com
    return NextResponse.redirect("https://ca.talent.com/sas2/billing/invoices", 302);
  }
};
