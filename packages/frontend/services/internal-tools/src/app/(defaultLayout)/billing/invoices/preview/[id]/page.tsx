import { Metadata } from "next";
import { Suspense } from "react";
import { redirect } from "next/navigation";
import InvoicePreviewPage from "@talent-front-services/internal-tools/components/templates/Billing/InvoicePreviewPage";
import { getPreviewByID } from "@talent-front-services/internal-tools/modules/network/billing/invoices";
import { transformInvoiceData } from "../../utility";

export const metadata: Metadata = {
  title: "Preview | Talent.com",
};

/**
 * Invoice Preview View
 * @returns
 */
export default async function PreviewInvoices({
  params,
  searchParams,
}: {
  params: { [key: string]: string | undefined };
  searchParams?: { [key: string]: string | string[] | undefined };
}) {
  // Case where there's no ID
  if (!params.id || !searchParams?.accountId) redirect("/billing/accounts/");

  // Check for reissues
  let id = params.id;
  if (searchParams.reissue) {
    id = atob(searchParams?.reissue as string);
    id = encodeURIComponent(id);
  }

  // Get the Invoice Data
  const invoiceData = await getPreviewByID(id as string, searchParams?.accountId as string);

  // If there's no preview, do not render the view
  if (invoiceData.statusCode !== 200) redirect("/billing/accounts/");

  const data = transformInvoiceData(invoiceData.payload);

  return (
    <Suspense fallback={<>Loading ...</>}>
      <InvoicePreviewPage invoiceDetails={data} />
    </Suspense>
  );
}
