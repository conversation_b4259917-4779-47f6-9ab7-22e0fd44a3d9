import { Metadata } from "next";
import { Suspense } from "react";
import { redirect } from "next/navigation";
import * as privacy from "libs/privacy/src";
import InvoiceViewPage from "@talent-front-services/internal-tools/components/templates/Billing/InvoiceViewPage";
import {
  getInvoiceByID,
  getEditInvoiceSidePanelByID,
} from "@talent-front-services/internal-tools/modules/network/billing/invoices";

export const metadata: Metadata = {
  title: "View | Talent.com",
};

/**
 * Transform the Invoice Data response
 * Decrypt values and Format them here
 */
const transformInvoiceData = (invoiceData: any) => {
  try {
    if (invoiceData?.dataFront?.emailToSend) {
      invoiceData.dataFront.emailToSend = invoiceData?.dataFront?.emailToSend
        ? privacy.cipher.decrypt(invoiceData.dataFront.emailToSend)
        : "";
    }
  } catch (error) {
    console.log();
  }

  // Re-map, Add or remove data keys as needed
  const data = {
    ...invoiceData,
  };

  return data;
};

/**
 * @returns
 */
export default async function ViewInvoices({
  params,
  searchParams,
}: {
  params: { [key: string]: string | string[] | undefined };
  searchParams?: { [key: string]: string | string[] | undefined };
}) {
  // Case where there's no ID
  if (!params.id) redirect("/billing/invoices/");
  // Get the Invoice Data
  const invoiceData = await getInvoiceByID(params.id as string, searchParams?.accountId as string);

  let sidePanelData: any = {};
  if (params.id) {
    sidePanelData = await getEditInvoiceSidePanelByID(
      params.id as string,
      searchParams?.accountId as string,
    );

    if (sidePanelData.statusCode === 200) {
      sidePanelData = sidePanelData?.payload;
      sidePanelData.invoiceId = params.id;
      sidePanelData.accountId = searchParams?.accountId;
    }
  }

  // If there's no invoice, do not render the view
  if (invoiceData.statusCode !== 200) redirect("/billing/invoices/");

  const data = transformInvoiceData(invoiceData?.payload);

  return (
    <Suspense fallback={<>CHECKING ...</>}>
      <InvoiceViewPage invoiceDetails={data} sidePanelData={sidePanelData} />
    </Suspense>
  );
}
