import { validatePermission } from "@talent-front-services/internal-tools/utils/Rbac";
import { useSession } from "next-auth/react";
import { useMemo } from "react";

/**
 * FUNCTION NOT NEEDED NOW ON THIS VERSION OF THE SERVICE
 * Custom hook for dashboard RBAC that uses the user's role directly from the session. 
 *
 * @param {string} page - The page to check permission for.
 * @param {string} action - The action to check permission for.
 * @returns {boolean} - Returns true if the user has the permission.
 */
export const useDashboardPermission = (page: string, action: string): boolean => {
  const { data: session } = useSession();
  // Directly retrieve the role from the session (assumes session.user.role exists)
  const role = session?.user?.role;

  const hasPermission = useMemo(() => {
    if (!role) return false;
    return validatePermission({ role, page, action });
  }, [role, page, action]);

  return hasPermission;
};
