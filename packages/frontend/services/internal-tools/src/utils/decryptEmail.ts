import * as privacy from "libs/privacy/src";
import { decrypt } from "libs/privacy/src/lib/cipher";
/**
 *
 */
export const decryptEmail = (email: string): string => {
  if (email && email !== "---") {
    try {
      return privacy.cipher.decrypt(email);
    } catch (error) {
      return "---?";
    }
  }
  return email;
};
interface AccountOwner {
  user?: {
    email: string;
    [key: string]: any;
  };
  [key: string]: any;
}

/**
 *
 */
export const decryptOwnerEmails = (owners: AccountOwner[]): AccountOwner[] =>
  owners.map((owner) => ({
    ...owner,
    user: owner.user ? { ...owner.user, email: decryptEmail(owner.user.email) } : owner.user,
  }));
interface Deal {
  primaryContactEmail?: string;
  dealOwnerEmail?: string;
  secondaryDoEmail?: string;
  customerSoEmail?: string;
  [key: string]: any;
}

/**
 *
 */
export const decryptDealEmails = (deal: Deal): Deal => {
  const emailFields: (keyof Deal)[] = [
    "primaryContactEmail",
    "dealOwnerEmail",
    "secondaryDoEmail",
    "customerSoEmail",
  ];
  return emailFields.reduce(
    (acc, field) => {
      if (acc[field]) {
        acc[field] = decryptEmail(acc[field] as string);
      }
      return acc;
    },
    { ...deal },
  );
};



export function safeDecrypt(value: string | null | undefined): string | null | undefined {
  if (!value) return value;
  try {
    // decrypt returns string | null
    return decrypt(value) ?? value;
  } catch {
    return value;
  }
}