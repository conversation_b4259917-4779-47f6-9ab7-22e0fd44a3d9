import SwaggerUI from '../../../components/SwaggerView/SwaggerView';
import Header from '../../../components/Navbar/Header/Header';
import { TabProperties } from '../../../components/Variables/global';
import { ParamsProps } from '../../../components/Interfaces/global.interface';

const Page: React.FC<ParamsProps> = ({ params }) => {
  // Check if the extracted type is one of the allowed words
  const isValidType = Object.keys(TabProperties).includes(params.type);

  return (
    <div className="row">
      <Header />
      <div className="col">
        {isValidType ? (
          <>
            <SwaggerUI type={params.type}/>
          </>
        ) : (
          <>
            <h1>Invalid type</h1>
          </>
        )}
      </div>
    </div>
  );
}

export default Page;