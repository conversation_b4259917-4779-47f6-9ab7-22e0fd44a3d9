import { Typography } from "@mui/material";
import { Box } from "@mui/material";

export function StatDisplay({ statDisplay, stat }: { statDisplay: string, stat: number }) {
    return (
        <Box sx={{ flexDirection: "row", display: "flex" }}>
            <Typography component="h1"
                variant="h6"
                color="inherit"
                noWrap
                sx={{ display: "flex", flexGrow: 1 }}>
                {statDisplay}
            </Typography>
            <Typography component="h1"
                variant="h6"
                color="inherit"
                noWrap
                sx={{ display: "flex" }}>
                {stat}
            </Typography>
        </Box>
    )
}