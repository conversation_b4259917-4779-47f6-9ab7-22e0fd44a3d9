package main

import (
	"flag"
	"log"
	"strings"

	"github.com/joho/godotenv"

	ner "talent/libs/ner/src"
)

var (
	titlesString string
)

// comment
func init() {
	flag.StringVar(&titlesString, "titles-string", "",
		"Comma test  separated string  with the job titles to be analyzed. Example: engineer,nurse.")
}

func main() {

	err := godotenv.Load()
	if err != nil {
		log.Panic(err)
	}

	flag.Parse()
	if len(titlesString) == 0 {
		flag.PrintDefaults()
		log.Println("invalid parameters, either -titles-file-name or -titles-json-string is required. Note that you should use only one dash (-) before each argument.")
	} else {
		texts := strings.Split(titlesString, ",")
		log.Printf("Submitting texts: %s", texts)
		n := ner.NewNER()
		result, err := n.GetEntities(texts)
		//log.Println(result) causing errors in SNYK please review the errors locally first
		//log.Println(err) causing errors in SNYK please review the errors locally first
		log.Printf("Results removed due to errors in Snyk, please review your code locally")
	}
}
