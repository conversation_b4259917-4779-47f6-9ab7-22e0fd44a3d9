package telemetry

import (
	"context"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/exporters/otlp/otlptrace"
	"go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracegrpc"
	"go.opentelemetry.io/otel/exporters/zipkin"
	"go.opentelemetry.io/otel/sdk/resource"
	sdktrace "go.opentelemetry.io/otel/sdk/trace"
	"go.opentelemetry.io/otel/trace"
)

type MockConfig struct {
	TimeMetrics  time.Duration
	TraceOptions struct {
		ResourceAttributes []attribute.KeyValue
	}
}

type MockTelemetry struct {
	mock.Mock
}

// MockZipkinExporter is a mock of ZipkinExporter interface
type MockZipkinExporter struct {
	mock.Mock
}

// New is mock of ZipkinExporter New method
func (m *MockZipkinExporter) New(url string, options ...zipkin.Option) (*zipkin.Exporter, error) {
	args := m.Called(url, options)
	return args.Get(0).(*zipkin.Exporter), args.Error(1)
}

func (m *MockZipkinExporter) ExportSpan(span trace.Span) {
	m.Called(span)
}

// MockSdkT is a mock of sdkT interface
type MockSdkT struct {
	mock.Mock
}

// NewBatchSpanProcessor is mock of sdkT NewBatchSpanProcessor method
func (m *MockSdkT) NewBatchSpanProcessor(exporter sdktrace.SpanExporter, options ...sdktrace.BatchSpanProcessorOption) sdktrace.SpanProcessor {
	args := m.Called(exporter, options)
	return args.Get(0).(sdktrace.SpanProcessor)
}

// NewTracerProvider is mock of sdkT NewTracerProvider method
func (m *MockSdkT) NewTracerProvider(options ...sdktrace.TracerProviderOption) *sdktrace.TracerProvider {
	args := m.Called(options)
	return args.Get(0).(*sdktrace.TracerProvider)
}

type mockOtlgrpc struct {
	mock.Mock
}

func (m *mockOtlgrpc) New(ctx context.Context, opts ...otlptracegrpc.Option) (*otlptrace.Exporter, error) {
	args := m.Called(ctx, opts)
	return args.Get(0).(*otlptrace.Exporter), args.Error(1)
}

type mockRes struct {
	mock.Mock
}

func (m *mockRes) New(ctx context.Context, opts ...resource.Option) (*resource.Resource, error) {
	args := m.Called(ctx, opts)
	return args.Get(0).(*resource.Resource), args.Error(1)
}

func TestSetTracer(t *testing.T) {
	tracer := SetTracer("testTracer")
	assert.NotNil(t, tracer)
}

func TestGetTracer(t *testing.T) {
	tracer := SetTracer("testTracer")
	retrievedTracer := GetTracer("testTracer")
	assert.Equal(t, tracer, retrievedTracer)
}

func TestStartSpan(t *testing.T) {
	tracer := SetTracer("testTracer")
	ctx, span := StartSpan(context.Background(), tracer, "testSpan")
	assert.NotNil(t, ctx)
	assert.NotNil(t, span)
	span.End()
}

type MockSpanProcessor struct {
	sdktrace.SpanProcessor
	// Add any additional methods or fields needed for your tests
}

func TestZipkinExporter(t *testing.T) {
	// Create mocks
	zipkinM := new(MockZipkinExporter)
	sdktraceM := new(MockSdkT)

	mockProcessor := &MockSpanProcessor{}
	// Define the behavior for the mocks
	zipkinM.On("New", mock.Anything, mock.Anything).Return(&zipkin.Exporter{}, nil)
	sdktraceM.On("NewBatchSpanProcessor", mock.Anything, mock.Anything).Return(mockProcessor, nil)

	// Create a Telemetry instance with the mocks
	telemetry := &Telemetry{
		zipkin:   zipkinM,
		sdktrace: sdktraceM,
	}

	// Call the method we are testing
	batcher, err := telemetry.zipkinExporter()

	// Assert that the expectations were met
	zipkinM.AssertExpectations(t)
	sdktraceM.AssertExpectations(t)

	// Assert that the method returned what we expected
	assert.NoError(t, err)
	assert.NotNil(t, batcher)
}

func TestZipkinExporterError(t *testing.T) {
	// Create mocks
	zipkinM := new(MockZipkinExporter)

	// Define the behavior for the mocks
	zipkinM.On("New", mock.Anything, mock.Anything).Return(&zipkin.Exporter{}, assert.AnError)

	// Create a Telemetry instance with the mocks
	telemetry := &Telemetry{
		zipkin: zipkinM,
	}

	// Call the method we are testing
	batcher, err := telemetry.zipkinExporter()

	// Assert that the expectations were met
	zipkinM.AssertExpectations(t)

	// Assert that the method returned what we expected
	assert.Error(t, err)
	assert.Nil(t, batcher)
}

func TestOtlpTracerExporter(t *testing.T) {
	// Create mocks
	otlpM := new(mockOtlgrpc)
	sdktraceM := new(MockSdkT)

	mockProcessor := &MockSpanProcessor{}
	// Define the behavior for the mocks
	otlpM.On("New", mock.Anything, mock.Anything).Return(&otlptrace.Exporter{}, nil)
	sdktraceM.On("NewBatchSpanProcessor", mock.Anything, mock.Anything).Return(mockProcessor, nil)

	// Create a Telemetry instance with the mocks
	telemetry := &Telemetry{
		otlptracegrpc: otlpM,
		sdktrace:      sdktraceM,
	}

	// Call the method we are testing
	batcher, err := telemetry.otlpTracerExporter(context.Background())

	// Assert that the expectations were met
	otlpM.AssertExpectations(t)
	sdktraceM.AssertExpectations(t)

	// Assert that the method returned what we expected
	assert.NoError(t, err)
	assert.NotNil(t, batcher)
}

func TestOtlpTracerExporterError(t *testing.T) {
	// Create mocks
	otlpM := new(mockOtlgrpc)

	// Define the behavior for the mocks
	otlpM.On("New", mock.Anything, mock.Anything).Return(&otlptrace.Exporter{}, assert.AnError)

	// Create a Telemetry instance with the mocks
	telemetry := &Telemetry{
		otlptracegrpc: otlpM,
	}

	// Call the method we are testing
	batcher, err := telemetry.otlpTracerExporter(context.Background())

	// Assert that the expectations were met
	otlpM.AssertExpectations(t)

	// Assert that the method returned what we expected
	assert.Error(t, err)
	assert.Nil(t, batcher)
}

func TestInitializeTracerExporterOtlp(t *testing.T) {
	// Create mocks
	otlpM := new(mockOtlgrpc)
	sdktraceM := new(MockSdkT)

	mockProcessor := &MockSpanProcessor{}
	// Define the behavior for the mocks
	otlpM.On("New", mock.Anything, mock.Anything).Return(&otlptrace.Exporter{}, nil)
	sdktraceM.On("NewBatchSpanProcessor", mock.Anything, mock.Anything).Return(mockProcessor, nil)

	// Create a Telemetry instance with the mocks
	telemetry := &Telemetry{
		config:        Config{TimeMetrics: 5 * time.Second},
		otlptracegrpc: otlpM,
		sdktrace:      sdktraceM,
	}

	// Call the method we are testing
	batcher, err := telemetry.initializeTracerExporter(context.Background())

	// Assert that the expectations were met
	otlpM.AssertExpectations(t)
	sdktraceM.AssertExpectations(t)

	// Assert that the method returned what we expected
	assert.NoError(t, err)
	assert.NotNil(t, batcher)
}

func TestInitializeTracerExporterZipkin(t *testing.T) {
	originalEnv := os.Getenv("ENVIRON")
	defer os.Setenv("ENVIRON", originalEnv)

	os.Setenv("ENVIRON", "remote")

	// Create mocks
	zipkinM := new(MockZipkinExporter)
	sdktraceM := new(MockSdkT)

	mockProcessor := &MockSpanProcessor{}
	// Define the behavior for the mocks
	zipkinM.On("New", mock.Anything, mock.Anything).Return(&zipkin.Exporter{}, nil)
	sdktraceM.On("NewBatchSpanProcessor", mock.Anything, mock.Anything).Return(mockProcessor, nil)

	// Create a Telemetry instance with the mocks
	telemetry := &Telemetry{
		config:   Config{TimeMetrics: 5 * time.Second},
		zipkin:   zipkinM,
		sdktrace: sdktraceM,
	}

	// Call the method we are testing
	batcher, err := telemetry.initializeTracerExporter(context.Background())

	// Assert that the expectations were met
	zipkinM.AssertExpectations(t)
	sdktraceM.AssertExpectations(t)

	// Assert that the method returned what we expected
	assert.NoError(t, err)
	assert.NotNil(t, batcher)
}

func TestNewTracerProvider(t *testing.T) {
	// Create mocks
	sdktraceM := new(MockSdkT)
	otlpM := new(mockOtlgrpc)
	resM := new(mockRes)

	mockProcessor := &MockSpanProcessor{}

	// Define the behavior for the mocks
	otlpM.On("New", mock.Anything, mock.Anything).Return(&otlptrace.Exporter{}, nil)
	sdktraceM.On("NewTracerProvider", mock.Anything).Return(&sdktrace.TracerProvider{})
	sdktraceM.On("NewBatchSpanProcessor", mock.Anything, mock.Anything).Return(mockProcessor, nil)

	resM.On("New", mock.Anything, mock.Anything).Return(&resource.Resource{}, nil)

	// Create a Telemetry instance with the mocks
	telemetry := &Telemetry{
		sdktrace:      sdktraceM,
		otlptracegrpc: otlpM,
		resource:      resM,
	}

	// Call the method we are testing
	traceProvider, err := telemetry.newTraceProvider(context.Background())

	// Assert that the expectations were met
	sdktraceM.AssertExpectations(t)
	otlpM.AssertExpectations(t)
	resM.AssertExpectations(t)

	// Assert that the method returned what we expected
	assert.NoError(t, err)
	assert.NotNil(t, traceProvider)
}

func TestNewTracerProviderError(t *testing.T) {
	// Create mocks
	otlpM := new(mockOtlgrpc)

	// Define the behavior for the mocks
	otlpM.On("New", mock.Anything, mock.Anything).Return(&otlptrace.Exporter{}, assert.AnError)

	// Create a Telemetry instance with the mocks
	telemetry := &Telemetry{
		otlptracegrpc: otlpM,
	}

	// Call the method we are testing
	traceProvider, err := telemetry.newTraceProvider(context.Background())

	// Assert that the expectations were met
	otlpM.AssertExpectations(t)

	// Assert that the method returned what we expected
	assert.Error(t, err)
	assert.Nil(t, traceProvider)
}

func TestNewTraceProviderError(t *testing.T) {
	// Create mocks
	sdktraceM := new(MockSdkT)
	otlpM := new(mockOtlgrpc)
	resM := new(mockRes)

	mockProcessor := &MockSpanProcessor{}

	// Define the behavior for the mocks
	otlpM.On("New", mock.Anything, mock.Anything).Return(&otlptrace.Exporter{}, nil)
	sdktraceM.On("NewBatchSpanProcessor", mock.Anything, mock.Anything).Return(mockProcessor, nil)
	resM.On("New", mock.Anything, mock.Anything).Return(&resource.Resource{}, assert.AnError)

	// Create a Telemetry instance with the mocks
	telemetry := &Telemetry{
		sdktrace:      sdktraceM,
		otlptracegrpc: otlpM,
		resource:      resM,
	}

	// Call the method we are testing
	traceProvider, err := telemetry.newTraceProvider(context.Background())

	// Assert that the expectations were met
	sdktraceM.AssertExpectations(t)
	otlpM.AssertExpectations(t)
	resM.AssertExpectations(t)

	// Assert that the method returned what we expected
	assert.Error(t, err)
	assert.Nil(t, traceProvider)
}
