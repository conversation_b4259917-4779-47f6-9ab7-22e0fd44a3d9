# Publishers Shared Libraries

## Introduction
This library contains common utilities and interfaces used across various microservices for publishers and their products. It aims to reduce code duplication and provide a standard implementation for frequent operations like database interactions and logging.

## Getting Started

### Prerequisites
- Go 1.22.1+
- Environment variables for database connections

### Installation
To use this library in your service, add the following line in your `go.mod`:
```go
replace talent/libs/openTelemetry => ../../libs/go/openTelemetry
```
To have a clearer understanding of how to use the code, please refer to the tests that implement the code.
