{"name": "cipher", "$schema": "../../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/backend/libs/ts/cipher/src", "projectType": "library", "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"rootDir": ".", "outputPath": "dist/packages/backend/libs/ts/cipher", "tsConfig": "packages/backend/libs/ts/cipher/tsconfig.lib.json", "packageJson": "packages/backend/libs/ts/cipher/package.json", "main": "packages/backend/libs/ts/cipher/src/index.ts", "assets": ["packages/backend/libs/ts/cipher/*.md"]}}, "snyk": {}, "snykCI": {}, "coverage": {"cache": false, "executor": "nx:run-commands", "options": {"commands": ["node infrastructure/scripts/tests/utils.js runCoverageTest Jest {projectRoot}"], "parallel": false}}}, "tags": []}