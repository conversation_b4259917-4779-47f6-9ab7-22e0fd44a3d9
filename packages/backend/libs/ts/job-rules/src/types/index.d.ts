export type JobToEvaluate = {
  id: number | string;
  [key: string]: any;
};

export type Rule = {
  id: number;
  json_rule: string;
};

export type NumericalComparators = "greater" | "lesser" | "equal" | "different";

export type TextComparators =
  | "fully contained"
  | "fully contained in all"
  | "fully not contained"
  | "fully not contained in all"
  | "fully contained fragment"
  | "partial contained"
  | "partial contained fragment"
  | "partial contained in all"
  | "partial not contained";
export type Comparators = NumericalComparator | TextComparator;

export type LogicalOperators = "and" | "or";

export type Condition = {
  field: string;
  comparator: Comparators;
  values: string[];
};

export type ConditionRule = {
  operator: LogicalOperators;
  conditions: object[Condition];
};

export type ToCompare = {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [key: string]: any;
};

export type EvaluatedCondition = {
  evaluated_condition: {
    field: string;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    field_value: any;
    comparator: string;
    value_list: string[];
  };
  is_excluded: boolean;
};

export type EvaluatedJob = { rule_id: number; job_id: number | string } & EvaluatedCondition;
export type EvaluatedJobSite = { job_site: string } & EvaluatedCondition;
