package main

import (
	"employers-job-processing/src/internal/application"
	"employers-job-processing/src/internal/infrastructure/cache"
	"encoding/json"
	"fmt"
	"io"
	"os"
	"sync"
)

func main() {
	/* mux := http.NewServeMux()
	mux.HandleFunc("GET /", func(w http.ResponseWriter, r *http.Request) {
		fmt.Fprintf(w, "Hello, World!!!!")
	})
	mux.HandleFunc("GET /process-job/{jobId}", func(w http.ResponseWriter, r *http.Request) {
		jobId := r.PathValue("jobId")
		resultChan := make(chan string)
		go func() {
			result := "Job processed: " + jobId
			resultChan <- result
		}()
		result := <-resultChan
		fmt.Fprintf(w, "finished processing job %s: %s", jobId, result)
	})
	http.ListenAndServe(":8080", mux) */
	redis := cache.NewRedisCache("data")
	/* exists, err := redis.Get("ideuzo-domino-ch")
	if err != nil {
		fmt.Println(err)
	}

	fmt.Println(string(exists)) */

	//if len(exists) == 0 {
	//loadDataToRedis(redis)
	//os.Exit(1)
	//}
	//testRedis(redis)
	testCampaingFilters(redis)
}

func testCampaingFilters(cache cache.Cache) {
	resut, err := cache.Get("join-ats-sponsored")
	if err != nil {
		fmt.Println("feedcode not found")
	}
	accountService := application.NewAccountService()
	err = accountService.ParseJSON(resut)
	if err != nil {
		fmt.Println("error parsing account", err)
	}
	for _, campaign := range accountService.Account.Campaigns {
		cs := application.NewCampaignService(campaign)
		filter, err := cs.ParseJSONToFilters()
		if err != nil {
			fmt.Println("error parsing campaign filter", err)
			fmt.Println("campaign: ", campaign.ID)
		}
		//fmt.Printf("campaign: %+v\n", campaign)
		fmt.Println(campaign.Type, campaign.ID)
		fmt.Printf("campaign filter: %+v\n", filter)
		//f := application.NewByTagFilter(*filter)
		//fmt.Printf("filter: %+v\n", f)
	}
}

func testRedis(cache cache.Cache) {
	resut, err := cache.Get("ideuzo-domino-ch")
	var account map[string]interface{}
	json.Unmarshal(resut, &account)
	fmt.Println(account)
	fmt.Println(err)
}

func loadDataToRedis(cache cache.Cache) {
	file, err := os.Open("./src/account_campaigns.json")
	if err != nil {
		fmt.Println(err)
		os.Exit(1)
	}
	defer file.Close()

	var result map[string]interface{}
	byteValue, _ := io.ReadAll(file)
	err = json.Unmarshal(byteValue, &result)
	if err != nil {
		fmt.Println(err)
		os.Exit(1)
	}
	var wg sync.WaitGroup
	semaphore := make(chan struct{}, 10)
	for feedcode, account := range result {
		wg.Add(1)
		go func(fc string, a interface{}) {
			defer wg.Done()
			semaphore <- struct{}{}
			defer func() { <-semaphore }()
			fmt.Println(fc)
			accountJSON, err := json.Marshal(a)
			if err != nil {
				fmt.Println(err)
				return
			}

			cache.Set(fc, accountJSON, nil)
		}(feedcode, account)
	}
	wg.Wait()
}
