package application

import (
	"context"
	"database/sql"
	"employers-job-processing/src/internal/models"
	"employers-job-processing/src/internal/shared"
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
)

func loadIndexationJob() *models.JobMessage {
	var j *models.JobMessage
	json.Unmarshal([]byte(shared.TestJobWithSourcePpc), &j)
	return j
}

func loadEmployerJob() *models.EmployerJobData {
	return &models.EmployerJobData{
		JobID:       "263430",
		Ppc:         models.NullInt64{sql.NullInt64{Int64: 5, Valid: true}},
		Ppcu:        models.NullInt64{sql.NullInt64{Int64: 10, Valid: true}},
		SpentBudget: 1000,
	}
}
func TestSetIndexationJob(t *testing.T) {
	js := NewJobService()
	jm := NewJobMessage()
	js.SetIndexationJob(jm)
	assert.Equal(t, jm, js.IndexationJob, "indexation job should be set correctly")
}

func TestGetIndexationJobSourcePpc(t *testing.T) {
	js := NewJobMessage()
	expectedPpc := 10
	job := loadIndexationJob()
	js.JobMessage = *job
	assert.Equal(t, expectedPpc, js.GetSourcePPC(), "should return correct PPC value")
}

func TestGetIndexationJobSourceBudget(t *testing.T) {
	js := NewJobMessage()
	expectedBudget := 10
	js.SetSourceJobBudget(10)
	assert.Equal(t, expectedBudget, js.GetSourceJobBudget(), "should return correct budget")
}

func TestGetIndexationJobID(t *testing.T) {
	js := NewJobMessage()
	expectedID := "263430"
	job := loadIndexationJob()
	js.JobMessage = *job
	assert.Equal(t, expectedID, js.GetID(), "should return correct job ID")
}

func TestSetEmployerJob(t *testing.T) {
	js := NewJobService()
	job := loadEmployerJob()
	js.SetEmployerJob(job)
	assert.Equal(t, job, js.employerJob, "employer job should be set correctly")
}

func TestGetEmployerJob(t *testing.T) {
	js := NewJobService()
	js.SetEmployerJob(loadEmployerJob())
	assert.NotNil(t, js.GetEmployerJob(), "should return non-nil employer job")
}

func TestGetEmployerJobNil(t *testing.T) {
	js := NewJobService()
	assert.NotNil(t, js.GetEmployerJob(), "should return non-nil employer job")
}

func TestGetEmployerJobPpc(t *testing.T) {
	js := NewJobService()
	expectedPpc := 5
	js.employerJob = loadEmployerJob()
	assert.Equal(t, expectedPpc, js.GetEmployerJobPpc(), "should return correct PPC")
}

func TestGetEmployerJobPpcu(t *testing.T) {
	js := NewJobService()
	expectedPpcu := int64(10)
	ppcu := models.NullInt64{sql.NullInt64{Int64: 10, Valid: true}}
	js.employerJob = &models.EmployerJobData{Ppcu: ppcu}
	assert.Equal(t, int(expectedPpcu), js.GetEmployerJobPpcu(), "should return correct PPCU")
}

func TestHasBudgetBeenReached(t *testing.T) {
	js := NewJobService()
	js.employerJob = loadEmployerJob()
	budget := 900
	assert.True(t, js.HasBudgetBeenReached(budget), "should report that budget has been reached")
}

func TestHasBudgetNotBeenReached(t *testing.T) {
	js := NewJobService()
	budget := 1100
	assert.False(t, js.HasBudgetBeenReached(budget), "should report that budget has not been reached")
}

func TestGetSpentBudget(t *testing.T) {
	js := NewJobService()
	js.employerJob = loadEmployerJob()
	expectedBudget := 1000
	assert.Equal(t, float64(expectedBudget), js.GetSpentBudget(), "should return correct spent budget")
}

type mockStorer struct{}

func (m *mockStorer) CountAllJobs(ctx context.Context) (int, error) {
	return 0, nil
}

func (m *mockStorer) SelectJob(ctx context.Context, jobData *models.EmployerJobData) (error, JobModel) {
	return nil, JobModel{}
}

func (m *mockStorer) SelectJobsByFilters(ctx context.Context, filters map[string]string) (error, []models.EmployerJobData) {
	return nil, []models.EmployerJobData{}
}

func TestFetchJob(t *testing.T) {
	js := NewJobService()
	js.SetEmployerJob(loadEmployerJob())
	storer := &mockStorer{}
	err := js.FetchJob("263430", storer)
	assert.Nil(t, err, "should not return an error")
}

func TestFetchJobsByFilter(t *testing.T) {
	js := NewJobService()
	js.SetEmployerJob(loadEmployerJob())
	storer := &mockStorer{}
	err, jobs := js.FetchJobsByFilter(map[string]string{"job_id": "263430"}, storer)
	assert.Nil(t, err, "should not return an error")
	assert.NotNil(t, jobs, "should return non-nil jobs")
}
