package application

import (
	"context"
	"employers-job-processing/src/internal/infrastructure/cache"
	"employers-job-processing/src/internal/models"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	logger "talent/libs/logger/src"
	telemetry "talent/libs/openTelemetry/src"

	"log/slog"

	"gorm.io/gorm"
)

type CampaignJobService struct {
	l logger.Logger
}

/**
* Defining new jobs campaign service
*
 */
func NewCampaingJobService() *CampaignJobService {
	return &CampaignJobService{l: logger.NewTalentLogger(logger.NewMultiOutput(&logger.ConsoleOutput{}))}
}

/*
*
GetCampaignJobsByFilter retrieves campaign jobs based on the provided filters.

This function interacts with the CampaignJobsDataSelector to fetch jobs
that match the given filters, applying pagination with offset and limit.

Parameters:
- storer: The data selector interface used to fetch campaign jobs.
- filters: The filters to apply when retrieving jobs.
- offset: The starting point for pagination (as a string).
- limit: The maximum number of records to retrieve (as a string).

Returns:
- A ResponseCampaignJobsFilters struct containing the filtered campaign jobs.
- An error if the operation fails.
*/
func (cj CampaignJobService) GetCampaignJobsByFilter(
	db *gorm.DB,
	filters models.RequestCampaignJobsFilters,
	offset string,
	limit string,
) (models.ResponseCampaignJobsFilters, error) {
	offsetParsedToInt, err := strconv.Atoi(offset)
	if err != nil {
		offsetParsedToInt = 0
	}
	limitParsedToInt, err := strconv.Atoi(limit)
	if err != nil {
		limitParsedToInt = 25
	}

	// SelectCampaignJobsByFilters retrieves campaign jobs that match the given filters.
	// It accepts a context and a filter input, returning the matching jobs or an error.
	selectRes, err := SelectCampaignJobsByFilters(
		context.Background(), models.InputByFiltersFilter{
			RequestCampaignJobsFilters: models.RequestCampaignJobsFilters{
				Feedcode:         filters.Feedcode,
				CampaignPriority: filters.CampaignPriority,
				ApplyType:        filters.ApplyType,
				Filters:          filters.Filters,
				Country:          filters.Country,
				CountryFilter:    filters.CountryFilter,
				JobIDs:           filters.JobIDs,
				Tag:              filters.Tag,
				CampaignName:     filters.CampaignName,
			},
			Limit:  limitParsedToInt,
			Offset: offsetParsedToInt,
		},
		db,
	)
	if err != nil {
		return models.ResponseCampaignJobsFilters{}, err
	}
	return selectRes, nil
}

/*
	SelectCampaignJobsByFilters: retrieves campaign jobs based on filters and count of total campaign jobs

This function first builds the query conditions and values based on the input and then
starts two goroutines: one to select the jobs and one to count the total number of jobs.
The results are then processed and returned in a ResponseCampaignJobsFilters struct.
*/
func SelectCampaignJobsByFilters(
	ctx context.Context,
	input models.InputByFiltersFilter,
	db *gorm.DB,
) (models.ResponseCampaignJobsFilters, error) {
	jobs := make([]models.CampaignJob, 0)
	var count int64

	queryConditions, queryConditionsValues, err := buildWhereCampaignConditions(input)
	if err != nil {
		return models.ResponseCampaignJobsFilters{}, err
	}

	var wg sync.WaitGroup
	var selectErr, countErr error

	wg.Add(2)
	go func() {
		defer wg.Done()
		selectErr = db.Where(queryConditions, queryConditionsValues...).Find(&jobs).Error
		if selectErr != nil {
			slog.Warn("error selecting data", "err", selectErr)
		}
	}()
	go func() {
		defer wg.Done()

		countConditions := strings.TrimSuffix(queryConditions, fmt.Sprintf("LIMIT %d OFFSET %d", input.Limit, input.Offset))
		countErr = db.Model(&models.CampaignJob{}).Where(countConditions, queryConditionsValues...).Count(&count).Error
		if countErr != nil {
			slog.Warn("error counting data", "err", countErr)
		}
	}()
	wg.Wait()

	if selectErr != nil {
		return models.ResponseCampaignJobsFilters{}, selectErr
	}

	return models.ResponseCampaignJobsFilters{
		Jobs:       jobs,
		TotalCount: int(count),
	}, nil
}

/*
	buildWhereCampaignConditions constructs a SQL WHERE clause and its corresponding

parameter values based on the provided InputByFiltersFilter.

This function validates the input for limit and offset, and then builds conditions
for various fields such as feedcode, apply_type, campaign_priority, job_id, tag,
country_filter, country, and additional filters specified in filters.

Conditions are added using the helper function addCondition, which supports exact
matches, LIKE queries, and IN clauses for slices. The function also handles specific
conditions for the apply_type and campaign_priority fields.

A LIMIT and OFFSET are appended to the WHERE clause if specified. The function
returns the constructed WHERE clause as a string, a slice of parameter values,
and an error if any validation fails.
*/
func buildWhereCampaignConditions(input models.InputByFiltersFilter) (string, []interface{}, error) {
	// Validate input
	if input.Limit < 0 || input.Offset < 0 {
		return "", nil, fmt.Errorf("invalid limit or offset: limit=%d, offset=%d", input.Limit, input.Offset)
	}

	var (
		whereBuilder string
		whereValues  []interface{}
	)

	// Helper function to add conditions
	addCondition := func(field, value interface{}, exactMatch bool) string {
		if reflect.TypeOf(value).Kind() == reflect.String && value == "" {
			return ""
		}
		var condition string
		if !exactMatch {
			condition = fmt.Sprintf("%s LIKE ?", field)
			whereValues = append(whereValues, "%"+value.(string)+"%")
		} else if reflect.TypeOf(value).Kind() == reflect.Slice {
			condition += fmt.Sprintf("%s IN ( ", field)
			for _, country := range value.([]string) {
				condition += "?, "
				whereValues = append(whereValues, country)
			}
			// remove the last comma and space
			condition = strings.TrimSuffix(condition, ", ") + " )"
		} else if field == "campaign_priority" && reflect.TypeOf(value).Kind() == reflect.Int {
			condition = fmt.Sprintf("%s >= ?", field)
			whereValues = append(whereValues, value)
		} else if field == "apply_type" && reflect.TypeOf(value).Kind() == reflect.String {
			//The apply_type field is optional, but in case it arrives in the request, the filter must include the job_url and job_source fields.
			if value != "" {
				switch strings.ToLower(value.(string)) {
				case "jobredirect":
					condition = fmt.Sprintf("%s IS NOT NULL AND %s<>''", "job_url", "job_url")
				case "talentapply":
					condition = fmt.Sprintf("((%s = '' AND job_source = 'enterpost') OR (job_source != 'enterpost'))", "job_url")
				}
			}
		} else {
			condition = fmt.Sprintf("%s = ?", field)
			whereValues = append(whereValues, value)
		}

		return condition
	}

	// Add country condition (applied globally with AND)
	if input.Feedcode != "" {
		condition := addCondition("feedcode", input.Feedcode, true)
		if condition != "" {
			whereBuilder += condition + " AND "
		}
	}

	if input.ApplyType != "" {
		condition := addCondition("apply_type", input.ApplyType, true)
		if condition != "" {
			whereBuilder += condition + " AND "
		}
	}
	if input.CampaignId != 0 {
		condition := addCondition("campaign_id", input.CampaignId, true)
		if condition != "" {
			whereBuilder += condition + " AND "
		}
	}
	if input.CampaignName != "" {
		condition := addCondition("campaign_name", input.CampaignName, false)
		if condition != "" {
			whereBuilder += condition + " AND "
		}
	}
	if input.AccountId != 0 {
		condition := addCondition("account_id", input.AccountId, true)
		if condition != "" {
			whereBuilder += condition + " AND "
		}
	}
	if input.CampaignPriority > 0 {
		condition := addCondition("campaign_priority", input.CampaignPriority, true)
		if condition != "" {
			whereBuilder += condition + " AND "
		}
	}

	if len(input.JobIDs) > 0 {
		condition := addCondition("job_id", input.JobIDs, true)
		if condition != "" {
			whereBuilder += condition + " AND "
		}
	}

	if len(input.Tag) > 0 {
		condition := addCondition("job_client_tag", input.Tag, true)
		if condition != "" {
			whereBuilder += condition + " AND "
		}
	}

	if input.CountryFilter != "" {
		if strings.ToLower(input.CountryFilter) != "all countries" {
			splitCountries := strings.Split(input.CountryFilter, ",")
			condition := addCondition("job_country", splitCountries, true)
			if condition != "" {
				whereBuilder += condition + " AND "
			}
		}
	} else if input.Country != "" {
		condition := addCondition("job_country", input.Country, true)
		if condition != "" {
			whereBuilder += condition + " AND "
		}
	}

	// Add conditions for each filter row
	var filterRowConditions []string
	for _, filter := range input.Filters {
		var subConditions []string

		// Add conditions for each field in the filter row
		if filter.Title != "" {
			condition := addCondition("job_title", filter.Title, filter.RawFilter == 1 && strings.Contains(filter.RawFilters, "title"))
			if condition != "" {
				subConditions = append(subConditions, condition)
			}
		}
		if filter.Empname != "" {
			condition := addCondition("company_name", filter.Empname, filter.RawFilter == 1 && strings.Contains(filter.RawFilters, "empname"))
			if condition != "" {
				subConditions = append(subConditions, condition)
			}
		}
		if filter.Region != "" {
			condition := addCondition("job_region", filter.Region, filter.RawFilter == 1 && strings.Contains(filter.RawFilters, "region"))
			if condition != "" {
				subConditions = append(subConditions, condition)
			}
		}
		if filter.City != "" {
			condition := addCondition("job_city", filter.City, filter.RawFilter == 1 && strings.Contains(filter.RawFilters, "city"))
			if condition != "" {
				subConditions = append(subConditions, condition)
			}
		}
		if filter.ReqId != "" {
			condition := addCondition("job_reqid", filter.ReqId, filter.RawFilter == 1 && strings.Contains(filter.RawFilters, "reqid"))
			if condition != "" {
				subConditions = append(subConditions, condition)
			}
		}

		// Combine sub-conditions with AND for the current filter row
		if len(subConditions) > 0 {
			filterRowConditions = append(filterRowConditions, "("+strings.Join(subConditions, " AND ")+")")
		}
	}

	// Combine all filter rows with OR
	if len(filterRowConditions) > 0 {
		whereBuilder += "(" + strings.Join(filterRowConditions, " OR ") + ")"
	} else {
		// remove the last END when there is no filter
		whereBuilder = strings.TrimSuffix(whereBuilder, "AND ")
	}

	// Add LIMIT and OFFSET
	if input.Limit > 0 {
		whereBuilder += fmt.Sprintf(" LIMIT %d OFFSET %d", input.Limit, input.Offset)
	}

	return whereBuilder, whereValues, nil
}

func (cj CampaignJobService) ProcessEnterpostJob(
	db *gorm.DB,
	c cache.Cache,
	jobMessage *JobMessage,
	oTel telemetry.OTel,
) (models.ResponseProcessEnterpostJob, error) {
	result, err := c.Get(jobMessage.SystemFeedcode)
	if err != nil {
		return models.ResponseProcessEnterpostJob{Campaign: models.Campaign{}}, fmt.Errorf("no account found, job will be set as organic: %s", err.Error())
	}

	accountService := NewAccountService()
	err = accountService.ParseJSON(result)
	if err != nil {
		return models.ResponseProcessEnterpostJob{Campaign: models.Campaign{}}, err
	}

	matcher := NewMatcher(jobMessage, accountService.Account.Campaigns, *accountService.Account, oTel, accountService)
	matchResult := matcher.MatchCampaigns()

	if matchResult.Err != nil {
		return models.ResponseProcessEnterpostJob{Campaign: models.Campaign{}}, matchResult.Err
	}

	return models.ResponseProcessEnterpostJob{Campaign: matchResult.Campaign}, nil
}
