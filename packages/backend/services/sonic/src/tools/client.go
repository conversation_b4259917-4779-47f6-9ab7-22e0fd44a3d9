package main

import (
	"bufio"
	"context"
	"encoding/csv"
	"errors"
	"fmt"
	"io"
	"log/slog"
	"os"
	"regexp"
	"strconv"
	"strings"
	"time"

	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"

	pb "talent/pb/sonic"
)

const maxPacketSize = 32 * 10e6

func findJobs(client pb.SonicClient, request *pb.SearchRequest, findTimeout int) ([]*pb.Job, error, []string) {
	slog.Debug("Searching jobs", "request", request)

	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(findTimeout)*time.Second)

	defer cancel()

	var stream pb.Sonic_FindJobsClient

	var err error

	var jobs []*pb.Job

	for i := 0; i < numSonicConnectRetries; i++ {
		stream, err = client.FindJobs(ctx, request)
		if err != nil {
			slog.Error("client.FindJobs failed", "error", err, "nextRetryIn", sonicConnectRetryTimeout.Seconds())
			time.Sleep(sonicConnectRetryTimeout)
		} else {
			break
		}
	}

	if err != nil {
		cancel()
		slog.Error("client.FindJobs failed", "error", err)
	}

	header, err := stream.Header()
	if err != nil {
		cancel()
		slog.Error("Reading header failed", "error", err)
	}

	slog.Debug("Receiving jobs from server", "serverid", header["serverid"])

	for {
		job, err := stream.Recv()
		if errors.Is(err, io.EOF) {
			break
		}

		if err != nil {
			slog.Error("In client.FindJobs stream.Recv failed", "error", err)

			return jobs, err, header["serverid"]
		}

		jobs = append(jobs, job)
	}

	return jobs, nil, header["serverid"]
}

func renderJobs(jobs []*pb.Job) {
	for _, job := range jobs {
		fmt.Printf("%v || %v\n", job.JobId, job.PpcU)
	}
}

const (
	timeout = 100
	maxJobs = 5000
	radius  = 25
	msm     = 3
)

func connectToServer(serverAddr string, numRetries int, retryTimeout time.Duration) *grpc.ClientConn {
	var opts []grpc.DialOption
	opts = append(opts, grpc.WithTransportCredentials(insecure.NewCredentials()))

	var conn *grpc.ClientConn

	var err error

	for i := 0; i < numRetries; i++ {
		conn, err = grpc.Dial(serverAddr, opts...)
		if err != nil {
			slog.Error("Failed to dial", "error", err, "nextRetryIn", retryTimeout.Seconds())
			time.Sleep(retryTimeout)
		}
	}

	if err != nil {
		slog.Error("Fail to dial sonic gRPC server", "retries", numRetries, "error", err)
	}

	return conn
}

const (
	numSonicConnectRetries   = 10
	sonicConnectRetryTimeout = 10 * time.Second
)

var kwRx = regexp.MustCompile(`\p{L}+`)

func getKws(title string) map[string]int {
	matches := kwRx.FindAllString(strings.ToLower(title), -1)

	ukws := make(map[string]int, len(matches))
	for _, kw := range matches {
		ukws[kw]++
	}

	return ukws
}

func getMessageFromCSVdev(csvfb *csv.Reader, header []string) (map[string]string, error) {
	line, er := csvfb.Read()
	if er != nil {
		return nil, er
	}

	jobRec := make(map[string]string, len(header))
	for i, h := range header {
		jobRec[h] = line[i]
	}

	return jobRec, nil
}

func getPoint(job map[string]string) (*pb.Location, error) {
	searchLat, er := strconv.ParseFloat(job["geo_latlon_0_geo_coordinate"], 64)
	if er != nil {
		slog.Error("Can not read latitude", "value", job["geo_latlon_0_geo_coordinate"])

		return nil, er
	}

	searchLng, er := strconv.ParseFloat(job["geo_latlon_1_geo_coordinate"], 64)
	if er != nil {
		slog.Error("Can not read longitude", "value", job["geo_latlon_1_geo_coordinate"])

		return nil, er
	}

	searchCountry := strings.ToLower(job["geo_country"])

	return &pb.Location{Lat: searchLat, Lng: searchLng, Country: searchCountry}, nil
}

type callStats struct {
	Search  string
	Nkws    int
	NJobs   int
	Latency time.Duration
}

func saveStats(stats []callStats, fileName string) {
	// Create a new CSV file
	file, err := os.Create(fileName)
	if err != nil {
		panic(err)
	}
	defer file.Close()

	// Create a CSV writer
	writer := csv.NewWriter(file)
	defer writer.Flush()

	// Write header
	err = writer.Write([]string{"Search", "NKws", "NJobs", "Latency"})
	if err != nil {
		panic(err)
	}

	// Write data rows
	for _, stat := range stats {
		// Convert time.Duration to string
		latencyMS := float64(stat.Latency) / float64(time.Millisecond)
		// Write row to CSV file
		err := writer.Write([]string{stat.Search, strconv.Itoa(stat.Nkws), strconv.Itoa(stat.NJobs), fmt.Sprintf("%.4f", latencyMS)})
		if err != nil {
			panic(err)
		}
	}

	slog.Info("Call stats file created successfully", "fileName", fileName)
}

const (
	maxKwCount = 6
	clientMSM  = 3
)

func getCsvReader(fileName string) (*os.File, *csv.Reader, []string) {
	f, err := os.Open(fileName)
	if err != nil {
		slog.Error("Can not open", "file", fileName, "error", err)
	}

	fb := bufio.NewReader(f)
	r := csv.NewReader(fb)

	header, err := r.Read()
	if err != nil {
		slog.Error("Can't read header", "file", fileName, "error", err)
	}

	return f, r, header
}

func produceSearches(client pb.SonicClient, kws map[string]int, point *pb.Location, findTimeout int, allStats *[]callStats) {
	searchPhrase := ""
	kc := 0

	for kw := range kws {
		searchPhrase += " " + kw

		kc++
		if kc > maxKwCount {
			break
		}

		start := time.Now()

		jobs, err, serverId := findJobs(
			client,
			&pb.SearchRequest{
				SearchPhrase: searchPhrase,
				Location:     point,
				Options:      &pb.Options{Timeout: timeout, Size: maxJobs, Msm: clientMSM},
			},
			findTimeout,
		)
		if err != nil {
			slog.Error("Exiting while searching jobs", "error", err)

			return
		}

		elapsed := time.Since(start)
		*allStats = append(*allStats, callStats{searchPhrase, kc, len(jobs), elapsed})
		// renderJobs(jobs)
		slog.Debug(
			"Job search result",
			"timeMs", elapsed.Milliseconds(), "nJobs", len(jobs), "search", searchPhrase, "serverId", serverId,
		)
	}
}

const wordsPerLine = 3

func emulateClient(serverAddr string, fileName string, findTimeout int, iterations int, clientId int, snapshot bool) {
	conn := connectToServer(serverAddr, numSonicConnectRetries, sonicConnectRetryTimeout)

	slog.Info("Running client", "clientId", clientId)

	client := pb.NewSonicClient(conn)

	if snapshot {
		ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)

		defer cancel()

		response, err := client.Snapshot(ctx, &pb.SnapshotRequest{Params: ""})
		if err != nil {
			slog.Error("Could not create snapshot", "error", err)
		} else {
			slog.Info(
				"Created snapshot successfully, it will appear in configured S3 location when upload completes",
				"response",
				response,
			)
		}

		return
	}

	f, r, header := getCsvReader(fileName)
	if f != nil {
		defer f.Close()
	}

	allStats := make([]callStats, 0, iterations*wordsPerLine)

	for i := 0; i < iterations; i++ {
		job, er := getMessageFromCSVdev(r, header)
		if er != nil {
			slog.Info("Finished reading file")

			break
		}

		kws := getKws(job["title"])

		point, er := getPoint(job)
		if er != nil {
			continue
		}

		point.Radius = radius

		produceSearches(client, kws, point, findTimeout, &allStats)
	}

	saveStats(allStats, fmt.Sprintf("data/stats-%03v.csv", clientId))

	requestStatus(client, findTimeout, clientId)
}

func requestStatus(client pb.SonicClient, findTimeout int, clientId int) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(findTimeout)*time.Second)

	defer cancel()

	request := &pb.StatusRequest{Key: "us"}

	maxSizeOption := grpc.MaxCallRecvMsgSize(maxPacketSize)

	status, err := client.GetStatus(ctx, request, maxSizeOption)
	if err != nil {
		slog.Error(err.Error())
	}

	slog.Info("Received status from server", "clientId", clientId)
	fmt.Printf("%s", status.Status)
}
