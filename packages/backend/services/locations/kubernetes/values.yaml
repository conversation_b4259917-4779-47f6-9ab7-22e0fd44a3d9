version: "0.0.4"
# Name of the service
name: locations
# Registry used by default to get the docker images
registry: 730335359603.dkr.ecr.us-east-1.amazonaws.com/services
# Tag used by default in all docker images
tag: latest
# Default environment
environment: dev

releases:
  - mode: Deployment
    replicas: 1
    perEnvironmentOverride:
      dev:
        minReplicas: 1
        maxReplicas: 2
        resources:
          requests:
            memory: 256Mi
            cpu: 50m
          limits:
            memory: 640Mi
      prod:
        replicas: 3
        minReplicas: 3
        maxReplicas: 9
    service:
      create: true
      ports:
        - name: http
          port: 3000
          targetPort: 3000
          protocol: TCP
    ports:
      - 3000
    nodeSelector:
      key: app
      value: cpu-optimized-svc
    resources:
      limits:
        memory: 512Mi
      requests:
        cpu: 10m
        memory: 256Mi
    horizontalPodAutoscaler:
      create: true
      min: 1
      max: 1
      cpuPercentage: 90
      memoryPercentage: 90
  
extraObjects: 
  - apiVersion: policy/v1
    kind: PodDisruptionBudget
    metadata:
      name: locations-pdb
      namespace: apps
    spec:
      maxUnavailable: 1
      selector:
        matchLabels:
          app: locations

virtualServices:
  - hosts:
      - locations-service.apps.talent.com
    gateways:
      - apps-gateway
    http:
      - match:
          - uri:
              prefix: /
        route:
          - destination:
              host: locations-service.apps.svc.cluster.local
              port:
                number: 3000
