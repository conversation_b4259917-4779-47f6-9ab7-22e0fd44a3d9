apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: locations-virtualservice
  labels:
    app: locations
spec:
  hosts:
    - locations-service.apps.talent.com
  gateways:
    - apps-gateway
  http:
    - match:
        - uri:
            prefix: /
      route:
        - destination:
            host: locations-service.apps.svc.cluster.local
            port:
              number: 3000
