from packages.data_querier import DataQuerier
from abc import ABC, abstractmethod
from urllib.parse import quote
from environs import Env
import concurrent.futures
import pandas as pd
import logging
import math
import requests
import json

class BaseSqlModel(ABC):
    def __init__(self):
        self.data_querier = DataQuerier()
        self.host = "prod"
        env = Env()
        env.read_env()
        self.monetization_base_url = env.str("MONETIZATION_BASE_URL", default="")
    
    @property
    @abstractmethod
    def table_name(self):
        pass

    @property
    @abstractmethod
    def fields(self):
        pass

    @property
    @abstractmethod
    def table_id_column(self):
        pass

    def get_monetization_rules(self, ruleset, filters, limit=10):
        base_url = self.monetization_base_url + ruleset + "?"
        # Construct the base URL with filters
        for field, filter in filters.items():
            if filter["operator"] == "=":
                value = filter["value"]
                base_url += f"{field}={value}&"
            else:
                base_url += f"{field}={quote(json.dumps(filter))}&"

        # Function to fetch a single page
        def fetch_page(page):
            url = f"{base_url}page={page}&pageSize={limit}"
            try:
                response = requests.get(url, headers={'Content-Type': 'application/json'})
                response.raise_for_status()  # Raise an exception for bad status codes
                return response.json()['data']
            except requests.RequestException as e:
                # logging.error(f"Error fetching page {page}: {str(e)}")
                return None

        # Fetch the first page to get pagination info
        first_page_data = fetch_page(1)
        if not first_page_data or 'pagination' not in first_page_data:
            # logging.error("Failed to fetch first page or invalid response format")
            return pd.DataFrame()  # Return an empty DataFrame if we can't get the first page

        total_pages = first_page_data['pagination']['totalPages']

        # Use concurrent.futures to fetch all pages simultaneously
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            future_to_page = {executor.submit(fetch_page, page): page for page in range(1, total_pages + 1)}
            all_results = []
            for future in concurrent.futures.as_completed(future_to_page):
                page_data = future.result()
                if page_data and 'results' in page_data and page_data["results"]:
                    all_results.extend(page_data['results'])
                # else:
                    # logging.warning(f"Invalid or empty response for page {future_to_page[future]}")

        # Convert all results to DataFrame
        if all_results:
            df = pd.json_normalize(all_results)
        else:
            df = pd.DataFrame()
            # logging.warning("No valid results found, returning empty DataFrame")

        return df
    
    def use_dev(self):
        return self.set_host("dev")
    
    def set_host(self, host):
        allowed_hosts = ["prod", "dev"]
        if host not in allowed_hosts:
            raise ValueError(f'The host provided ("{host}") is not allowed, please use one of these: ["{", ".join(allowed_hosts)}"]')
        self.host = host
        return self

    def iter(self):
        d = {k: self.fields[k]["value"] for k in self.fields.keys() if "value" in self.fields[k].keys()}
        return enumerate([{k: v[i] for k, v in d.items()} for i in range(len(list(d.values())[0]))])
    
    def add_field(self, field, default = None):
        if field in self.fields.keys():
            raise ValueError(f"Field '{field}' already exists. Override not allowed")
        self.fields[field] = {"value": [default] * len(list(self.iter()))}
        return True

    def chunks(self, length):
        ins = []
        for i in range(math.ceil(len(list(self.iter())) / length)):
            new = (type(self))()
            for k in self.fields.keys():
                if "value" in self.fields[k].keys():
                    new.fields[k]["value"] = self.fields[k]["value"][(length * i):(length * (i+1))]
            ins.append(new)
        return ins

    def update_values_at(self, i, vals):
        for field, value in vals.items():
            if field in self.fields.keys():
                self.fields[field]["value"][i] = value
            else:
                raise ValueError(f"Fields must be added first. Try with self.add_field('{field}').")

    def data_instance(self, data):
        instance = type(self)()
        for field_name, field_info in instance._fields.items():
            if field_name in data.columns:
                field_info['value'] = data[field_name].tolist()
        return instance
    
    def data(self):
        result = pd.DataFrame({k: self.fields[k]["value"] for k in self.fields.keys() if "value" in self.fields[k].keys()})
        return result
    
    def belongs_to(self, owner, foreign_key, local_key):
        model = owner()
        return model.get({foreign_key: {"operator": "in", "value": self.fields[local_key]["value"]}}, limit=0)
    
    def has_many(self, properties, foreign_key, local_key, limit = 10, select = []):
        model = properties()
        return model.get({foreign_key: {"operator": "in", "value": self.fields[local_key]["value"]}}, limit, select)
    
    def has_one(self, property, foreign_key, local_key):
        model = property()
        return model.getOne({foreign_key: {"operator": "in", "value": self.fields[local_key]["value"]}})        

    def where(self, filters, limit = 10, select = []):
        select, filters, order_by, limit = self.formatReadData(filters, select, limit)
        query = 'SELECT {select} FROM {table_name} {filters} {order_by}{limit}'.format(select=select, table_name=self.table_name, filters=filters, order_by=order_by, limit=limit)
        data = self.data_querier.get_script_data(class_name="Sql", method_name="dbQuery", params={
            'query': query
        }, class_params={"hostType": "read" if self.host == 'prod' else self.host}, method='post')
        return pd.DataFrame(data['data'][0])
    
    def get(self, filters, limit = 10, select = []):
        data = self.where(filters, limit, select)
        return self.data_instance(data)

    def getOne(self, filters, select = []):
        data = self.where(filters, 1, select)
        return self.data_instance(data)
    
    def getOneById(self, id, select = []):
        data = self.where({self.table_id_column: id}, 1, select)
        return self.data_instance(data)
    
    def getAll(self):
        data = self.where(filters={}, select='*')
        return self.data_instance(data)
    
    def update(self, filters, data, limit = 1):
        filters, data= self.formatUpdateData(filters, data)
        query = 'UPDATE {table_name} SET {data} {filters} LIMIT {limit}'.format(table_name=self.table_name, data=data, filters=filters, limit=limit)
        # print(query)
        data = self.data_querier.get_script_data(class_name="Sql", method_name="dbQuery", params={
            'query': query,
        }, class_params={"hostType":"write" if self.host == 'prod' else self.host}, method="post")
        return data
    
    def upsert(self, data):
        fields, values = self.formatInsertData(data)
        upsert_data = self.formatUpsert(data)
        query = 'INSERT INTO {table_name} ({fields}) VALUES ({values}){upsert_data}'.format(table_name=self.table_name, fields=fields, values=values, upsert_data=upsert_data)
        data = self.data_querier.get_script_data(class_name="Sql", method_name="dbQuery", params={
            'query': query,
        }, class_params={"hostType":"write" if self.host == 'prod' else self.host}, method="post")
        return data
    
    def create(self, data):
        fields, values= self.formatInsertData(data)
        query = 'INSERT INTO {table_name} ({fields}) VALUES ({values})'.format(table_name=self.table_name, fields=fields, values=values)
        data = self.data_querier.get_script_data(class_name="Sql", method_name="dbQuery", params={
            'query': query,
        }, class_params={"hostType":"write" if self.host == 'prod' else self.host}, method="post")
        return data
    
    def delete(self, filters, limit = 1):
        filters = self.processFilters(filters)
        query = 'DELETE FROM {table_name} {filters} LIMIT {limit}'.format(table_name=self.table_name, filters=filters, limit=limit)
        data = self.data_querier.get_script_data(class_name="Sql", method_name="dbQuery", params={
            'query': query,
        }, class_params={"hostType":"write" if self.host == 'prod' else self.host}, method="post")
        return data

    def deleteOneById(self, id):
        data = self.delete({self.table_id_column: id}, 1) 
        return data

    def formatReadData(self, filters, select, limit):
        query_filter = self.processFilters(filters).strip()
        processed_select = self.processSelect(select)
        order_by = ""
        limit = " LIMIT {limit}".format(limit=limit) if limit > 0 else ""
        return processed_select, query_filter, order_by, limit
    
    def formatUpdateData(self, filters, data):
        query_filter = self.processFilters(filters)
        data = self.processData(data)
        return query_filter.strip(), data.strip()
    
    def formatUpsert(self, data):
        update_clause = " ON DUPLICATE KEY UPDATE"
        for field, field_description in self.fields.items():
            if(("upsertable" in field_description.keys() and field_description["upsertable"] is True) and field in data.keys()):
                update_clause = f"{update_clause} `{field}` = VALUES(`{field}`), " 
        
        if update_clause == " ON DUPLICATE KEY UPDATE":
            update_clause =  ""
        return update_clause.rstrip().rstrip(",")
        
    def formatInsertData(self, data):
        fields = []
        values = []

        for field, field_description in self.fields.items():
            is_nullable = field_description.get("nullable", False)
            is_field_present = field in data

            # Check for required fields not present in data
            if not is_nullable and not is_field_present:
                raise ValueError(f"{field} is required")

            # Process only fields present in data
            if is_field_present:
                value = data[field]

                # Format the value based on its type
                formatted_value = f"'{value}'" if isinstance(value, str) else str(value)
                formatted_value = formatted_value if formatted_value != "'NOW'" else 'CURRENT_TIMESTAMP'

                # Append the formatted field and value
                fields.append(f"`{field}`")
                values.append(formatted_value)

        # Join the fields and values into comma-separated strings
        fields_str = ", ".join(fields)
        values_str = ", ".join(values)

        return fields_str, values_str
    
    def processFilters(self, filters):
        if not filters:
            return ""
        
        # Ensure filters is a list for uniform processing
        filters_list = filters if isinstance(filters, list) else [filters]
        
        # Build the query filters
        query_filters = [self.build_query_filter(f) for f in filters_list]
        
        # Format the final WHERE clause
        where_clause = "WHERE " + " OR ".join(f"({f})" for f in query_filters)
        return where_clause

    def build_query_filter(self, filter_item):
        """
        Build a query filter string from a single filter item.
        """
        query_parts = []
        for field, value in filter_item.items():
            if field in self.fields:
                operator, value = self.get_operator_and_formatted_value(value)
                query_part = f"`{field}` {operator} {value}"
                query_parts.append(query_part)
        return " AND ".join(query_parts)

    def get_operator_and_formatted_value(self, value):
        """
        Determine the operator and appropriately format the value for the SQL query.
        """
        # Default operator and value formatting
        operator = "="
        if isinstance(value, dict):
            operator, value = self.extract_value_and_operator(value)
        elif isinstance(value, str) and operator not in ['in', 'not in']:
            value = f'"{value}"'
        return operator, value

    def extract_value_and_operator(self, value_dict):
        """
        Extract the operator and value from a dictionary.
        Assumes the dictionary contains a single key-value pair.
        """
        operator, value = value_dict.values()
        if operator in ['in', 'not in']:
            value = ", ".join([f"'{v}'" if isinstance(v, str) else str(v) for v in value])
            value = f"({value})"
        else:
            value = f'"{value}"' if isinstance(value, str) else value
        return operator, value
    
    def processData(self, data):
        processed_data = None
        separator = ''
        for field, value in data.items():
            if(field in self.fields.keys()):
                value = value if type(value) is not str else "'{value}'".format(value=value)
                value = value if value is not None else "null"
                value = value if value != '\'NOW\'' else 'CURRENT_TIMESTAMP'
                processed_data = '{processed_data}{separator} `{field}` = {value}'.format(processed_data=processed_data if processed_data else "", separator=separator, field=field, value=value)
                separator = ','
        return processed_data.strip()
    
    def processSelect(self, select):
        processed_select = []
        for field in select:
            if (isinstance(field,dict) and (field["column"] in self.fields.keys() or (field["agg"] == "count" and field["column"] == "*"))):
                as_statement = " as {as_name}".format(as_name=field["as"]) if "as" in field.keys() else ""
                if field["agg"] in ["count", "sum", "min", "max", "avg"]:
                    processed_select.append("{action}({field}){as_statement}".format(action=field["agg"].upper(), field=field["column"], as_statement=as_statement))
            elif(field in self.fields.keys()):
                processed_select.append(field)
        if(len(processed_select)):
            processed_select = ", ".join([str(i) for i in processed_select])
        else:
            processed_select = "*"
        return processed_select.strip()
    
    def get_value_and_operator(self, data):
        # Check if data value is a string and add quotes if not already provided
        if not isinstance(data["value"], (int, float, list)) and data["value"] is not None:
            data["value"] = "'" + str(data["value"]) + "'"

        # Define operator mapping
        operator_mapping = {
            "eq": "=",
            "gt": ">",
            "gte": ">=",
            "lt": "<",
            "lte": "<=",
            "in": "in",
            "nin": "not in",
            "ne": "!=" if data["value"] is not None else "is not"
        }

        # Define value formatting for different operators
        value_formatting = {
            "in": lambda x: "('" + "', '".join(map(str, x)) + "')" if isinstance(x[0], str) else "(" + ", ".join(map(str, x)) + ")",
            "nin": lambda x: '("' + '", "'.join(map(str, x)) + '")' if isinstance(x[0], str) else "(" + ", ".join(map(str, x)) + ")",
            "ne": lambda x: x if data["value"] is not None else "null"
        }

        operator = operator_mapping.get(data["operator"])
        value = value_formatting.get(data["operator"], lambda x: x)(data["value"])
        
        return operator, value