from cem.models.cem import CEMModel
from execution_tracker.execution_tracker import ExecutionTracker
from cs_rules.cs_rules import CSRules
from handlers.kafka import send_event

# Modules needed for the linear programming logic
import datetime
import os, sys
import traceback
import uuid

class CEM:
    def __init__(self, use_kafka = False):
        self.cem_model = CEMModel(use_kafka=use_kafka)
        self.cs_rules = CSRules()
        # For production run debug = False
        self.execution_tracker = ExecutionTracker("CEM2.0", debug=True)
        timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
        unique_id = str(uuid.uuid4())[:8]
        self.execution_id = f"{timestamp}_{unique_id}"
        print(f"This Execution's ID is: {self.execution_id}")
        self.use_kafka = use_kafka

    def run(self):
        """Run Campaign Exposure Manager 2.0 for all campaigns with the CEM flag active"""
        try:
            if self.use_kafka and False:
                event = {"execution_id": self.execution_id}
                send_event(event, "cem_2_cron_start")
            # Retrieve campaigns
            self.execution_tracker.set_script_checkpoint("Retrieve Campaigns Data")
            self.cem_model.get_common_data()

            # solve cem for campaigns
            self.cem_model.solve_cem(self.execution_tracker, self.execution_id)

            # Finish script and flush logs
            self.execution_tracker.set_script_checkpoint("FINISH", "Script has finished successfully.")
            print(f"This Execution's ID is: {self.execution_id}")
            if self.use_kafka:
                send_event({
                    "execution_id": self.execution_id
                }, "cem2_succeeded")
        except Exception as e:
            exc_type, _, exc_tb = sys.exc_info()
            print(traceback.format_exc())
            fname = os.path.split(exc_tb.tb_frame.f_code.co_filename)[1]
            if self.use_kafka:
                send_event({
                    "error": exc_type,
                    "execution_id": self.execution_id
                }, "cem2_failed")
            self.execution_tracker.set_script_checkpoint("An exception has been CATCHED", {
                "error_details": {
                        "exception": exc_type,
                        "file": fname,
                        "line": exc_tb.tb_lineno,
                        "traceback": traceback.format_exc()
                    }, 
                })
            raise e

    def process_campaign(self, campaign, sponsored_jobs_today=None):
        """_summary_

        Args:
            campaign (_type_, optional): _description_. Defaults to None.

        Raises:
            ValueError: _description_
        """

        # Get information about the campaigns from the Campaigns model
        self.execution_tracker.set_script_checkpoint("Retrieve campaign info: {campaign}.")
        self.cem_model.get_campaigns_data_in_chunks(campaign)
        campaigns = self.cem_model.campaigns_data['campaignsList']
        formatted_chunk = campaigns['campaign'].str.cat(sep=', ')

        # Solve campaign
        self.execution_tracker.set_script_checkpoint("Retrieve traffic data for campaign {campaign}")
        result_ecpc_acr, result_traffic_data, result_traffic_data_yesterday, sponsored_jobs, clicks_for_job, cvr_priors_data = self.cem_model.retrieve_data_for_cem_per_thread(formatted_chunk, campaign, sponsored_jobs_today)

        self.execution_tracker.set_script_checkpoint("Process traffic data for {campaign}")
        list_of_traffic_data = self.cem_model.process_traffic_data(result_ecpc_acr, result_traffic_data, result_traffic_data_yesterday, sponsored_jobs, clicks_for_job, campaigns, cvr_priors_data)

        # Apply pulp solver to each dataframe in the list (each dataframe = 1 campaign)
        self.execution_tracker.set_script_checkpoint("Solve LP for campaign {campaign}")
        traffic_data = self.cem_model.solve(list_of_traffic_data)

        # Create rules for each campaign
        self.execution_tracker.set_script_checkpoint("Creating rules for campaign {campaign}")
        _ = [self.cs_rules.create_rules(row, execution_tracker=None, thread_id=None) for row in traffic_data]

        self.execution_tracker.set_script_checkpoint("FINISH")
        return traffic_data