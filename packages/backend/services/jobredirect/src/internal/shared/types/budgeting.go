package types

import "time"

type CampaignBillingRequest struct {
	JobInfo                   JobInfo   `json:"job_info"`
	JobExceedsExpirationLimit bool      `json:"exceedsExpirationLimit"`
	IsGeoFenceCompliant       bool      `json:"is_geofence_compliant"`
	Country                   string    `json:"country"`
	GetParams                 GetParams `json:"get_params"`
	IsBulk                    bool      `json:"is_bulk"`
	IsForceRedirect           bool      `json:"is_force_redirect"`
	IsQualitySelected         bool      `json:"is_quality_selected"`
}

type GetParams struct {
	Context         string  `json:"context"`
	UtmId           string  `json:"utm_id"`
	UtmMedium       string  `json:"utm_medium"`
	CacheUtmMedium  string  `json:"cache_utm_medium"`
	Puid            string  `json:"puid"`
	Ip              *string `json:"ip"`
	UserAgent       string  `json:"user_agent"`
	UtmProduct      string  `json:"utm_product"`
	CacheUtmProduct string  `json:"cache_utm_product"`
}

type (
	CampaignBillingResponse struct {
		JobInfo            JobInfo        `json:"job_info"`
		BillingInfo        FullCampaign   `json:"billing_info"`
		PartnerInfo        PartnerAccount `json:"partner_info"`
		ContextParams      ContextParams  `json:"context_params"`
		JobRules           JobRules       `json:"job_rules"`
		BudgetJob          float64        `json:"budget_job"`
		BudgetSpent        float64        `json:"budget_spent"`
		DiscountRate       float64        `json:"discount_rate"`
		BilledIsValid      *bool          `json:"billed_is_valid"`
		BilledWarning      *string        `json:"billed_warning"`
		BilledWarningGroup *string        `json:"billed_warning_group"`
		ChargedInvalid     *bool          `json:"charged_invalid"`
		ChargedWarning     *string        `json:"charged_warning"`
		IsCadc             *bool          `json:"is_cadc"`
		IsSpike            *bool          `json:"is_spike"`
		IsOrganicClick     bool           `json:"is_organic_click"`
		IsBudgetWarning    *bool          `json:"is_budget_warning"`
		IsSupressedCountry *bool          `json:"is_supressed_country"`
	}

	CampaignBillingParams struct {
		JobInfo                   JobInfo
		JobExceedsExpirationLimit bool
		IsGeoFenceCompliant       bool
		Country                   string
		GetParams                 GetParams
		IsBulk                    bool
		IsForceRedirect           bool
		IsQualitySelected         bool
	}

	FullCampaign struct {
		Campaign             `json:"campaign"`
		Account              AccountForCampaignsList `json:"account"`
		Status               *string                 `json:"status"`
		Pacing               *int16                  `json:"pacing"`
		Sponsored            *int8                   `json:"sponsored"`
		CampaignsSettings    `json:"campaigns_settings"`
		CpcModifier          *float64   `json:"cpc_modifier"`
		CampaignPpc          *float64   `json:"job_ppc"`
		SourcePPC            *float64   `json:"source_ppc"`
		Country              *string    `json:"country"`
		ConversionType       *string    `json:"conversion_type"`
		ConversionTargetCost *float64   `json:"conversion_target_cost"`
		ForcedPPC            *float64   `json:"forced_ppc"`
		ForcedPPCU           *float64   `json:"forced_ppcu"`
		DateStart            *time.Time `json:"date_start"`
		DateEnd              *time.Time `json:"date_end"`
		LastDateStart        *time.Time `json:"last_date_start"`
		LastDateStartString  *string    `json:"last_date_start_string"`
	}

	Campaign struct {
		CampaignID                   int64          `json:"campaign_id"`
		BudgetType                   *string        `json:"budget_type"`
		BudgetDay                    *float32       `json:"budget_day"`
		BudgetMonth                  *float32       `json:"budget_month"`
		BudgetJob                    *float64       `json:"budget_job"`
		DailyBudget                  *float64       `json:"daily_budget"`
		SpentToday                   *float64       `json:"spent_today"`
		MonthlySpent                 *float64       `json:"monthly_spent"`
		FlexibleSpent                *float64       `json:"flexible_spent"`
		CumulativeAllowance          *float64       `json:"cumulative_allowance"`
		DailyAllowance               *float64       `json:"daily_allowance"`
		SpikeManagerThreshold        *int64         `json:"spike_manager_threshold"`
		EstimatedBudgetRemainingDays *int32         `json:"estimated_budget_remaining_days"`
		DynamicDailyAllowance        *float64       `json:"dynamic_daily_allowance"`
		FrontLoadedDailyAllowance    *float64       `json:"front_loaded_daily_allowance"`
		CreditSpent                  *float64       `json:"credit_spent"`
		CampaignCSOwner              CampaignOwners `json:"campaign_cs_owner"`
		ApplyType                    *string        `json:"apply_type"`
		CampaignName                 *string        `json:"campaign_name"`
	}

	CampaignOwners struct {
		CampaignID    *int64  `json:"campaign_id"`
		CampaignCS    Users   `json:"campaign_cs"`
		CampaignOwner Users   `json:"campaign_owner"`
		Type          *string `json:"type"`
	}

	Users struct {
		UserID *int64  `json:"user_id"`
		Email  *string `json:"email"`
	}

	AccountForCampaignsList struct {
		AccountID              *int64          `json:"account_id"`
		Feedcode               *string         `json:"feedcode"`
		Budget                 *float64        `json:"budget"`
		Spend                  *float64        `json:"spend"`
		Discount               *float64        `json:"discount"`
		Currency               *string         `json:"currency"`
		Status                 *string         `json:"status"`
		DailyBudget            *float64        `json:"daily_budget"`
		SumDailyBudget         *float64        `json:"sum_daily_budget"`
		CurrencyRate           *float64        `json:"currency_rate"`
		AgencyTag              *string         `json:"agency_tag"`
		ForceRedirection       *string         `json:"force_redirection"`
		AccountType            *string         `json:"account_type"`
		AutoCampaignApiTagName *string         `json:"auto_campaign_api_tag_name"`
		CompanyLabel           *string         `json:"company_label"`
		AccountOwner           AccountOwners   `json:"account_owner"`
		AccountSettings        AccountSettings `json:"account_settings"`
	}

	AccountOwners struct {
		AccountID      *int64  `json:"account_id"`
		AccountCSOwner Users   `json:"account_cs_owner"`
		AccountOwner   Users   `json:"account_owner"`
		Type           *string `json:"type"`
	}

	AccountSettings struct {
		PpcUrlParam  *string `json:"ppc_url_parameter"`
		PpcUrlFormat *string `json:"ppc_url_format_value"`
	}

	CampaignsSettings struct {
		Cem                    *int8    `json:"cem"`
		SpikeManager           *int8    `json:"spike_manager"`
		ReliableConversions    *int8    `json:"reliable_conversions"`
		EcpaStatus             *int8    `json:"ecpa_status"`
		RollingAverage         *float64 `json:"rolling_average"`
		RollingCpa             *int8    `json:"rolling_cpa"`
		CpaTarget              *float64 `json:"cpa_target"`
		SmartBidding           *int8    `json:"smart_bidding"`
		ExternalConversionData *int8    `json:"external_conversion_data"`
		CctModifier            *float64 `json:"cct_modifier"`
		ConversionData         *int8    `json:"conversion_data"`
	}

	PartnerAccount struct {
		PartnerIsActive  *bool `json:"partner_is_active"`
		SupressedCountry *bool `json:"supressed_country"`
	}

	ContextParams struct {
		Channel           string `json:"channel"`
		UserAgent         string `json:"user_agent"`
		Partner           string `json:"partner"`
		IsBulk            *bool  `json:"is_bulk"`
		IsGeoFence        *bool  `json:"is_geofence"`
		ExceedsExpLimit   *bool  `json:"exceeds_expiration_limit"`
		IsQualitySelected *bool  `json:"is_quality_selected"`
		Ip                string `json:"ip"`
	}

	JobRules struct {
		Ppc         int64   `json:"ppc"`
		PpcU        int64   `json:"ppc_u"`
		NetPpc      float64 `json:"net_ppc"`
		NetPpcU     float64 `json:"net_ppc_u"`
		Ecpc        float64 `json:"ecpc"`
		PreEcpcPpc  float64 `json:"pre_ecpc_ppc"`
		PreEcpcPpcU float64 `json:"pre_ecpc_ppc_u"`
		Exposure    int64   `json:"exposure"`
	}
)
