package tools

import (
	"fmt"
	"strings"
	"time"
)

// Converts Zulu time (ISO 8601) to a Go time object after removing 'T' and 'Z'.
func UnZulu(zuluDate string) (time.Time, error) {
	// Replace 'T' with space and 'Z' with empty.
	layout := "2006-01-02 15:04:05"
	cleanDate := strings.Replace(zuluDate, "T", " ", 1)
	cleanDate = strings.Replace(cleanDate, "Z", "", 1)
	return time.Parse(layout, cleanDate)
}

// Computes the difference in days between two dates.
func GetDaysBetweenZuluDates(startDate, endDate string) int {
	// Parse start date
	start, err := UnZulu(startDate)
	if err != nil {
		fmt.Println("Error parsing start date:", err)
		return -1 // or handle the error as appropriate
	}

	// Parse end date
	end, err := UnZulu(endDate)
	if err != nil {
		fmt.Println("Error parsing end date:", err)
		return -1 // or handle the error as appropriate
	}

	// Calculate duration and convert to days
	duration := end.Sub(start)
	days := int(duration.Hours() / 24) // Convert duration to days
	return days
}
