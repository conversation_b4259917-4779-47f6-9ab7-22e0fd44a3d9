import { ServiceRequestValidation } from "../../../src/common/helpers/service-request-validation";
import { ArgumentsHost, BadRequestException } from "@nestjs/common";
import { Test, TestingModule } from "@nestjs/testing";
import { Response, Request } from "express";
import { CustomMetricService } from "../../../src/common/helpers/custom-metric.service";
import { Metrics } from "../../../../../libs/ts/metrics/src/metrics";

describe("ServiceRequestValidation", () => {
  let serviceRequestValidation: ServiceRequestValidation;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ServiceRequestValidation,
        CustomMetricService,
        {
          provide: Metrics,
          useValue: {
            initializeMetrics: jest.fn(),
            setMeter: jest.fn(),
            getMeter: jest.fn(),
            createCounter: jest.fn(),
            createObservableGauge: jest.fn(),
            addObservable: jest.fn(),
            addValueToCounter: jest.fn(),
            createHistogram: jest.fn(),
            startHistogramMeasure: jest.fn(),
            finishHistogramMeasure: jest.fn(),
          },
        },
      ],
    }).compile();

    serviceRequestValidation = module.get<ServiceRequestValidation>(ServiceRequestValidation);
  });

  it("should handle BadRequestException and return a proper response", () => {
    const exception = new BadRequestException(["Validation error message"]);
    const mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    } as unknown as Response;

    const mockRequest = {
      url: "/test-url",
    } as unknown as Request;
    let mockNext: any;
    const host: ArgumentsHost = {
      // eslint-disable-next-line jsdoc/require-jsdoc
      switchToHttp: () => ({
        // eslint-disable-next-line jsdoc/require-jsdoc
        getResponse: () => mockResponse as any,
        // eslint-disable-next-line jsdoc/require-jsdoc
        getRequest: () => mockRequest as any,
        // eslint-disable-next-line jsdoc/require-jsdoc
        getNext: () => mockNext,
      }),
      getArgs: jest.fn(),
      getArgByIndex: jest.fn(),
      switchToRpc: jest.fn(),
      switchToWs: jest.fn(),
      getType: jest.fn(),
    };

    serviceRequestValidation.catch(exception, host);

    const response = host.switchToHttp().getResponse<Response>();
    const request = host.switchToHttp().getRequest<Request>();

    if (response && response.status(400)) {
      expect(response.status).toHaveBeenCalledWith(400);
    }

    expect(response.json).toHaveBeenCalledWith({
      statusCode: 400,
      timestamp: expect.any(String),
      path: request.url === undefined ? "URL path not found" : request.url,
      message: ["Validation error message"],
      error: "Bad Request",
    });
  });
});
