package geocode

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"os"
	logger "talent/libs/logger/src"
	"talent/publishers-search-api/src/internal/mocks"
	"talent/publishers-search-api/src/internal/models"
	"testing"
	"time"

	"github.com/stretchr/testify/mock"
)

// Helper function to start a test server
func startTestServer(handler http.HandlerFunc) *httptest.Server {
	return httptest.NewServer(handler)
}

const (
	geoCodeTestAddress = "1600 Amphitheatre Parkway, Mountain View, CA"
)

// Test function for GetGeoCode
func TestGetGeoCode(t *testing.T) {
	tests := []struct {
		name       string
		query      models.GeocodeServiceRequest
		serverFunc http.HandlerFunc
		want       models.GeocodeResult
		wantErr    bool
	}{
		{
			name:  "Happy Path",
			query: models.GeocodeServiceRequest{Location: geoCodeTestAddress},
			serverFunc: func(w http.ResponseWriter, r *http.Request) {
				if r.Method != http.MethodGet {
					t.Fatalf("expected method GET, got %v", r.Method)
				}
				w.Write<PERSON>eader(http.StatusOK)
				resp := models.GeocodeResult{
					LocationKey: "some-key",
					FromCache:   false,
					ProcessingTimes: struct {
						TotalTime int `json:"totalTime"`
					}{TotalTime: 100},
					Source:              "test-source",
					GoogleAPICallsToday: 42,
					Data: struct {
						Status                   string  `json:"status"`
						Type                     string  `json:"type"`
						Lat                      float64 `json:"lat"`
						Lng                      float64 `json:"lng"`
						AdministrativeAreaLevel1 string  `json:"administrative_area_level_1"`
						Country                  string  `json:"country"`
						CountryCode              string  `json:"countrycode"`
						FormattedAddress         string  `json:"formatted_address"`
						Locality                 string  `json:"locality"`
						Search                   string  `json:"_search"`
					}{
						Status:                   "OK",
						Type:                     "location",
						Lat:                      37.4224764,
						Lng:                      -122.0842499,
						AdministrativeAreaLevel1: "California",
						Country:                  "United States",
						CountryCode:              "US",
						FormattedAddress:         "1600 Amphitheatre Parkway, Mountain View, CA",
						Locality:                 "Mountain View",
						Search:                   "1600 Amphitheatre Parkway, Mountain View, CA",
					},
				}
				json.NewEncoder(w).Encode(resp)
			},
			want: models.GeocodeResult{
				LocationKey: "some-key",
				FromCache:   false,
				ProcessingTimes: struct {
					TotalTime int `json:"totalTime"`
				}{TotalTime: 100},
				Source:              "test-source",
				GoogleAPICallsToday: 42,
				Data: struct {
					Status                   string  `json:"status"`
					Type                     string  `json:"type"`
					Lat                      float64 `json:"lat"`
					Lng                      float64 `json:"lng"`
					AdministrativeAreaLevel1 string  `json:"administrative_area_level_1"`
					Country                  string  `json:"country"`
					CountryCode              string  `json:"countrycode"`
					FormattedAddress         string  `json:"formatted_address"`
					Locality                 string  `json:"locality"`
					Search                   string  `json:"_search"`
				}{
					Status:                   "OK",
					Type:                     "location",
					Lat:                      37.4224764,
					Lng:                      -122.0842499,
					AdministrativeAreaLevel1: "California",
					Country:                  "United States",
					CountryCode:              "US",
					FormattedAddress:         geoCodeTestAddress,
					Locality:                 "Mountain View",
					Search:                   geoCodeTestAddress,
				},
			},
			wantErr: false,
		},
		{
			name:  "HTTP Client Error",
			query: models.GeocodeServiceRequest{Location: "Invalid Address"},
			serverFunc: func(w http.ResponseWriter, r *http.Request) {
				w.WriteHeader(http.StatusInternalServerError)
			},
			want:    models.GeocodeResult{},
			wantErr: true,
		},
		{
			name:  "Malformed JSON Response",
			query: models.GeocodeServiceRequest{Location: geoCodeTestAddress},
			serverFunc: func(w http.ResponseWriter, r *http.Request) {
				w.WriteHeader(http.StatusOK)
				w.Write([]byte(`{data:{"latitude": "invalid", "longitude": -122.0842499}}asdas`))
			},
			want:    models.GeocodeResult{},
			wantErr: true,
		},
		{
			name:  "Context Deadline Exceeded",
			query: models.GeocodeServiceRequest{Location: geoCodeTestAddress},
			serverFunc: func(w http.ResponseWriter, r *http.Request) {
				time.Sleep(2 * time.Second)
				w.WriteHeader(http.StatusOK)
				resp := models.GeocodeResult{
					LocationKey: "some-key",
					FromCache:   false,
					ProcessingTimes: struct {
						TotalTime int `json:"totalTime"`
					}{TotalTime: 100},
					Source:              "test-source",
					GoogleAPICallsToday: 42,
					Data: struct {
						Status                   string  `json:"status"`
						Type                     string  `json:"type"`
						Lat                      float64 `json:"lat"`
						Lng                      float64 `json:"lng"`
						AdministrativeAreaLevel1 string  `json:"administrative_area_level_1"`
						Country                  string  `json:"country"`
						CountryCode              string  `json:"countrycode"`
						FormattedAddress         string  `json:"formatted_address"`
						Locality                 string  `json:"locality"`
						Search                   string  `json:"_search"`
					}{
						Status:                   "OK",
						Type:                     "location",
						Lat:                      37.4224764,
						Lng:                      -122.0842499,
						AdministrativeAreaLevel1: "California",
						Country:                  "United States",
						CountryCode:              "US",
						FormattedAddress:         geoCodeTestAddress,
						Locality:                 "Mountain View",
						Search:                   geoCodeTestAddress,
					},
				}
				json.NewEncoder(w).Encode(resp)
			},
			want:    models.GeocodeResult{},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ts := startTestServer(tt.serverFunc)
			defer ts.Close()
			mockTelemetry := new(mocks.MockTelemetry)
			// Override the geoCodeUrl with the test server URL
			os.Setenv("GEOCODE_URL", ts.URL)
			l := logger.NewTalentLogger(logger.NewMultiOutput(&logger.ConsoleOutput{}))
			svc := NewGeocodeService(l, mockTelemetry)

			ctx := context.Background()
			if tt.name == "Context Deadline Exceeded" {
				var cancel context.CancelFunc
				ctx, cancel = context.WithTimeout(ctx, 1*time.Second)
				defer cancel()
			}
			mockTelemetry.On("AddValue", ctx, "publisher_api_geocode_elapsed_time", mock.Anything).Return(nil)
			got, err := svc.GetGeoCode(ctx, tt.query)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetGeoCode() error = %v, wantErr %v test %v", err, tt.wantErr, tt.name)
				return
			}
			if !tt.wantErr && !compareGeocodeResults(got, tt.want) {
				t.Errorf("GetGeoCode() got = %v, want %v", got, tt.want)
			}
		})
	}
}

// Helper function to compare GeocodeResults
func compareGeocodeResults(a, b models.GeocodeResult) bool {
	if a.LocationKey != b.LocationKey || a.FromCache != b.FromCache || a.Source != b.Source || a.GoogleAPICallsToday != b.GoogleAPICallsToday {
		return false
	}
	if a.ProcessingTimes.TotalTime != b.ProcessingTimes.TotalTime {
		return false
	}
	if a.Data.Status != b.Data.Status || a.Data.Type != b.Data.Type || a.Data.Lat != b.Data.Lat || a.Data.Lng != b.Data.Lng || a.Data.AdministrativeAreaLevel1 != b.Data.AdministrativeAreaLevel1 || a.Data.Country != b.Data.Country || a.Data.CountryCode != b.Data.CountryCode || a.Data.FormattedAddress != b.Data.FormattedAddress || a.Data.Locality != b.Data.Locality || a.Data.Search != b.Data.Search {
		return false
	}
	return true
}
