package search

import (
	"context"
	"encoding/json"
	"encoding/xml"
	"fmt"
	"log/slog"
	"math"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"talent/publishers-search-api/src/internal/models"
	"talent/publishers-search-api/src/internal/tools"
	"time"

	"github.com/google/uuid"
)

var PbCache PublisherCache

type Exchange interface {
	GetExchangeRatesList() models.ExchangeRates
}

const (
	ApiCallEvent = "api_called"
)

func DecodeSearchRequest(ctx context.Context, r *http.Request) (interface{}, error) {
	var errs []error
	var request models.SearchServiceRequest
	request.RequestTime = time.Now()
	request.RequestTimeUnix = request.RequestTime.UTC().Unix()
	uuid := uuid.New()
	request.RequestID = models.RequestID(uuid.String())

	// Extract common query parameter parsing logic into a helper function
	setStringFields(&request, r)
	setOutputFormat(&request.Format, r, "format")
	if request.Chnl1 == "" {
		request.Chnl1 = "default"
	}
	if request.Chnl2 == "" {
		request.Chnl2 = "default"
	}
	if request.Chnl3 == "" {
		request.Chnl3 = "default"
	}

	if request.Language == "" {
		request.Language = "en"
	}
	if request.Location != "" {
		var err error
		request.Location, err = url.QueryUnescape(request.Location)
		if err != nil {
			errs = append(errs, err)
		}
	}

	if request.Rdr != "" {
		var err error
		request.Rdr, err = url.QueryUnescape(request.Rdr)
		if err != nil {
			errs = append(errs, err)
		}
	}

	if ua := getUserAgentValue(r.URL.RawQuery); ua != "" {
		if ua, err := url.QueryUnescape(ua); err == nil {
			request.UserAgent = ua
		} else {
			errs = append(errs, err)
		}
	}

	// Extract common integer query parameter parsing logic into a helper function
	errs = setIntFields(&request, r, errs)

	if err := setBoolField(&request.JobDesc, r, "jobdesc"); err != nil {
		errs = append(errs, err)
		request.JobDesc = false
	}
	if r.URL.Query().Get("exact") == "1" {
		request.Exact = true
	}
	request.Backfill = 1
	if err := setIntField(&request.Backfill, r, "backfill"); r.URL.Query().Get("backfill") != "" && err != nil {
		errs = append(errs, err)
	}

	request.CompanyContext = "talent"
	request.CompanyContext2 = "talent" // delete after we're fully in prod

	exchangeRates, ok := PbCache.GetCurrencies()
	if !ok {
		return nil, fmt.Errorf("error getting exchange rates")
	}
	currencies := tools.Currencies()
	defaultCurrency, ok := currencies[strings.ToLower(request.Country)]
	if !ok {
		defaultCurrency = "USD"
	}
	request.ExchangeRateForMinimumBid = exchangeRates[defaultCurrency]
	request.ExchangeRateForReturnBid = exchangeRates[defaultCurrency]
	request.ReturnCurrencyBid = defaultCurrency
	request.MinimumAllowedBidCurrency = defaultCurrency
	request.Currency = defaultCurrency
	if ok {
		request.ExchangeRateForReturnBid = exchangeRates[defaultCurrency]
	}

	accountBytes, ok := PbCache.Get(request.Publisher)
	if ok {
		request.MinimumAllowedBidCurrency = *accountBytes.PaymentCurrency
		request.ExchangeRateForMinimumBid = exchangeRates[request.MinimumAllowedBidCurrency]
	}

	request.Domain = strings.Replace(generateDomain(request.Country), "https://", "", -1)

	request.TotalRequests = 1

	go func(req models.SearchServiceRequest) {
		err := tools.SendApiCalledEvent(req)
		if err != nil {
			slog.Error(fmt.Sprintf("error sending event: %v", err))
		}
	}(request)

	if conditions := len(errs); conditions > 0 {
		go func(req models.SearchServiceRequest) {
			req.TotalErrors = 1
			err := tools.SendApiCalledEvent(req)
			if err != nil {
				slog.Error(fmt.Sprintf("error sending event: %v", err))
			}
		}(request)

		err := fmt.Errorf("error parsing query parameters: %v", errs)
		return nil, err
	}

	return request, nil
}

func setIntFields(request *models.SearchServiceRequest, r *http.Request, errs []error) []error {
	if r.URL.Query().Get("radius") != "" {
		request.Radius = 0
		radius, err := parseNumericInput(r.URL.Query().Get("radius"), "radius")
		if err == nil {
			request.Radius = radius
		} else {
			errs = append(errs, err)
		}
	}
	if request.Radius == 0 {
		request.Radius = defaultRadius
	}
	if err := setIntField(&request.Page, r, "page"); err != nil {
		errs = append(errs, err)
	}
	if request.Page == 0 {
		request.Page = 1
	}

	if r.URL.Query().Get("cpcfloor") != "" {
		cpcfloor, err := parseNumericInput(r.URL.Query().Get("cpcfloor"), "cpcfloor")
		if err == nil {
			request.CPCFloor = cpcfloor
			request.MinimumAllowedBid = float64(cpcfloor) / 100
		} else {
			errs = append(errs, err)
		}
	}
	if err := setIntField(&request.Debug, r, "debug"); err != nil && r.URL.Query().Get("query") != "" {
		errs = append(errs, err)
	}

	if err := setIntField(&request.Limit, r, "limit"); err != nil {
		errs = append(errs, err)
	}
	if request.Limit == 0 {
		request.Limit = 15
	}
	if request.Limit > 100 {
		request.Limit = 100
	}
	if err := setIntField(&request.Start, r, "start"); err != nil {
		errs = append(errs, err)
		request.Start = 0
	}
	return errs
}

func parseNumericInput(input any, variable string) (int, error) {
	var floatValue float64

	switch v := input.(type) {
	case string:
		// Replace comma with dot for decimal separator
		v, _ = url.QueryUnescape(v)
		strValue := strings.Replace(v, ",", ".", -1)
		strValue = strings.Replace(strValue, "\n", "", -1)

		// Try parsing as float64
		parsedFloat, err := strconv.ParseFloat(strValue, 64)
		if err == nil {
			floatValue = parsedFloat
		} else {
			// If parsing as float fails, try parsing as int
			parsedInt, err := strconv.Atoi(strValue)
			if err != nil {
				return 0, fmt.Errorf("unable to parse %v: %v", variable, input)
			}
			floatValue = float64(parsedInt)
		}
	case float64:
		floatValue = v
	case int:
		return v, nil
	case int64:
		return int(v), nil
	default:
		return 0, fmt.Errorf("unsupported type for %v: %T", variable, input)
	}

	// Round to nearest integer
	return int(math.Round(floatValue)), nil
}

func setStringFields(request *models.SearchServiceRequest, r *http.Request) {
	setStringField(&request.Keyword, r, "k", "q")
	setStringField(&request.Location, r, "l")
	setStringField(&request.Country, r, "country")
	setStringField(&request.RemoteAddr, r, "ip")
	setStringField(&request.SubId, r, "subid")
	setStringField(&request.PublisherSubid, r, "subid")
	setStringField(&request.PublisherSubid2, r, "subid")
	setStringField(&request.Chnl1, r, "chnl1")
	setStringField(&request.Chnl2, r, "chnl2")
	setStringField(&request.Chnl3, r, "chnl3")
	setStringField(&request.Ch1, r, "chnl1")
	setStringField(&request.Ch2, r, "chnl2")
	setStringField(&request.Ch3, r, "chnl3")
	setStringField(&request.Publisher, r, "publisher")
	setStringField(&request.Publisher2, r, "publisher")
	setStringField(&request.Language, r, "language")
	setStringField(&request.SearchOn, r, "searchOn")
	setStringField(&request.ContentType, r, "contenttype")
	setStringField(&request.Rdr, r, "rdr")
}

func setStringField(target *string, r *http.Request, keys ...string) {
	for _, key := range keys {
		if val := r.URL.Query().Get(key); val != "" {
			*target = val
			return
		}
	}
}

func setOutputFormat(target *models.OutputFormats, r *http.Request, key string) {
	if val := r.URL.Query().Get(key); val != "" {
		*target = models.OutputFormats(val)
		format, ok := r.URL.Query()[key]
		if ok && len(format) > 1 {
			*target = models.OutputFormats(format[1])
		}
		switch *target {
		case models.XML, models.JSON:
			*target = models.OutputFormats(val)
		default:
			*target = models.JSON
		}
	} else {
		*target = models.XML
	}
}

func setIntField(target *int, r *http.Request, key string) error {
	if val := r.URL.Query().Get(key); val != "" {
		i, err := strconv.Atoi(val)
		if err != nil {
			return err
		}
		*target = i
	}
	return nil
}

func setBoolField(target *bool, r *http.Request, key string) error {
	if val := r.URL.Query().Get(key); val != "" {
		i, err := strconv.Atoi(val)
		if err != nil {
			return err
		}
		*target = i == 1
	}
	return nil
}

const (
	ContentTypeJson = "application/json"
	ContentTypeXml  = "application/xml"
	ContentType     = "Content-Type"
)

type ResponseFormatterService interface {
	FormatResponse(response interface{}) ([]byte, int, error)
}

func EncodeResponse(ctx context.Context, w http.ResponseWriter, response interface{}) error {
	format := getFormatFromContext(ctx)
	formattedResponse, err := formatResponse(format, response, w)

	if err != nil {
		if req, ok := ctx.Value(RequestKey).(models.SearchServiceRequest); ok {
			return handleEncodingError(w, err, req)
		}
		return handleEncodingError(w, err, models.SearchServiceRequest{})
	}

	if publisher, ok := ctx.Value(PublisherKey).(string); ok && publisher != "" {
		setAllowOriginHeader(ctx, w, publisher)
	}

	if req, ok := ctx.Value(RequestKey).(models.SearchServiceRequest); ok {
		return writeResponse(w, formattedResponse, req)
	}
	return writeResponse(w, formattedResponse, models.SearchServiceRequest{})
}

func getFormatFromContext(ctx context.Context) models.OutputFormats {
	format := ctx.Value(FormatKey)
	if outputFormat, ok := format.(models.OutputFormats); ok {
		return outputFormat
	}
	return models.JSON
}

func formatResponse(outputFormat models.OutputFormats, response interface{}, w http.ResponseWriter) ([]byte, error) {
	var (
		formattedResponse []byte
		err               error
	)

	setStatusCode(w, response)

	if resp, ok := response.(models.SearchServiceResponse); ok {
		if resp.StatusCode != http.StatusOK {
			outputFormat = models.JSON
		}
	}

	switch outputFormat {
	case models.JSON:
		formattedResponse, err = json.Marshal(response)
		w.Header().Set(ContentType, ContentTypeJson)
	case models.XML:
		formattedResponse, err = xml.Marshal(response)
		w.Header().Set(ContentType, ContentTypeXml)
	default:
		formattedResponse, err = json.Marshal(response)
		w.Header().Set(ContentType, ContentTypeJson)
	}

	return formattedResponse, err
}

func handleEncodingError(w http.ResponseWriter, err error, request models.SearchServiceRequest) error {
	http.Error(w, err.Error(), http.StatusInternalServerError)

	go func(req models.SearchServiceRequest) {
		req.TotalErrors = 1
		req.RequestTook = time.Since(req.RequestTime)
		err := tools.SendApiCalledEvent(req)
		if err != nil {
			slog.Error(fmt.Sprintf("error sending event: %v", err))
		}
	}(request)

	return err
}

func setAllowOriginHeader(ctx context.Context, w http.ResponseWriter, publisher string) {
	allowOriginHost := "https://ca.talent.com"

	cacheValue, ok := PbCache.Get(publisher)
	if ok {
		if cacheValue.ID > 0 && cacheValue.Origin != nil && isValidPublisherOrigin(ctx, cacheValue.Origin) {
			allowOriginHost = "*"
		}
	}
	w.Header().Set("Access-Control-Allow-Origin", allowOriginHost)
}

func isValidPublisherOrigin(ctx context.Context, origin *string) bool {
	host := ctx.Value(HostKey).(models.HostValue)
	return *origin != "" && isValidOrigin(*origin, string(host))
}

func writeResponse(w http.ResponseWriter, formattedResponse []byte, request models.SearchServiceRequest) error {
	_, err := w.Write(formattedResponse)

	go func(req models.SearchServiceRequest) {
		req.RequestTook = time.Since(req.RequestTime)
		if err != nil {
			req.TotalErrors = 1
		} else {
			req.TotalCompleted = 1
		}

		err := tools.SendApiCalledEvent(req)
		if err != nil {
			slog.Error(fmt.Sprintf("error sending event: %v", err))
		}
	}(request)

	return err
}

func setStatusCode(w http.ResponseWriter, response interface{}) {
	if resp, ok := response.(models.SearchServiceResponse); ok {
		if resp.StatusCode != http.StatusOK {
			w.WriteHeader(resp.StatusCode)
		}
	} else {
		// Default status code if response type is not SearchServiceResponse
		w.WriteHeader(http.StatusOK)
	}
}

func getUserAgentValue(rawQuery string) string {
	// Find the start of the useragent parameter
	start := strings.Index(rawQuery, "useragent=")
	if start == -1 {
		return ""
	}

	// Move start to the end of "useragent="
	start += len("useragent=")

	// Find the end of the useragent parameter
	end := strings.Index(rawQuery[start:], "&")
	if end == -1 {
		// If no & is found, useragent is the last parameter
		end = len(rawQuery)
	} else {
		end += start
	}

	// Extract the useragent value
	return rawQuery[start:end]
}

// isValidOrigin checks if the request host is valid based on the publisher's origin
func isValidOrigin(origin string, host string) bool {
	var origins []string
	err := json.Unmarshal([]byte(origin), &origins)
	if err != nil {
		return false
	}
	for _, o := range origins {
		if o == host {
			return true
		}
	}
	return false
}
