package publishercache

import (
	"context"
	"coreg-lead-registration-api/src/internal/cache"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"sync"
	database "talent/libs/database/src"
	accountconfig "talent/libs/publishers-shared-libs/src/accounts-config"
	coregconfig "talent/libs/publishers-shared-libs/src/accounts-coreg-config"
	"time"
)

type PublisherCache struct {
	cache           sync.Map
	refreshInterval time.Duration
	db              database.ReadWrite
	ctx             context.Context
	errorChannel    chan error
	stopRefresh     chan struct{}
	lastRefresh     *time.Time
}

func NewPublisherCacheService(refreshInterval time.Duration, db database.ReadWrite) (*PublisherCache, error) {
	pc := &PublisherCache{
		refreshInterval: refreshInterval,
		errorChannel:    make(chan error, 1),
		stopRefresh:     make(chan struct{}),
		cache:           sync.Map{},
		db:              db,
		ctx:             context.Background(),
	}
	// Perform initial load
	err := pc.updateCache()
	if err != nil {
		return nil, fmt.Errorf("failed to perform initial load of publishers: %w", err)
	}
	go pc.refreshCache()
	return pc, nil
}

func (pc *PublisherCache) GetPublisher(publisher string) (cache.Publisher, bool) {
	value, ok := pc.cache.Load(publisher)
	if !ok {
		return cache.Publisher{}, false
	}
	return value.(cache.Publisher), true
}

func (pc *PublisherCache) GetSuppressedCountry(country string) (cache.SuppressedCountry, bool) {
	value, ok := pc.cache.Load("suppressed_countries_" + country)
	if !ok {
		return cache.SuppressedCountry{}, false
	}
	return value.(cache.SuppressedCountry), true
}

func (pc *PublisherCache) GetExchangeRatesList() (cache.ExchangeRates, bool) {
	value, ok := pc.cache.Load("rates")
	if !ok {
		return nil, false
	}
	return value.(cache.ExchangeRates), true
}

func (pc *PublisherCache) refreshCache() {
	ticker := time.NewTicker(pc.refreshInterval)
	defer ticker.Stop()
	for {
		select {
		case <-ticker.C:
			if err := pc.updateCache(); err != nil {
				select {
				case pc.errorChannel <- err:
				default:
					fmt.Printf("Error updating cache (channel full): %v\n", err)
				}
			}
		case <-pc.stopRefresh:
			return
		}
	}
}

func (pc *PublisherCache) updateCache() error {
	now := time.Now()

	if pc.lastRefresh != nil && time.Since(*pc.lastRefresh) > 24*time.Hour {
		rates, err := pc.fetchExchangeRates(os.Getenv("CURRENCIES_API_URL"))
		if err != nil {
			return fmt.Errorf("error fetching exchange rates: %w", err)
		}
		pc.cache.Store("rates", rates)
	} else {
		pc.lastRefresh = &now
		rates, err := pc.fetchExchangeRates(os.Getenv("CURRENCIES_API_URL"))
		if err != nil {
			return fmt.Errorf("error fetching exchange rates: %w", err)
		}
		pc.cache.Store("rates", rates)
	}
	publishers, err := pc.fetchPublishersFromDB()
	if err != nil {
		return fmt.Errorf("error fetching publishers: %w", err)
	}
	for _, pub := range publishers {
		pc.cache.Store(pub.Source, cache.Publisher{
			IsActive:            *pub.Status == "active",
			LastUpdated:         time.Now(),
			AccountsCoregConfig: pub,
		})
	}

	suppressedCountries, err := pc.fetchSuppressedCountries()
	if err != nil {
		return err
	}
	for _, country := range suppressedCountries {
		pc.cache.Store("suppressed_countries_"+country.Country, cache.SuppressedCountry{
			Country:      country.Country,
			IsSuppressed: country.Value == "1",
		})
	}
	return nil
}

func (pc *PublisherCache) Stop() {
	close(pc.stopRefresh)
}

func (pc *PublisherCache) Errors() <-chan error {
	return pc.errorChannel
}

func (pc *PublisherCache) fetchPublishersFromDB() ([]coregconfig.AccountsCoregConfig, error) {
	coregPublishers := coregconfig.NewAccountsCoregConfig(pc.db)
	res, total, err := coregPublishers.List(pc.ctx, &coregconfig.ListFilter{
		Count: true,
	})
	if err != nil {
		return nil, err
	}
	if total == 0 {
		return nil, fmt.Errorf("no publisher coreg config found")
	}
	var publishers []coregconfig.AccountsCoregConfig
	for _, r := range res {
		result := r.(*coregconfig.AccountsCoregConfig)
		publishers = append(publishers, *result)
	}
	return publishers, nil
}

func (pc *PublisherCache) fetchSuppressedCountries() ([]accountconfig.AccountConfig, error) {
	accountConfigs := accountconfig.NewAccountConfig(pc.db)
	res, total, err := accountConfigs.List(pc.ctx, &accountconfig.ListConfigFilters{
		VarName:       "suppressed_payable_country",
		ProductTypeId: 5,
		Count:         true,
	})

	if err != nil {
		return nil, err
	}

	if total == 0 {
		return nil, fmt.Errorf("no account country config found")
	}

	var results []accountconfig.AccountConfig

	for _, c := range res {
		result := c.(*accountconfig.AccountConfig)
		results = append(results, *result)
	}
	return results, nil
}

func (pc *PublisherCache) fetchExchangeRates(apiUrl string) (cache.ExchangeRates, error) {
	req, _ := http.NewRequest("GET", apiUrl, nil)
	req.Header.Set("Content-Type", "application/json")
	client := &http.Client{}
	resp, err := client.Do(req)
	var cl cache.ExchangeRates
	if err != nil {
		return nil, err
	}
	body, _ := io.ReadAll(resp.Body)
	err = json.Unmarshal(body, &cl)

	return cl, err
}
