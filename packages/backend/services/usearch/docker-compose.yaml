version: '3.8'

services:
  usearch-service:
    build:
      context: ../../../../
      dockerfile: packages/backend/services/usearch/Dockerfile
      target: dev
    ports:
      - "8080:8080"
    environment:
      - USEARCH_LOCAL_RUN=true
      - USEARCH_KAFKA_BROKERS=kafka:9092
      - USEARCH_KAFKA_TOPIC=jobs-vector-index
      - USEARCH_KAFKA_GROUP_ID=usearch-service
      - USEARCH_INDEX_PATH=./data/usearch.index
      - USEARCH_INDEX_DIMENSIONS=384
      - USEARCH_INDEX_METRIC=cos
      - USEARCH_SAGEMAKER_ENDPOINT=text-embedding-endpoint
      - USEARCH_SAGEMAKER_REGION=us-east-1
      - USEARCH_NUM_WORKERS=4
    volumes:
      - ./data:/talent/packages/backend/services/usearch/data
      - .:/talent/packages/backend/services/usearch
    depends_on:
      - kafka
      - zookeeper
    networks:
      - usearch-network

  usearch-consumer:
    build:
      context: ../../../../
      dockerfile: packages/backend/services/usearch/Dockerfile
      target: dev
    environment:
      - USEARCH_LOCAL_RUN=true
      - USEARCH_KAFKA_BROKERS=kafka:9092
      - USEARCH_KAFKA_TOPIC=jobs-vector-index
      - USEARCH_KAFKA_GROUP_ID=usearch-consumer
      - USEARCH_INDEX_PATH=./data/usearch.index
      - USEARCH_INDEX_DIMENSIONS=384
      - USEARCH_INDEX_METRIC=cos
      - USEARCH_SAGEMAKER_ENDPOINT=text-embedding-endpoint
      - USEARCH_SAGEMAKER_REGION=us-east-1
      - USEARCH_NUM_WORKERS=4
    volumes:
      - ./data:/talent/packages/backend/services/usearch/data
      - .:/talent/packages/backend/services/usearch
    command: CompileDaemon -build="go build -o cmd/consumer/main cmd/consumer/main.go" -command=cmd/consumer/main
    depends_on:
      - kafka
      - zookeeper
    networks:
      - usearch-network

  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    hostname: zookeeper
    container_name: usearch-zookeeper
    ports:
      - "2181:2181"
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    networks:
      - usearch-network

  kafka:
    image: confluentinc/cp-kafka:7.4.0
    hostname: kafka
    container_name: usearch-kafka
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
      - "9101:9101"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: 'zookeeper:2181'
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:29092,PLAINTEXT_HOST://localhost:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
      KAFKA_GROUP_INITIAL_REBALANCE_DELAY_MS: 0
      KAFKA_JMX_PORT: 9101
      KAFKA_JMX_HOSTNAME: localhost
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: 'true'
    networks:
      - usearch-network

networks:
  usearch-network:
    driver: bridge

volumes:
  usearch-data:
