package usearch

import (
	"encoding/json"
	"fmt"
	"math"
	"os"
	"sort"
	"sync"

	"github.com/pkg/errors"
)

// VectorIndex represents a simple in-memory vector index
type VectorIndex struct {
	dimensions uint
	metric     string
	vectors    map[string][]float32
	metadata   map[string][]byte
	mutex      sync.RWMutex
}

// SearchResult represents a search result with ID, score and metadata
type IndexSearchResult struct {
	ID       string
	Score    float32
	Metadata []byte
}

// NewVectorIndex creates a new vector index
func NewVectorIndex(dimensions uint, metric string) *VectorIndex {
	return &VectorIndex{
		dimensions: dimensions,
		metric:     metric,
		vectors:    make(map[string][]float32),
		metadata:   make(map[string][]byte),
	}
}

// LoadVectorIndex loads a vector index from file
func LoadVectorIndex(path string) (*VectorIndex, error) {
	data, err := os.ReadFile(path)
	if err != nil {
		return nil, errors.Wrap(err, "failed to read index file")
	}

	var indexData struct {
		Dimensions uint                    `json:"dimensions"`
		Metric     string                  `json:"metric"`
		Vectors    map[string][]float32    `json:"vectors"`
		Metadata   map[string][]byte       `json:"metadata"`
	}

	if err := json.Unmarshal(data, &indexData); err != nil {
		return nil, errors.Wrap(err, "failed to unmarshal index data")
	}

	return &VectorIndex{
		dimensions: indexData.Dimensions,
		metric:     indexData.Metric,
		vectors:    indexData.Vectors,
		metadata:   indexData.Metadata,
	}, nil
}

// Add adds a vector to the index
func (idx *VectorIndex) Add(id string, vector []float32) error {
	if len(vector) != int(idx.dimensions) {
		return fmt.Errorf("vector dimension mismatch: expected %d, got %d", idx.dimensions, len(vector))
	}

	idx.mutex.Lock()
	defer idx.mutex.Unlock()

	idx.vectors[id] = make([]float32, len(vector))
	copy(idx.vectors[id], vector)
	return nil
}

// Update updates a vector in the index
func (idx *VectorIndex) Update(id string, vector []float32) error {
	if len(vector) != int(idx.dimensions) {
		return fmt.Errorf("vector dimension mismatch: expected %d, got %d", idx.dimensions, len(vector))
	}

	idx.mutex.Lock()
	defer idx.mutex.Unlock()

	if _, exists := idx.vectors[id]; !exists {
		return fmt.Errorf("vector with id %s does not exist", id)
	}

	idx.vectors[id] = make([]float32, len(vector))
	copy(idx.vectors[id], vector)
	return nil
}

// Remove removes a vector from the index
func (idx *VectorIndex) Remove(id string) error {
	idx.mutex.Lock()
	defer idx.mutex.Unlock()

	delete(idx.vectors, id)
	delete(idx.metadata, id)
	return nil
}

// Contains checks if a vector exists in the index
func (idx *VectorIndex) Contains(id string) (bool, error) {
	idx.mutex.RLock()
	defer idx.mutex.RUnlock()

	_, exists := idx.vectors[id]
	return exists, nil
}

// SetMetadata sets metadata for a vector
func (idx *VectorIndex) SetMetadata(id string, metadata []byte) error {
	idx.mutex.Lock()
	defer idx.mutex.Unlock()

	if _, exists := idx.vectors[id]; !exists {
		return fmt.Errorf("vector with id %s does not exist", id)
	}

	idx.metadata[id] = make([]byte, len(metadata))
	copy(idx.metadata[id], metadata)
	return nil
}

// GetMetadata gets metadata for a vector
func (idx *VectorIndex) GetMetadata(id string) ([]byte, error) {
	idx.mutex.RLock()
	defer idx.mutex.RUnlock()

	metadata, exists := idx.metadata[id]
	if !exists {
		return nil, fmt.Errorf("metadata for id %s does not exist", id)
	}

	result := make([]byte, len(metadata))
	copy(result, metadata)
	return result, nil
}

// Search searches for similar vectors
func (idx *VectorIndex) Search(query []float32, limit uint) ([]string, []float32, error) {
	if len(query) != int(idx.dimensions) {
		return nil, nil, fmt.Errorf("query vector dimension mismatch: expected %d, got %d", idx.dimensions, len(query))
	}

	idx.mutex.RLock()
	defer idx.mutex.RUnlock()

	type result struct {
		id    string
		score float32
	}

	var results []result
	for id, vector := range idx.vectors {
		score, err := idx.calculateSimilarity(query, vector)
		if err != nil {
			continue
		}
		results = append(results, result{id: id, score: score})
	}

	// Sort by score (higher is better for cosine similarity)
	sort.Slice(results, func(i, j int) bool {
		if idx.metric == "cos" {
			return results[i].score > results[j].score
		}
		// For L2 distance, lower is better
		return results[i].score < results[j].score
	})

	// Limit results
	if limit > 0 && int(limit) < len(results) {
		results = results[:limit]
	}

	ids := make([]string, len(results))
	scores := make([]float32, len(results))
	for i, r := range results {
		ids[i] = r.id
		scores[i] = r.score
	}

	return ids, scores, nil
}

// Size returns the number of vectors in the index
func (idx *VectorIndex) Size() (uint, error) {
	idx.mutex.RLock()
	defer idx.mutex.RUnlock()
	return uint(len(idx.vectors)), nil
}

// Save saves the index to a file
func (idx *VectorIndex) Save(path string) error {
	idx.mutex.RLock()
	defer idx.mutex.RUnlock()

	indexData := struct {
		Dimensions uint                    `json:"dimensions"`
		Metric     string                  `json:"metric"`
		Vectors    map[string][]float32    `json:"vectors"`
		Metadata   map[string][]byte       `json:"metadata"`
	}{
		Dimensions: idx.dimensions,
		Metric:     idx.metric,
		Vectors:    idx.vectors,
		Metadata:   idx.metadata,
	}

	data, err := json.Marshal(indexData)
	if err != nil {
		return errors.Wrap(err, "failed to marshal index data")
	}

	if err := os.WriteFile(path, data, 0644); err != nil {
		return errors.Wrap(err, "failed to write index file")
	}

	return nil
}

// calculateSimilarity calculates similarity between two vectors
func (idx *VectorIndex) calculateSimilarity(a, b []float32) (float32, error) {
	if len(a) != len(b) {
		return 0, fmt.Errorf("vector length mismatch")
	}

	switch idx.metric {
	case "cos":
		return cosineSimilarity(a, b), nil
	case "l2":
		return euclideanDistance(a, b), nil
	case "ip":
		return innerProduct(a, b), nil
	default:
		return 0, fmt.Errorf("unsupported metric: %s", idx.metric)
	}
}

// cosineSimilarity calculates cosine similarity between two vectors
func cosineSimilarity(a, b []float32) float32 {
	var dotProduct, normA, normB float32

	for i := range a {
		dotProduct += a[i] * b[i]
		normA += a[i] * a[i]
		normB += b[i] * b[i]
	}

	if normA == 0 || normB == 0 {
		return 0
	}

	return dotProduct / (float32(math.Sqrt(float64(normA))) * float32(math.Sqrt(float64(normB))))
}

// euclideanDistance calculates Euclidean distance between two vectors
func euclideanDistance(a, b []float32) float32 {
	var sum float32
	for i := range a {
		diff := a[i] - b[i]
		sum += diff * diff
	}
	return float32(math.Sqrt(float64(sum)))
}

// innerProduct calculates inner product between two vectors
func innerProduct(a, b []float32) float32 {
	var product float32
	for i := range a {
		product += a[i] * b[i]
	}
	return product
}
