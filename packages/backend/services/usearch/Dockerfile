FROM public.ecr.aws/docker/library/golang:1.22-alpine3.19 AS build

# Deactivate the gowork usage to avoid using all the services files in the docker build
ENV GOWORK off

# Use talent as the workdir
WORKDIR /talent

# Copy the go mod and the pb libraries to download the needed packages. Cache strategy
COPY packages/backend/libs packages/backend/libs
COPY packages/backend/services/usearch/go* packages/backend/services/usearch/
COPY pb /talent/pb

# Move the workdir to the service to be built
WORKDIR /talent/packages/backend/services/usearch/

# Download all needed libraries
RUN go mod download

# Copy the rest of files
COPY packages/backend/services/usearch .

# Build the service binaries
RUN GOMAXPROCS=1 go build -o cmd/usearch-service/main cmd/usearch-service/main.go
RUN GOMAXPROCS=1 go build -o cmd/consumer/main cmd/consumer/main.go

# Dev stage
FROM public.ecr.aws/docker/library/golang:1.22-alpine3.19 AS dev

# Deactivate the gowork usage to avoid using all the services files in the docker build
ENV GOWORK off

# Use the talent workdir
WORKDIR /talent

# Copy the go mod and the pb libraries to download the needed packages. Cache strategy
COPY packages/backend/libs packages/backend/libs
COPY packages/backend/services/usearch/go* packages/backend/services/usearch/
COPY pb /talent/pb

# Move to the service folder
WORKDIR /talent/packages/backend/services/usearch/

# Download all needed libraries
RUN go mod download

# Copy the rest of files
COPY packages/backend/services/usearch .

# Build the service binaries
RUN go build -o cmd/usearch-service/main cmd/usearch-service/main.go
RUN go build -o cmd/consumer/main cmd/consumer/main.go

# Install the Compile daemon
RUN go get github.com/githubnemo/CompileDaemon
RUN go install github.com/githubnemo/CompileDaemon

# Expose the needed ports
EXPOSE 8080

# Environment variables
ENV KAFKA_BROKERS localhost:9092
ENV KAFKA_TOPIC jobs-vector-index
ENV KAFKA_GROUP_ID usearch-service
ENV INDEX_PATH ./data/usearch.index
ENV INDEX_DIMENSIONS 384
ENV INDEX_METRIC cos
ENV SAGEMAKER_ENDPOINT text-embedding-endpoint
ENV SAGEMAKER_REGION us-east-1
ENV NUM_WORKERS 4

# Start the compile daemon with the main file
CMD CompileDaemon -build="go build -o cmd/usearch-service/main cmd/usearch-service/main.go" -command=cmd/usearch-service/main

# Prod stage
FROM public.ecr.aws/debian/debian:stable AS prod

# Needed to use AWS services
RUN apt-get update && apt-get install -y --no-install-recommends ca-certificates

WORKDIR /talent/packages/backend/services/usearch/

# Copy the built binaries from the build stage
COPY --from=build /talent/packages/backend/services/usearch/cmd/usearch-service/main ./cmd/usearch-service/main
COPY --from=build /talent/packages/backend/services/usearch/cmd/consumer/main ./cmd/consumer/main

# Create data directory for index
RUN mkdir -p ./data

# Expose the needed ports
EXPOSE 8080

# Environment variables
ENV KAFKA_BROKERS localhost:9092
ENV KAFKA_TOPIC jobs-vector-index
ENV KAFKA_GROUP_ID usearch-service
ENV INDEX_PATH ./data/usearch.index
ENV INDEX_DIMENSIONS 384
ENV INDEX_METRIC cos
ENV SAGEMAKER_ENDPOINT text-embedding-endpoint
ENV SAGEMAKER_REGION us-east-1
ENV NUM_WORKERS 4

# Start the service
CMD ["./cmd/usearch-service/main"]
