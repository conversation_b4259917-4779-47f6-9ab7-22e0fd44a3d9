# USearch Service

A high-performance vector search service that consumes job data from Kafka, generates embeddings using SageMaker, and provides fast similarity search using USearch.

## Features

- **Kafka Consumer**: Consumes job messages from Kafka topics
- **Vector Embeddings**: Generates embeddings using AWS SageMaker endpoints
- **Fast Search**: Uses USearch for high-performance vector similarity search
- **HTTP API**: Provides REST endpoints for search operations
- **Persistent Storage**: Saves and loads vector indices to/from disk
- **Metrics**: Tracks service statistics and performance

## Architecture

The service consists of two main components:

1. **USearch Service** (`cmd/usearch-service`): HTTP API server for search operations
2. **Consumer** (`cmd/consumer`): Dedicated Kafka consumer for processing job messages

## Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `USEARCH_KAFKA_BROKERS` | Kafka broker addresses | `localhost:9092` |
| `USEARCH_KAFKA_TOPIC` | Kafka topic to consume from | `jobs-vector-index` |
| `USEARCH_KAFKA_GROUP_ID` | Kafka consumer group ID | `usearch-service` |
| `USEARCH_INDEX_PATH` | Path to save the vector index | `./data/usearch.index` |
| `USEARCH_INDEX_DIMENSIONS` | Vector dimensions | `384` |
| `USEARCH_INDEX_METRIC` | Distance metric (cos, ip, l2) | `cos` |
| `USEARCH_SAGEMAKER_ENDPOINT` | SageMaker endpoint name | `text-embedding-endpoint` |
| `USEARCH_SAGEMAKER_REGION` | AWS region for SageMaker | `us-east-1` |
| `USEARCH_NUM_WORKERS` | Number of worker goroutines | `4` |

### Command Line Flags

Both services support command line flags that override environment variables:

```bash
# USearch Service
./cmd/usearch-service/main \
  -port 8080 \
  -kafka-brokers localhost:9092 \
  -kafka-topic jobs-vector-index \
  -kafka-group usearch-service \
  -index-path ./data/usearch.index \
  -dimensions 384 \
  -metric cos \
  -sagemaker-endpoint text-embedding-endpoint \
  -sagemaker-region us-east-1 \
  -workers 4

# Consumer
./cmd/consumer/main \
  -kafka-brokers localhost:9092 \
  -kafka-topic jobs-vector-index \
  -kafka-group usearch-consumer \
  -index-path ./data/usearch.index \
  -dimensions 384 \
  -metric cos \
  -sagemaker-endpoint text-embedding-endpoint \
  -sagemaker-region us-east-1 \
  -workers 4 \
  -batch-size 100 \
  -max-queue-size 10000 \
  -poll-timeout 10000 \
  -commit-interval 10000 \
  -save-interval 300
```

## Usage

### Development

1. **Start with Docker Compose**:
   ```bash
   docker-compose up
   ```

2. **Build and run locally**:
   ```bash
   # Build the service
   go build -o cmd/usearch-service/main cmd/usearch-service/main.go
   go build -o cmd/consumer/main cmd/consumer/main.go
   
   # Run the HTTP service
   ./cmd/usearch-service/main
   
   # Run the consumer (in another terminal)
   ./cmd/consumer/main
   ```

3. **Using NX commands**:
   ```bash
   # Serve the HTTP service
   nx serve usearch
   
   # Serve the consumer
   nx serve-consumer usearch
   
   # Build both
   nx build usearch
   nx build-consumer usearch
   ```

### API Endpoints

#### Search
```bash
POST /search
Content-Type: application/json

{
  "text": "software engineer python",
  "limit": 10
}

# OR with vector directly
{
  "vector": [0.1, 0.2, ...],
  "limit": 10
}
```

#### Statistics
```bash
GET /stats
```

#### Save Index
```bash
POST /save
```

### Message Format

The service expects Kafka messages in the following format:

```json
{
  "id": "job-123",
  "title": "Software Engineer",
  "description": "We are looking for a skilled software engineer...",
  "location": "San Francisco, CA",
  "metadata": {
    "company": "TechCorp",
    "salary": "120000"
  },
  "headers": {
    "event_type": "job_created",
    "partial_update": 0,
    "refresh_id": 12345
  }
}
```

### Event Types

- `job_created`: Add/update job vector
- `job_updated`: Update job vector
- `job_deleted`: Remove job vector
- `job_expired`: Remove job vector

## Deployment

### Docker

```bash
# Build production image
docker build -t usearch:latest --target prod .

# Run service
docker run -p 8080:8080 \
  -e USEARCH_KAFKA_BROKERS=kafka:9092 \
  -e USEARCH_SAGEMAKER_ENDPOINT=your-endpoint \
  usearch:latest

# Run consumer
docker run \
  -e USEARCH_KAFKA_BROKERS=kafka:9092 \
  -e USEARCH_SAGEMAKER_ENDPOINT=your-endpoint \
  usearch:latest ./cmd/consumer/main
```

### Kubernetes

The service includes Kubernetes manifests in the `kubernetes/` directory.

## Monitoring

The service provides metrics through the `/stats` endpoint:

```json
{
  "stats": {
    "jobs_processed": 1000,
    "vectors_added": 950,
    "vectors_deleted": 50,
    "sagemaker_calls": 950,
    "sagemaker_errors": 5,
    "kafka_errors": 2,
    "last_save_time": "2024-01-01T12:00:00Z",
    "start_time": "2024-01-01T10:00:00Z"
  },
  "index_size": 950,
  "uptime": "2h0m0s"
}
```

## Performance

- **Vector Search**: Sub-millisecond search times for millions of vectors
- **Throughput**: Processes thousands of jobs per second
- **Memory**: Efficient memory usage with configurable batch sizes
- **Persistence**: Automatic index saving with configurable intervals

## Troubleshooting

### Common Issues

1. **Kafka Connection**: Ensure Kafka brokers are accessible
2. **SageMaker Permissions**: Verify AWS credentials and endpoint access
3. **Index Path**: Ensure write permissions for index directory
4. **Memory**: Monitor memory usage for large indices

### Logs

The service uses structured logging with different levels:
- `INFO`: General operation information
- `WARN`: Non-critical issues
- `ERROR`: Critical errors requiring attention
- `DEBUG`: Detailed debugging information
