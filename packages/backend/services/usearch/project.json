{"name": "usearch", "version": "0.1.0", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "sourceRoot": "{projectRoot}/src", "targets": {"build": {"dependsOn": ["download"], "executor": "nx:run-commands", "options": {"command": "go build -o ../../../../dist/{projectRoot}/cmd/usearch-service/main cmd/usearch-service/main.go", "cwd": "{projectRoot}"}}, "build-consumer": {"dependsOn": ["download"], "executor": "nx:run-commands", "options": {"command": "go build -o ../../../../dist/{projectRoot}/cmd/consumer/main cmd/consumer/main.go", "cwd": "{projectRoot}"}}, "snyk": {}, "snykCI": {}, "coverage": {"cache": false, "executor": "nx:run-commands", "options": {"commands": ["node ../../../../infrastructure/scripts/tests/utils.js runCoverageTest GO {projectRoot}"], "parallel": false, "cwd": "{projectRoot}"}}, "dev": {"executor": "nx:run-commands", "options": {"command": "$HOME/go/bin/CompileDaemon -build='go build -o ../../../../dist/{projectRoot}/cmd/usearch-service/main cmd/usearch-service/main.go' -command=../../../../dist/{projectRoot}/cmd/usearch-service/main -directory=./ -color=true", "cwd": "{projectRoot}"}}, "dev-consumer": {"executor": "nx:run-commands", "options": {"command": "$HOME/go/bin/CompileDaemon -build='go build -o ../../../../dist/{projectRoot}/cmd/consumer/main cmd/consumer/main.go' -command=../../../../dist/{projectRoot}/cmd/consumer/main -directory=./ -color=true", "cwd": "{projectRoot}"}}, "docker": {}, "download": {"executor": "nx:run-commands", "options": {"command": "go mod download", "cwd": "{projectRoot}"}}, "install": {"executor": "nx:run-commands", "options": {"command": "go get {args.package}", "cwd": "{projectRoot}"}}, "lint": {"executor": "nx:run-commands", "options": {"command": "golangci-lint run", "cwd": "{projectRoot}"}}, "serve": {"executor": "nx:run-commands", "options": {"command": "go run cmd/usearch-service/main.go", "cwd": "{projectRoot}"}}, "serve-consumer": {"executor": "nx:run-commands", "options": {"command": "go run cmd/consumer/main.go", "cwd": "{projectRoot}"}}, "test": {"cache": false, "executor": "nx:run-commands", "options": {"command": "go test ./... -v", "cwd": "{projectRoot}"}}, "tidy": {"executor": "nx:run-commands", "options": {"command": "go mod tidy", "cwd": "{projectRoot}"}}}, "tags": ["language:go", "type:service"]}