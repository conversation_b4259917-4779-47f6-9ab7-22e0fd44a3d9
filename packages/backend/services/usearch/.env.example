# USearch Service Configuration

# Kafka Configuration
USEARCH_KAFKA_BROKERS=localhost:9092
USEARCH_KAFKA_TOPIC=jobs-vector-index
USEARCH_KAFKA_GROUP_ID=usearch-service

# Index Configuration
USEARCH_INDEX_PATH=./data/usearch.index
USEARCH_INDEX_DIMENSIONS=384
USEARCH_INDEX_METRIC=cos

# SageMaker Configuration
USEARCH_SAGEMAKER_ENDPOINT=text-embedding-endpoint
USEARCH_SAGEMAKER_REGION=us-east-1

# Service Configuration
USEARCH_NUM_WORKERS=4

# AWS Credentials (if not using IAM roles)
# AWS_ACCESS_KEY_ID=your_access_key
# AWS_SECRET_ACCESS_KEY=your_secret_key
# AWS_REGION=us-east-1

# Local Development
USEARCH_LOCAL_RUN=true
