package usearch

import (
    "context"
    "encoding/json"
    "fmt"
    "log"
    "os"
    "sync"
    "time"

    "github.com/aws/aws-sdk-go-v2/aws"
    "github.com/aws/aws-sdk-go-v2/config"
    "github.com/aws/aws-sdk-go-v2/service/sagemakerruntime"
    "github.com/confluentinc/confluent-kafka-go/kafka"
    "github.com/pkg/errors"
)

// Config holds configuration for the USearch service
type Config struct {
    // Kafka configuration
    KafkaBrokers      string
    KafkaTopic        string
    KafkaGroupID      string
    KafkaMaxPollTime  time.Duration
    KafkaCommitInterval time.Duration

    // USearch configuration
    IndexPath         string
    IndexDimensions   uint
    IndexMetric       string
    IndexQuantization string
    IndexSaveInterval time.Duration

    // SageMaker configuration
    SageMakerEndpoint string
    SageMakerRegion   string
    SageMakerTimeout  time.Duration

    // Service configuration
    NumWorkers        int
    BatchSize         int
    MaxQueueSize      int
}

// DefaultConfig returns a default configuration
func DefaultConfig() Config {
    return Config{
        KafkaBrokers:      "localhost:9092",
        KafkaTopic:        "jobs-vector-index",
        KafkaGroupID:      "usearch-service",
        KafkaMaxPollTime:  time.Second * 5,
        KafkaCommitInterval: time.Second * 10,

        IndexPath:         "./data/usearch.index",
        IndexDimensions:   384,
        IndexMetric:       "cos",
        IndexQuantization: "f32",
        IndexSaveInterval: time.Minute * 5,

        SageMakerEndpoint: "text-embedding-endpoint",
        SageMakerRegion:   "us-east-1",
        SageMakerTimeout:  time.Second * 30,

        NumWorkers:        4,
        BatchSize:         100,
        MaxQueueSize:      10000,
    }
}

// JobMessage represents a job message from Kafka
type JobMessage struct {
    ID          string            `json:"id"`
    Title       string            `json:"title"`
    Description string            `json:"description"`
    Location    string            `json:"location"`
    Metadata    map[string]string `json:"metadata"`
    Headers     struct {
        EventType    string `json:"event_type"`
        PartialUpdate int    `json:"partial_update"`
        RefreshID     int    `json:"refresh_id"`
    } `json:"headers"`
}

// Service implements the USearch service
type Service struct {
    config         Config
    index          *VectorIndex
    indexMutex     sync.RWMutex
    consumer       *kafka.Consumer
    sageMakerClient *sagemakerruntime.Client
    jobQueue       chan JobMessage
    stopChan       chan struct{}
    wg             sync.WaitGroup
    stats          ServiceStats
    statsMutex     sync.RWMutex
}

// ServiceStats tracks service statistics
type ServiceStats struct {
    JobsProcessed      int64
    VectorsAdded       int64
    VectorsDeleted     int64
    SageMakerCalls     int64
    SageMakerErrors    int64
    KafkaErrors        int64
    LastSaveTime       time.Time
    StartTime          time.Time
}

// NewService creates a new USearch service
func NewService(cfg Config) (*Service, error) {
    // Create Kafka consumer
    consumer, err := kafka.NewConsumer(&kafka.ConfigMap{
        "bootstrap.servers":     cfg.KafkaBrokers,
        "group.id":             cfg.KafkaGroupID,
        "auto.offset.reset":    "earliest",
        "enable.auto.commit":   false,
        "max.poll.interval.ms": int(cfg.KafkaMaxPollTime / time.Millisecond),
    })
    if err != nil {
        return nil, errors.Wrap(err, "failed to create Kafka consumer")
    }

    // Create AWS config
    awsCfg, err := config.LoadDefaultConfig(context.Background(),
        config.WithRegion(cfg.SageMakerRegion))
    if err != nil {
        return nil, errors.Wrap(err, "failed to load AWS config")
    }

    // Create SageMaker client
    sageMakerClient := sagemakerruntime.NewFromConfig(awsCfg)

    // Create or load vector index
    var index *VectorIndex
    if _, err := os.Stat(cfg.IndexPath); err == nil {
        // Load existing index
        index, err = LoadVectorIndex(cfg.IndexPath)
        if err != nil {
            return nil, errors.Wrap(err, "failed to load vector index")
        }
    } else {
        // Create new index
        index = NewVectorIndex(cfg.IndexDimensions, cfg.IndexMetric)
    }

    return &Service{
        config:         cfg,
        index:          index,
        indexMutex:     sync.RWMutex{},
        consumer:       consumer,
        sageMakerClient: sageMakerClient,
        jobQueue:       make(chan JobMessage, cfg.MaxQueueSize),
        stopChan:       make(chan struct{}),
        stats: ServiceStats{
            StartTime: time.Now(),
        },
    }, nil
}

// Start starts the USearch service
func (s *Service) Start() error {
    // Subscribe to Kafka topic
    if err := s.consumer.Subscribe(s.config.KafkaTopic, nil); err != nil {
        return errors.Wrap(err, "failed to subscribe to Kafka topic")
    }

    // Start workers
    for i := 0; i < s.config.NumWorkers; i++ {
        s.wg.Add(1)
        go s.worker()
    }

    // Start Kafka consumer
    s.wg.Add(1)
    go s.consumeMessages()

    // Start index saver
    s.wg.Add(1)
    go s.periodicSave()

    log.Printf("USearch service started with %d workers, consuming from topic %s", 
        s.config.NumWorkers, s.config.KafkaTopic)

    return nil
}

// Stop stops the USearch service
func (s *Service) Stop() error {
    close(s.stopChan)
    s.wg.Wait()

    // Save index
    if err := s.SaveIndex(); err != nil {
        log.Printf("Error saving index: %v", err)
    }

    // Close consumer
    if err := s.consumer.Close(); err != nil {
        return errors.Wrap(err, "failed to close Kafka consumer")
    }

    return nil
}

// consumeMessages consumes messages from Kafka
func (s *Service) consumeMessages() {
    defer s.wg.Done()

    commitTicker := time.NewTicker(s.config.KafkaCommitInterval)
    defer commitTicker.Stop()

    for {
        select {
        case <-s.stopChan:
            return
        case <-commitTicker.C:
            if _, err := s.consumer.Commit(); err != nil {
                log.Printf("Error committing offsets: %v", err)
                s.statsMutex.Lock()
                s.stats.KafkaErrors++
                s.statsMutex.Unlock()
            }
        default:
            ev := s.consumer.Poll(100)
            if ev == nil {
                continue
            }

            switch e := ev.(type) {
            case *kafka.Message:
                var job JobMessage
                if err := json.Unmarshal(e.Value, &job); err != nil {
                    log.Printf("Error unmarshalling message: %v", err)
                    s.statsMutex.Lock()
                    s.stats.KafkaErrors++
                    s.statsMutex.Unlock()
                    continue
                }

                // Add job to queue
                select {
                case s.jobQueue <- job:
                    // Successfully added to queue
                default:
                    // Queue is full, log and continue
                    log.Printf("Job queue is full, dropping message for job %s", job.ID)
                    s.statsMutex.Lock()
                    s.stats.KafkaErrors++
                    s.statsMutex.Unlock()
                }
            case kafka.Error:
                log.Printf("Kafka error: %v", e)
                s.statsMutex.Lock()
                s.stats.KafkaErrors++
                s.statsMutex.Unlock()
            }
        }
    }
}

// worker processes jobs from the queue
func (s *Service) worker() {
    defer s.wg.Done()

    for {
        select {
        case <-s.stopChan:
            return
        case job := <-s.jobQueue:
            s.processJob(job)
        }
    }
}

// processJob processes a job
func (s *Service) processJob(job JobMessage) {
    s.statsMutex.Lock()
    s.stats.JobsProcessed++
    s.statsMutex.Unlock()

    // Check if this is a delete operation
    if job.Headers.EventType == "job_deleted" || job.Headers.EventType == "job_expired" {
        if err := s.DeleteVector(job.ID); err != nil {
            log.Printf("Error deleting vector for job %s: %v", job.ID, err)
        }
        return
    }

    // Generate text for embedding
    text := fmt.Sprintf("Title: %s\nDescription: %s\nLocation: %s", 
        job.Title, job.Description, job.Location)

    // Get embedding from SageMaker
    vector, err := s.getEmbedding(text)
    if err != nil {
        log.Printf("Error getting embedding for job %s: %v", job.ID, err)
        return
    }

    // Create metadata
    metadata := make(map[string]interface{})
    for k, v := range job.Metadata {
        metadata[k] = v
    }
    metadata["title"] = job.Title
    metadata["location"] = job.Location
    metadata["refresh_id"] = job.Headers.RefreshID

    // Add or update vector
    if err := s.AddVector(job.ID, vector, metadata); err != nil {
        log.Printf("Error adding vector for job %s: %v", job.ID, err)
    }
}

// getEmbedding gets an embedding from SageMaker
func (s *Service) getEmbedding(text string) ([]float32, error) {
    ctx, cancel := context.WithTimeout(context.Background(), s.config.SageMakerTimeout)
    defer cancel()

    s.statsMutex.Lock()
    s.stats.SageMakerCalls++
    s.statsMutex.Unlock()

    // Prepare request payload
    payload := map[string]interface{}{
        "inputs": text,
    }
    payloadBytes, err := json.Marshal(payload)
    if err != nil {
        return nil, errors.Wrap(err, "failed to marshal payload")
    }

    // Call SageMaker endpoint
    resp, err := s.sageMakerClient.InvokeEndpoint(ctx, &sagemakerruntime.InvokeEndpointInput{
        EndpointName: aws.String(s.config.SageMakerEndpoint),
        ContentType:  aws.String("application/json"),
        Body:         payloadBytes,
    })
    if err != nil {
        s.statsMutex.Lock()
        s.stats.SageMakerErrors++
        s.statsMutex.Unlock()
        return nil, errors.Wrap(err, "failed to invoke SageMaker endpoint")
    }

    // Parse response
    var result struct {
        Embedding []float32 `json:"embedding"`
    }
    if err := json.Unmarshal(resp.Body, &result); err != nil {
        return nil, errors.Wrap(err, "failed to unmarshal response")
    }

    return result.Embedding, nil
}

// AddVector adds or updates a vector in the index
func (s *Service) AddVector(id string, vector []float32, metadata map[string]interface{}) error {
    s.indexMutex.Lock()
    defer s.indexMutex.Unlock()

    // Check if vector already exists
    exists, err := s.index.Contains(id)
    if err != nil {
        return errors.Wrap(err, "failed to check if vector exists")
    }

    // Add or update vector
    if exists {
        if err := s.index.Update(id, vector); err != nil {
            return errors.Wrap(err, "failed to update vector")
        }
    } else {
        if err := s.index.Add(id, vector); err != nil {
            return errors.Wrap(err, "failed to add vector")
        }
        s.statsMutex.Lock()
        s.stats.VectorsAdded++
        s.statsMutex.Unlock()
    }

    // Store metadata
    metadataBytes, err := json.Marshal(metadata)
    if err != nil {
        return errors.Wrap(err, "failed to marshal metadata")
    }

    if err := s.index.SetMetadata(id, metadataBytes); err != nil {
        return errors.Wrap(err, "failed to set metadata")
    }

    return nil
}

// DeleteVector deletes a vector from the index
func (s *Service) DeleteVector(id string) error {
    s.indexMutex.Lock()
    defer s.indexMutex.Unlock()

    // Check if vector exists
    exists, err := s.index.Contains(id)
    if err != nil {
        return errors.Wrap(err, "failed to check if vector exists")
    }

    if !exists {
        return nil
    }

    // Delete vector
    if err := s.index.Remove(id); err != nil {
        return errors.Wrap(err, "failed to remove vector")
    }

    s.statsMutex.Lock()
    s.stats.VectorsDeleted++
    s.statsMutex.Unlock()

    return nil
}

// SearchVectors searches for vectors in the index
func (s *Service) SearchVectors(vector []float32, limit int) ([]SearchResult, error) {
    s.indexMutex.RLock()
    defer s.indexMutex.RUnlock()

    // Search index
    ids, distances, err := s.index.Search(vector, uint(limit))
    if err != nil {
        return nil, errors.Wrap(err, "failed to search index")
    }

    // Get metadata for each result
    results := make([]SearchResult, len(ids))
    for i, id := range ids {
        metadataBytes, err := s.index.GetMetadata(id)
        if err != nil {
            return nil, errors.Wrapf(err, "failed to get metadata for id %s", id)
        }

        var metadata map[string]interface{}
        if err := json.Unmarshal(metadataBytes, &metadata); err != nil {
            return nil, errors.Wrapf(err, "failed to unmarshal metadata for id %s", id)
        }

        results[i] = SearchResult{
            ID:       id,
            Score:    distances[i],
            Metadata: metadata,
        }
    }

    return results, nil
}

// SearchResult represents a search result
type SearchResult struct {
    ID       string                 `json:"id"`
    Score    float32                `json:"score"`
    Metadata map[string]interface{} `json:"metadata"`
}

// SaveIndex saves the index to disk
func (s *Service) SaveIndex() error {
    s.indexMutex.RLock()
    defer s.indexMutex.RUnlock()

    // Create directory if it doesn't exist
    dir := s.config.IndexPath
    if err := os.MkdirAll(dir, 0755); err != nil {
        return errors.Wrap(err, "failed to create index directory")
    }

    // Save index
    if err := s.index.Save(s.config.IndexPath); err != nil {
        return errors.Wrap(err, "failed to save index")
    }

    s.statsMutex.Lock()
    s.stats.LastSaveTime = time.Now()
    s.statsMutex.Unlock()

    log.Printf("Index saved to %s", s.config.IndexPath)
    return nil
}

// periodicSave periodically saves the index
func (s *Service) periodicSave() {
    defer s.wg.Done()

    ticker := time.NewTicker(s.config.IndexSaveInterval)
    defer ticker.Stop()

    for {
        select {
        case <-s.stopChan:
            return
        case <-ticker.C:
            if err := s.SaveIndex(); err != nil {
                log.Printf("Error saving index: %v", err)
            }
        }
    }
}

// GetStats returns service statistics
func (s *Service) GetStats() ServiceStats {
    s.statsMutex.RLock()
    defer s.statsMutex.RUnlock()
    return s.stats
}

// GetIndexSize returns the number of vectors in the index
func (s *Service) GetIndexSize() (uint, error) {
    s.indexMutex.RLock()
    defer s.indexMutex.RUnlock()
    return s.index.Size()
}

// GetEmbedding gets an embedding from SageMaker (public method for external use)
func (s *Service) GetEmbedding(text string) ([]float32, error) {
    return s.getEmbedding(text)
}

// GetConfig returns the service configuration
func (s *Service) GetConfig() Config {
    return s.config
}