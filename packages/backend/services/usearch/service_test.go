package usearch

import (
	"testing"
	"time"
)

func TestDefaultConfig(t *testing.T) {
	config := DefaultConfig()

	// Test default values
	if config.KafkaBrokers != "localhost:9092" {
		t.<PERSON><PERSON>("Expected KafkaBrokers to be 'localhost:9092', got %s", config.KafkaBrokers)
	}

	if config.KafkaTopic != "jobs-vector-index" {
		t.<PERSON><PERSON>("Expected KafkaTopic to be 'jobs-vector-index', got %s", config.KafkaTopic)
	}

	if config.KafkaGroupID != "usearch-service" {
		t.<PERSON><PERSON>("Expected KafkaGroupID to be 'usearch-service', got %s", config.KafkaGroupID)
	}

	if config.IndexDimensions != 384 {
		t.<PERSON><PERSON>("Expected IndexDimensions to be 384, got %d", config.IndexDimensions)
	}

	if config.IndexMetric != "cos" {
		t.<PERSON>("Expected IndexMetric to be 'cos', got %s", config.IndexMetric)
	}

	if config.NumWorkers != 4 {
		t.<PERSON><PERSON>("Expected NumWorkers to be 4, got %d", config.NumWorkers)
	}
}

func TestJobMessage(t *testing.T) {
	job := JobMessage{
		ID:          "test-job-123",
		Title:       "Software Engineer",
		Description: "Test job description",
		Location:    "San Francisco, CA",
		Metadata: map[string]string{
			"company": "TestCorp",
			"salary":  "120000",
		},
	}

	job.Headers.EventType = "job_created"
	job.Headers.RefreshID = 12345

	if job.ID != "test-job-123" {
		t.Errorf("Expected ID to be 'test-job-123', got %s", job.ID)
	}

	if job.Headers.EventType != "job_created" {
		t.Errorf("Expected EventType to be 'job_created', got %s", job.Headers.EventType)
	}

	if job.Headers.RefreshID != 12345 {
		t.Errorf("Expected RefreshID to be 12345, got %d", job.Headers.RefreshID)
	}
}

func TestServiceStats(t *testing.T) {
	stats := ServiceStats{
		JobsProcessed:   100,
		VectorsAdded:    95,
		VectorsDeleted:  5,
		SageMakerCalls:  95,
		SageMakerErrors: 2,
		KafkaErrors:     1,
		StartTime:       time.Now(),
	}

	if stats.JobsProcessed != 100 {
		t.Errorf("Expected JobsProcessed to be 100, got %d", stats.JobsProcessed)
	}

	if stats.VectorsAdded != 95 {
		t.Errorf("Expected VectorsAdded to be 95, got %d", stats.VectorsAdded)
	}

	if stats.VectorsDeleted != 5 {
		t.Errorf("Expected VectorsDeleted to be 5, got %d", stats.VectorsDeleted)
	}
}