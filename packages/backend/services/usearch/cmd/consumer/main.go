package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"log/slog"
	"os"
	"os/signal"
	"sync"
	"syscall"
	"time"

	"github.com/confluentinc/confluent-kafka-go/kafka"
	"github.com/joho/godotenv"
	"usearch"
)

var (
	kafkaBrokers      = flag.String("kafka-brokers", "localhost:9092", "Kafka brokers")
	kafkaTopic        = flag.String("kafka-topic", "jobs-vector-index", "Kafka topic")
	kafkaGroupID      = flag.String("kafka-group", "usearch-consumer", "Kafka consumer group ID")
	indexPath         = flag.String("index-path", "./data/usearch.index", "Path to save the index")
	indexDimensions   = flag.Uint("dimensions", 384, "Vector dimensions")
	indexMetric       = flag.String("metric", "cos", "Distance metric (cos, ip, l2)")
	sageMakerEndpoint = flag.String("sagemaker-endpoint", "text-embedding-endpoint", "SageMaker endpoint name")
	sageMakerRegion   = flag.String("sagemaker-region", "us-east-1", "AWS region for SageMaker")
	numWorkers        = flag.Int("workers", 4, "Number of worker goroutines")
	batchSize         = flag.Int("batch-size", 100, "Batch size for processing")
	maxQueueSize      = flag.Int("max-queue-size", 10000, "Maximum queue size")
	pollTimeout       = flag.Int("poll-timeout", 10000, "Kafka poll timeout in milliseconds")
	commitInterval    = flag.Int("commit-interval", 10000, "Kafka commit interval in milliseconds")
	saveInterval      = flag.Int("save-interval", 300, "Index save interval in seconds")
)

func main() {
	flag.Parse()

	// Load environment variables if running locally
	if os.Getenv("USEARCH_LOCAL_RUN") == "true" {
		godotenv.Load(".env")
	}

	// Override flags with environment variables if set
	if brokers := os.Getenv("USEARCH_KAFKA_BROKERS"); brokers != "" {
		*kafkaBrokers = brokers
	}
	if topic := os.Getenv("USEARCH_KAFKA_TOPIC"); topic != "" {
		*kafkaTopic = topic
	}
	if group := os.Getenv("USEARCH_KAFKA_GROUP_ID"); group != "" {
		*kafkaGroupID = group
	}
	if path := os.Getenv("USEARCH_INDEX_PATH"); path != "" {
		*indexPath = path
	}
	if endpoint := os.Getenv("USEARCH_SAGEMAKER_ENDPOINT"); endpoint != "" {
		*sageMakerEndpoint = endpoint
	}
	if region := os.Getenv("USEARCH_SAGEMAKER_REGION"); region != "" {
		*sageMakerRegion = region
	}

	slog.Info("Starting USearch Kafka Consumer", 
		"brokers", *kafkaBrokers,
		"topic", *kafkaTopic,
		"group", *kafkaGroupID,
		"workers", *numWorkers)

	ctx := context.Background()

	// Create config
	config := usearch.Config{
		KafkaBrokers:        *kafkaBrokers,
		KafkaTopic:          *kafkaTopic,
		KafkaGroupID:        *kafkaGroupID,
		KafkaMaxPollTime:    time.Duration(*pollTimeout) * time.Millisecond,
		KafkaCommitInterval: time.Duration(*commitInterval) * time.Millisecond,
		IndexPath:           *indexPath,
		IndexDimensions:     *indexDimensions,
		IndexMetric:         *indexMetric,
		IndexQuantization:   "f32",
		IndexSaveInterval:   time.Duration(*saveInterval) * time.Second,
		SageMakerEndpoint:   *sageMakerEndpoint,
		SageMakerRegion:     *sageMakerRegion,
		SageMakerTimeout:    time.Second * 30,
		NumWorkers:          *numWorkers,
		BatchSize:           *batchSize,
		MaxQueueSize:        *maxQueueSize,
	}

	// Create service
	service, err := usearch.NewService(config)
	if err != nil {
		slog.Error("Failed to create USearch service", "error", err)
		os.Exit(1)
	}

	// Start consumer only (no HTTP server)
	if err := startConsumerOnly(ctx, service); err != nil {
		slog.Error("Failed to start consumer", "error", err)
		os.Exit(1)
	}
}

func startConsumerOnly(ctx context.Context, service *usearch.Service) error {
	// Create Kafka consumer
	consumer, err := kafka.NewConsumer(&kafka.ConfigMap{
		"bootstrap.servers":     service.GetConfig().KafkaBrokers,
		"group.id":             service.GetConfig().KafkaGroupID,
		"auto.offset.reset":    "earliest",
		"enable.auto.commit":   false,
		"max.poll.interval.ms": int(service.GetConfig().KafkaMaxPollTime / time.Millisecond),
	})
	if err != nil {
		return err
	}
	defer consumer.Close()

	// Subscribe to topic
	if err := consumer.Subscribe(service.GetConfig().KafkaTopic, nil); err != nil {
		return err
	}

	// Create job queue
	jobQueue := make(chan usearch.JobMessage, service.GetConfig().MaxQueueSize)

	// Start workers
	var wg sync.WaitGroup
	for i := 0; i < service.GetConfig().NumWorkers; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()
			worker(ctx, workerID, service, jobQueue)
		}(i)
	}

	// Start periodic index saver
	wg.Add(1)
	go func() {
		defer wg.Done()
		periodicSave(ctx, service)
	}()

	// Start message consumer
	wg.Add(1)
	go func() {
		defer wg.Done()
		consumeMessages(ctx, consumer, jobQueue, service)
	}()

	slog.Info("USearch consumer started", 
		"workers", service.GetConfig().NumWorkers,
		"topic", service.GetConfig().KafkaTopic)

	// Wait for interrupt signal
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)
	<-sigChan

	slog.Info("Shutting down consumer...")

	// Cancel context to stop all goroutines
	// Note: We would need to modify this to use a cancellable context
	
	// Wait for all workers to finish
	wg.Wait()

	// Save index one final time
	if err := service.SaveIndex(); err != nil {
		slog.Error("Error saving index during shutdown", "error", err)
	}

	slog.Info("Consumer shutdown complete")
	return nil
}

func consumeMessages(ctx context.Context, consumer *kafka.Consumer, jobQueue chan<- usearch.JobMessage, service *usearch.Service) {
	commitTicker := time.NewTicker(service.GetConfig().KafkaCommitInterval)
	defer commitTicker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-commitTicker.C:
			if _, err := consumer.Commit(); err != nil {
				slog.Error("Error committing offsets", "error", err)
			}
		default:
			ev := consumer.Poll(100)
			if ev == nil {
				continue
			}

			switch e := ev.(type) {
			case *kafka.Message:
				var job usearch.JobMessage
				if err := json.Unmarshal(e.Value, &job); err != nil {
					slog.Error("Error unmarshalling message", "error", err)
					continue
				}

				// Add job to queue
				select {
				case jobQueue <- job:
					// Successfully added to queue
				default:
					// Queue is full, log and continue
					slog.Warn("Job queue is full, dropping message", "job_id", job.ID)
				}
			case kafka.Error:
				slog.Error("Kafka error", "error", e)
			}
		}
	}
}

func worker(ctx context.Context, workerID int, service *usearch.Service, jobQueue <-chan usearch.JobMessage) {
	slog.Info("Worker started", "worker_id", workerID)
	
	for {
		select {
		case <-ctx.Done():
			slog.Info("Worker stopping", "worker_id", workerID)
			return
		case job := <-jobQueue:
			processJob(service, job, workerID)
		}
	}
}

func processJob(service *usearch.Service, job usearch.JobMessage, workerID int) {
	// Check if this is a delete operation
	if job.Headers.EventType == "job_deleted" || job.Headers.EventType == "job_expired" {
		if err := service.DeleteVector(job.ID); err != nil {
			slog.Error("Error deleting vector", "job_id", job.ID, "worker_id", workerID, "error", err)
		} else {
			slog.Debug("Vector deleted", "job_id", job.ID, "worker_id", workerID)
		}
		return
	}

	// Generate text for embedding
	text := generateJobText(job)

	// Get embedding from SageMaker
	vector, err := service.GetEmbedding(text)
	if err != nil {
		slog.Error("Error getting embedding", "job_id", job.ID, "worker_id", workerID, "error", err)
		return
	}

	// Create metadata
	metadata := createJobMetadata(job)

	// Add or update vector
	if err := service.AddVector(job.ID, vector, metadata); err != nil {
		slog.Error("Error adding vector", "job_id", job.ID, "worker_id", workerID, "error", err)
	} else {
		slog.Debug("Vector added/updated", "job_id", job.ID, "worker_id", workerID)
	}
}

func generateJobText(job usearch.JobMessage) string {
	return fmt.Sprintf("Title: %s\nDescription: %s\nLocation: %s", 
		job.Title, job.Description, job.Location)
}

func createJobMetadata(job usearch.JobMessage) map[string]interface{} {
	metadata := make(map[string]interface{})
	for k, v := range job.Metadata {
		metadata[k] = v
	}
	metadata["title"] = job.Title
	metadata["location"] = job.Location
	metadata["refresh_id"] = job.Headers.RefreshID
	metadata["event_type"] = job.Headers.EventType
	return metadata
}

func periodicSave(ctx context.Context, service *usearch.Service) {
	ticker := time.NewTicker(service.GetConfig().IndexSaveInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			if err := service.SaveIndex(); err != nil {
				slog.Error("Error saving index", "error", err)
			} else {
				slog.Info("Index saved successfully")
			}
		}
	}
}
