package main

import (
    "encoding/json"
    "flag"
    "fmt"
    "log"
    "net/http"
    "os"
    "os/signal"
    "syscall"
    "time"

    "github.com/gorilla/mux"
    "usearch"
)

var (
    httpPort          = flag.Int("port", 8080, "HTTP server port")
    kafkaBrokers      = flag.String("kafka-brokers", "localhost:9092", "Kafka brokers")
    kafkaTopic        = flag.String("kafka-topic", "jobs-vector-index", "Kafka topic")
    kafkaGroupID      = flag.String("kafka-group", "usearch-service", "Kafka consumer group ID")
    indexPath         = flag.String("index-path", "./data/usearch.index", "Path to save the index")
    indexDimensions   = flag.Uint("dimensions", 384, "Vector dimensions")
    indexMetric       = flag.String("metric", "cos", "Distance metric (cos, ip, l2)")
    sageMakerEndpoint = flag.String("sagemaker-endpoint", "text-embedding-endpoint", "SageMaker endpoint name")
    sageMakerRegion   = flag.String("sagemaker-region", "us-east-1", "AWS region for SageMaker")
    numWorkers        = flag.Int("workers", 4, "Number of worker goroutines")
)

func main() {
    flag.Parse()

    // Create config
    config := usearch.DefaultConfig()
    config.KafkaBrokers = *kafkaBrokers
    config.KafkaTopic = *kafkaTopic
    config.KafkaGroupID = *kafkaGroupID
    config.IndexPath = *indexPath
    config.IndexDimensions = *indexDimensions
    config.IndexMetric = *indexMetric
    config.SageMakerEndpoint = *sageMakerEndpoint
    config.SageMakerRegion = *sageMakerRegion
    config.NumWorkers = *numWorkers

    // Create service
    service, err := usearch.NewService(config)
    if err != nil {
        log.Fatalf("Failed to create USearch service: %v", err)
    }

    // Start service
    if err := service.Start(); err != nil {
        log.Fatalf("Failed to start USearch service: %v", err)
    }

    // Create router
    r := mux.NewRouter()
    r.HandleFunc("/search", func(w http.ResponseWriter, r *http.Request) {
        handleSearch(w, r, service)
    }).Methods("POST")
    r.HandleFunc("/stats", func(w http.ResponseWriter, r *http.Request) {
        handleStats(w, r, service)
    }).Methods("GET")
    r.HandleFunc("/save", func(w http.ResponseWriter, r *http.Request) {
        handleSave(w, r, service)
    }).Methods("POST")

    // Create HTTP server
    srv := &http.Server{
        Addr:         fmt.Sprintf(":%d", *httpPort),
        Handler:      r,
        ReadTimeout:  10 * time.Second,
        WriteTimeout: 10 * time.Second,
    }

    // Start HTTP server
    go func() {
        log.Printf("Starting HTTP server on port %d", *httpPort)
        if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
            log.Fatalf("Failed to start HTTP server: %v", err)
        }
    }()

    // Wait for interrupt signal
    sigChan := make(chan os.Signal, 1)
    signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)
    <-sigChan

    log.Println("Shutting down...")

    // Stop service
    if err := service.Stop(); err != nil {
        log.Printf("Error stopping service: %v", err)
    }

    log.Println("Shutdown complete")
}

type searchRequest struct {
    Text  string  `json:"text,omitempty"`
    Vector []float32 `json:"vector,omitempty"`
    Limit int     `json:"limit"`
}

func handleSearch(w http.ResponseWriter, r *http.Request, service *usearch.Service) {
    var req searchRequest
    if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
        http.Error(w, err.Error(), http.StatusBadRequest)
        return
    }

    if req.Limit <= 0 {
        req.Limit = 10
    }

    var vector []float32
    var err error

    // If vector is provided, use it directly
    if len(req.Vector) > 0 {
        vector = req.Vector
    } else if req.Text != "" {
        // Otherwise, get embedding from text
        vector, err = service.GetEmbedding(req.Text)
        if err != nil {
            http.Error(w, fmt.Sprintf("Failed to get embedding: %v", err), http.StatusInternalServerError)
            return
        }
    } else {
        http.Error(w, "Either text or vector must be provided", http.StatusBadRequest)
        return
    }

    // Search vectors
    results, err := service.SearchVectors(vector, req.Limit)
    if err != nil {
        http.Error(w, fmt.Sprintf("Search failed: %v", err), http.StatusInternalServerError)
        return
    }

    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(results)
}

func handleStats(w http.ResponseWriter, r *http.Request, service *usearch.Service) {
    stats := service.GetStats()
    
    // Get index size
    indexSize, err := service.GetIndexSize()
    if err != nil {
        http.Error(w, fmt.Sprintf("Failed to get index size: %v", err), http.StatusInternalServerError)
        return
    }
    
    // Create response
    response := struct {
        Stats    usearch.ServiceStats `json:"stats"`
        IndexSize uint                `json:"index_size"`
        Uptime   string               `json:"uptime"`
    }{
        Stats:    stats,
        IndexSize: indexSize,
        Uptime:   time.Since(stats.StartTime).String(),
    }

    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(response)
}

func handleSave(w http.ResponseWriter, r *http.Request, service *usearch.Service) {
    if err := service.SaveIndex(); err != nil {
        http.Error(w, fmt.Sprintf("Failed to save index: %v", err), http.StatusInternalServerError)
        return
    }

    w.WriteHeader(http.StatusOK)
    w.Write([]byte("Index saved successfully"))
}