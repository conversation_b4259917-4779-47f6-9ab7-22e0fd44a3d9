/**
 * Interface defining an HTTP Adapter for making HTTP requests.
 */
export interface HttpAdapter {
  /**
   * Method for making a GET request.
   * @param url The URL to make the GET request to.
   * @param headers Optional headers to include in the request.
   * @returns A promise resolving to the response data.
   */
  get(url: string, headers?: Record<string, string>): Promise<any>;

  /**
   * Method for making a POST request.
   * @param url The URL to make the POST request to.
   * @param data The data to send in the POST request body.
   * @param headers Optional headers to include in the request.
   * @returns A promise resolving to the response data.
   */
  post(url: string, data: any, headers?: Record<string, string>): Promise<any>;

  /**
   * Method for making a PATCH request.
   * @param url The URL to make the PATCH request to.
   * @param data The data to send in the PATCH request body.
   * @param headers Optional headers to include in the request.
   * @returns A promise resolving to the response data.
   */
  patch(url: string, data: any, headers?: Record<string, string>): Promise<any>;

    /**
   * Method for making a PUT request.
   * @param url The URL to make the PATCH request to.
   * @param data The data to send in the PATCH request body.
   * @param headers Optional headers to include in the request.
   * @returns A promise resolving to the response data.
   */
  put(url: string, data: any, headers?: Record<string, string>): Promise<any>;

  /**
   * Method for making a POST request via form-data.
   * @param url The URL to make the POST request to.
   * @param data The data to send in the POST request body.
   * @param headers Optional headers to include in the request.
   * @returns A promise resolving to the response data.
   */
  postForm(url: string, data: any, headers?: Record<string, string>): Promise<any>;
}
