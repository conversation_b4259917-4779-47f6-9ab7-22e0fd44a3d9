package couchbase

import (
	"publishers-xml-feed-generation-db/src/internal/config"
	"publishers-xml-feed-generation-db/src/internal/models"
	"testing"

	"github.com/couchbase/gocb/v2"
)

func TestCouchConnect(t *testing.T) {
	config.LoadTestEnvVariables()
	t.Run("Connect Couchbase DB", func(t *testing.T) {
		_, collection, err := CouchConnect()

		if err != nil {
			t.<PERSON>("Expected couchbase conextion, got [%v]", err.Error())
			t.Fail()
		}

		key := "test"
		res, err := collection.Upsert(key, &gocb.UpsertOp{
			ID:    key,
			Value: key,
		}, nil)
		if err != nil {
			t.<PERSON><PERSON>("Expected couchbase upsert work, got [%v], [%#v]", err.Error(), res)
		}
	})
}

func TestInsertJob(t *testing.T) {
	config.LoadTestEnvVariables()
	t.Run("Insert 1 Jobs", func(t *testing.T) {
		_, collection, err := CouchConnect()

		if err != nil {
			t.<PERSON><PERSON>("Expected couchbase collection, got [%v]", err.Error())
			t.Fail()
		}

		partner := "partner_test"
		country := "us"
		job := models.MonetizationJob{
			Id:       "aaaa",
			Ppc:      10,
			Cpc:      10,
			Exposure: 10,
			Display:  true,
			Bid:      1,
		}
		jobs := make([]models.MonetizationJob, 0)
		jobs = append(jobs, job)

		InsertJob(partner, country, jobs, collection)

		getResult, err := collection.Get("partner_test-aaaa-us", nil)
		if err != nil {
			t.Errorf("Expected inserted aaaa job, got [%v]", err.Error())
		}

		var key models.MonetizationJob
		err = getResult.Content(&key)
		if err != nil {
			t.Errorf("Expected parse 1 jobs, got [%v]", err.Error())
		} else {
			_, err = collection.Remove("partner_test-aaaa-us", &gocb.RemoveOptions{})
			if err != nil {
				t.Errorf("Fail Deleting doc partner_test-aaaa-us couchbase [%v]", err.Error())
			}
		}
	})

}
