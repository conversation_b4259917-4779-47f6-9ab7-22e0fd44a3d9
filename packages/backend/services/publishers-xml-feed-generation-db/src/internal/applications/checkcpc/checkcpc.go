package checkcpc

import (
	"fmt"
	"log"
	"math/big"
	"math/rand"
	"publishers-xml-feed-generation-db/src/internal/applications/exchange"
	"publishers-xml-feed-generation-db/src/internal/config"
	"publishers-xml-feed-generation-db/src/internal/models"
	"strconv"
	"strings"
	"time"
)

type SCheckCPC struct {
	Currencies *exchange.CurrencyService
	random     *rand.Rand
}

func NewCheckCPC(curr *exchange.CurrencyService) *SCheckCPC {
	source := rand.NewSource(time.Now().UnixNano())
	random := rand.New(source)
	return &SCheckCPC{
		Currencies: curr,
		random:     random,
	}
}

type CPCValid struct {
	Logic  string
	ValueL string
	ValueR string
}

func (i *SCheckCPC) CheckJobs(
	feed models.FeedChann,
	moneData []models.MonetizationJob,
	jobsMap *map[string]models.JOBChann,
	delJobsId *[]string,
	logger *log.Logger,
) ([]models.MonetizationJob, error) {
	configCPC, err := parseCPCConfig(feed)
	if err != nil {
		return []models.MonetizationJob{}, err
	}

	numL, numR, err := parseCPCValues(configCPC)
	if err != nil {
		return []models.MonetizationJob{}, err
	}
	//fmt.Println(config, numL, numR)
	finalJobs := filterJobsByCPCLogic(configCPC, moneData, jobsMap, numL, numR, feed, i.Currencies, i.random, delJobsId, logger)

	return finalJobs, nil
}

func parseCPCConfig(feed models.FeedChann) (CPCValid, error) {
	var valueL, valueR string
	if requiresCPCSplit(feed.Cpc_logic) && strings.Contains(feed.Cpc, ",") {
		parts := strings.Split(feed.Cpc, ",")
		if len(parts) == 2 {
			valueL = parts[0]
			valueR = parts[1]
		} else {
			valueL = "0"
			valueR = "0"
			// need to log, this is an error
		}
	} else {
		valueL = feed.Cpc
	}

	return CPCValid{
		Logic:  feed.Cpc_logic,
		ValueL: valueL,
		ValueR: valueR,
	}, nil
}

func requiresCPCSplit(cpcLogic string) bool {
	return strings.ContainsAny(cpcLogic, "btososeqoseqaoseqb")
}

func parseCPCValues(config CPCValid) (float32, float32, error) {
	numL, err := parseCPCValue(config.ValueL)
	if err != nil {
		return 0, 0, err
	}

	numR, err := parseCPCValue(config.ValueR)
	if err != nil {
		return 0, 0, err
	}

	return numL, numR, nil
}

func parseCPCValue(value string) (float32, error) {
	if value == "" {
		value = "0"
	}
	tmp, err := strconv.ParseFloat(value, 32)
	if err != nil {
		// need to log, this is an error
		return 0, err
	}
	return float32(tmp), nil
}

func filterJobsByCPCLogic(
	configCPC CPCValid,
	moneData []models.MonetizationJob,
	jobsMap *map[string]models.JOBChann,
	numL, numR float32,
	feed models.FeedChann,
	Currencies *exchange.CurrencyService,
	random *rand.Rand,
	delJobsId *[]string,
	logger *log.Logger,
) []models.MonetizationJob {

	j := 0
	size := len(moneData)
	finalJobs := make([]models.MonetizationJob, size)
	cpcPrecision := 4
	for _, mone := range moneData {
		cpc_check, rate, err := convertCurrency(float64(mone.CpcU), feed.Currency, cpcPrecision, Currencies, random, logger)
		if err == nil {
			if checkCPC(configCPC, cpc_check, numL, numR) {
				// if it's not a flat bid feed
				if feed.Flat == 0 {
					// put the partners currency rounding down
					mone.Cpc_display = cpc_check
				} else {
					// if it's a flat bid feed
					// if its an organic job BUT there's a rule that overwrites the organic_ppc_u (ppc=0 from monetization)
					// we use it instead
					if mone.Ppc == 0 && mone.Bid > 0 {
						cpc_check, _, err := convertCurrency(float64(mone.Bid), feed.Currency, cpcPrecision, Currencies, random, logger)
						if err != nil {
							logger.Printf("Error Calculating cpc_check for ppc=0 %s", err.Error())
						} else {
							mone.Cpc_display = cpc_check
						}
					} else {
						mone.Cpc_display = feed.Bid
					}
				}
				finalJobs[j] = mone
				j++
			} else {
				if config.CheckDBULevel(config.DBULEVEL_4) {
					logger.Printf("Job discard [%s]-currency[%s]-numL[%f]-cpc_check[%f]-numR[%f]-CpcU[%d]-Cpc_display[%f]-Rate[%f]\n", mone.Id, feed.Currency, numL, cpc_check, numR, mone.CpcU, mone.Cpc_display, rate)
				}
				*delJobsId = append(*delJobsId, mone.Id)
			}
		}
	}
	return finalJobs[:j]
}

func checkCPC(config CPCValid, cpc_check, numL, numR float32) bool {
	switch config.Logic { // ENUM('<', '>', '==', '!=', '<=', '>=', 'bt', 'os')
	case "<":
		return !(cpc_check < numL)
	case ">":
		return !(cpc_check > numL)
	case "==":
		return !(cpc_check == numL)
	case "!=":
		return !(cpc_check != numL)
	case "<=":
		return !(cpc_check <= numL)
	case ">=":
		return !(cpc_check >= numL)
	case "bt": // between
		return !(cpc_check > numL && cpc_check < numR)
	case "os": // outside
		return !(cpc_check < numL || cpc_check > numR)
	case "oseq": // outside
		return !(cpc_check <= numL || cpc_check >= numR)
	case "oseqa": // outside
		return !(cpc_check <= numL || cpc_check > numR)
	case "oseqb": // outside
		return !(cpc_check < numL || cpc_check >= numR)
	case "none": // not need
		return true
	default:
		// need to log, this is an error
		fmt.Printf("no cpc logic for feed %s_%s", config.Logic, config.Logic)
		return false
	}
}

func convertCurrency(
	cpc_u float64,
	currency string,
	cpcPrecision int,
	Currencies *exchange.CurrencyService,
	random *rand.Rand,
	logger *log.Logger,
) (float32, float64, error) {

	// get the current rate of the currency
	rate, err := Currencies.GetRate(currency)
	if err != nil {
		i := random.Intn(1000000)
		if i%1000 == 0 {
			logger.Printf("Error get Currency [%s] in convertPpcuToCpc, %s", currency, err.Error())
		}
		return 0, 0, err
	}

	// calculate cpc rounding down
	cpcCheck, _ := bcdiv((cpc_u*rate)/1000, 1, cpcPrecision)
	cpcCheckFloat, _ := strconv.ParseFloat(cpcCheck, 64)

	// return the converted cost per click
	return float32(cpcCheckFloat), rate, nil
}

func bcdiv(a, b float64, scale int) (string, error) {
	// Convertir los float64 a big.Float
	num1 := new(big.Float).SetFloat64(a)
	num2 := new(big.Float).SetFloat64(b)

	// Realizar la división
	result := new(big.Float).Quo(num1, num2)

	// Establecer la precisión
	result.SetPrec(uint(scale * 3)) // Aproximadamente 3 bits por dígito decimal

	// Formatear el resultado a la escala deseada
	return result.Text('f', scale), nil
}
