package metrics

import (
	"context"
	"log"
	"log/slog"
	"os"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/exporters/otlp/otlpmetric/otlpmetricgrpc"
	"go.opentelemetry.io/otel/exporters/prometheus"
	oMetric "go.opentelemetry.io/otel/metric"
	"go.opentelemetry.io/otel/sdk/metric"
)

var (
	meter                                oMetric.Meter
	MeterName                            = "xml-generation-service"
	GenerationDurationTotal              oMetric.Float64Histogram
	JobsSentInFeed                       oMetric.Int64Counter
	SuccessfullyCachedFeed               oMetric.Int64Counter
	SuccessfullyCachedFeedPartnerCountry oMetric.Int64Counter
	ErrorCachingFeed                     oMetric.Int64Counter
)

func InitMetrics(ctx context.Context) {
	NewMetricExporter(ctx)
	GenerationDurationTotal, _ = meter.Float64Histogram("xml_generation_duration_total", oMetric.WithDescription("Total minutes an XML generation took"))
	JobsSentInFeed, _ = meter.Int64Counter("xml_generation_jobs_added", oMetric.WithDescription("Total number of jobs added to a feed"))
	SuccessfullyCachedFeed, _ = meter.Int64Counter("xml_cache_successfully_total", oMetric.WithDescription("Total number of successfully cached feeds"))
	SuccessfullyCachedFeedPartnerCountry, _ = meter.Int64Counter("xml_cache_successfully_partner_country", oMetric.WithDescription("Total feeds cached per country/partner"))
	ErrorCachingFeed, _ = meter.Int64Counter("xml_caching_error_partner_country", oMetric.WithDescription("Errors caching feed per partner/country"))
}

func NewMetricExporter(ctx context.Context) {
	// The URL to export is set via environment variable
	// OTEL_EXPORTER_OTLP_METRICS_ENDPOINT and if not set it is  "localhost:4317"

	if os.Getenv("OTEL_EXPORTER_OTLP_METRICS_ENDPOINT") == "" {
		NewMetricExporterLocal(ctx)
		return
	}

	exporter, err := otlpmetricgrpc.New(ctx)
	if err != nil {
		slog.Error("Creating GRPC exporter", "error", err)

		return
	}

	provider := metric.NewMeterProvider(metric.WithReader(metric.NewPeriodicReader(exporter)))
	otel.SetMeterProvider(provider)
	meter = provider.Meter(MeterName)
	slog.Info("exporter created", "meter name", MeterName)
}

func NewMetricExporterLocal(ctx context.Context) {
	exporter, err := prometheus.New()
	if err != nil {
		log.Fatalf("failed to create the Prometheus stats exporter: %v", err)
	}
	provider := metric.NewMeterProvider(metric.WithReader(exporter))
	meter = provider.Meter(MeterName)
	slog.Info("local exporter created", "meter name", MeterName)
}
