import { Test, TestingModule } from "@nestjs/testing";
import { VisualCuesController } from "./../../src/visualCues/visualCues.controller";
import { VisualCuesService } from "./../../src/visualCues/visualCues.service";
import { VisualCuesDto, CreateVisualCuesDto } from "./../../src/visualCues/dto/visualCues.dto";

describe("VisualCuesController", () => {
  let controller: VisualCuesController;
  let service: VisualCuesService;

  const mockVisualCuesService = {
    findAll: jest.fn(),
    findOneById: jest.fn(),
    findOneByCode: jest.fn(),
    insertMany: jest.fn(),
    remove: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [VisualCuesController],
      providers: [
        {
          provide: VisualCuesService,
          useValue: mockVisualCuesService,
        },
      ],
    }).compile();

    controller = module.get<VisualCuesController>(VisualCuesController);
    service = module.get<VisualCuesService>(VisualCuesService);
  });

  describe("getAllVisualCues", () => {
    it("should return an array of visualCues", async () => {
      const result: VisualCuesDto[] = [];
      jest.spyOn(service, "findAll").mockResolvedValue(result);

      expect(await controller.getAllVisualCues()).toBe(result);
    });
  });

  describe("getVisualCuesById", () => {
    it("should return a visualCue by ID", async () => {
      const id = 1;
      const result: VisualCuesDto = {
        remote: 1,
        newJob: 1,
        quickApply: 1,
        salary: 1,
        estimatedSalary: 1,
        nearYou: 1,
        match: 1,
        datePosted: 1,
        topElement: 1,
        jobType: 1,
        uniqueCombination: "**********",
      };
      jest.spyOn(service, "findOneById").mockResolvedValue(result);

      expect(await controller.getVisualCuesById(id)).toBe(result);
    });
  });

  describe("getVisualCuesByCode", () => {
    it("should return a visualCue by Code", async () => {
      const id = "**********";
      const result: VisualCuesDto = {
        remote: 1,
        newJob: 1,
        quickApply: 1,
        salary: 1,
        estimatedSalary: 1,
        nearYou: 1,
        match: 1,
        datePosted: 1,
        topElement: 1,
        jobType: 1,
        uniqueCombination: "**********",
      };
      jest.spyOn(service, "findOneByCode").mockResolvedValue(result);

      expect(await controller.getVisualCuesByCode(id)).toBe(result);
    });
  });

  describe("createVisualCues", () => {
    it("should create a new visualCue", async () => {
      const payload: CreateVisualCuesDto = {
        remote: 1,
        newJob: 1,
        quickApply: 1,
        salary: 1,
        estimatedSalary: 1,
        nearYou: 1,
        match: 1,
        datePosted: 1,
        topElement: 1,
        jobType: 1,
      };
      const result: VisualCuesDto = {
        remote: 1,
        newJob: 1,
        quickApply: 1,
        salary: 1,
        estimatedSalary: 1,
        nearYou: 1,
        match: 1,
        datePosted: 1,
        topElement: 1,
        jobType: 1,
        uniqueCombination: "**********",
      };
      jest.spyOn(service, "create").mockResolvedValue(result);

      expect(await controller.createVisualCues(payload)).toBe(result);
    });
  });

  describe("updateVisualCues", () => {
    it("should update a visualCue", async () => {
      const id = 1;
      const payload: VisualCuesDto = {
        remote: 1,
        newJob: 1,
        quickApply: 1,
        salary: 1,
        estimatedSalary: 1,
        nearYou: 1,
        match: 1,
        datePosted: 1,
        topElement: 1,
        jobType: 1,
        uniqueCombination: "**********",
      };
      const result: VisualCuesDto = {
        remote: 1,
        newJob: 1,
        quickApply: 1,
        salary: 1,
        estimatedSalary: 1,
        nearYou: 1,
        match: 1,
        datePosted: 1,
        topElement: 1,
        jobType: 1,
        uniqueCombination: "**********",
      };
      jest.spyOn(service, "update").mockResolvedValue(result);

      expect(await controller.updateVisualCues(id, payload)).toBe(result);
    });
  });

  describe("deleteVisualCues", () => {
    it("should delete a visualCue", async () => {
      const id = 1;
      const result = 1;
      jest.spyOn(service, "remove").mockResolvedValue(result);

      expect(await controller.deleteVisualCues(id)).toBe(result);
    });
  });

  describe("createManyVisualCues", () => {
    it("should create new visual cues", async () => {
      const payload: any = {
        data: [
          [1, 1, 1, 1, 1, 1, 1, 1, 1, 1],
          [0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
        ]
      };
      const result = { message: `Inserted ${payload.data.length} rows` };
      jest.spyOn(service, "insertMany").mockResolvedValue(result);

      expect(await controller.createManyVisualCues(payload)).toBe(result);
    });
  });
});
