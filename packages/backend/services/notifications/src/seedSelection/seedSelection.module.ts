import { Module } from "@nestjs/common";
import { SeedSelectionService } from "./services/seedSelection.service";
import { SeedSelectionController } from "./controllers/seedSelection.controller";
import { EmailEventsModule } from "../emailEvents/emailEvents.module";
import { TrackingModule } from "../tracking/tracking.module";

/**
 * Module for handling seed selection functionalities.
 */
@Module({
  imports: [EmailEventsModule, TrackingModule], // Import the EmailEventsModule as it provides EmailEventsService
  controllers: [SeedSelectionController], // Register the controller
  providers: [SeedSelectionService], // Register the service
  exports: [SeedSelectionService],
})
export class SeedSelectionModule {}
