import { IsString, <PERSON>NotEmpty, <PERSON><PERSON><PERSON>, IsDateString, IsOptional } from "class-validator";
import { Transform } from "class-transformer";
import * as DOMPurify from "isomorphic-dompurify";

export enum EventType {
  JobSearch = "job_search",
  JobSearched = "job_searched",
  JobClick = "job_click",
  JobCardClicked = "job_card_clicked",
  JobIOA = "job_ioa",
  JobFavorite = "job_favorite",
  JobSaved = "job_saved",
  applyStarted = "apply_started",
  JobAlert = "jbalerts_customized",
  JobConversion = "job_conversion",
}

/**
 * DTO (Data Transfer Object) for user event broker messages.
 */
export class UserEventsDto {
  @IsString()
  @IsNotEmpty()
  @Transform(({ value }) => DOMPurify.sanitize(value)) // Transform decorator to sanitize with DOMPurify
  user_id: string;

  @IsEnum(EventType)
  @IsNotEmpty()
  eventType: EventType;

  @IsOptional()
  @Transform(({ value }) => DOMPurify.sanitize(value)) // Transform decorator to sanitize with DOMPurify
  country: string;

  @IsNotEmpty()
  @Transform(({ value }) => DOMPurify.sanitize(value)) // Transform decorator to sanitize with DOMPurify
  eventId: string;

  @IsNotEmpty()
  location: any;

  @IsOptional()
  @Transform(({ value }) => DOMPurify.sanitize(value)) // Transform decorator to sanitize with DOMPurify
  language: string;

  @IsString()
  @IsOptional()
  @Transform(({ value }) => DOMPurify.sanitize(value)) // Transform decorator to sanitize with DOMPurify
  jobId?: string;

  @IsOptional()
  @Transform(({ value }) => DOMPurify.sanitize(value)) // Transform decorator to sanitize with DOMPurify
  keyword: string;

  @IsDateString()
  @IsOptional()
  eventDate: Date;
}
