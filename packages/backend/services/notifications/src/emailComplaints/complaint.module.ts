import { Modu<PERSON> } from "@nestjs/common";
import { Co<PERSON>laintController } from "./complaint.controller";
import { ComplaintService } from "./complaint.service";
import { TrackingModule } from "./../tracking/tracking.module";
import { EmailUnsubscribeService } from "./../emailUnsubscribe/email-unsubscribe.service";

/**
 * Module for managing email complaints.
 */
@Module({
  imports: [TrackingModule],
  controllers: [ComplaintController],
  providers: [ComplaintService, EmailUnsubscribeService],
})
export class ComplaintModule {}
