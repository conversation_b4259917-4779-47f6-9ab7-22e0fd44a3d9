import {
  IsEmail,
  IsString,
  IsNotEmpty,
  IsOptional,
  IsArray,
  ValidateNested,
  ArrayUnique,
  ArrayNotEmpty,
  IsNumber,
} from "class-validator";
import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { Attachments } from "./attachments.dto";

/**
 * DTO (Data Transfer Object) for email data.
 */
export class EmailDto {
  /**
   * Array or single email address of the recipient.
   * @type {string | string[]}
   */
  @ApiProperty({
    description: "Array or single email address of the recipient",
    example: ["<EMAIL>", "<EMAIL>"],
  })
  @IsArray({ message: "email_address must be an array" }) // Ensure it's an array
  @ArrayNotEmpty({ message: "email_address array must not be empty" }) // Ensure the array is not empty
  @IsEmail({}, { each: true, message: "Each email must be valid" }) // Validate each email in the array
  emails: string[];
  /**
   * CC email addresses.
   * @type {string[]}
   */
  @ApiPropertyOptional({
    type: [String],
    description: "Optional array of CC email addresses.",
  })
  @IsOptional()
  @IsArray()
  @ArrayUnique()
  @ArrayNotEmpty()
  @IsEmail({}, { each: true })
  cc_addresses?: string[];

  /**
   * BCC email addresses.
   * @type {string[]}
   */
  @ApiPropertyOptional({
    type: [String],
    description: "Optional array of BCC email addresses.",
  })
  @IsOptional()
  @IsArray()
  @ArrayUnique()
  @ArrayNotEmpty()
  @IsEmail({}, { each: true })
  bcc_addresses?: string[];

  /**
   * Sender's email address.
   * @type {string | undefined}
   */
  @ApiProperty({
    description: "Sender's email address",
    example: "ses",
    required: false,
  })
  @IsString()
  sender?: string | undefined;

  /**
   * Campaign Name
   * @type {string | undefined}
   */
  @ApiProperty({
    description: "Campaign Name",
    example: "jobalerts_a",
    required: false,
  })
  @IsOptional()
  @IsString()
  campaign_name?: string | undefined;

  /**
   * Optional message content.
   * @type {string | undefined}
   */
  @ApiProperty({
    description: "Optional message content",
    example: "Hello!",
    required: false,
  })
  @IsOptional()
  @IsString()
  message: string | undefined;

  /**
   * Sender's email address.
   * @type {string | undefined}
   */
  @ApiProperty({
    description: "Sender's email address",
    example: "<EMAIL>",
  })
  @IsEmail()
  from: string | undefined;

  /**
   * Optional name of the sender.
   * @type {string | undefined}
   */
  @ApiProperty({
    description: "Optional name of the sender",
    example: "noreply",
    required: false,
  })
  @IsOptional()
  @IsString()
  fromName?: string | undefined;

  /**
   * Optional subject of the email.
   * @type {string | undefined}
   */
  @ApiProperty({
    description: "Optional subject of the email",
    example: "Test Email",
    required: false,
  })
  @IsOptional()
  @IsString()
  subject: string | undefined;

  @ApiProperty({
    description: "Optional number of sents per test",
    example: 100,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  total_emails?: number;

  /**
   * Optional array of attachments.
   * @type {Attachment[] | undefined}
   */
  @ApiProperty({
    description: "Optional array of attachments",
    type: [Attachments],
    required: false,
    example: [
      {
        fileName: "document.pdf",
        fileData: "base64encodeddata",
      },
    ],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => Attachments)
  attachments?: Attachments[];

  @IsOptional()
  headers?: Record<string, any>;

  //Optional property -non mandatory- to define the MTA Block to use
  @ApiProperty({
    description: "MTA Block to use",
    example: "MTA-100",
    required: false,
  })
  @IsOptional()
  @IsString()
  mta_block?: string;
}