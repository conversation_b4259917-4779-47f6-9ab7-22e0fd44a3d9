import { Controller, Post, Body, UsePipes, HttpCode, HttpException, Logger } from "@nestjs/common";
import { BounceService } from "./bounce.service";
import { ApiBody, ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";
import { BounceDto } from "./common/dto/bounce.dto";
import * as constantsBounces from "./common/bounceConstants";
import { CleanJsonPipe } from "./common/cleanJson.pipe";
import { BounceInfoDto } from "./common/dto/bounceInfo.dto";
import { BounceZoneMtaDataDto } from "./common/dto/bounceZoneMta.dto";
import { AmazonSesBounceDto } from "./common/dto/bounceSes.dto";
import { BounceTrackerDto } from "./../tracking/dto/bounce-tracker.dto";
import { SparkpostBounceDto } from "./common/dto/bounceSparkpost.dto";

/**
 * Controller for managing email bounces.
 */
@Controller({
  path: "email-bounces",
  version: "1", // API version: 1, 2024-06-04
})
@ApiTags("Bounces Management")
export class BounceController {
  /**
   * Creates an instance of NotificationsEmailController.
   * @constructor
   * @param {BounceService} bounceService - Instance of the bounce service.
   */
  constructor(private readonly bounceService: BounceService) {}

  /**
   * @summary Handles Zone MTA Bounces
   * @description Processes Zone MTA bounce data and saves the information.
   * @param {BounceDataDto} bounceData - The bounce data from Zone MTA.
   * @returns {Promise<any>} Any content.
   * @throws {HttpException} Throws an exception if the bounce data is invalid.
   */
  @Post("endpoint/zone-mta-handler")
  @UsePipes(new CleanJsonPipe())
  @HttpCode(200)
  @ApiOperation({ summary: "Handles Zone MTA Bounces" })
  @ApiBody({
    type: BounceZoneMtaDataDto,
    description: "The bounce data from Zone MTA",
    examples: {
      zone_mta_bounce: {
        value: {
          interface: "bounce",
          transtype: "HTTP",
          id: "18c8e6b3a3600086da",
          sessionId: "vliikjiwme5qrjob",
          to: "<EMAIL>",
          seq: "001",
          from: "<EMAIL>",
          returnPath: "<EMAIL>",
          category: "recipient",
          bouncetime: "1703196446456",
          timenow: "1703196446473",
          talentMessage:
            'DKIM-Signature: v=1; a=rsa-sha256; c=relaxed/relaxed; d=alerts.talent.com;\r\n q=dns/txt; s=mail; bh=f20wd1mC/xCkkAsxGUcpQGsTcAp7TtcBS6oP8gedM4M=;\r\n h=from:reply-to:subject:date:message-id:to:mime-version:content-type:content-transfer-encoding:list-unsubscribe:feedback-id;\r\n b=O0AGnZfVJYHF7u6sWMClwzkkq0w6WBVsZD4+qhyHayRQ11mmvHZE4FU4edJarV07HVloIOThY\r\n yzuObJcuojXqL0rX2NG0BZ/OD01anywYkm4TZrLCAI/q5Mg8M6jSZ+3U10Q1aGSwe4ZoskzwFnj\r\n pMrBKMi4XycVecbcfCLCyKP5CU+6cS1NJPcm/VhViuVCgczGndu0KWmlhKJle9GSrVHiBD9JIsR\r\n Y5fuDJrxhMkaMHXH5MdKVvDUwJ7EVHIsFQIgCUmc/jUOHmkefXBb6Yy2btqhbEk6jb9Z/gEcAKZ\r\n eUsoYJ8tVqKN1tNhN/oJLY7s7IJhLGYa5G0XbzgHGKqA==\r\nReceived: from [127.0.0.1] ([*************] ip-172-21-25-177.ec2.internal)\r\n (Authenticated sender: root)\r\n by mail388.alerts.talent.com (Talent.com MTA) with ESMTPSA id 18c8e6b3a3600086da.001\r\n for <<EMAIL>>\r\n (version=TLSv1.2 cipher=ECDHE-RSA-AES128-GCM-SHA256);\r\n Thu, 21 Dec 2023 22:07:21 +0000\r\nMessage-ID: <<EMAIL>>\r\nDate: Thu, 21 Dec 2023 17:07:21 -0500\r\nSubject: Luis, This Work From Home position is for you\r\nFrom: "Talent.com" <<EMAIL>>\r\nReply-To: "" <<EMAIL>>\r\nTo: <EMAIL>\r\nMIME-Version: 1.0\r\nContent-Type: text/html; charset=utf-8\r\nContent-Transfer-Encoding: quoted-printable\r\nX-User-ID: 123e4567-e89b-12d3-a456-426614174000\r\nX-Test-Version: 15\r\nX-Template-ID: 389\r\nX-Sender-MTA: zone_mta\r\nX-Search-ID: ec2efbd41e7226cecccf1d70\r\nX-Masksi-ID: mask380\r\nX-Language-ID: en\r\nX-Group-ID: job_alert_champion_us\r\nX-Email-ID: 3cfb71e8ad5f4a9b5cd4\r\nX-Date-Sent: 1703196440\r\nX-Country-ID: us\r\nX-Campaign-ID: jbalerts_c\r\nList-Unsubscribe: <mailto:<EMAIL>?subject=t:389-c:jbalerts_c-g:job_alert_champion_us-co:us-l:en-ds:1703196440-js_tag:not_provided-tv:15-eid:3cfb71e8ad5f4a9b5cd4-sid:ec2efbd41e7226cecccf1d70-sen:zone_mta-is:job_search>\r\nFeedback-ID: :116ab8fb61456702a551e79ab8b73a742964ca96:jobalert:mask380\r\nBIMI-Selector: v=BIMI1;s=talent;',
          response:
            "553 5.1.1 flpd594 DNSBL:RBL 521< ************ >_is_blocked.For assistance forward this <NAME_EMAIL>",
        },
      },
    },
  })
  @ApiResponse({ status: 200, description: "Bounce data processed successfully" })
  @ApiResponse({ status: 400, description: "Bad request" })
  @ApiResponse({ status: 404, description: "User ID could not be mapped" })
  async handleZoneMtaBounces(@Body() bounceData: BounceZoneMtaDataDto): Promise<any> {
    try {
      // Extracting information from bounce data
      const setBounceData = await this.bounceService.extractZoneMtaBounceInfo(bounceData);
      // Validations
      if (setBounceData.status !== 200) {
        return Promise.reject(new HttpException(setBounceData.response, 400));
      }
      // Getting user type with diagnostic code
      let bounceTypeResponse = await this.bounceService.getGeneralBounceType(
        setBounceData.payload.diagnostic_code,
      );
      // If the bounce type could not be found, we try with status code
      if (bounceTypeResponse.payload.bounce_type === "unknown") {
        bounceTypeResponse = await this.bounceService.getGeneralBounceType(
          setBounceData.payload.status_code,
        );

        // Marking if it is an undefined status code
        if (
          bounceTypeResponse.payload.bounce_type === "internal_error" &&
          bounceTypeResponse.payload.exists === "no"
        ) {
          setBounceData.payload.undefined_status_code = true;
        }
      }

      // Setting bounce type
      setBounceData.payload.bounce_type = bounceTypeResponse.payload.bounce_type;

      // Setting user ID
      const userId = setBounceData.payload.user_id ? setBounceData.payload.user_id : "";

      // Checking if we have a user ID
      //if (userId === undefined) {
      if (userId === "") {
        return Promise.reject(new HttpException("No user ID found", 404));
      }

      // Saving bounce data
      const bounceStructure: BounceDto = {
        email_id: setBounceData.payload.email_id,
        sender: "zone_mta",
        schedule_id: setBounceData.payload.schedule_id ?? setBounceData.payload.campaign_id,
        template_id: setBounceData.payload.template_id,
        experiment_id: setBounceData.payload.experiment_id ?? setBounceData.payload.group_name,
        type: setBounceData.payload.bounce_type,
        code: setBounceData.payload.status_code,
        error: setBounceData.payload.diagnostic_code,
        date_sent: setBounceData.payload.date_sent,
        date: new Date(),
      };

      // Save Bounce in postgres
      Logger.log(`Saving Bounce`);
      await this.bounceService.saveBounce(userId, bounceStructure);

      const ipRegex = /\b\d{1,3}\-\d{1,3}\-\d{1,3}\-\d{1,3}\b/
      const serverIPEval = ipRegex.exec(bounceData.talentMessage)
      const serverIP = (serverIPEval instanceof Array) ? serverIPEval[0].replaceAll('-','.') : "not_provided"

      console.log('>> Request event log for handleZoneMtaBounces ', JSON.stringify(bounceData))

      const bounceTracker: BounceTrackerDto = {
        event_type: "email_bounced",
        bounce_type: setBounceData.payload.bounce_type,
        bounce_status_code: setBounceData.payload.status_code,
        email_id: setBounceData.payload.email_id,
        user_id: userId,
        template_id: setBounceData.payload.template_id,
        campaign_name: setBounceData.payload.campaign_id,
        search_id: "",
        date_email_sent: setBounceData.payload.date_sent,
        server_ip: serverIP, //data.server_ip,
        id: bounceData.id,
        unixtime: Math.floor(Date.now() / 1000),
      };

      await this.bounceService.checkAndUpdateBounceStatus(userId);
      await this.bounceService.sendEventBounceTracker(bounceTracker);
      // Returning response
      return {
        status: 200,
        response: "Zone MTA bounce has been handled and registered",
        payload: bounceStructure,
      };
    } catch (error: any) {
      console.log(error);
      return Promise.reject(new HttpException(error.message, 400));
    }
  }

  /**
   * @summary Handles Amazon SES bounce and complaint notifications
   * @description Processes Amazon SES bounce data and saves the information
   * @param {any} bounceData The data received from Amazon SES.
   * @returns {Promise<any>} Any content.
   * @throws {HttpException} Throws an exception if something is wrong
   */
  @Post("endpoint/aws-ses-handler")
  @UsePipes(new CleanJsonPipe())
  @HttpCode(200)
  @ApiOperation({ summary: "Handles Amazon SES bounces and complaints" })
  @ApiBody({
    type: AmazonSesBounceDto,
    description: "The bounce data from Amazon SES",
    examples: constantsBounces.examplesSESData,
  })
  @ApiResponse({ status: 200, description: "Bounce data processed successfully" })
  @ApiResponse({ status: 400, description: "Bad request" })
  @ApiResponse({ status: 404, description: "User ID could not be mapped" })
  @ApiResponse({ status: 503, description: "Complaints are not handled yet" })
  async handleSesBouncesComplaints(@Body() bounceData: any): Promise<any> {
    const validURLPattern = /^https?:\/\//;
    if (!validURLPattern.test(bounceData.SubscribeURL)) {
      Logger.log(`Invalid URL for bounce data`);
      return Promise.reject(new HttpException("Invalid URL", 400));
    }

    //Confirmation URL
    //If the message contains SubscribeURL, let's ping it and confirm the subscription
    if (bounceData.SubscribeURL) {
      console.log("SubscribeURL", bounceData.SubscribeURL);
      const response = await fetch(bounceData.SubscribeURL);
      return {
        status: response.status,
        response: response,
      };
    }

    try {
      const dataNotification =
        await this.bounceService.handleSESBouncesComplaintsDelivery(bounceData);

      return {
        status: 200,
        response: `Amazon SES ${dataNotification.notificationType} has been handled and registered`,
        payload: dataNotification,
      };
    } catch (error: any) {
      return Promise.reject(new HttpException(error.message, 400));
    }
  }

  /**
   * @summary Handles Sparkpost Bounces
   * @description Processes Sparkpost bounce data and saves the information.
   * @param {SparkpostBounceDto} bounceData - The bounce data from Sparkpost.
   * @returns {Promise<any>} Any content.
   */
  @Post("endpoint/sparkpost-handler")
  @UsePipes(new CleanJsonPipe())
  @HttpCode(200)
  @ApiOperation({ summary: "Handles Sparkpost Bounces" })
  @ApiBody({
    type: SparkpostBounceDto,
    description: "The bounce data from Sparkpost",
    examples: {
      sparpost_bounce: {
        value: {
          msys: {
            message_event: {
              bounce_class: "10",
              click_tracking: true,
              customer_id: "304526",
              error_code: "550",
              event_id: "7291793852422949321",
              friendly_from: "<EMAIL>",
              initial_pixel: true,
              injection_time: "2023-10-23T11:00:34.000Z",
              ip_address: "test_ip",
              ip_pool: "default",
              mailbox_provider: "Hotmail / Outlook",
              mailbox_provider_region: "Global",
              message_id: "653152523665943f457c",
              msg_from: "msprvs1=19660DkG9GUOF=<EMAIL>",
              msg_size: "102453",
              num_retries: "0",
              open_tracking: true,
              rcpt_meta: {
                "X-Language-ID": "en",
                "X-User-ID": "123e4567-e89b-12d3-a456-426614174000",
                "X-Country-ID": "us",
                "BIMI-Selector": "v=BIMI1;s=talent;",
                "X-Campaign-ID": "jbalerts_a_us",
                "List-Unsubscribe":
                  "<mailto:<EMAIL>?subject=t:403-c:jbalerts_a_us-g:jobMatchingNewUsersTestUS-co:us-l:en-ds:**********-js_tag:job_matching_new_users_email_test_v2-tv:2-eid:c84ca82371e4cfcbacd9-sid:9ed794fcf4d5ed503458290e-sen:spark_post_microsoft_alerts-is:job_conversion>",
                "X-Template-ID": "403",
                "Feedback-ID": ":f03efa27edc9b1828a2c40a4725acfa3e38b176e:jobalert:sparkpost",
                "X-Group-ID": "not_provided",
                "X-Date-Sent": "**********",
                "X-Sender-MTA": "spark_post_microsoft_alerts",
                "X-Search-ID": "9ed794fcf4d5ed503458290e",
                "X-Email-ID": "emailidtest1234",
                "X-Test-Version": "2",
              },
              rcpt_tags: [],
              rcpt_to: "<EMAIL>",
              raw_reason:
                "550 5.5.0 Requested action not taken: mailbox unavailable (S2017062302). [VI1EUR05FT028.eop-eur05.prod.protection.outlook.com 2023-10-23T11:00:39.213Z 08DBD224F80BB658]",
              reason:
                "550 5.5.0 Requested action not taken: mailbox unavailable (S{hex}). [VI1EUR05FT028.eop-{base64} {hash}:00:39.213Z {hash}]",
              recv_method: "esmtp",
              routing_domain: "live.com",
              sending_ip: "test_ip",
              subaccount_id: "1",
              subject: "This position is for you",
              template_id: "smtp_7291772270043170402",
              template_version: "0",
              timestamp: "**********",
              transmission_id: "7291772270043170402",
              type: "bounce",
              raw_rcpt_to: "<EMAIL>",
              recipient_domain: "live.com",
            },
          },
        },
      },
    },
  })
  @ApiResponse({ status: 200, description: "Bounce data processed successfully" })
  @ApiResponse({ status: 400, description: "Bad request" })
  @ApiResponse({ status: 404, description: "User ID could not be mapped" })
  @ApiResponse({ status: 503, description: "Complaints are not handled yet" })
  async handleSparkpostBounces(
    @Body() bounceData: SparkpostBounceDto[] | SparkpostBounceDto,
  ): Promise<any> {
    try {
      // check if the data is an array or not
      if (Array.isArray(bounceData)) {
        bounceData = bounceData[0];
      }

      // Condition only to make connection with sparkpost, will be removed in the future
      if (
        bounceData.msys.message_event?.campaign_id != undefined &&
        bounceData.msys.message_event.campaign_id === "Example Campaign Name"
      ) {
        return {
          status: 200,
          response: "Test Sparkpost Complaint",
        };
      }
      // Array to store reformatted headers
      const reformattedRCPTMeta: { [key: string]: any } = {};
      // Iterate over the headers and reformat them
      Object.entries(bounceData.msys.message_event.rcpt_meta).forEach(([key, value]) => {
        reformattedRCPTMeta[key] = value;
      });

      const statusCode = RegExp(/\d\.\d\.\d/i).exec(bounceData.msys.message_event.reason);

      // Setting user ID
      const userId = reformattedRCPTMeta["X-User-ID"] ?? "";

      // Checking if we have a user ID
      if (userId === "") {
        return Promise.reject(new HttpException("No user ID found", 404));
      }

      const dateSentMs = parseInt(reformattedRCPTMeta["X-Date-Sent"], 10) * 1000;
      const dateSent = new Date(dateSentMs);

      // Saving bounce data
      const bounceStructure: BounceDto = {
        email_id: reformattedRCPTMeta["X-Email-ID"],
        sender: reformattedRCPTMeta["X-Sender-MTA"],
        schedule_id: 0,
        template_id: reformattedRCPTMeta["X-Template-ID"],
        experiment_id: "not_provided",
        type: "hard",
        code: statusCode ? statusCode[0] : "not_provided",
        error: bounceData.msys.message_event.reason,
        date_sent: dateSent,
        date: new Date(),
      };

      // Save Bounce in postgres
      Logger.log(`Saving Bounce`);
      await this.bounceService.saveBounce(userId, bounceStructure);

      // Make data to Send bounce tracker
      const bounceTracker: BounceTrackerDto = {
        event_type: "email_bounced",
        bounce_type: bounceStructure.type,
        bounce_status_code: bounceStructure.code,
        email_id: bounceStructure.email_id,
        user_id: userId,
        template_id: reformattedRCPTMeta["X-Template-ID"],
        campaign_name: bounceData.msys.message_event.campaign_id ?? reformattedRCPTMeta["X-Campaign-ID"] ?? "",
        search_id: reformattedRCPTMeta["X-Search-ID"] ?? "",
        date_email_sent: dateSentMs,
        server_ip: bounceData.msys.message_event.sending_ip,
        id: reformattedRCPTMeta["Feedback-ID"],
        unixtime: Math.floor(Date.now() / 1000),
      };

      await this.bounceService.checkAndUpdateBounceStatus(userId);
      await this.bounceService.sendEventBounceTracker(bounceTracker);

      return {
        status: 200,
        response: "Sparkpost bounce has been handled and registered",
        payload: bounceStructure,
      };
    } catch (error: any) {
      return Promise.reject(new HttpException(error.message, 400));
    }
  }

  /**
   * @summary Gets general bounce type information
   * @description Retrieves general bounce type based on the provided bounce information.
   * @param {BounceInfoDto} bounceInfo - The bounce information as a JSON string.
   * @returns {Promise<any>} The general bounce type information.
   * @throws {HttpException} Throws an exception if the bounce information is invalid.
   */
  @Post("get-bounce-type")
  @UsePipes(new CleanJsonPipe())
  @HttpCode(200)
  @ApiOperation({ summary: "Gets general bounce type information" })
  @ApiBody({
    type: BounceInfoDto,
    description: "The bounce information as a JSON string",
    examples: {
      soft_bounce: {
        value: {
          bounce_info: "4.0.0",
        },
      },
      hard_bounce: {
        value: {
          bounce_info: "5.0.0",
        },
      },
      fatal_error: {
        value: {
          bounce_info: "The email account that you tried to reach does not exist",
        },
      },
    },
  })
  @ApiResponse({ status: 200, description: "General bounce type information" })
  @ApiResponse({ status: 400, description: "Bad request" })
  async getGeneralBounceType(@Body() bounceInfo: BounceInfoDto): Promise<any> {
    try {
      const bounceType = await this.bounceService.getGeneralBounceType(bounceInfo.bounce_info);
      return bounceType;
    } catch (error: any) {
      throw new HttpException(error.message, 400);
    }
  }
}
