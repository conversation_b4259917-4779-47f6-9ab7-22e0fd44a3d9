// General bounce list
export const bouncesStatusCodeList: Record<string, string> = {
  "4.0.0": "soft",
  "4.1.0": "soft",
  "4.1.1": "soft",
  "4.1.2": "soft",
  "4.1.3": "soft",
  "4.1.4": "soft",
  "4.1.5": "soft",
  "4.1.6": "soft",
  "4.1.7": "soft",
  "4.1.8": "soft",
  "4.1.9": "soft",
  "4.2.0": "soft",
  "4.2.1": "soft",
  "4.2.2": "soft",
  "4.2.3": "soft",
  "4.2.4": "soft",
  "4.3.0": "soft",
  "4.3.1": "soft",
  "4.3.2": "soft",
  "4.3.3": "soft",
  "4.3.4": "soft",
  "4.3.5": "soft",
  "4.3.6": "soft",
  "4.4.0": "soft",
  "4.4.1": "soft",
  "4.4.2": "soft",
  "4.4.3": "soft",
  "4.4.4": "soft",
  "4.4.5": "soft",
  "4.4.6": "soft",
  "4.4.7": "soft",
  "4.5.0": "soft",
  "4.5.1": "soft",
  "4.5.2": "soft",
  "4.5.3": "soft",
  "4.5.4": "soft",
  "4.5.5": "soft",
  "4.5.6": "soft",
  "4.6.0": "soft",
  "4.6.1": "soft",
  "4.6.2": "soft",
  "4.6.3": "soft",
  "4.6.4": "soft",
  "4.6.5": "soft",
  "4.6.6": "soft",
  "4.6.7": "soft",
  "4.6.8": "soft",
  "4.6.9": "soft",
  "4.7.0": "soft",
  "4.7.1": "soft",
  "4.7.2": "soft",
  "4.7.3": "soft",
  "4.7.4": "soft",
  "4.7.5": "soft",
  "4.7.6": "soft",
  "4.7.7": "soft",
  "4.7.8": "soft",
  "4.7.9": "soft",
  "4.7.10": "hard",
  "4.7.11": "hard",
  "4.7.12": "hard",
  "4.7.13": "hard",
  "4.7.14": "hard",
  "4.7.15": "hard",
  "4.7.16": "hard",
  "5.0.0": "hard",
  "5.1.5": "hard",
  "5.2.0": "hard",
  "5.2.1": "hard",
  "5.2.2": "hard",
  "5.2.3": "hard",
  "5.2.4": "hard",
  "5.3.0": "hard",
  "5.3.1": "hard",
  "5.3.2": "hard",
  "5.3.3": "hard",
  "5.3.4": "hard",
  "5.3.5": "hard",
  "5.3.6": "hard",
  "5.4.0": "hard",
  "5.4.1": "hard",
  "5.4.2": "hard",
  "5.4.3": "hard",
  "5.4.4": "hard",
  "5.4.5": "hard",
  "5.4.6": "hard",
  "5.4.7": "hard",
  "5.5.0": "hard",
  "5.5.1": "hard",
  "5.5.2": "hard",
  "5.5.3": "hard",
  "5.5.4": "hard",
  "5.5.5": "hard",
  "5.5.6": "hard",
  "5.6.0": "hard",
  "5.6.1": "hard",
  "5.6.10": "hard",
  "5.6.2": "hard",
  "5.6.3": "hard",
  "5.6.4": "hard",
  "5.6.5": "hard",
  "5.6.6": "hard",
  "5.6.7": "hard",
  "5.6.8": "hard",
  "5.6.9": "hard",
  "5.7.0": "hard",
  "5.7.10": "hard",
  "5.7.11": "hard",
  "5.7.12": "hard",
  "5.7.13": "hard",
  "5.7.14": "hard",
  "5.7.15": "hard",
  "5.7.16": "hard",
  "5.7.2": "hard",
  "5.7.3": "hard",
  "5.7.4": "hard",
  "5.7.5": "hard",
  "5.7.6": "hard",
  "5.7.7": "hard",
  "5.7.8": "hard",
  "5.7.9": "hard",
  "5.1.0": "fatal_error",
  "5.1.1": "fatal_error",
  "5.1.2": "fatal_error",
  "5.1.3": "fatal_error",
  "5.1.4": "fatal_error",
  "5.1.6": "fatal_error",
  "5.1.7": "fatal_error",
  "5.1.8": "fatal_error",
  "5.1.9": "fatal_error",
  "5.7.1": "fatal_error",
};

// Bounces' keywords array (All of them are considered as fatal_error)
export const bouncesKeywordTrapList: string[] = [
  "The email account that you tried to reach does not exist",
  "user does not exist",
  "Host not found",
  "mailbox not found",
  "over quota",
  "disabled",
  "Relaying denied",
  "typo",
  "invalid recipient",
  "RestrictDomainsToIPAddresses",
  "RestrictDomainsToCertificate",
  "Recipient not found",
  "Recipient rejected",
  "address rejected",
  "Recipient address rejected",
  "Message rejected due to: SPF fail",
  "not authorized",
  "Relay Access Denied",
  "failed permanently",
  "permanent failure",
  "may not exist",
  "not have permission",
  "Quota exceeded",
  "permanent error",
  "Failed addresses",
  "Relaying denied",
];

// Campaign bounce rules params
export const campaignBounceRules: Record<string, any> = {
  default: { soft: 5, hard: 3, grace: 3 },
  jbalerts_d: { soft: 5, hard: 3, grace: 3 },
  jbalerts_c: { soft: 5, hard: 3, grace: 3 },
  jbalerts_b: { soft: 5, hard: 3, grace: 3 },
  jbalerts_a: { soft: 5, hard: 3, grace: 3 },
  jbalerts_a_us: { soft: 5, hard: 3, grace: 3 },
  jbalerts_sunset: { soft: 3, hard: 1, grace: 10 },
  jbalerts_customized_d: { soft: 5, hard: 3, grace: 3 },
  jbalerts_customized_c: { soft: 5, hard: 3, grace: 3 },
  jbalerts_customized_b: { soft: 5, hard: 3, grace: 3 },
  jbalerts_customized_a: { soft: 5, hard: 3, grace: 3 },
  jbalerts_customized_a_us: { soft: 5, hard: 3, grace: 3 },
  jbalerts_customized_sunset: { soft: 3, hard: 1, grace: 10 },
};

// Bounce examination results
export interface BounceError {
  message: string;
  description: string;
  bounce_type: string;
}
export interface BounceResult {
  exists: string;
  bounce_type: string;
  bounce_trap?: string;
  error?: BounceError;
}

const mail: any = {
  timestamp: "2023-06-05T11:59:00.000Z",
  source: "<EMAIL>",
  sourceArn: "arn:aws:ses:us-east-1:************:identity/talent.com",
  sendingAccountId: "************",
  messageId: "01000185e0913f8d-213c2915-42fe-4806-88f3-1a380c0e0890-000000",
  destination: ["<EMAIL>"],
  headersTruncated: false,
  headers: [
    {
      name: "From",
      value: '"Talent.com" <<EMAIL>>',
    },
    {
      name: "To",
      value: "<EMAIL>",
    },
    {
      name: "Subject",
      value: "Test email",
    },
    {
      name: "MIME-Version",
      value: "1.0",
    },
    {
      name: "Content-Type",
      value: "text/html; charset=UTF-8",
    },
    {
      name: "X-Group-ID",
      value: "not_provided",
    },
    {
      name: "X-Campaign-ID",
      value: "not_provided",
    },
    {
      name: "X-Template-ID",
      value: 11,
    },
    {
      name: "X-Country-ID",
      value: "us",
    },
    {
      name: "X-Language-ID",
      value: "en",
    },
    {
      name: "X-Test-Version",
      value: 1,
    },
    {
      name: "X-Sender-MTA",
      value: "ses",
    },
    {
      name: "X-Sunset-Type",
      value: "none",
    },
    {
      name: "X-Date-Sent",
      value: 1703196446456,
    },
    {
      name: "X-User-ID",
      value: "123e4567-e89b-12d3-a456-426614174000",
    },
    {
      name: "X-Search-ID",
      value: "447f78121418a3d4b94d5754b79a1828",
    },
    {
      name: "X-Email-ID",
      value: "123abc456def",
    },
  ],
  commonHeaders: {
    from: ["Talent.com <<EMAIL>>"],
    to: ["<EMAIL>"],
    subject: "Test email",
    messageId: "000001234567890abcdef",
  },
};

export const examplesSESData: any = {
  bounce: {
    value: {
      notificationType: "Bounce",
      bounce: {
        bounceType: "Permanent",
        bounceSubType: "General",
        bouncedRecipients: [
          {
            emailAddress: "<EMAIL>",
            action: "failed",
            status: "5.1.1",
            diagnosticCode: "smtp; 550 5.1.1 <<EMAIL>>... User unknown",
          },
        ],
        timestamp: "2023-06-05T12:00:00.000Z",
        feedbackId: "000001234567890abcde",
        remoteMtaIp: "127.0.0.1",
        reportingMTA: "mta.example.com",
      },
      mail: mail,
    },
  },
  complaint: {
    value: {
      notificationType: "Complaint",
      complaint: {
        complainedRecipients: [
          {
            emailAddress: "<EMAIL>",
          },
        ],
        timestamp: "2023-06-05T12:00:00.000Z",
        feedbackId: "000001234567890abcde",
        complaintFeedbackType: "abuse",
        userAgent: "AnyEmailClient/1.0",
      },
      mail: mail,
    },
  },
  delivery: {
    value: {
      notificationType: "Delivery",
      mail: {
        timestamp: "2024-04-27T12:34:56.789Z",
        source: "<EMAIL>",
        messageId: "01000185e0913f8d-213c2915-42fe-4806-88f3-1a380c0e0890-000000",
        destination: ["<EMAIL>"],
        headersTruncated: false,
        headers: [
          {
            name: "From",
            value: "<EMAIL>",
          },
          {
            name: "To",
            value: "<EMAIL>",
          },
          {
            name: "Subject",
            value: "Test Email",
          },
          {
            name: "X-User-ID",
            value: "123e4567-e89b-12d3-a456-426614174000",
          },
          {
            name: "X-Template-ID",
            value: 11,
          },
        ],
        commonHeaders: {
          from: ["<EMAIL>"],
          to: ["<EMAIL>"],
          subject: "Test Email",
        },
      },
      delivery: {
        timestamp: "2024-04-27T12:35:00.123Z",
        processingTimeMillis: 3000,
        recipients: ["<EMAIL>"],
        smtpResponse: "250 2.0.0 OK  1633019700 d4si12345678qko.123 - gsmtp",
        reportingMTA: "a1-2.smtp-out.amazonses.com",
      },
    },
  },
};

export const templatesId = ["6", "11", "13", "14", "15", "16", "21", "30", "36"];
