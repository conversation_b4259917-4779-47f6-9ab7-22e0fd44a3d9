import { Is<PERSON><PERSON>, <PERSON>NotEmpty, <PERSON><PERSON><PERSON><PERSON><PERSON>, A<PERSON>yNotEmpty, Is<PERSON><PERSON><PERSON> } from "class-validator";
import { Type } from "class-transformer";

/**
 *
 */
export class bounceEmployerDto {
  @IsNotEmpty()
  @IsString()
  notificationType: string;

  @IsNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => mail)
  mail: mail;

  @IsNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => bounce)
  bounce: bounce;
}

/**
 *
 */
class mail {
  @IsNotEmpty()
  @IsString()
  messageId: string;

  @IsArray()
  @ArrayNotEmpty()
  @IsString({ each: true }) // Ensures each element in the array is a string
  destination: string[];
}

/**
 *
 */
class bounce {
  @IsNotEmpty()
  @IsString()
  bounceType: string;

  @IsNotEmpty()
  @IsString()
  bounceSubType: string;

  @IsNotEmpty()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => bounceRecipients)
  bouncedRecipients: bounceRecipients[];

  @IsNotEmpty()
  @IsString()
  timestamp: string;
}

/**
 *
 */
class bounceRecipients {
  emailAddress: string;
  status: string;
  action: string;
  diagnosticCode: string;
}
