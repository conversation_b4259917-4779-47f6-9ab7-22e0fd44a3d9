import logging
from collections import defaultdict
from typing import DefaultDict

from src.app.models.prediction import (
    BulkBidReqTranformed,
    DebugInfo,
    PredictionResponse,
)
from src.app.models.user_value_data_res import (
    AllDataForUserValue,
    CohortAnalysisResult,
    CohortData,
    SocStatus,
)
from src.app.services.cohort_service import CohortService
from src.app.services.ml_model_services import MlModelService
from src.app.services.traffic_service import TrafficService
from src.app.services.user_creation_service import UserCreationService
from src.app.utils.decorators.exceptions_decorator import handle_service_exceptions

logger = logging.getLogger(__name__)


class UserValueService:
    def __init__(
        self,
        ml_model_service: MlModelService,
        user_creation_service: UserCreationService,
        traffic_service: TrafficService,
        cohort_service: CohortService,
    ):
        logger.info("UserValueService initialized")
        self.user_creation_service: UserCreationService = user_creation_service
        self.comparison_ranges: list[int] = []
        self.ml_model_service: MlModelService = ml_model_service
        self.traffic_service: TrafficService = traffic_service
        self.cohort_service: CohortService = cohort_service

    @handle_service_exceptions
    def get_user_value_prediction(
        self, request: BulkBidReqTranformed
    ) -> dict[str, PredictionResponse]:
        """
        Retrieves a user value prediction for a specified host and sources over a given prediction range.
        Args:
            request (UserValuePredictionRequest):
                prediction_day (date): The date to predict for.
                host (str): The host/domain to analyze.
                sources (List[str]): List of sources or partners to include in the prediction.
                comparison_range (int): Number of days in the past to compare against.
                prediction_range (int): Number of days into the future to predict.
        Returns:
            dict[str, PredictionResponse]: A dictionary of SOC to PredictionResponse objects.
        Raises:
            ValueError: If an error occurs during prediction or required cohort data is missing.
        """
        response: DefaultDict[str, PredictionResponse] = defaultdict(
            lambda: PredictionResponse(prediction_value=0)
        )
        data_by_soc: dict[str, AllDataForUserValue] = self._get_data(request)
        ml_model_prediction: float = self.ml_model_service.get_cpc(
            request.prediction_day
        )
        for soc, data in data_by_soc.items():
            response[soc].debug = self._get_debug_info(
                data,
                ml_model_prediction,
                request,
            )
            response[soc].prediction_value = (
                ml_model_prediction * response[soc].debug.old_prediction
            )
            response[soc].soc_status = self.user_creation_service.soc_status.get(
                soc, SocStatus.LESS_DATA
            )
        logger.info(f"UserValueService: Response: {response}")
        return response

    def _get_debug_info(
        self,
        data: AllDataForUserValue,
        ml_model_prediction: float,
        request: BulkBidReqTranformed,
    ) -> DebugInfo:
        """
        Retrieves debug information for a user value prediction.
        """
        debug = DebugInfo()
        debug.traffic_cpc = self.traffic_service.get_recent_cpc(data, request)
        (
            cohort_comp_comp,
            cohort_pred_comp,
            cohort_pred_pred,
        ) = self._get_cohort_ranges(data, request)
        debug.click_delta = self._get_click_delta(
            recent_cohort=cohort_comp_comp, prediction_cohort=cohort_pred_comp
        )
        debug.predicted_clicks = cohort_pred_pred.clicks_per_user * debug.click_delta
        debug.old_prediction = debug.predicted_clicks * debug.traffic_cpc.cpc
        debug.ml_model_prediction = ml_model_prediction
        debug.cohort_comp_comp = cohort_comp_comp
        debug.cohort_pred_pred = cohort_pred_pred
        debug.cohort_pred_comp = cohort_pred_comp
        return debug

    def _get_click_delta(
        self,
        recent_cohort: CohortData,
        prediction_cohort: CohortData,
    ) -> float:
        """
        Calculates the performance of users who created their accounts recently,
        versus users who created their accounts a while back.
        """
        recent_clicks_per_user = recent_cohort.clicks_per_user
        prediction_clicks_per_user = prediction_cohort.clicks_per_user
        if recent_clicks_per_user == 0:
            raise ValueError(
                "Recent cohort clicks per user is zero, cannot calculate delta."
            )
        return prediction_clicks_per_user / recent_clicks_per_user

    def _get_data(
        self, request: BulkBidReqTranformed
    ) -> dict[str, AllDataForUserValue]:
        """
        Retrieves data for a user value prediction.
        Returns:
            A dictionary keyed by SOC, with values being AllDataForUserValue objects.
            AllDataForUserValue contains lists of UsersCreated, TrafficByDay and ClicksByRegistrationDate objects.
        """
        logger.info("UserValueService: Fetching data for User Value Prediction")
        data_by_soc: dict[str, AllDataForUserValue] = defaultdict(AllDataForUserValue)
        data_by_soc = self.user_creation_service.fetch(request, data_by_soc)
        data_by_soc = self.traffic_service.fetch(request, data_by_soc)
        data_by_soc = self.cohort_service.fetch(request, data_by_soc)
        result = {
            soc: data
            for soc, data in data_by_soc.items()
            if data.created_users
            and data.clicks_by_reg_day
            and data.traffic
        }
        return result

    def _get_cohort_ranges(
        self, data: AllDataForUserValue, request: BulkBidReqTranformed
    ) -> tuple[CohortData, CohortData, CohortData]:
        cohort_results: CohortAnalysisResult = (
            self.cohort_service.transform_clicks_by_ranges(data, request)
        )
        cohort_comp_comp = cohort_results.get_required_cohort(
            request.comparison_range, request.comparison_range
        )
        cohort_pred_comp = cohort_results.get_required_cohort(
            request.prediction_range, request.comparison_range
        )
        cohort_pred_pred = cohort_results.get_required_cohort(
            request.prediction_range, request.prediction_range
        )
        return (
            cohort_comp_comp,
            cohort_pred_comp,
            cohort_pred_pred,
        )
