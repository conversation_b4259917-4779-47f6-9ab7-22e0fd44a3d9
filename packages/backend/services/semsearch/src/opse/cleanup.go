package opse

import (
	"bytes"
	"context"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"log/slog"
	"net/http"
	"sync"
	"time"

	"golang.org/x/sync/errgroup"

	"github.com/aws/aws-sdk-go-v2/aws"
	v4 "github.com/aws/aws-sdk-go-v2/aws/signer/v4"
	"github.com/aws/aws-sdk-go-v2/config"
)

type CleanupAction string

const (
	ActionUpdate CleanupAction = "_update_by_query"
	ActionDelete CleanupAction = "_delete_by_query"
)

type cleanupRequest struct {
	Slice  map[string]interface{} `json:"slice,omitempty"`
	Query  map[string]interface{} `json:"query"`
	Script map[string]interface{} `json:"script,omitempty"`
}

type jobStatus struct {
	Completed bool `json:"completed"`
	Task      struct {
		Status struct {
			SliceId          int `json:"slice_id,omitempty"`
			Total            int `json:"total"`
			Updated          int `json:"updated,omitempty"`
			Deleted          int `json:"deleted,omitempty"`
			VersionConflicts int `json:"version_conflicts,omitempty"`
		} `json:"status"`
	} `json:"task"`
	Response struct {
		Failures []any `json:"failures"`
	} `json:"response"`
}

func createSigner() (aws.Config, *v4.Signer, error) {
	cfg, err := config.LoadDefaultConfig(context.TODO())
	if err != nil {
		return aws.Config{}, nil, err
	}

	signer := v4.NewSigner()
	return cfg, signer, nil
}

func RunCleanupAction(ctx context.Context, endpoint, index string, refreshId int, action CleanupAction, sliceCount int) error {
	var (
		log        = slog.Default()
		jobs       = make([]string, sliceCount)
		jobErrors  = make([]error, sliceCount)
		jobMutex   sync.Mutex
		jobResults = make(map[int]*jobStatus)
	)

	cfg, signer, err := createSigner()
	if err != nil {
		return fmt.Errorf("failed to create signer: %w", err)
	}

	// We just ran bunch of updates, force index refresh before running the cleanup.
	refreshIndex(ctx, cfg, signer, index, endpoint, action)

	// start all slices
	g := new(errgroup.Group)
	for i := 0; i < sliceCount; i++ {
		sliceID := i
		g.Go(func() error {
			jobID, err := startJob(ctx, cfg, signer, endpoint, index, refreshId, action, sliceID, sliceCount)
			if err != nil {
				log.Error("Failed to start job", slog.Int("slice", sliceID), slog.String("error", err.Error()))
				jobErrors[sliceID] = err
				return err
			}
			log.Info("Started job", slog.Int("slice", sliceID), slog.String("job_id", jobID))
			jobMutex.Lock()
			jobs[sliceID] = jobID
			jobMutex.Unlock()
			return nil
		})
	}
	if err := g.Wait(); err != nil {
		return fmt.Errorf("failed to start one or more jobs: %w", err)
	}

	// poll for status
	done := make(chan struct{})
	go func() {
		ticker := time.NewTicker(30 * time.Second)
		defer ticker.Stop()
		for {
			select {
			case <-ctx.Done():
				return
			case <-ticker.C:
				log.Info("Polling job statuses")
				allDone := true
				for i, jobID := range jobs {
					if jobID == "" {
						continue
					}
					status, err := getJobStatus(ctx, cfg, signer, endpoint, jobID)
					if err != nil {
						log.Error("Failed to get job status", slog.Int("slice", i), slog.String("job_id", jobID), slog.String("error", err.Error()))
						continue
					}
					if status.Completed {
						log.Info("Job completed", slog.Int("slice", i), slog.String("job_id", jobID), slog.Any("status", status.Task.Status))
						jobMutex.Lock()
						jobResults[i] = status
						jobs[i] = ""
						jobMutex.Unlock()
					} else {
						log.Info("Job in progress", slog.Int("slice", i), slog.String("job_id", jobID), slog.Any("status", status.Task.Status))
						allDone = false
					}
				}
				if allDone {
					close(done)
					return
				}
			}
		}
	}()

	<-done
	slog.Info("All jobs completed for cleanup", "action", action)
	return nil
}

func startJob(ctx context.Context, cfg aws.Config, signer *v4.Signer, endpoint, index string, refreshId int, action CleanupAction, sliceID, sliceCount int) (string, error) {
	url := ""

	req := cleanupRequest{
		Slice: map[string]interface{}{
			"id":  sliceID,
			"max": sliceCount,
		},
		Query: map[string]interface{}{
			"match_all": map[string]interface{}{},
		},
	}

	if action == ActionUpdate {
		url = fmt.Sprintf("%s/%s/%s?wait_for_completion=false&conflicts=proceed", endpoint, index, action)

		req.Script = map[string]interface{}{
			"source": "ctx._source.headers.refresh_id = params.status",
			"lang":   "painless",
			"params": map[string]interface{}{
				"status": refreshId,
			},
		}
	} else if action == ActionDelete {
		url = fmt.Sprintf("%s/%s/%s?wait_for_completion=false", endpoint, index, action)

		req.Query = map[string]interface{}{
			"bool": map[string]interface{}{
				"should": []interface{}{
					map[string]interface{}{
						"range": map[string]interface{}{
							"headers.refresh_id": map[string]interface{}{
								"lt": refreshId, // assume refreshId is a variable of appropriate type
							},
						},
					},
					map[string]interface{}{
						"bool": map[string]interface{}{
							"must_not": map[string]interface{}{
								"exists": map[string]interface{}{
									"field": "headers.refresh_id",
								},
							},
						},
					},
				},
				"minimum_should_match": 1,
			},
		}
	} else {
		return "", fmt.Errorf("Unknown action requested")
	}

	body, err := json.Marshal(req)
	if err != nil {
		return "", err
	}

	resp, err := performSignedRequest(ctx, cfg, signer, "POST", url, body)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	var result struct {
		Task string `json:"task"`
	}
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return "", fmt.Errorf("failed to decode response: %w", err)
	}
	return result.Task, nil
}

func refreshIndex(ctx context.Context, cfg aws.Config, signer *v4.Signer, index string, endpoint string, action CleanupAction) error {
	url := fmt.Sprintf("%s/%s/%s", endpoint, index, "_refresh")

	resp, err := performSignedRequest(ctx, cfg, signer, "POST", url, nil)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	var result struct {
		Shards struct {
			Total      int `json:"total"`
			Successful int `json:"successful"`
			Failed     int `json:"failed"`
		} `json:"_shards"`
	}
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return fmt.Errorf("failed to decode response: %w", err)
	}
	slog.Info("Refreshed indexes", "result", result)
	return nil
}

func getJobStatus(ctx context.Context, cfg aws.Config, signer *v4.Signer, endpoint, taskID string) (*jobStatus, error) {
	url := fmt.Sprintf("%s/_tasks/%s", endpoint, taskID)
	resp, err := performSignedRequest(ctx, cfg, signer, "GET", url, nil)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	var status jobStatus
	if err := json.NewDecoder(resp.Body).Decode(&status); err != nil {
		return nil, err
	}
	return &status, nil
}

func performSignedRequest(ctx context.Context, cfg aws.Config, signer *v4.Signer, method, url string, body []byte) (*http.Response, error) {
	var bodyReader io.ReadSeeker
	var payloadHash string

	if body != nil {
		bodyReader = bytes.NewReader(body)

		// Compute SHA-256 hash of the body
		hash := sha256.Sum256(body)
		payloadHash = hex.EncodeToString(hash[:])
	} else {
		// Use SHA-256 hash of empty string
		bodyReader = bytes.NewReader([]byte{})
		payloadHash = "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855"
	}

	req, err := http.NewRequestWithContext(ctx, method, url, bodyReader)
	if err != nil {
		return nil, err
	}
	req.Header.Set("Content-Type", "application/json")

	// Retrieve AWS credentials
	creds, err := cfg.Credentials.Retrieve(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to retrieve AWS credentials: %w", err)
	}

	// Sign the HTTP request
	err = signer.SignHTTP(ctx, creds, req, payloadHash, "es", cfg.Region, time.Now())
	if err != nil {
		return nil, fmt.Errorf("failed to sign HTTP request: %w", err)
	}

	client := &http.Client{}
	return client.Do(req)
}
