apiVersion: apps/v1
kind: Deployment
metadata:
  name: publishers-quality-summary-api-deployment
  labels:
    app: publishers-quality-summary-api
spec:
  replicas: 1
  selector:
    matchLabels:
      app: publishers-quality-summary-api
  template:
    metadata:
      labels:
        app: publishers-quality-summary-api
    spec:
      serviceAccountName: aws-role-serviceaccount
      containers:
        - name: publishers-quality-summary-api
          image: AWS_ACCOUNT_ID_TAG.dkr.ecr.us-east-1.amazonaws.com/services/publishers-quality-summary-api:IMAGE_TAG
          imagePullPolicy: Always
          env:
            - name: ENVIRONMENT
              value: ENVIRONMENT_TAG
          ports:
            - containerPort: 8080
