import { Test, TestingModule } from "@nestjs/testing";

import { EmployersClientApiController } from "../src/employers-client-api.controller";
import { EmployersClientApiService } from "../src/employers-client-api.service";

describe("EmployersClientApiController", () => {
  let controller: EmployersClientApiController;
  // Mock service for controller interactions
  let service: EmployersClientApiService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [EmployersClientApiController],
      providers: [EmployersClientApiService],
    }).compile();

    controller = module.get<EmployersClientApiController>(EmployersClientApiController);
    // Access mock service
    service = module.get<EmployersClientApiService>(EmployersClientApiService);
  });

  // Test if the controller is properly created and injectable
  it("should be defined", () => {
    expect(controller).toBeDefined();
  });

  // Tests for controller methods, indirectly testing service calls:

  // Test for create method
  describe("create", () => {
    it("should call the service create method", () => {
      jest.spyOn(service, "create").mockReturnValue("This action adds a new entry");

      const result = controller.create();

      expect(service.create).toHaveBeenCalled();
      expect(result).toBe("This action adds a new entry"); // Assert controller response
    });
  });

  // Test for findAll method
  describe("findAll", () => {
    it("should call the service findAll method", () => {
      jest.spyOn(service, "findAll").mockReturnValue("This action returns all entries");

      const result = controller.findAll();

      expect(service.findAll).toHaveBeenCalled();
      expect(result).toBe("This action returns all entries"); // Assert controller response
    });
  });

  // Test for findOne method
  describe("findOne", () => {
    it("should call the service findOne method with parsed id", () => {
      const id = 1;
      jest.spyOn(service, "findOne").mockReturnValue(`This action returns a #${id} entry`);

      controller.findOne(id.toString()); // Pass string id to controller

      expect(service.findOne).toHaveBeenCalledWith(id); // Assert service call with parsed id
      expect(controller.findOne(id.toString())).toBe(`This action returns a #${id} entry`); // Assert controller response
    });
  });

  // Test for update method
  describe("update", () => {
    it("should call the service update method with parsed id", () => {
      const id = 2;
      jest.spyOn(service, "update").mockReturnValue(`This action updates a #${id} entry`);

      controller.update(id.toString()); // Pass string id to controller

      expect(service.update).toHaveBeenCalledWith(id); // Assert service call with parsed id
      expect(controller.update(id.toString())).toBe(`This action updates a #${id} entry`); // Assert controller response
    });
  });

  // Test for remove method
  describe("remove", () => {
    it("should call the service remove method with parsed id", () => {
      const id = 3;
      jest.spyOn(service, "remove").mockReturnValue(`This action removes a #${id} entry`);

      controller.remove(id.toString()); // Pass string id to controller

      expect(service.remove).toHaveBeenCalledWith(id); // Assert service call with parsed id
      expect(controller.remove(id.toString())).toBe(`This action removes a #${id} entry`); // Assert controller response
    });
  });
});
