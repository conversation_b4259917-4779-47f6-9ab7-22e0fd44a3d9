apiVersion: apps/v1
kind: Deployment
metadata:
  name: publishers-performance-log-api-deployment
spec:
  replicas: 1
  revisionHistoryLimit: 5
  minReadySeconds: 60
  selector:
    matchLabels:
      app: publishers-performance-log-api
  template:
    metadata:
      labels:
        app: publishers-performance-log-api
    spec:
      serviceAccountName: aws-role-s3
      nodeSelector:
        app: pub-rec-api-svc
      tolerations:
        - key: "app"
          operator: "Equal"
          value: "pub-rec-api-svc"
          effect: "NoSchedule"
      containers:
        - name: publishers-performance-log-api
          image: AWS_ACCOUNT_ID_TAG.dkr.ecr.us-east-1.amazonaws.com/publishers-performance-log-api:IMAGE_TAG
          imagePullPolicy: Always
          env:
            - name: ENVIRONMENT
              value: ENVIRONMENT_TAG
          ports:
            - containerPort: 8080
