package types

type RevenueCostDataRaw struct {
	Job_feedcode      string  `json:"job_feedcode"`
	Job_campaign_id   int64   `json:"campaign_id"`
	Cache_utm_product string  `json:"cache_utm_product"`
	Cache_utm_medium  string  `json:"cache_utm_medium"`
	PPC               float64 `json:"ppc"`
	CPC               float64 `json:"cpc"`
	TheoreticalCost   float64 `json:"theoretical_cost"`
}

type CadcApiDataRaw struct {
	Job_feedcode      string  `json:"job_feedcode"`
	Job_campaign_id   int64   `json:"campaign_id"`
	Cache_utm_product string  `json:"cache_utm_product"`
	Cache_utm_medium  string  `json:"cache_utm_medium"`
	TheoreticalCost   float64 `json:"theoretical_cost"`
	BidShare          float64 `json:"avg_bid_share"`
}

type RevenueCostData struct {
	CampaignID      string
	Medium          string
	Publisher       string
	Feedcode        string
	PPC             float64
	CPC             float64
	TheoreticalCost float64
}

type ContributionResult struct {
	CampaignID   string
	Medium       string
	Publisher    string
	Contribution float64
	Feedcode     string
}

type CadcApiData struct {
	CampaignID      string
	Medium          string
	Publisher       string
	Feedcode        string
	TheoreticalCost float64
	BidShare        float64
}

type ContributionError struct {
	CampaignID   string
	ErrorMessage string
}

type MVData struct {
	JobFeedcode     string
	CampaignID      int64
	Medium          string
	Publisher       string
	PPC             float64
	CPC             float64
	TheoreticalCost float64
	AvgBidshare     float64
}

type RuleUpsertEvent struct {
	Timestamp     int64
	RuleEventType string
	Campaign      string
	Channel       string
	Source        string
	Exposure      int
}
