package types

type SpikeManagerRequest struct {
	CampaignID string `json:"campaign_id"`
	Channel    string `json:"channel"`
	Partner    string `json:"partner"`
	Feedcode   string `json:"feedcode"`
}

type SpikeManagerResponse struct {
}

type SpikeClickData struct {
	Clicks     float64 `json:"jobredirects"`
	ClicksDay  string  `json:"dayclick"`
	CampaignID int     `json:"campaign_id"`
	// AvgCPC     float64
}
