
# Options for analysis running.
run:
  # Which dirs to skip: issues from them won't be reported.
  # Can use regexp here: `generated.*`, regexp is applied on full path,
  # including the path prefix if one is set.
  # Default dirs are skipped independently of this option's value (see skip-dirs-use-default).
  # "/" will be replaced by current OS file path separator to properly work on Windows.
  # Default: []
  skip-dirs:
    - scratches

  # Allow multiple parallel golangci-lint instances running.
  # If false, golangci-lint acquires file lock on start.
  # Default: false
  allow-parallel-runners: true

  # Show statistics per linter.
  # Default: false
  show-stats: true


# output configuration options
output:
  # Format: colored-line-number|line-number|json|colored-tab|tab|checkstyle|code-climate|junit-xml|github-actions|teamcity
  #
  # Multiple can be specified by separating them by comma, output can be provided
  # for each of them by separating format name and path by colon symbol.
  # Output path can be either `stdout`, `stderr` or path to the file to write to.
  # Example: "checkstyle:report.xml,json:stdout,colored-line-number"
  #
  # Default: colored-line-number
  format: colored-line-number

  # Sort results by: filepath, line and column.
  # Default: false
  sort-results: true

linters-settings:
  depguard:
    rules:
      prevent_unmaintained_packages:
        list-mode: lax # allow unless explicitely denied
        files:
          - $all
          - "!$test"
        allow:
          - $gostd
        deny:
          - pkg: io/ioutil
            desc: "replaced by io and os packages since Go 1.16: https://tip.golang.org/doc/go1.16#ioutil"

linters:
  # Disable specific linter
  # https://golangci-lint.run/usage/linters/#disabled-by-default
  disable:
    - protogetter
    - gomoddirectives

  # Enable presets.
  # https://golangci-lint.run/usage/linters
  # Default: []
  presets:
    - bugs
    - comment
    - complexity
    - error
    - format
    - import
    - metalinter
    - module
    - performance
    - sql
    - style
    - test
    - unused

  # Enable only fast linters from enabled linters set (first run won't be fast)
  # Default: false
  fast: true


issues:
  # List of regexps of issue texts to exclude.
  #
  # But independently of this option we use default exclude patterns,
  # it can be disabled by `exclude-use-default: false`.
  # To list all excluded by default patterns execute `golangci-lint run --help`
  #
  # Default: https://golangci-lint.run/usage/false-positives/#default-exclusions
  exclude:
    - "G108: Profiling endpoint is automatically exposed on /debug/pprof"

  # Excluding configuration per-path, per-linter, per-text and per-source
  exclude-rules:
    # Exclude some linters from running on tests files.
    - path: _test\.go
      linters:
        - gocyclo
        - errcheck
        - dupl
        - gosec
