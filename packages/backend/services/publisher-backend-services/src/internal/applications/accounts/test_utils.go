package accounts

import (
	"log"
	"os"
	database "talent/libs/database/src"
	"time"
)

func tearUp(db *database.MySQLDB) {
	_, err := db.Exec("ATTACH DATABASE ':memory:' AS publishers")
	if err != nil {
		log.Fatal("Failed to attach database: ", err)
	}
	createTableSQL := `
    CREATE TABLE publishers.accounts (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		name TEXT DEFAULT NULL UNIQUE,
		contact_email TEXT DEFAULT NULL,
		contact_name TEXT DEFAULT NULL,
		language TEXT DEFAULT NULL,
		account_currency TEXT DEFAULT NULL,
		payment_currency TEXT DEFAULT NULL,
		account_owner TEXT DEFAULT NULL,
		secondary_account_owner TEXT DEFAULT NULL,
		date_created TEXT DEFAULT NULL,
		status TEXT CHECK(status IN ('active', 'inactive')) DEFAULT 'active',
		regional_owner TEXT DEFAULT NULL
	);`
	// Execute the create table SQL statement
	db.Exec(createTableSQL)
	createTableSQL = `
    CREATE TABLE publishers.accounts_products (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		account_id INTEGER NOT NULL,
		producttype_id INTEGER NOT NULL,
		product_name VARCHAR(50) NOT NULL,
		source VARCHAR(50) NOT NULL,
		status TEXT NOT NULL DEFAULT 'active',
		encoded_token VARCHAR(100),
		date_created DATETIME DEFAULT CURRENT_TIMESTAMP,
		date_updated DATETIME DEFAULT NULL,
		UNIQUE (source, producttype_id)
	  );`

	// Execute the create table SQL statement
	db.Exec(createTableSQL)
}
func newDB() (*database.MySQLDB, error) {
	os.Setenv("ENVIRON", "dev")
	os.Setenv("DB_TYPE", "sqlite")
	db := database.NewMySQLDB()
	return db, nil
}
func createDummyRecord(db *database.MySQLDB) {
	db.Exec("INSERT INTO publishers.accounts (name, contact_email, contact_name, language, account_currency, payment_currency, account_owner, secondary_account_owner, date_created, status, regional_owner) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",

		"Updated Publisher", // name
		"V001gd_w0HnsNGVNQvtMS2dd0YSPLnRs3hYYX3zSPPBvpLI6JssHSEV6vxYAhsvLBGT6", // contact_email
		"Jane Doe",                               // contact_name
		"English",                                // language
		"EUR",                                    // account_currency
		"EUR",                                    // payment_currency
		"Jane Doe",                               // account_owner
		"John Doe",                               // secondary_account_owner
		time.Now().Format("2006-01-02 15:04:05"), // date_created
		"active",                                 // status
		"EMEA")
}
