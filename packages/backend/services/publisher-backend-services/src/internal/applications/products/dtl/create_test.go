package dtl

import (
	"bytes"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"net/http/httptest"
	"os"
	database "talent/libs/database/src"
	"testing"
	"time"

	"publisher-backend-services/src/internal/config"
	"publisher-backend-services/src/internal/models"

	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"

	libDtl "talent/libs/publishers-shared-libs/src/accounts-dtl-config"
	libAccProduct "talent/libs/publishers-shared-libs/src/accounts-products"
)

func tearUp(db *database.MySQLDB) {
	_, err := db.Exec("ATTACH DATABASE ':memory:' AS publishers")
	if err != nil {
		log.Fatal("Failed to attach database: ", err)
	}

	createTableSQL := `
    CREATE TABLE publishers.accounts_dtl_config (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		host TEXT DEFAULT NULL,
		source TEXT DEFAULT NULL,
		keyword TEXT DEFAULT NULL,
		location TEXT DEFAULT NULL,
		price INTEGER DEFAULT NULL,
		currency TEXT DEFAULT NULL,
		active INTEGER CHECK(active IN ('1', '0')) DEFAULT '1',
		email TEXT DEFAULT NULL,
		display_ads INTEGER DEFAULT NULL,
		account_product_id INTEGER DEFAULT NULL
	);`

	// Execute the create table SQL statement
	_, err = db.Exec(createTableSQL)
	if err != nil {
		log.Fatal("Failed to create table: ", err)
	}

	createTableSQL = `
    CREATE TABLE publishers.accounts_products (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		account_id INTEGER NOT NULL,
		producttype_id INTEGER NOT NULL,
		product_name VARCHAR(50) NOT NULL,
		source VARCHAR(50) NOT NULL,
		status TEXT NOT NULL DEFAULT 'active',
		encoded_token VARCHAR(100),
		date_created DATETIME DEFAULT CURRENT_TIMESTAMP,
		date_updated DATETIME DEFAULT NULL,
		UNIQUE (source, producttype_id)
	  );`

	// Execute the create table SQL statement
	_, err = db.Exec(createTableSQL)
	if err != nil {
		log.Fatal("Failed to create table: ", err)
	}
}

func newDB() (*database.MySQLDB, error) {
	os.Setenv("ENVIRON", "dev")
	os.Setenv("DB_TYPE", "sqlite")
	db := database.NewMySQLDB()
	if err := recover(); err != nil {
		return nil, fmt.Errorf(fmt.Sprintf("failed to create a new instance of the publisherDB: %v", err))
	}
	return db, nil
}

func createDummyRecord(db *database.MySQLDB) {
	db.Exec("INSERT INTO publishers.accounts_products (id, account_id, producttype_id, source, status, encoded_token, date_created) VALUES (?, ?, ?, ?, ?, ?, ?)",
		1,
		1,
		productType,
		"test",
		"active",
		"encoded_token",
		time.Now(),
	)

	emails := []string{"null"}
	emailJSON, err := json.Marshal(emails)
	if err != nil {
		fmt.Println("Error al convertir el array de correos a JSON:", err)
		return
	}
	db.Exec("INSERT INTO publishers.accounts_dtl_config (host, source, keyword, location, price, currency, active, email, display_ads, account_product_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
		"neuvoo.ca",
		"dtl_jobrapido",
		"*",
		"*",
		0,
		"EUR",
		1,
		string(emailJSON),
		1,
		1,
	)
}

func createDummyRecordNoHost(db *database.MySQLDB) {
	db.Exec("INSERT INTO publishers.accounts_products (id, account_id, producttype_id, source, status, encoded_token, date_created) VALUES (?, ?, ?, ?, ?, ?, ?)",
		1,
		1,
		productType,
		"test",
		"active",
		"encoded_token",
		time.Now(),
	)

	emails := []string{"null"}
	emailJSON, err := json.Marshal(emails)
	if err != nil {
		fmt.Println("Error al convertir el array de correos a JSON:", err)
		return
	}
	db.Exec("INSERT INTO publishers.accounts_dtl_config (host, source, keyword, location, price, currency, active, email, display_ads, account_product_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
		"",
		"dtl_jobrapido",
		"*",
		"*",
		0,
		"EUR",
		1,
		string(emailJSON),
		1,
		1,
	)
}

func TestCreate(t *testing.T) {
	// Create a new Echo instance
	e := echo.New()

	body := `{
		"Product" : {
			"source":"partner_dtl_1",
			"account_id": 52,
			"status": "active"
		},
		"Config": {
			"host": "ca",
			"keyword":"*",
			"location":"*",
			"price":200,
			"currency":"CAD",
			"email":["<EMAIL>"],
			"display_ads":1
		}
	}`

	bBody := []byte(body)

	// Create a new HTTP request
	req := httptest.NewRequest(http.MethodPost, "/v1/products/dtl", bytes.NewBuffer(bBody))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()
	c := e.NewContext(req, rec)
	c.Set("executionId", "1")

	db, err := newDB()
	assert.NoError(t, err)
	db.Connect()
	config.CreateDummyTableForTests(db)

	product := NewHandler(db)

	err = product.Create(c)

	// Assert the response
	assert.NoError(t, err)
	assert.Equal(t, http.StatusCreated, rec.Code)

	// Additional assertions based on the expected response
}

func TestCreateFail(t *testing.T) {
	// Create a new Echo instance
	e := echo.New()
	source := "partner_dtl_1"
	cp := models.DtlProduct{
		Product: libAccProduct.AccountProduct{
			AccountId: 1,
			Status:    "active",
		},
		Config: libDtl.PublisherAccountDtl{
			Host:     "ca",
			Source:   &source,
			Price:    200,
			Currency: "CAD",
		},
	}

	// Create a new request body
	jsonBody, _ := json.Marshal(cp)

	// Create a new HTTP request
	req := httptest.NewRequest(http.MethodPost, "/v1/products/xml/create", bytes.NewBuffer(jsonBody))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()
	c := e.NewContext(req, rec)

	db, err := newDB()
	assert.NoError(t, err)

	db.Connect()
	tearUp(db)

	product := NewHandler(db)

	err = product.Create(c)

	// Assert the response
	assert.NoError(t, err)
	assert.Equal(t, http.StatusBadRequest, rec.Code)
}
