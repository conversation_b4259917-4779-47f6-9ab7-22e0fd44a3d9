package coreg

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"publisher-backend-services/src/internal/applications/products"
	"publisher-backend-services/src/internal/models"
	"publisher-backend-services/src/internal/tools"
	"strconv"

	"github.com/labstack/echo/v4"

	libAccCoreg "talent/libs/publishers-shared-libs/src/accounts-coreg-config"
	libAccProduct "talent/libs/publishers-shared-libs/src/accounts-products"

	_ "publisher-backend-services/docs"
)

// @Summary Update a coreg product
// @Description Update an existing coreg product and its configuration
// @Tags Coreg
// @Accept json
// @Produce json
// @Param id path int true "Coreg Product ID"
// @Param product body models.CoregProduct true "Updated coreg product details"
// @Success 200 {object} models.DefaultResponse
// @Failure 400 {object} models.DefaultResponse
// @Failure 404 {object} models.DefaultResponse
// @Failure 500 {object} models.DefaultResponse
// @Router /products/coreg/{id} [patch]
func (h *Handler) Update(c echo.Context) error {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		return tools.SendErrorResponse(c, http.StatusBadRequest, nil, fmt.Errorf("id must be a number"))
	}
	params, err := getPostParamsCreate(c, true)
	if err != nil {
		return tools.SendErrorResponse(c, http.StatusBadRequest, nil, err)
	}
	// Coreg Config Record
	coregProduct := libAccCoreg.NewAccountsCoregConfig(h.DB)

	cfilter := libAccCoreg.ListFilter{
		ID: id,
	}

	err = coregProduct.Read(context.Background(), &cfilter)

	if err != nil {
		return tools.SendErrorResponse(c, http.StatusBadRequest, nil, err)
	}

	// Product Record
	product := libAccProduct.NewAccountProduct(h.DB)
	//product.AccountId = params.Product.AccountId
	product.ProductTypeId = params.Product.ProductTypeId
	product.Source = params.Product.Source
	product.ProductName = params.Product.ProductName
	if product.ProductName == "" {
		product.ProductName = params.Product.Source
	}
	if params.Product.AccountId != 0 {
		product.AccountId = params.Product.AccountId
	}
	product.Status = params.Product.Status
	product.EncodedToken = tools.GenerateHash(params.Product.Source)

	err = product.Update(context.Background())

	if err != nil {
		return tools.SendErrorResponse(c, http.StatusBadRequest, nil, err)
	}

	//Coreg Config Fields
	coregProduct.Source = params.Product.Source
	coregProduct.Price = params.Config.Price
	coregProduct.Currency = params.Config.Currency
	coregProduct.Emails = params.Config.Emails

	err = coregProduct.Update(context.Background())

	if err != nil {
		return tools.SendErrorResponse(c, http.StatusBadRequest, nil, err)
	}

	go func(c echo.Context, params models.CoregProduct) {
		paramsJSON, _ := json.Marshal(params)
		eventData := map[string]interface{}{
			"url":     c.Request().URL.String(),
			"updated": json.RawMessage(paramsJSON),
		}
		products.TrackEvent(kafkaEventUpdate, models.EventTrackerParams{
			Id:    c.Get("executionId").(string),
			Error: 0,
			Event: eventData,
		})
	}(c, models.CoregProduct{
		Product: product,
		Config:  coregProduct,
	})

	//msg := fmt.Sprintf("Product coreg update successfully with id %d", params.Product.Id)
	return c.NoContent(http.StatusNoContent)
}
