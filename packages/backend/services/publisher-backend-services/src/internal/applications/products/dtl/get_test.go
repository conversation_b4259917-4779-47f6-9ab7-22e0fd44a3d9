package dtl

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"

	"testing"

	"publisher-backend-services/src/internal/config"
	"publisher-backend-services/src/internal/models"

	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
)

func TestGet(t *testing.T) {
	db, err := newDB()
	assert.NoError(t, err)

	db.Connect()
	config.CreateDummyTableForTests(db)
	config.CreateDummyRecords(db, 4)

	p := NewHandler(db)
	// Create a new Echo instance
	e := echo.New()

	e.GET("/v1/products/dtl/:id", p.Get)

	// Create a new HTTP request
	req := httptest.NewRequest(http.MethodGet, "/v1/products/dtl/1", nil)
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()

	e.ServeHTTP(rec, req)

	var response models.ProductResponse
	if err := json.Unmarshal(rec.Body.Bytes(), &response); err != nil {
		t.Errorf("Expected json but no response %v", err)
	}

	if response.StatusCode != http.StatusOK {
		t.Errorf("Expected code 00 but got %s", response.StatusCode)
		t.Errorf("Error Mesagge: %s", response.Message)
	}
}

func TestGetFailBadRequest(t *testing.T) {
	db, err := newDB()
	assert.NoError(t, err)

	db.Connect()
	config.CreateDummyTableForTests(db)

	p := NewHandler(db)

	// Create a new Echo instance
	e := echo.New()

	e.GET("/v1/products/dtl/:id", p.Get)

	// Create a new HTTP request
	req := httptest.NewRequest(http.MethodGet, "/v1/products/dtl/gasda", nil)
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()

	e.ServeHTTP(rec, req)

	var response models.ProductResponse
	if err := json.Unmarshal(rec.Body.Bytes(), &response); err != nil {
		t.Errorf("Expected json but no response %v", err)
	}

	if response.StatusCode != http.StatusBadRequest {
		t.Errorf("Expected code 01 but got %s", response.StatusCode)
		t.Errorf("Error Mesagge: %s", response.Message)
	}
}

func TestGetFailNotFound(t *testing.T) {
	db, err := newDB()
	assert.NoError(t, err)

	db.Connect()
	config.CreateDummyTableForTests(db)

	p := NewHandler(db)

	// Create a new Echo instance
	e := echo.New()

	e.GET("/v1/products/dtl/:id", p.Get)

	// Create a new HTTP request
	req := httptest.NewRequest(http.MethodGet, "/v1/products/dtl/222", nil)
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()

	e.ServeHTTP(rec, req)

	var response models.ProductResponse
	if err := json.Unmarshal(rec.Body.Bytes(), &response); err != nil {
		t.Errorf("Expected json but no response %v", err)
	}

	if response.StatusCode != http.StatusNotFound {
		t.Errorf("Expected code 02 but got %s", response.StatusCode)
		t.Errorf("Error Mesagge: %s", response.Message)
	}
}
