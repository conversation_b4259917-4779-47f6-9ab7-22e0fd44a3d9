import { Injectable, Inject, HttpException, HttpStatus } from "@nestjs/common";
import { Repository } from "typeorm";
import { InjectRepository } from "@nestjs/typeorm";
import { SeoSearches } from "../../entities/seo-searches.entity";
import { SeoLocations } from "../../entities/seo-locations.entity";
import { CACHE_MANAGER } from "@nestjs/cache-manager";
import { Cache } from "cache-manager";
import { FaqService } from "../../faq/src/faq.service";
import { SeoJobClassification } from "../../entities/seo-job-classification.entity";
import { normalize, determineHost }  from '../../utils/tools-utils';

@Injectable()
export class RelatedSearchesService {
  constructor(
    @InjectRepository(SeoSearches)
    private seoSearches: Repository<SeoSearches>,
    @InjectRepository(SeoLocations)
    private seoLocations: Repository<SeoLocations>,
    @InjectRepository(SeoJobClassification)
    private seoJobClassification: Repository<SeoJobClassification>,
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
  ) { }

  private cacheTTL = 43200; //12 hours

  /**
 * Fetches related searches based on country, language, location, and socCode.
 * It first checks for cached data, then fetches data from various services if not found.
 * 
 * @param country - The country code.
 * @param language - The language code.
 * @param location - (Optional) The location identifier.
 * @param socCode - (Optional) The socCode identifier.
 * @returns A Promise resolving to the related searches data.
 */
  async getRelatedSearches(
    country: string,
    language: string,
    location?: string,
    socCode?: string
  ): Promise<any> {
    const cacheKey = `related_searches_${country}_${language}_${normalize(location)}_${socCode}`;
    const cachedData = await this.cacheManager.get<any>(cacheKey);
    if (cachedData) return cachedData;

    const faqService = new FaqService(this.seoSearches, this.seoLocations, this.cacheManager);
    const locationData = location ? await faqService.getLocationData(country, language, location) : null;
    const socIdData = socCode ? await this.getSocIdData(country, language, socCode) : null;
    const isLocationValid = Boolean(locationData);

    let relationIdsList: any[] = [];
    let results: any[] = [];
    const baseQuery = { orderBy: 'google_search_volume', limit: 30 };
    let type = "related";

    if (isLocationValid && socIdData) {
      // Fetch related searches for valid location and socCode
      relationIdsList = [socIdData.id, ...socIdData.related_occupations];
      results = await this.fetchSearchResults(country, { ...baseQuery, related_occupations: relationIdsList, location_id: locationData.id });
    }

    if (results.length < 10) {
      // Adjust type and query if results are insufficient
      if (isLocationValid) {
        type = "popular";
        results = await this.fetchSearchResults(country, { ...baseQuery, location_id: locationData.id });
      } else if (socIdData) {
        relationIdsList = [socIdData.id, ...socIdData.related_occupations];
        results = await this.fetchSearchResults(country, { ...baseQuery, related_occupations: relationIdsList });
      } else {
        type = "popular";
        results = await this.fetchSearchResults(country, { ...baseQuery, country, language });
      }
    }

    if (results.length < 10) {
      // Final fallback to popular searches if results are still insufficient
      type = "popular";
      results = await this.fetchSearchResults(country, { ...baseQuery, country, language, socCode: socCode ?? '' });
    }

    const sortedResults = this.sortResultsByRelationId(results, relationIdsList);
    const finalResults = this.extractFields(sortedResults);
    const output = { type, data: finalResults };

    if (!output.data.length) {
      const errorMessage = `No related searches found.`;
      await this.cacheManager.set(cacheKey, { error: errorMessage }, this.cacheTTL);
      throw new HttpException({ status: HttpStatus.NOT_FOUND, error: errorMessage }, HttpStatus.NOT_FOUND);
    }

    await this.cacheManager.set(cacheKey, output, this.cacheTTL);
    return output;
  }



  extractFields(results: any[]): any[] {
    return results.map(result => ({
      keyword: result.seo_searches_keyword,
      canonical: result.canonical,
    }));
  }

  async getSocIdData(country: string, language: string, socCode: string) {

    // Construct a unique cache key based on the country, language, and normalized location.
    const cacheKey = `soc_id_data_${country}_${language}_${socCode}`;
    // Attempt to retrieve location data from cache.
    let socIdData = await this.cacheManager.get<any>(cacheKey);

    // If the data is not in the cache, fetch it from the database.
    if (!socIdData) {
      socIdData = await this.seoJobClassification.findOne({
        where: {
          occupation_id: socCode,
          data_source_country: country,
          data_source_language: language,
        }
      });
      // If the location data is still not found after querying the database, throw an error.
      if (!socIdData) {
        throw new HttpException({
          status: HttpStatus.NOT_FOUND,
          error: "Soc code not found."
        }, HttpStatus.NOT_FOUND);
      }

      // Cache the newly retrieved location data to reduce future database queries.
      await this.cacheManager.set(cacheKey, socIdData, this.cacheTTL);
    }

    // Return the location data, either from cache or from the database fetch.
    return socIdData;
  }

  // Function to fetch search results using the fetchSearchResults function
  async fetchSearchResults(
    country: string,
    query: any
  ): Promise<any> {
    const host = determineHost(country);
    const queryBuilder = this.seoSearches.createQueryBuilder('seo_searches')
      .innerJoinAndSelect('seo_searches.location', 'location')
      .innerJoinAndSelect('seo_searches.job_classification', 'job_classification')
      .addSelect(['seo_searches.relation_id',
        'seo_searches.location_id',
        `'https://' || :host || '.talent.com/jobs/k-' || seo_searches.keyword_normalized || '-l-' || location.location_url_normalized AS canonical`,
      ]);

    queryBuilder.setParameter("host", host);
    queryBuilder.where("seo_searches.status = :status", { status: 1 });
    queryBuilder.andWhere("seo_searches.search_type = :searchType", { searchType: "kl" });

    if (query.related_occupations) {
      queryBuilder.andWhere("seo_searches.relation_id IN (:...relationIds)", { relationIds: query.related_occupations })
    }

    if (query.country && query.language) {
      queryBuilder.andWhere('location.country_code = :country', { country: query.country })
      queryBuilder.andWhere('location.language_code = :language', { language: query.language });
      queryBuilder.andWhere('location.region_1 = :emptyRegion', { emptyRegion: '' });
    }

    if (query.location_id) {
      queryBuilder.andWhere('seo_searches.location_id = :location', { location: query.location_id });
    }

    queryBuilder
      .orderBy('seo_searches.google_search_volume', 'DESC')
      .limit(query.limit || 30);

    return await queryBuilder.getRawMany();
  }

  sortResultsByRelationId(results: any[], ids: string[]): any[] {
    if (ids.length === 0) {
      return results;
    }
    const idOrderMap = new Map(ids.map((id, index) => [id, index]));

    return results.sort((a, b) => {
      const indexA = idOrderMap.get(a.job_classification_id) ?? Infinity;
      const indexB = idOrderMap.get(b.job_classification_id) ?? Infinity;
      return indexA - indexB;
    });
  }
}
