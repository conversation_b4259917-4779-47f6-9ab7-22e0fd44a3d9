package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/url"
	"os"
	"time"

	"budgeting/src/cmd/event-consumer/models"

	"github.com/joho/godotenv"
	"github.com/segmentio/kafka-go"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

type LoadEnvFunc func(filenames ...string) error

type KafkaReader interface {
	FetchMessage(ctx context.Context) (kafka.Message, error)
	CommitMessages(ctx context.Context, msgs ...kafka.Message) error
	Close() error
}

type DB interface {
	Create(value interface{}) *gorm.DB
}

func initialize(loadEnv LoadEnvFunc) (context.Context, error) {
	if os.Getenv("ENVIRON") != "remote" {

		err := loadEnv()
		if err != nil {
			log.Print("Error loading .env file" + err.Error())
			return nil, err
		}
	}

	return context.Background(), nil
}

var postgresConnection = func(ctx context.Context) (*gorm.DB, error) {
	user := os.Getenv("CPC_TRSX_USER")
	password := os.Getenv("CPC_TRSX_PASSWORD")
	host := os.Getenv("CPC_TRSX_HOST")
	port := os.Getenv("CPC_TRSX_PORT")

	if user == "" || password == "" || host == "" || port == "" {
		return nil, fmt.Errorf("no env variables found")
	}

	// Building PostgreSQL URL
	posgresUrl := fmt.Sprintf(
		"postgres://%s:%s@%s:%s/%s",
		user,
		url.QueryEscape(password),
		host,
		port,
		"talent",
	)

	// Connect to PostgreSQL using gorm
	db, err := gorm.Open(postgres.Open(posgresUrl), &gorm.Config{
		SkipDefaultTransaction: true,
		PrepareStmt:            true,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to connect to postgres: %v", err)
	}

	if os.Getenv("ENVIRON") != "remote" {
		db.AutoMigrate(&models.CpcTrsxJobredirect{})
	}

	// Get generic database object sql.DB to use its functions
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("failed to configure the postgres connection: %v", err)
	}

	// Configure the postgres connection to handle multiple connections by the use of a pool
	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetMaxOpenConns(100)
	sqlDB.SetConnMaxLifetime(time.Hour)

	return db, err
}

var kafkaConnection = func(ctx context.Context) (*kafka.Reader, error) {
	kafkaBroker1 := os.Getenv("REDIRECT_KAFKA_BROKER_1")
	kafkaBroker2 := os.Getenv("REDIRECT_KAFKA_BROKER_2")
	kafkaBroker3 := os.Getenv("REDIRECT_KAFKA_BROKER_3")
	kafkaGroupId := os.Getenv("REDIRECT_KAFKA_GROUP")
	kafkaTopic := os.Getenv("REDIRECT_KAFKA_QUEUE")

	if kafkaBroker1 == "" || kafkaBroker2 == "" || kafkaBroker3 == "" || kafkaGroupId == "" || kafkaTopic == "" {
		return nil, fmt.Errorf("no env variables found")
	}

	// Configure Kafka reader
	reader := kafka.NewReader(kafka.ReaderConfig{
		Brokers:     []string{kafkaBroker1, kafkaBroker2, kafkaBroker3},
		GroupID:     kafkaGroupId,
		Topic:       kafkaTopic,
		StartOffset: kafka.LastOffset,
		MinBytes:    10e3, // 10KB
		MaxBytes:    10e6, // 10MB
	})

	fmt.Println("Starting Kafka consumer...")

	return reader, nil
}

func run(ctx context.Context, db DB, reader KafkaReader, done chan bool) error {
	for {
		select {
		case <-done:
			return nil
		default:
			m, err := reader.FetchMessage(context.Background())
			if err != nil {
				log.Printf("Failed to fetch message: %v", err)
				time.Sleep(time.Second) // Wait for a second before retrying
				continue
			}

			if os.Getenv("ENVIRON") != "remote" {
				log.Printf("Message received: key = %s, value = %s", string(m.Key), string(m.Value))
			}

			event := &models.CpcTrsxJobredirect{}
			err = json.Unmarshal([]byte(m.Value), &event)

			if err != nil {
				log.Println("Error unmarshalling message", "err", err)
				continue
			}

			if event.JobId != "" {

				result := db.Create(&event)
				if result.Error != nil {
					log.Printf("Failed to save message: %v", err)
					continue
				}
			}

			if err := reader.CommitMessages(context.Background(), m); err != nil {
				log.Printf("Failed to commit message: %v", err)
			}
		}
	}
}

func main() {
	var loadEnv LoadEnvFunc = godotenv.Load
	ctx, err := initialize(loadEnv)

	if err != nil {
		log.Fatalf("Failed to initialize application: %v", err)
	}

	db, err := postgresConnection(ctx)
	if err != nil {
		log.Fatalf("Failed to connect to PostgreSQL: %v", err)
	}

	reader, err := kafkaConnection(ctx)
	if err != nil {
		log.Fatalf("Failed to connect to PostgreSQL: %v", err)
	}
	defer reader.Close()

	done := make(chan bool)
	err = run(ctx, db, reader, done)
	if err != nil {
		log.Fatalf("Failed to start the service: %v", err)
	}
}
