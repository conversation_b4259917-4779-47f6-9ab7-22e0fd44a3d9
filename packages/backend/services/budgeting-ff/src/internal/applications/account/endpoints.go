package accounts

import (
	models "budgeting/src/internal/models"
	"budgeting/src/internal/shared/types"
	"context"

	"github.com/go-kit/kit/endpoint"
)

/*** Accounts Billing Budgeting ***/
func makeGetAccountBudgetBillingEndpoint(service AccountsService) endpoint.Endpoint {
	return func(ctx context.Context, request interface{}) (interface{}, error) {
		accountRequest := request.(models.AccountRetrievalRequest)
		account, err := service.GetAccount(ctx, accountRequest)
		if err != nil {
			return nil, err
		}

		return &models.AccountResponse{
			Account: account,
		}, nil
	}
}

/*** Accounts Budgeting ***/
func makeGetAccountBudgetEndpoint(service AccountsService) endpoint.Endpoint {
	return func(ctx context.Context, request interface{}) (interface{}, error) {
		accountRequest := request.(models.AccountRetrievalRequest)
		accountBudget, err := service.GetAccountBudget(ctx, accountRequest)
		if err != nil {
			return nil, err
		}

		return accountBudget, nil
	}
}

func makeUpdateAccountBudgetEndpoint(service AccountsService) endpoint.Endpoint {
	return func(ctx context.Context, request interface{}) (interface{}, error) {
		accountRequest := request.(types.AccountBudgetRequest)
		err := service.UpdateAccountBudget(ctx, accountRequest)
		if err != nil {
			return nil, err
		}
		return "Account updated successfully", nil
	}
}

func makeCreateAccountBudgetEndpoint(service AccountsService) endpoint.Endpoint {
	return func(ctx context.Context, request interface{}) (interface{}, error) {
		accountRequest := request.(types.AccountBudgetRequest)
		err := service.CreateAccountBudget(ctx, accountRequest)
		if err != nil {
			return nil, err
		}
		return "Account created successfully", nil
	}
}
