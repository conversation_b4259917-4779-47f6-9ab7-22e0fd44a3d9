package redis

import (
	"context"
	"os"
	"time"

	redis "github.com/go-redis/redis/v8"
)

type (
	RedisClient interface {
		Get(ctx context.Context, key string) (string, error)
		Set(ctx context.Context, key string, value []byte, expiration time.Duration) error
		GetHash(ctx context.Context, key string) (map[string]string, error)
		SetHash(ctx context.Context, key string, fields map[string]interface{}, expiration time.Duration) error
		IncrementHashField(ctx context.Context, key string, field string, increment int64) error
	}

	RedisClientImpl struct {
		Client *redis.Client
	}
)

var (
	pass string = ""
	db   int    = 0
)

func NewRedisClient() RedisClient {
	rdb := redis.NewClient(&redis.Options{
		Addr:     os.Getenv("REDIS_HOST") + ":" + os.Getenv("REDIS_PORT"),
		Password: pass,
		DB:       db,
	})

	return &RedisClientImpl{Client: rdb}
}

func (r RedisClientImpl) Get(ctx context.Context, key string) (string, error) {
	val, err := r.Client.Get(ctx, key).Result()
	if err != nil {
		return "", err
	}
	return val, nil
}

func (r RedisClientImpl) Set(ctx context.Context, key string, value []byte, expiration time.Duration) error {
	err := r.Client.Set(ctx, key, value, expiration).Err()
	if err != nil {
		return err
	}
	return nil
}

func (r RedisClientImpl) GetHash(ctx context.Context, key string) (map[string]string, error) {
	val, err := r.Client.HGetAll(ctx, key).Result()
	if err != nil {
		return nil, err
	}
	return val, nil
}

func (r RedisClientImpl) SetHash(ctx context.Context, key string, fields map[string]interface{}, expiration time.Duration) error {
	_, err := r.Client.HSet(ctx, key, fields).Result()
	if err != nil {
		return err
	}

	errExp := r.Client.Expire(ctx, key, expiration).Err()
	if errExp != nil {
		return errExp
	}

	return nil
}

func (r RedisClientImpl) IncrementHashField(ctx context.Context, key string, field string, number int64) error {
	ttl, _ := r.Client.TTL(ctx, key).Result()

	_, err := r.Client.HIncrBy(ctx, key, field, number).Result()
	if err != nil {
		return err
	}

	if ttl > 0 {
		r.Client.Expire(ctx, key, ttl).Err()
	}

	return nil
}
