apiVersion: apps/v1
kind: Deployment
metadata:
  name: publishers-validity-rules-deployment
spec:
  replicas: 1
  revisionHistoryLimit: 5
  minReadySeconds: 60
  selector:
    matchLabels:
      app: publishers-validity-rules
  template:
    metadata:
      labels:
        app: publishers-validity-rules
    spec:
      serviceAccountName: aws-role-s3
      nodeSelector:
        app: pub-validity-rules-svc
      tolerations:
        - key: "app"
          operator: "Equal"
          value: "pub-validity-rules-svc"
          effect: "NoSchedule"
      containers:
        - name: publishers-validity-rules
          image: AWS_ACCOUNT_ID_TAG.dkr.ecr.us-east-1.amazonaws.com/services/publishers-validity-rules:IMAGE_TAG
          imagePullPolicy: Always
          env:
            - name: ENVIRONMENT
              value: ENVIRONMENT_TAG
          ports:
            - containerPort: 8080
