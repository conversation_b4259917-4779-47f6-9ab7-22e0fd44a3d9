package cache

import (
	"fmt"
	"os"
	"time"

	"github.com/go-redis/redis"
	"github.com/joho/godotenv"
)

const (
	DB = 0
)

type RedisCache struct {
	client *redis.Client
}

func NewRedisCache() *RedisCache {
	godotenv.Load()
	return &RedisCache{
		client: redis.NewClient(&redis.Options{
			Addr:     os.Getenv("REDIS_HOST") + ":" + os.Getenv("REDIS_PORT"),
			Password: os.<PERSON>env("REDIS_PASS"),
			DB:       DB,
		})}
}

func (r *RedisCache) Get(key string) ([]byte, error) {
	val, err := r.client.Get(key).Bytes()
	if err != nil {
		if err == redis.Nil {
			err = fmt.Errorf("item not found")
		}
		return nil, err
	}
	return val, nil
}

func (r *RedisCache) Set(key string, value []byte, expiration time.Duration) error {
	return r.client.Set(key, value, expiration).Err()
}
