from confluent_kafka import Consumer, KafkaError
import boto3
import json
import time
import os
import sys
from concurrent.futures import ThreadPoolExecutor, as_completed

# ------------------------
# General Config
# ------------------------

FIREHOSE_STREAM = os.getenv("FIREHOSE_STREAM")

KAFKA_SERVER = os.getenv("KAFKA_SERVER")
KAFKA_GROUP_ID = os.getenv("KAFKA_GROUP_ID")
KAFKA_TOPICS = [t.strip() for t in os.getenv("KAFKA_TOPICS").split(",") if t.strip()]

BATCH_SIZE = 10000
BATCH_TIMEOUT = 60
MAX_BATCH_FIREHOSE = 500
MAX_WORKERS = 20

# ------------------------
# Kafka Config
# ------------------------

kafka_config = {
    'bootstrap.servers': KAFKA_SERVER,
    'group.id': KAFKA_GROUP_ID,
    'auto.offset.reset': 'earliest',
    'enable.auto.commit': True,

    # Recommended tuning for AWS MSK (network-friendly)
    'session.timeout.ms': 30000,             # Consumer session timeout (30 seconds)
    'max.poll.interval.ms': 300000,          # Max time between polls (5 minutes)
    'request.timeout.ms': 30000,             # Request timeout for broker requests (30 seconds)
    'socket.timeout.ms': 60000,              # Socket timeout (60 seconds)
    'reconnect.backoff.max.ms': 10000        # Max backoff time between reconnections (10 seconds)
}
consumer = Consumer(kafka_config)
consumer.subscribe(KAFKA_TOPICS)

# ------------------------
# Firehose Client
# ------------------------

firehose_client = boto3.client('firehose')

# ------------------------
# Mapper Function
# ------------------------

def map_batch(batch):
    values = []

    for record in batch:
        value = record["value"]
        
        # Remove html text
        value.pop("source_jobdesc_html", None)
        value.pop("source_jobdesc_text", None)
        value.pop("enrich_jobdesc_html", None)

        # Flatten employer_data into value with 'employer_' prefix
        employer_data = record.get("employer_data", {})
        for key, val in employer_data.items():
            value[f"employer_{key}"] = val

        # Enrich value with selected headers metadata
        value["partial_update"] = record["headers"]["partial_update"]
        value["kafka_offset"] = record["headers"]["kafka_offset"]
        value["kafka_partition"] = record["headers"]["kafka_partition"]
        value["kafka_topic"] = record["headers"]["kafka_topic"]
        value["kafka_timestamp"] = record["headers"]["kafka_timestamp"]
        
        value["event_type"] = record["headers"]["event_type"]
        value["unixtime"] = value.get("employer_execution_time")

        # Remove empty string fields
        for k in list(value.keys()):
            if value[k] == "":
                del value[k]

        values.append(value)

    return values

def clean_batch(batch):
    values = []

    for record in batch:
        value = record

        # Remove html text
        value.pop("source_jobdesc_html", None)
        value.pop("source_jobdesc_text", None)
        value.pop("enrich_jobdesc_html", None)

        # Enrich value with selected headers kafka info
        value["kafka_offset"] = record["headers"]["kafka_offset"]
        value["kafka_partition"] = record["headers"]["kafka_partition"]
        value["kafka_topic"] = record["headers"]["kafka_topic"]
        value["kafka_timestamp"] = record["headers"]["kafka_timestamp"]

        value.pop("headers", None)
        
        # Remove empty string fields
        for k in list(value.keys()):
            if value[k] == "":
                del value[k]

        values.append(value)

    return values

# ------------------------
# Firehose Ingest
# ------------------------

def send_batch(records, stream_name, retries=3, backoff_base=1):
    attempt = 0
    total_records = len(records)

    while attempt <= retries:
        try:
            response = firehose_client.put_record_batch(DeliveryStreamName=stream_name, Records=records)
            failed_count = response.get("FailedPutCount", 0)

            # If nothing failed, return early
            if failed_count == 0:
                return total_records, 0

            # If there are failures, collect failed records
            failed_records = []
            for i, res in enumerate(response['RequestResponses']):
                if 'ErrorCode' in res:
                    failed_records.append(records[i])

            print(f"Retry attempt {attempt + 1}: {failed_count} records failed", flush=True)

            # Prepare to retry
            records = failed_records
            attempt += 1
            time.sleep(backoff_base ** attempt)  # exponential backoff

        except Exception as e:
            print(f"Batch send failed on attempt {attempt + 1}: {e}", flush=True)
            attempt += 1
            time.sleep(backoff_base ** attempt)

    # Final fallback
    print(f"Final retry failed after {retries} attempts. Dropping {len(records)} records.", flush=True)
    return total_records, len(records)

def firehose_ingest(values, stream_name):
    total_records = 0
    total_failed = 0
    total_size_bytes = 0
    start = time.time()

    with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
        futures = []

        for i in range(0, len(values), MAX_BATCH_FIREHOSE):
            batch = values[i:i + MAX_BATCH_FIREHOSE]
            records = []
            batch_size = 0
            for record in batch:
                data = json.dumps(record) + '\n'
                record_size = len(data.encode('utf-8'))
                batch_size += record_size
                records.append({'Data': data})
            total_size_bytes += batch_size
            futures.append(executor.submit(send_batch, records, stream_name))

        for future in as_completed(futures):
            sent, failed = future.result()
            total_records += sent
            total_failed += failed

    elapsed = time.time() - start

    print(f"\n--- BATCH METRICS ---", flush=True)
    print(f"Total Records Sent    : {total_records}", flush=True)
    print(f"Total Records Failed  : {total_failed}", flush=True)
    print(f"Total Batch Size (MB) : {total_size_bytes / (1024*1024):.2f} MB", flush=True)
    print(f"Total Time (s)        : {elapsed:.2f} seconds", flush=True)
    print(f"----------------------\n", flush=True)

total_processed = 0

try:
    while True:
        batch = []
        start_time = time.time()

        while len(batch) < BATCH_SIZE and (time.time() - start_time) < BATCH_TIMEOUT:
            try:
                msg = consumer.poll(1.0)
                if msg is None:
                    continue
                if msg.error():
                    if msg.error().code() != KafkaError._PARTITION_EOF:
                        print(f"Kafka error: {msg.error()}", flush=True)
                    continue

                data = json.loads(msg.value().decode('utf-8'))

                if "headers" not in data or not isinstance(data["headers"], dict):
                    data["headers"] = {}

                data["headers"]["kafka_offset"] = msg.offset()
                data["headers"]["kafka_partition"] = msg.partition()
                data["headers"]["kafka_topic"] = msg.topic()
                data["headers"]["kafka_timestamp"] = msg.timestamp()[1]

                batch.append(data)

            except Exception as e:
                print(f"Error processing message: {e}", flush=True)
                continue

        print(f"Batch ready with {len(batch)} messages", flush=True)

        # ------------------------
        # Mapping or clean
        # ------------------------

        if (FIREHOSE_STREAM == 'firehose-stream-jobs-event'):
            values = clean_batch(batch)           
        else:
            values = map_batch(batch)
        
        total_processed += len(values)

        # ------------------------
        # Send to Firehose
        # ------------------------

        firehose_ingest(values, FIREHOSE_STREAM)

except Exception as e:
    print(f"General error in the consumer: {e}", flush=True)

finally:
    consumer.close()
    print("Consumer closed successfully", flush=True)
