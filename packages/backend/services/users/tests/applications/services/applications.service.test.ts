import axios from "axios";
import { Test, TestingModule } from "@nestjs/testing";
import { HttpException, HttpStatus, NotFoundException } from "@nestjs/common";

import { Status } from "../../../src/applications/enums/status.enum";
import { Interested } from "../../../src/applications/enums/interested.enum";
import { Applications } from "../../../src/applications/entities/application.entity";
import { ApplicationsService } from "../../../src/applications/services/applications.service";
import { ApplicationsRepository } from "../../../src/applications/repository/applications.repository";
import { CreateApplicationDto } from "../../../src/applications/dto/create-application.dto";
import {
  transformPropertiesForCreate,
  transformToRawInfo,
} from "../../../src/applications/utils/applications.util";
import { CreateApplicationTransformedDTO } from "../../../src/applications/dto/create-application-transformed.dto";
import { KafkaManagerService } from "../../../../../libs/ts/queue/src/kafka/kafka-manager.service";
import { UpdateEmployerApplicationDto } from "../../../src/applications/dto/update-employer-application.dto";

// Mocking the util function
jest.mock("axios");

jest.mock("../../../src/applications/utils/applications.util", () => ({
  transformPropertiesForCreate: jest.fn(),
  transformToRawInfo: jest.fn(),
}));

jest.mock("libs/privacy/src/lib/cipher", () => ({
  decrypt: jest.fn((value: string) => `decrypted_${value}`),
}));

describe("ApplicationsService", () => {
  let service: ApplicationsService;
  let repository: jest.Mocked<ApplicationsRepository>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ApplicationsService,
        {
          provide: ApplicationsRepository,
          useValue: {
            createApplications: jest.fn(),
            getApplicationByCampaignId: jest.fn(),
            getAppliedUserJobId: jest.fn(),
            getApplicationsByUserId: jest.fn(),
            getApplicationByUserId: jest.fn(),
            getApplicationByAmazonID: jest.fn(),
            deletApplicationByeUserId: jest.fn(),
            removeAllApplicationByUserId: jest.fn(),
            updateEmployerApplicationInfo: jest.fn(),
            getNoProcessedApplications: jest.fn(),
          },
        },
        {
          provide: KafkaManagerService,
          useFactory: () =>
            KafkaManagerService.getInstance(
              "kafka-broker-employers-job-stream-0.talent.private:9092",
              "employers-applications",
            ),
        },
      ],
    }).compile();

    service = module.get<ApplicationsService>(ApplicationsService);
    repository = module.get<ApplicationsRepository>(
      ApplicationsRepository,
    ) as jest.Mocked<ApplicationsRepository>;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });

  describe("create", () => {
    it("should create application successfully", async () => {
      const job_info = {
        job_id: "12345",
        scanid: "scan123",
        title: "Software Engineer",
        empcode: "EMP001",
        empname: "John Doe",
        location: "New York",
        geo_city: "New York City",
        geo_country: "USA",
        geo_region1: "East Coast",
        geo_region2: "Northeast",
        apply_type: "Direct",
        apply_delivery: "Email",
        campaign_id: 54321,
        campaign_name: "Tech Jobs 2024",
        flag_active: 0,
        ppc: 2.5,
        job_source: "LinkedIn",
        link: "https://example.com/job",
        soc_onetsoc_code: "15-1132.00",
        soc_matched_title: "Software Developer",
        soc_major_group: "Computer and Mathematical",
        soc_minor_group: "Software Developers",
        soc_broad_group: "Applications",
        soc_detailed_group: "Web Developers",
        soc_classifier: "SOC 2024",
        apply_extra_questions: "What is your experience level?",
        apply_standard_questions: "Why do you want this job?",
        account_type: "Premium",
        apply_api_endpoint: "https://api.example.com/apply",
        reqid: "REQ12345",
        source_apply_email: "<EMAIL>",
        emp_email: "<EMAIL>",
        apply_email: "<EMAIL>",
        delivery_data_source: "Internal System",
        apply_extra_info: "Referral Bonus Available",
        apply_client_jobid: "CLIENTJOB123",
        domain: "example.com",
        apply_distant_location: "Remote",
        apply_number_questions: "5",
        fast_apply: 0,
      };

      const application_data = {
        first_name: "John",
        last_name: "Doe",
        phone_number: "**********",
        email: "<EMAIL>",
      };

      const user_id = "123";
      const date = new Date();

      const mockApplication: CreateApplicationDto = {
        job_info: job_info,
        application_data: application_data,
        user_file_id: 789,
        interested: "yes",
        status: "reviewed",
        user_server_info: "success",
        processed: 0,
        status_code: 200,
        server_response_code: 200,
        status_description: "success",
        server_response_description: "",
        api_ip_info: "success",
        email_response_info_id: "[0001, 0002]",
        delivery_rate: 0.75,
        delivery_status: "",
        country: "co",
        language: "es",
      };

      const mockTransformedApplication = new CreateApplicationTransformedDTO();
      mockTransformedApplication.user_id = user_id;
      mockTransformedApplication.job_info = job_info;
      mockTransformedApplication.application_data = application_data;
      mockTransformedApplication.user_file_id = 789;

      const application = new Applications();
      application.id = 123;
      application.user_id = user_id;
      application.job_info = job_info;
      application.application_data = application_data;
      application.date_created = date;

      (transformPropertiesForCreate as jest.Mock).mockReturnValue(mockTransformedApplication);
      repository.createApplications.mockResolvedValue(application);

      (axios.post as jest.Mock).mockResolvedValue({ response: { status: 200 } });

      const result = await service.create(mockApplication, {
        user_id: user_id,
        email: application_data.email,
      });

      expect(transformPropertiesForCreate).toHaveBeenCalledWith(
        { user_id: user_id, email: application_data.email },
        mockApplication,
      );
      expect(repository.createApplications).toHaveBeenCalledWith(mockTransformedApplication);
      expect(result).toEqual({
        result: "ok",
        message: "The application was created successfully",
        statusCode: 200,
      });
    });

    it("should create application successfully taken the email from app data", async () => {
      const job_info = {
        job_id: "12345",
        scanid: "scan123",
        title: "Software Engineer",
        empcode: "EMP001",
        empname: "John Doe",
        location: "New York",
        geo_city: "New York City",
        geo_country: "USA",
        geo_region1: "East Coast",
        geo_region2: "Northeast",
        apply_type: "Direct",
        apply_delivery: "Email",
        campaign_id: 54321,
        campaign_name: "Tech Jobs 2024",
        flag_active: 0,
        ppc: 2.5,
        job_source: "LinkedIn",
        link: "https://example.com/job",
        soc_onetsoc_code: "15-1132.00",
        soc_matched_title: "Software Developer",
        soc_major_group: "Computer and Mathematical",
        soc_minor_group: "Software Developers",
        soc_broad_group: "Applications",
        soc_detailed_group: "Web Developers",
        soc_classifier: "SOC 2024",
        apply_extra_questions: "What is your experience level?",
        apply_standard_questions: "Why do you want this job?",
        account_type: "Premium",
        apply_api_endpoint: "https://api.example.com/apply",
        reqid: "REQ12345",
        source_apply_email: "<EMAIL>",
        emp_email: "<EMAIL>",
        apply_email: "<EMAIL>",
        delivery_data_source: "Internal System",
        apply_extra_info: "Referral Bonus Available",
        apply_client_jobid: "CLIENTJOB123",
        domain: "example.com",
        apply_distant_location: "Remote",
        apply_number_questions: "5",
        fast_apply: 0,
      };

      const application_data = {
        first_name: "John",
        last_name: "Doe",
        phone_number: "**********",
        email: "<EMAIL>",
      };

      const user_id = "123";
      const date = new Date();

      const mockApplication: CreateApplicationDto = {
        job_info: job_info,
        application_data: application_data,
        user_file_id: 789,
        interested: "yes",
        status: "reviewed",
        user_server_info: "success",
        processed: 0,
        status_code: 200,
        server_response_code: 200,
        status_description: "success",
        server_response_description: "",
        api_ip_info: "success",
        email_response_info_id: "[0001, 0002]",
        delivery_rate: 0.75,
        delivery_status: "",
        country: "co",
        language: "es",
      };

      const mockTransformedApplication = new CreateApplicationTransformedDTO();
      mockTransformedApplication.user_id = user_id;
      mockTransformedApplication.job_info = job_info;
      mockTransformedApplication.application_data = application_data;
      mockTransformedApplication.user_file_id = 789;

      const application = new Applications();
      application.id = 123;
      application.user_id = user_id;
      application.job_info = job_info;
      application.application_data = application_data;
      application.date_created = date;

      (transformPropertiesForCreate as jest.Mock).mockReturnValue(mockTransformedApplication);
      repository.createApplications.mockResolvedValue(application);

      (axios.post as jest.Mock).mockResolvedValue({ response: { status: 200 } });

      const result = await service.create(mockApplication, { user_id: user_id, email: "" });

      expect(transformPropertiesForCreate).toHaveBeenCalledWith(
        { user_id: user_id, email: "" },
        mockApplication,
      );
      expect(repository.createApplications).toHaveBeenCalledWith(mockTransformedApplication);
      expect(result).toEqual({
        result: "ok",
        message: "The application was created successfully",
        statusCode: 200,
      });
    });

    it("should create application successfully but fail sending the email", async () => {
      const job_info = {
        job_id: "12345",
        scanid: "scan123",
        title: "Software Engineer",
        empcode: "EMP001",
        empname: "John Doe",
        location: "New York",
        geo_city: "New York City",
        geo_country: "USA",
        geo_region1: "East Coast",
        geo_region2: "Northeast",
        apply_type: "Direct",
        apply_delivery: "Email",
        campaign_id: 54321,
        campaign_name: "Tech Jobs 2024",
        flag_active: 0,
        ppc: 2.5,
        job_source: "LinkedIn",
        link: "https://example.com/job",
        soc_onetsoc_code: "15-1132.00",
        soc_matched_title: "Software Developer",
        soc_major_group: "Computer and Mathematical",
        soc_minor_group: "Software Developers",
        soc_broad_group: "Applications",
        soc_detailed_group: "Web Developers",
        soc_classifier: "SOC 2024",
        apply_extra_questions: "What is your experience level?",
        apply_standard_questions: "Why do you want this job?",
        account_type: "Premium",
        apply_api_endpoint: "https://api.example.com/apply",
        reqid: "REQ12345",
        source_apply_email: "<EMAIL>",
        emp_email: "<EMAIL>",
        apply_email: "<EMAIL>",
        delivery_data_source: "Internal System",
        apply_extra_info: "Referral Bonus Available",
        apply_client_jobid: "CLIENTJOB123",
        domain: "example.com",
        apply_distant_location: "Remote",
        apply_number_questions: "5",
        fast_apply: 0,
      };

      const application_data = {
        first_name: "John",
        last_name: "Doe",
        phone_number: "**********",
        email: "<EMAIL>",
      };

      const user_id = "123";
      const date = new Date();

      const mockApplication: CreateApplicationDto = {
        job_info: job_info,
        application_data: application_data,
        user_file_id: 789,
        interested: "yes",
        status: "reviewed",
        user_server_info: "success",
        processed: 0,
        status_code: 200,
        server_response_code: 200,
        status_description: "success",
        server_response_description: "",
        api_ip_info: "success",
        email_response_info_id: "[0001, 0002]",
        delivery_rate: 0.75,
        delivery_status: "",
        country: "co",
        language: "es",
      };

      const mockTransformedApplication = new CreateApplicationTransformedDTO();
      mockTransformedApplication.user_id = user_id;
      mockTransformedApplication.job_info = job_info;
      mockTransformedApplication.application_data = application_data;
      mockTransformedApplication.user_file_id = 789;

      const application = new Applications();
      application.id = 123;
      application.user_id = user_id;
      application.job_info = job_info;
      application.application_data = application_data;
      application.date_created = date;

      (transformPropertiesForCreate as jest.Mock).mockReturnValue(mockTransformedApplication);
      repository.createApplications.mockResolvedValue(application);

      (axios.post as jest.Mock).mockRejectedValue({ response: { status: 500 } });
      // const result =
      await expect(
        service.create(mockApplication, { user_id: user_id, email: application_data.email }),
      ).rejects.toThrow(HttpException);
    });

    it("should throw an error when transformation returns null", async () => {
      const user_id = "1235";
      const email = "<EMAIL>";
      const mockApplication: CreateApplicationDto = {
        job_info: {
          job_id: "12345",
          scanid: "scan123",
          title: "Software Engineer",
          empcode: "EMP001",
          empname: "John Doe",
          location: "New York",
          geo_city: "New York City",
          geo_country: "USA",
          geo_region1: "East Coast",
          geo_region2: "Northeast",
          apply_type: "Direct",
          apply_delivery: "Email",
          campaign_id: 54321,
          campaign_name: "Tech Jobs 2024",
          flag_active: 0,
          ppc: 2.5,
          job_source: "LinkedIn",
          link: "https://example.com/job",
          soc_onetsoc_code: "15-1132.00",
          soc_matched_title: "Software Developer",
          soc_major_group: "Computer and Mathematical",
          soc_minor_group: "Software Developers",
          soc_broad_group: "Applications",
          soc_detailed_group: "Web Developers",
          soc_classifier: "SOC 2024",
          apply_extra_questions: "What is your experience level?",
          apply_standard_questions: "Why do you want this job?",
          account_type: "Premium",
          apply_api_endpoint: "https://api.example.com/apply",
          reqid: "REQ12345",
          source_apply_email: "<EMAIL>",
          emp_email: "<EMAIL>",
          apply_email: "<EMAIL>",
          delivery_data_source: "Internal System",
          apply_extra_info: "Referral Bonus Available",
          apply_client_jobid: "CLIENTJOB123",
          domain: "example.com",
          apply_distant_location: "Remote",
          apply_number_questions: "5",
          fast_apply: 0,
        },
        application_data: {
          first_name: "John",
          last_name: "Doe",
          phone_number: "**********",
          email: email,
        },
        user_file_id: 7894,
        interested: "yes",
        status: "reviewed",
        user_server_info: "success",
        processed: 0,
        status_code: 200,
        server_response_code: 200,
        status_description: "success",
        server_response_description: "",
        api_ip_info: "success",
        email_response_info_id: "[0001, 0002]",
        delivery_rate: 0.75,
        delivery_status: "",
        country: "co",
        language: "es",
      };

      (transformPropertiesForCreate as jest.Mock).mockReturnValue(null);

      await expect(
        service.create(mockApplication, { user_id: user_id, email: email }),
      ).rejects.toThrow("Wrong values.");
      expect(transformPropertiesForCreate).toHaveBeenCalledWith(
        { user_id: user_id, email: email },
        mockApplication,
      );
      expect(repository.createApplications).not.toHaveBeenCalled();
    });
  });

  describe("findApplicationUserIdByCampaignId", () => {
    it("should return applications for a given user and campaign IDs", async () => {
      const userId = "1";
      const campaignId = 123;
      const expectedResult = [{ id: 1, user_id: userId, job_info: '{"campaign_id": 123}' }];

      jest
        .spyOn(repository, "getApplicationByCampaignId")
        .mockResolvedValueOnce(expectedResult as any);

      (transformToRawInfo as jest.Mock).mockReturnValue(expectedResult[0]);

      const result = await service.findApplicationUserIdByCampaignId(campaignId);

      expect(result).toEqual(expectedResult);
      expect(repository.getApplicationByCampaignId).toHaveBeenCalledWith(campaignId);
    });

    it("should throw a NotFoundException if no applications are found for the user", async () => {
      const userId = "1";
      const campaignId = 6;
      const errorMessage = `Record with id userFile ${userId} not found`;

      jest
        .spyOn(repository, "getApplicationByCampaignId")
        .mockRejectedValueOnce(new NotFoundException(errorMessage));

      await expect(service.findApplicationUserIdByCampaignId(campaignId)).rejects.toThrow(
        NotFoundException,
      );
      expect(repository.getApplicationByCampaignId).toHaveBeenCalledWith(campaignId);
    });

    it("should throw a NotFoundException if no applications are found with the given campaignId", async () => {
      const userId = "1";
      const campaignId = 123;
      const errorMessage = "The user is not applied";

      jest
        .spyOn(repository, "getApplicationByCampaignId")
        .mockRejectedValueOnce(new NotFoundException(errorMessage));

      await expect(service.findApplicationUserIdByCampaignId(campaignId)).rejects.toThrow(
        NotFoundException,
      );
      expect(repository.getApplicationByCampaignId).toHaveBeenCalledWith(campaignId);
    });
  });

  describe("findAppliedUserJobId", () => {
    it("should call getAppliedUserJobId method of repository with correct parameters", async () => {
      // Arrange
      const userId = "2589";
      const jobId = 6524;
      const mockResult = { result: "User is already applied" };

      jest.spyOn(repository, "getAppliedUserJobId").mockResolvedValueOnce(mockResult);

      // Act
      const result = await service.findAppliedUserJobId(userId, jobId);

      // Assert
      expect(repository.getAppliedUserJobId).toHaveBeenCalledWith(userId, jobId);
      expect(result).toEqual(mockResult);
    });

    it("should throw a NotFoundException if no applications are found for the user", async () => {
      const userId = "2589";
      const jobId = 6524;
      const errorMessage = `Record with id userFile ${userId} not found`;

      jest
        .spyOn(repository, "getAppliedUserJobId")
        .mockRejectedValue(new NotFoundException(errorMessage));

      await expect(service.findAppliedUserJobId(userId, jobId)).rejects.toThrow(NotFoundException);
      await expect(service.findAppliedUserJobId(userId, jobId)).rejects.toThrow(errorMessage);
      expect(repository.getAppliedUserJobId).toHaveBeenCalledWith(userId, jobId);
    });

    it("should throw a NotFoundException if the user is not applied to the job", async () => {
      const userId = "2589";
      const jobId = 6524;
      const errorMessage = "The user is not applied";

      jest
        .spyOn(repository, "getAppliedUserJobId")
        .mockRejectedValue(new NotFoundException(errorMessage));

      await expect(service.findAppliedUserJobId(userId, jobId)).rejects.toThrow(NotFoundException);
      await expect(service.findAppliedUserJobId(userId, jobId)).rejects.toThrow(errorMessage);
      expect(repository.getAppliedUserJobId).toHaveBeenCalledWith(userId, jobId);
    });
  });

  describe("findApplicationByAmazonID", () => {
    it("should get an application correctly", async () => {
      // Arrange
      const userId = "2589";
      const message_id = "01000185e0913f8d-213c2915-42fe-4806-88f3-1a380c0e0890-000000";

      const mockApplicationData = {
        first_name: "Daniela",
        last_name: "Laverde",
        phone_number: "3103754806",
        email: "<EMAIL>",
      };

      const mockExpectedtApplicationData = {
        first_name: "decrypted_Daniela",
        last_name: "decrypted_Laverde",
        phone_number: "decrypted_3103754806",
        email: "<EMAIL>",
      };

      const mockApplication: Applications = {
        id: 1,
        user_id: userId,
        job_info: {
          job_id: "12345",
          scanid: "scan123",
          title: "Software Engineer",
          empcode: "EMP001",
          empname: "John Doe",
          location: "New York",
          geo_city: "New York City",
          geo_country: "USA",
          geo_region1: "East Coast",
          geo_region2: "Northeast",
          apply_type: "Direct",
          apply_delivery: "Email",
          campaign_id: 54321,
          campaign_name: "Tech Jobs 2024",
          flag_active: 0,
          ppc: 2.5,
          job_source: "LinkedIn",
          link: "https://example.com/job",
          soc_onetsoc_code: "15-1132.00",
          soc_matched_title: "Software Developer",
          soc_major_group: "Computer and Mathematical",
          soc_minor_group: "Software Developers",
          soc_broad_group: "Applications",
          soc_detailed_group: "Web Developers",
          soc_classifier: "SOC 2024",
          apply_extra_questions: "What is your experience level?",
          apply_standard_questions: "Why do you want this job?",
          account_type: "Premium",
          apply_api_endpoint: "https://api.example.com/apply",
          reqid: "REQ12345",
          source_apply_email: "<EMAIL>",
          emp_email: "<EMAIL>",
          apply_email: "<EMAIL>",
          delivery_data_source: "Internal System",
          apply_extra_info: "Referral Bonus Available",
          apply_client_jobid: "CLIENTJOB123",
          domain: "example.com",
          apply_distant_location: "Remote",
          apply_number_questions: "5",
          fast_apply: 0,
        },
        application_data: mockApplicationData,
        user_file_id: 1,
        interested: Interested.interested,
        status: Status.hired,
        user_server_info: "success",
        processed: 0,
        status_code: 200,
        server_response_code: 200,
        status_description: "success",
        server_response_description: "",
        api_ip_info: "success",
        email_response_info_id:
          '{"01000185e0913f8d-213c2915-42fe-4806-88f3-1a380c0e0890-000000": "pending"}',
        delivery_rate: 0.75,
        delivery_status: "",
        country: "co",
        language: "es",
        date_processed: new Date(),
        date_created: new Date(),
      };

      jest.spyOn(repository, "getApplicationByAmazonID").mockResolvedValueOnce(mockApplication);

      mockApplication.application_data = mockExpectedtApplicationData;
      (transformToRawInfo as jest.Mock).mockResolvedValue(mockApplication);

      // Act
      const result = await service.findApplicationByAmazonID(message_id);

      // Assert
      expect(repository.getApplicationByAmazonID).toHaveBeenCalledWith(message_id);
      expect(result).toEqual(mockApplication);
    });
  });

  describe("updateEmployerApplicationInfo", () => {
    it("should update an application correctly", async () => {
      // Arrange
      const appToUpdate: UpdateEmployerApplicationDto = {
        id: [1, 2],
        interested: Interested.interested,
        status: Status.rejected,
      };

      jest.spyOn(repository, "updateEmployerApplicationInfo").mockResolvedValueOnce({
        statusCode: 200,
        message: `All applications updated successfully`,
      });

      // Act
      const result = await service.updateEmployerApplicationInfo(appToUpdate);

      // Assert
      expect(repository.updateEmployerApplicationInfo).toHaveBeenCalledWith(appToUpdate);
      expect(result).toEqual({
        statusCode: 200,
        message: `All applications updated successfully`,
      });
    });
  });

  describe("findApplicationUserIdByUserId", () => {
    it("should call getApplicationByUserId method of repository with correct parameters", async () => {
      // Arrange
      const userId = "2589";

      const mockApplicationData = {
        first_name: "Daniela",
        last_name: "Laverde",
        phone_number: "3103754806",
        email: "<EMAIL>",
      };

      const mockExpectedtApplicationData = {
        first_name: "decrypted_Daniela",
        last_name: "decrypted_Laverde",
        phone_number: "decrypted_3103754806",
        email: "<EMAIL>",
      };

      const mockApplications: Applications[] = [
        {
          id: 1,
          user_id: userId,
          job_info: {
            job_id: "12345",
            scanid: "scan123",
            title: "Software Engineer",
            empcode: "EMP001",
            empname: "John Doe",
            location: "New York",
            geo_city: "New York City",
            geo_country: "USA",
            geo_region1: "East Coast",
            geo_region2: "Northeast",
            apply_type: "Direct",
            apply_delivery: "Email",
            campaign_id: 54321,
            campaign_name: "Tech Jobs 2024",
            flag_active: 0,
            ppc: 2.5,
            job_source: "LinkedIn",
            link: "https://example.com/job",
            soc_onetsoc_code: "15-1132.00",
            soc_matched_title: "Software Developer",
            soc_major_group: "Computer and Mathematical",
            soc_minor_group: "Software Developers",
            soc_broad_group: "Applications",
            soc_detailed_group: "Web Developers",
            soc_classifier: "SOC 2024",
            apply_extra_questions: "What is your experience level?",
            apply_standard_questions: "Why do you want this job?",
            account_type: "Premium",
            apply_api_endpoint: "https://api.example.com/apply",
            reqid: "REQ12345",
            source_apply_email: "<EMAIL>",
            emp_email: "<EMAIL>",
            apply_email: "<EMAIL>",
            delivery_data_source: "Internal System",
            apply_extra_info: "Referral Bonus Available",
            apply_client_jobid: "CLIENTJOB123",
            domain: "example.com",
            apply_distant_location: "Remote",
            apply_number_questions: "5",
            fast_apply: 0,
          },
          application_data: mockApplicationData,
          user_file_id: 1,
          interested: Interested.interested,
          status: Status.hired,
          user_server_info: "success",
          processed: 0,
          status_code: 200,
          server_response_code: 200,
          status_description: "success",
          server_response_description: "",
          api_ip_info: "success",
          email_response_info_id: "[0001, 0002]",
          delivery_rate: 0.75,
          delivery_status: "",
          country: "co",
          language: "es",
          date_processed: new Date(),
          date_created: new Date(),
        },
      ];

      jest.spyOn(repository, "getApplicationsByUserId").mockResolvedValueOnce(mockApplications);

      mockApplications[0].application_data = mockExpectedtApplicationData;
      (transformToRawInfo as jest.Mock).mockReturnValue(mockApplications[0]);

      // Act
      const result = await service.findApplicationUserIdByUserId(userId);

      // Assert
      expect(repository.getApplicationsByUserId).toHaveBeenCalledWith(userId);

      // mockApplications[1].application_data = mockExpectedtApplicationData;

      expect(result).toEqual(mockApplications);
    });

    it("should call getApplicationByUserId method of repository with empty extra_questions", async () => {
      // Arrange
      const userId = "2589";

      const mockApplicationData: any = {
        first_name: "Daniela",
        last_name: "",
        phone_number: "",
        email: "<EMAIL>",
        screening_questions: [
          {
            "0001": {
              question: "What do you do?",
              response: "",
            },
          },
          {
            "002": {
              question: "Industry",
              response: "IT",
            },
          },
        ],
      };

      const mockExpectedtApplicationData: any = {
        first_name: "decrypted_Daniela",
        last_name: "",
        phone_number: "",
        email: "<EMAIL>",
        screening_questions: [
          {
            "0001": {
              question: "What do you do?",
              response: "",
            },
          },
          {
            "002": {
              question: "Industry",
              response: "decrypted_IT",
            },
          },
        ],
      };

      const mockApplications: Applications[] = [
        {
          id: 1,
          user_id: userId,
          job_info: {
            job_id: "12345",
            scanid: "scan123",
            title: "Software Engineer",
            empcode: "EMP001",
            empname: "John Doe",
            location: "New York",
            geo_city: "New York City",
            geo_country: "USA",
            geo_region1: "East Coast",
            geo_region2: "Northeast",
            apply_type: "Direct",
            apply_delivery: "Email",
            campaign_id: 54321,
            campaign_name: "Tech Jobs 2024",
            flag_active: 0,
            ppc: 2.5,
            job_source: "LinkedIn",
            link: "https://example.com/job",
            soc_onetsoc_code: "15-1132.00",
            soc_matched_title: "Software Developer",
            soc_major_group: "Computer and Mathematical",
            soc_minor_group: "Software Developers",
            soc_broad_group: "Applications",
            soc_detailed_group: "Web Developers",
            soc_classifier: "SOC 2024",
            apply_extra_questions: "What is your experience level?",
            apply_standard_questions: "Why do you want this job?",
            account_type: "Premium",
            apply_api_endpoint: "https://api.example.com/apply",
            reqid: "REQ12345",
            source_apply_email: "<EMAIL>",
            emp_email: "<EMAIL>",
            apply_email: "<EMAIL>",
            delivery_data_source: "Internal System",
            apply_extra_info: "Referral Bonus Available",
            apply_client_jobid: "CLIENTJOB123",
            domain: "example.com",
            apply_distant_location: "Remote",
            apply_number_questions: "5",
            fast_apply: 0,
          },
          application_data: mockApplicationData,
          user_file_id: 1,
          interested: Interested.interested,
          status: Status.notAFit,
          user_server_info: "success",
          processed: 0,
          status_code: 200,
          server_response_code: 200,
          status_description: "success",
          server_response_description: "",
          api_ip_info: "success",
          email_response_info_id: "[0001, 0002]",
          delivery_rate: 0.75,
          delivery_status: "",
          country: "co",
          language: "es",
          date_processed: new Date(),
          date_created: new Date(),
        },
      ];

      jest.spyOn(repository, "getApplicationsByUserId").mockResolvedValueOnce(mockApplications);

      mockApplications[0].application_data = mockExpectedtApplicationData;
      (transformToRawInfo as jest.Mock).mockReturnValue(mockApplications[0]);

      // Act
      const result = await service.findApplicationUserIdByUserId(userId);

      // Assert
      expect(repository.getApplicationsByUserId).toHaveBeenCalledWith(userId);
      expect(result).toEqual(mockApplications);
    });

    it("should throw a NotFoundException if no applications are found for the user", async () => {
      // Arrange
      const userId = "2589";
      const errorMessage = `Record with id userFile ${userId} not found`;

      jest
        .spyOn(repository, "getApplicationsByUserId")
        .mockRejectedValueOnce(new NotFoundException(errorMessage));

      // Act & Assert
      await expect(service.findApplicationUserIdByUserId(userId)).rejects.toThrow(
        NotFoundException,
      );
      expect(repository.getApplicationsByUserId).toHaveBeenCalledWith(userId);
    });
  });

  describe("removeAllApplicationByUserId", () => {
    it("should remove all applications related to user_id", async () => {
      // Arrange
      const userId = "123";
      const expectedResult = {
        result: "ok",
        message: `All applications related to ${userId} were deleted successfully`,
      };

      // Act
      jest.spyOn(repository, "deletApplicationByeUserId").mockResolvedValueOnce(expectedResult);
      const result = await service.removeAllApplicationByUserId(userId);

      // Assert
      expect(result).toEqual(expectedResult);
      expect(repository.deletApplicationByeUserId).toHaveBeenCalledWith(userId);
    });
  });

  describe("getNoProcessedApplications", () => {
    it("should return applications if found", async () => {
      const date_ini = "2024-11-01";
      // Arrange
      const userId = "2589";

      const mockApplicationData: any = {
        first_name: "Daniela",
        last_name: "",
        phone_number: "",
        email: "<EMAIL>",
        screening_questions: [
          {
            "0001": {
              question: "What do you do?",
              response: "",
            },
          },
          {
            "002": {
              question: "Industry",
              response: "IT",
            },
          },
        ],
      };
      const mockApplications: Applications[] = [
        {
          id: 1,
          user_id: userId,
          job_info: {
            job_id: "12345",
            scanid: "scan123",
            title: "Software Engineer",
            empcode: "EMP001",
            empname: "John Doe",
            location: "New York",
            geo_city: "New York City",
            geo_country: "USA",
            geo_region1: "East Coast",
            geo_region2: "Northeast",
            apply_type: "Direct",
            apply_delivery: "Email",
            campaign_id: 54321,
            campaign_name: "Tech Jobs 2024",
            flag_active: 0,
            ppc: 2.5,
            job_source: "LinkedIn",
            link: "https://example.com/job",
            soc_onetsoc_code: "15-1132.00",
            soc_matched_title: "Software Developer",
            soc_major_group: "Computer and Mathematical",
            soc_minor_group: "Software Developers",
            soc_broad_group: "Applications",
            soc_detailed_group: "Web Developers",
            soc_classifier: "SOC 2024",
            apply_extra_questions: "What is your experience level?",
            apply_standard_questions: "Why do you want this job?",
            account_type: "Premium",
            apply_api_endpoint: "https://api.example.com/apply",
            reqid: "REQ12345",
            source_apply_email: "<EMAIL>",
            emp_email: "<EMAIL>",
            apply_email: "<EMAIL>",
            delivery_data_source: "Internal System",
            apply_extra_info: "Referral Bonus Available",
            apply_client_jobid: "CLIENTJOB123",
            domain: "example.com",
            apply_distant_location: "Remote",
            apply_number_questions: "5",
            fast_apply: 0,
          },
          application_data: mockApplicationData,
          user_file_id: 1,
          interested: Interested.interested,
          status: Status.notAFit,
          user_server_info: "success",
          processed: 0,
          status_code: 200,
          server_response_code: 200,
          status_description: "success",
          server_response_description: "",
          api_ip_info: "success",
          email_response_info_id: "[0001, 0002]",
          delivery_rate: 0.75,
          delivery_status: "",
          country: "co",
          language: "es",
          date_processed: new Date(),
          date_created: new Date(),
        },
      ];

      jest.spyOn(repository, "getNoProcessedApplications").mockResolvedValue(mockApplications);

      const result = await service.getNoProcessedApplications(date_ini, 200);

      expect(repository.getNoProcessedApplications).toHaveBeenCalledWith(date_ini, 200);
      expect(result).toEqual(mockApplications);
    });

    it("should throw an HttpException if no applications are found", async () => {
      const date_ini = "2024-11-01";

      jest.spyOn(repository, "getNoProcessedApplications").mockResolvedValue([]);

      await expect(service.getNoProcessedApplications(date_ini, 200)).rejects.toThrow(
        new HttpException("No unprocessed applications were found", HttpStatus.NOT_FOUND),
      );

      expect(repository.getNoProcessedApplications).toHaveBeenCalledWith(date_ini, 200);
    });

    it("should propagate errors from the repository", async () => {
      const date_ini = "2024-11-01";

      jest
        .spyOn(repository, "getNoProcessedApplications")
        .mockRejectedValue(new Error("Database error"));

      await expect(service.getNoProcessedApplications(date_ini, 200)).rejects.toThrow(
        "Database error",
      );

      expect(repository.getNoProcessedApplications).toHaveBeenCalledWith(date_ini, 200);
    });
  });
});
