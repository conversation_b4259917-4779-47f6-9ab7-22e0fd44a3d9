import { Controller, Post, Body, HttpCode, BadRequestException } from "@nestjs/common";
import { ApiTags, ApiOperation, ApiResponse, ApiBody } from "@nestjs/swagger";
import { TimezoneService } from "./timezone.service";
import { Public } from "../auth/v1/decorators/auth.decorator";
import { TimezoneTargetHourDto } from "./dto/target-hour.dto";

/**
 * Controller to handle timezone-related endpoints.
 * @version 1
 * @tags Timezone Tools
 */
@ApiTags("Timezone Tools") // Swagger tag for the group of endpoints
@Public()
@Controller({
  path: "timezone",
  version: "1",
})
export class TimezoneController {
  constructor(private readonly timezoneService: TimezoneService) {}

  /**
   * POST endpoint to get time zones where the hour matches the local hour.
   *
   * @param {SimulatedHourDto} simulatedHourDto - The hour to simulate in the request body.
   * @returns {string[]} - An array of time zone names where the simulated hour matches the local hour.
   */
  @Post("get-timezones-by-hour")
  @ApiOperation({ summary: "Get time zones matching from a specific hour" })
  @HttpCode(200)
  @ApiResponse({ status: 200, description: "List of matching time zones." })
  @ApiResponse({ status: 400, description: "Wrong parameter." })
  @ApiBody({
    description: "The hour to simulate (0-23)",
    type: TimezoneTargetHourDto,
  })
  getTimezonesForSpecificHour(@Body() simulatedHourDto: TimezoneTargetHourDto): string[] {
    try {
      // Call the service method to get matching time zones
      return this.timezoneService.getTimezonesForSpecificHour(simulatedHourDto);
    } catch (error: any) {
      throw new BadRequestException(error.message);
    }
  }
}
