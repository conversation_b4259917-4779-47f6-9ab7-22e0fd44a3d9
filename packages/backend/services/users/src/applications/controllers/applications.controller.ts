import { ApiTags } from "@nestjs/swagger";
import { AuthGuard } from "../../auth/v1/guards/auth.guard";
import { User } from "../../auth/v1/decorators/user.decorator";
import { Controller, Post, Body, Get, Param, Delete, UseGuards, Patch, UnauthorizedException } from "@nestjs/common";

import { ApplicationsService } from "../services/applications.service";
import { CreateApplicationDto } from "../dto/create-application.dto";
import { decrypt } from "libs/privacy/src/lib/cipher";

/**
 * Controller for managing Applications.
 */
@UseGuards(AuthGuard)
@ApiTags("Applications")
@Controller({
  path: "applications",
  version: "1", // API V1.0, 8/05/2024
})
export class ApplicationsController {
  /**
   * Constructor of the ap: applicationsService class.
   * @param {ApplicationsService} applicationsService Injection of the services of the userFile object
   */
  constructor(private readonly applicationsService: ApplicationsService) {}

  /**
   * Create a new Applications.
   * @param {CreateApplicationDto} userFile - The data of the new userFile to be created.
   * @returns
   */
  @Post()
  create(@Body() createApplicationDto: CreateApplicationDto, @User() user: { user_id: string, email: string }) {

    return this.applicationsService.create(createApplicationDto, user);
  }

  /**
   * Get a new Applications related by campaing id .
   * @param  Applications - The data of the new Applications .
   * @returns
   */
  @Get("/campaign")
  async getApplicationByCampaignId(@User() user: {campaign_id: number}   ) {
    return await this.applicationsService.findApplicationUserIdByCampaignId(user.campaign_id);
  }

  /**
   * Ger a new Applications related by campaing id .
   * @param  Applications - The data of the new Applications .
   * @returns
   */
  @Get("/users/me/applied/:jobId")
  async getAppliedUser(@User() user: { user_id: string }, @Param("jobId") jobId: number) {
    return await this.applicationsService.findAppliedUserJobId(user.user_id, jobId);
  }

  /**
   * Ger a new Applications related by campaing id .
   * @param  userId  .
   * @returns
   */
  @Get("/users/me")
  async getApplicationByUserId(@User() user: { user_id: string }) {
    return await this.applicationsService.findApplicationUserIdByUserId(user.user_id);
  }

  /**
   * Deletes all applications related to user_Id
   * @param id
   * @returns
   */
  @Delete("/users/me")
  async deleteUserId(@User() user: { user_id: string }) {
    return await this.applicationsService.removeAllApplicationByUserId(user.user_id);
  }

  /**
   * Update the file id in the application.
   * @param  Applications - The data of the new Applications .
   * @returns
   */
  @Patch("/add-file")
  async updateCorruptApps(@User() user: { user_id: string }, @Body() app: {tk: string}) {

    const data = JSON.parse(decrypt(app.tk))

    if(user.user_id !== data.user_id) {
      throw UnauthorizedException
    }

    return await this.applicationsService.updateCorruptApps(data);
  }
}
