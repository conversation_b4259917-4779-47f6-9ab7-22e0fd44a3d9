import {
  Entity,
  Column,
  PrimaryColumn,
  Index,
  CreateDateColumn,
  PrimaryGeneratedColumn,
} from "typeorm";

export enum EventType {
  JobSearch = "job_search",
  JobSearched = "job_searched",
  JobClick = "job_click",
  JobCardClicked = "job_card_clicked",
  JobIOA = "job_ioa",
  JobFavorite = "job_favorite",
  JobSaved = "job_saved",
  applyStarted = "apply_started",
  JobAlert = "jbalerts_customized",
  JobConversion = "job_conversion",
}

export enum EventSource {
  NotificationsRetargeting = "consumer_notification_retargeting",
  SerpProducer = "serp_producer",
  ApplyProducer = "apply_producer",
  RedirectProducer = "redirect_producer",
  CoregProducer = "coreg_producer",
}

/**
 *
 */
@Entity("user_events_new_partitioned")
@Index("idx_user_events_new_partitioned_composite", [
  "eventType",
  "eventSource",
  "eventDate",
  "userId",
])
@Index("idx_user_events_new_partitioned_user_id", ["userId"])
@Index("idx_user_events_new_partitioned_user_id_event_type", ["userId", "eventType"])
@Index("idx_user_id_events_partitioned_event_type_source_date", [
  "eventType",
  "eventSource",
  "validLocation",
  "eventDate",
  "userId",
])
export class UserEvents {
  // Primary columns (Composite key)
  @PrimaryColumn({ type: "timestamptz", name: "event_date" })
  eventDate: Date;

  @PrimaryGeneratedColumn({ type: "int4" })
  id: number;

  // Indexed columns
  @Column({ name: "user_id", type: "uuid" })
  userId: string;

  @Column({ name: "event_type", type: "varchar" })
  eventType: EventType;

  @Column({ name: "event_source", type: "varchar", nullable: true })
  eventSource: EventSource;

  @Column({ name: "country", type: "varchar", nullable: true })
  country: string;

  @Column({ name: "event_id", type: "varchar", comment: "Event ID." })
  eventId: string;

  @Column({ name: "location", type: "jsonb" })
  location: any;

  @Column({ name: "language", type: "varchar" })
  language: string;

  @Column({ name: "job_id", type: "varchar", nullable: true })
  jobId?: string;

  @Column({ name: "keyword", type: "varchar" })
  keyword: string;

  @CreateDateColumn({
    name: "date_created",
    type: "timestamp",
    comment: "The date when the record was inserted.",
  })
  dateCreated!: Date;

  @Column({
    name: "valid_location",
    nullable: true,
    type: "boolean",
    comment: "User's valid location indicator.",
  })
  validLocation?: boolean | null;

  @Column({
    name: "soc_code",
    nullable: true,
    type: "varchar",
    comment: "SOC Code",
  })
  soc_code?: string | null;
  
  @Column({
    name: "soc_major_group",
    nullable: true,
    type: "varchar",
    comment: "SOC Major Group",
  })
  soc_major_group?: string | null;
  

  @Column({
    name: "soc_minor_group",
    nullable: true,
    type: "varchar",
    comment: "SOC Minor Group",
  })
  soc_minor_group?: string | null;
  

  @Column({
    name: "soc_matched_title",
    nullable: true,
    type: "varchar",
    comment: "SOC Matched Tutle",
  })
  soc_matched_title?: string | null;
  
  

  /**
   *
   */
  constructor(partial?: Partial<UserEvents>) {
    Object.assign(this, partial);
  }
}
