package cache

import (
	"fmt"
	"os"
	"strconv"
	"time"

	"github.com/go-redis/redis"
	"github.com/joho/godotenv"
)

const (
	DB = 0
)

type RedisCache struct {
	client *redis.Client
	field  string
}

func NewRedisCache(field string) *RedisCache {
	godotenv.Load()
	poolSize, _ := strconv.Atoi(os.Getenv("REDIS_POOL_SIZE"))
	MinIdleConns, _ := strconv.Atoi(os.Getenv("REDIS_MIN_IDLE_CONNS"))
	return &RedisCache{
		client: redis.NewClient(&redis.Options{
			Addr:         os.Getenv("REDIS_HOST") + ":" + os.Getenv("REDIS_PORT"),
			Password:     os.Getenv("REDIS_PASS"),
			DB:           DB,
			PoolSize:     poolSize,
			MinIdleConns: MinIdleConns,
			PoolTimeout:  10 * time.Second,
			IdleTimeout:  5 * time.Hour,
			ReadTimeout:  5 * time.Second,
		}),
		field: field}
}

func (r *RedisCache) Get(key string) ([]byte, error) {

	val, err := r.client.HGet(key, r.field).Bytes()
	if (err != nil && err != redis.Nil) || len(val) == 0 {
		/* if len(val) == 0 {
			fmt.Println(fmt.Sprintf("key not found: %v", key), err)
		} else {
			fmt.Println("error getting value from redis: ", err)
		} */
		return nil, err
	}
	return val, nil
}

func (r *RedisCache) Set(key string, value []byte) error {

	return r.client.HSet(key, r.field, value).Err()
}

func (r *RedisCache) Close() {
	r.client.Close()
}

func (r *RedisCache) Delete(key string) error {
	exists := r.client.HExists(key, r.field)
	if !exists.Val() {
		return fmt.Errorf("key not found")
	}
	return r.client.Del(key).Err()
}
