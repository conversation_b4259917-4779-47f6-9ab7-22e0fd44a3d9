package main

import (
	"context"
	"employers-job-processing/src/internal/application"
	"employers-job-processing/src/internal/infrastructure/cache"
	"employers-job-processing/src/internal/infrastructure/messaging"
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"strconv"
	jobsharding "talent/libs/job-sharding/src"
	logger "talent/libs/logger/src"
	telemetry "talent/libs/openTelemetry/src"
	"time"

	"github.com/cenkalti/backoff/v4"
	"github.com/joho/godotenv"
	"github.com/segmentio/kafka-go"
)

var shards int = 8

func main() {
	ctx, l, kafkaPublisher, cache, err := loadDependencies()
	ctx, cancel := context.WithCancel(ctx)
	defer cancel()

	defer cache.Close()
	//Open Telemetr
	oTel := telemetry.NewOTel(ctx, "employers-job-processing.apps", "1.0.0")

	errOTel := oTel.BootstrapErrors()
	if errOTel != nil {
		panic(errOTel)
	}

	otelShutdown := oTel.ShutdownFuncs()
	defer func() {
		errOTel = errors.Join(errOTel, otelShutdown(context.Background()))
	}()

	oTel.SetMetrics(map[string]string{
		"jobs_received":                    "counter",
		"jobs_successfully_processed":      "counter",
		"jobs_processed_elapsed_time":      "histogram",
		"jobs_processed_event_created":     "counter",
		"jobs_processed_event_updated":     "counter",
		"jobs_processed_event_inactivated": "counter",
		"jobs_processed_without_account":   "counter",
		"jobs_unmarshal_error":             "counter",
		"jobs_processed_persistence_error": "counter",
	})

	storer := application.NewStorer()
	if err != nil {
		l.Error("Failed to initialize application: %v", err)
	}
	if err := run(ctx, l, kafkaPublisher, cache, storer, oTel); err != nil {
		l.Error("error running application", err)
		time.Sleep(5 * time.Millisecond)
		os.Exit(1)
	}
}

func loadDependencies() (context.Context, logger.Logger, messaging.MessagePublisher, cache.Cache, error) {
	l := logger.NewTalentLogger(logger.NewMultiOutput(&logger.ConsoleOutput{}))

	if os.Getenv("ENVIRON") == "" {
		l.Warn("No .env file found, loading default environment variables")
		err := godotenv.Load()
		if err != nil {
			return nil, nil, nil, nil, fmt.Errorf("error loading .env file: %v", err)
		}
	}
	ctx := context.Background()

	// Initialize the messaging system
	kafkaPublisher := messaging.NewKafkaPublisher([]string{
		os.Getenv("JOB_PROCESSING_KAFKA_ADDRESS_0"),
		os.Getenv("JOB_PROCESSING_KAFKA_ADDRESS_1"),
		os.Getenv("JOB_PROCESSING_KAFKA_ADDRESS_2")})
	cache := cache.NewRedisCache("data")
	return ctx, l, kafkaPublisher, cache, nil

}

func run(ctx context.Context, l logger.Logger, publisher messaging.MessagePublisher, cache cache.Cache, storer application.EmployerDataManager, oTel telemetry.OTel) error {
	messages := make(chan messaging.Message) // Channel for passing messages
	errChan := make(chan error)

	ppcProcessedTopic := os.Getenv("JOB_PROCESSING_KAFKA_OUTGOING_JOBS_SHARDED_TOPIC")
	ppcProcessedGroupId := os.Getenv("INDEXATION_KAFKA_GROUP") + "_hybrid_"

	//jobmetaTopic := os.Getenv("JOB_META_INIT_KAFKA_TOPIC")
	//jobmetaTopicGroupId := os.Getenv("INDEXATION_KAFKA_GROUP") + "_meta_"

	// Start the message fetcher goroutine
	for i := 0; i < shards; i++ {
		go func() {
			kafkaSubscriber := messaging.NewKafkaSubscriber(
				[]string{os.Getenv("INDEXATION_KAFKA_ADDRESS_0"), os.Getenv("INDEXATION_KAFKA_ADDRESS_1"), os.Getenv("INDEXATION_KAFKA_ADDRESS_2")},
				os.Getenv("JOB_PROCESSING_KAFKA_OUTGOING_JOBS_SHARDED_TOPIC"),
				os.Getenv("INDEXATION_KAFKA_GROUP"),
			)

			// THIS IS TEMPORARY WHILE WE'RE ON FALCON PHASE 1 "PRIME"
			// WE WILL READ FROM job_ppc_processed_{shard} AND INSERT THE JOBS IN POSTGRESQL
			// WHY? BECAUSE THE JOBS ARE BEING DUMPED FROM ELASTIC SEARCH SO WE DONT NEED TO ASSIGN THEM A CAMPAIGN
			// TO GO BACK TO NORMAL JUST REMOVE THE IF CONDITION AND/OR CHANGE THE ENV VARIABLE "FEATURE_HYBRID_FALCON"

			if os.Getenv("FEATURE_HYBRID_FALCON") == "1" {

				// fmt.Println(os.Getenv("JOB_PROCESSING_KAFKA_ADDRESS_0"), os.Getenv("JOB_PROCESSING_KAFKA_ADDRESS_1"), os.Getenv("JOB_PROCESSING_KAFKA_ADDRESS_2"))
				// fmt.Println("topic ", fmt.Sprintf(os.Getenv("JOB_PROCESSING_KAFKA_OUTGOING_JOBS_SHARDED_TOPIC"), strconv.Itoa(i)))
				// fmt.Println("consumer group", os.Getenv("INDEXATION_KAFKA_GROUP")+"_hybrid_"+strconv.Itoa(i))

				kafkaSubscriber = messaging.NewKafkaSubscriber(
					[]string{os.Getenv("JOB_PROCESSING_KAFKA_ADDRESS_0"), os.Getenv("JOB_PROCESSING_KAFKA_ADDRESS_1"), os.Getenv("JOB_PROCESSING_KAFKA_ADDRESS_2")},
					fmt.Sprintf(ppcProcessedTopic, i),
					ppcProcessedGroupId+strconv.Itoa(i),
				)
			}

			fetchMessages(ctx, l, kafkaSubscriber, messages, errChan)
		}()
	}

	return processMessages(ctx, l, messages, cache, publisher, storer, errChan, oTel)

}

func fetchMessages(ctx context.Context, l logger.Logger, subscriber messaging.MessageSubscriber, messages chan messaging.Message, errChan chan error) {
	defer subscriber.Close()

	l.Info("Starting message fetcher " + os.Getenv("INDEXATION_KAFKA_TOPIC"))
	// Create a new exponential backoff
	expBackoff := backoff.NewExponentialBackOff()
	expBackoff.InitialInterval = 100 * time.Millisecond
	expBackoff.MaxInterval = 10 * time.Second

	// Circuit breaker
	var consecutiveErrors int
	maxConsecutiveErrors := 5

	for {
		select {
		case <-ctx.Done():
			l.Info("Context cancelled, stopping message fetcher")
			return
		default:
			msg, err := subscriber.ReadMessage()
			if err != nil {
				if err == context.Canceled {
					return
				}

				if kafkaError, ok := err.(kafka.Error); ok && kafkaError.Title() == "Rebalance in progress" {
					backoffDuration := expBackoff.NextBackOff()
					l.Warn(fmt.Sprintf("Rebalance in progress, waiting %v", backoffDuration))
					time.Sleep(backoffDuration)
					continue
				}

				consecutiveErrors++
				if consecutiveErrors >= maxConsecutiveErrors {
					l.Error("Too many consecutive errors, breaking fetch loop", err)
					errChan <- err
					return
				}

				l.Warn("Error reading message", err)
				continue
			}

			// Reset backoff and error count on successful message read
			expBackoff.Reset()
			consecutiveErrors = 0

			select {
			case messages <- msg:
				// Message sent successfully
			case <-ctx.Done():
				return
			}
		}
	}
}

func processMessages(ctx context.Context, l logger.Logger, messages <-chan messaging.Message, cache cache.Cache, publisher messaging.MessagePublisher, storer application.EmployerDataManager, errChan <-chan error, oTel telemetry.OTel) error {
	workers, _ := strconv.Atoi(os.Getenv("MAX_PROCESSING_WORKERS"))
	semaphore := make(chan struct{}, workers)
	partitionedTopics := make(map[int]string, jobsharding.NumReplicas)
	for i := 0; i < jobsharding.NumReplicas; i++ {
		partitionedTopics[i] = fmt.Sprintf(os.Getenv("JOB_PROCESSING_KAFKA_OUTGOING_JOBS_SHARDED_TOPIC"), i)
	}
	for {
		select {
		case msg := <-messages:
			semaphore <- struct{}{}
			go func() {
				defer func() {
					<-semaphore
				}()
				oTel.AddValue(ctx, "jobs_received", 1)
				job := application.NewJobMessage()
				err := json.Unmarshal([]byte(msg.Value), job)
				if err != nil {
					l.Warn(fmt.Sprintf("error unmarshalling message, key: %v ", string(msg.Key)), err)
					oTel.AddValue(ctx, "jobs_unmarshal_error", 1)
				} else {
					// telemetry, _ := application.NewTelemetry()
					startTime := time.Now()
					params := application.JobParams{
						Ctx:                  ctx,
						Cache:                cache,
						Publisher:            publisher,
						Job:                  job,
						Topic:                partitionedTopics,
						UnprocessedJobsTopic: os.Getenv("JOB_PROCESSING_KAFKA_UNPROCESSED_JOBS_TOPIC"),
						Storer:               storer,
						Message:              msg,
						OTel:                 oTel,
					}
					if os.Getenv("FEATURE_HYBRID_FALCON") == "1" {
						params.OnlyStoreJob = true
					}
					application.ProcessJob(params)
					params.OTel.AddValue(ctx, "jobs_processed_elapsed_time", float64(time.Since(startTime)))
				}
			}()
		case <-ctx.Done():
			l.Info("Context cancelled, stopping message processing")
			return nil
		case err := <-errChan:
			l.Error("error reading message", err)
			time.Sleep(5 * time.Millisecond)
			return err
		}
	}
}
