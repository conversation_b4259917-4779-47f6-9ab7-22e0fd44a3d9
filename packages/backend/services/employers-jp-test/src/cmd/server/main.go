package main

import (
	"context"
	"employers-job-processing/src/api/rest"
	"employers-job-processing/src/internal/application"
	"employers-job-processing/src/internal/handlers"
	"employers-job-processing/src/internal/infrastructure/cache"
	"flag"
	"net/http"
	"os"
	"os/signal"
	"sync"
	logger "talent/libs/logger/src"
	"time"
)

func main() {
	ctx := context.Background()
	l := logger.NewTalentLogger(logger.NewMultiOutput(&logger.ConsoleOutput{}))
	if err := run(ctx, l); err != nil {
		l.Error("error running server:", err)
		os.Exit(1)
	}
}

func run(ctx context.Context, l logger.Logger) error {
	ctx, cancel := signal.NotifyContext(ctx, os.Interrupt)
	defer cancel()

	var (
		port = flag.String("port", "8080", "port to listen on")
		host = flag.String("host", "", "host to listen on")
	)

	as := application.NewAccountService()
	js := application.NewJobService()
	storer := application.NewStorer()
	c := cache.NewRedisCache("data")
	server := rest.NewServer(l, rest.Config{
		Host: *host,
		Port: *port,
	})
	server.Post("/api/v1/cache-account", handlers.AddAccountToCacheHandler(l, c, as))
	server.Post("/api/v1/delete-cache-account", handlers.RemoveAccountToCacheHandler(l, c, as))
	server.Get("/api/v1/get-employer-data", handlers.EmployerJobDataHandler(l, js, storer))
	server.Get("/api/v1/get-jobs-by-filter", handlers.EmployerJobsByFilterHandler(l, js, storer))
	server.Get("/api/v1/get-employer-data-bulk", handlers.EmployerJobsByFilterHandler(l, js, storer))
	server.Get("/api/v1/get-employer-jobs-count", handlers.EmployerJobCountHandler(l, js, storer))
	server.Get("/", http.NotFoundHandler())

	go func() {
		l.Info("starting server on", *host+":"+*port)
		if err := server.ServeHTTP(); err != nil && err != http.ErrServerClosed {
			l.Error("error starting server:", err)
		}
	}()
	var wg sync.WaitGroup
	wg.Add(1)
	go func() {
		defer wg.Done()
		<-ctx.Done()
		shutdownCtx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()
		if err := server.Shutdown(shutdownCtx); err != nil {
			l.Error("error shutting down server:", err)
		}
	}()
	wg.Wait()
	return nil
}
