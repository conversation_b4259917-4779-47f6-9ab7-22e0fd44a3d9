import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "typeorm";
import { HttpException, HttpStatus, Injectable, Logger } from "@nestjs/common";
import { CampaignFiltersRepository } from "../repositories/campaign-filters.repository";
import { CampaignFilters } from "../entities/campaign-filter.entity";
import { JobsFieldsForFilters } from "../../common/resources/enums";
import { SearchJobsDto } from "../dto/search-jobs.dto";
import { CreateCampaignFilterDto } from "../dto/create-campaign-filter.dto";
import { plainToClassFromExist } from "class-transformer";
import { ValidationError, validate } from "class-validator";
import {
  ByFeedcodeFilterDto,
  ByFiltersFilterDto,
  ByJobsFilterDto,
  ByLocationFilterDto,
  ByTagsFilterDto,
} from "../dto/campaign-filters.dto";
import { Campaigns } from "../entities/campaign.entity";
import axios from "axios";

interface GeocodeServiceRequest {
  location?: string;
  country?: string;
}

interface GeoLocationResponse {
  AdministrativeAreaLevel1?: string;
  Locality?: string;
  [key: string]: any;
}

/**
 * This is intended to provide features related to campaigns filters such as create/ update/ getFilters
 */
@Injectable()
export class CampaignFiltersService {
  private readonly geoCodeUrl =
    "http://geocode-service.apps.talent.com/api/v1/geocode/geo-from-string";
  jsonFilters: any = {}; // Declare jsonFilters as a class variable
  private jobsEndpointFields: string = "";
  private internalFiltersPriorities = {
    reqid: 18,
    title: 7,
    empname: 5,
    city: 3,
    region: 1,
  };

  /**
   * Init the class
   */
  constructor(
    private readonly entityManager: EntityManager,
    private readonly filtersRepository: CampaignFiltersRepository,
  ) {}
  /**
   * Get the filters in json format and turn them into mysql campaign_filters records.
   * In the future, this will work to get the filters of a campaign multicountry but then the country will have to be included
   * into the filterGroup like this:
   * {"filters":[{"country":"at","region":"Upper Austria","title":"Verschub","empname":"ÖBB-Konzern","rawFilter":"yes","rawFilters":"title,empname,region"},...another group]}
   * not like this (currently):
   * {"country":"at","filters":[{"region":"Upper Austria","title":"Verschub","empname":"ÖBB-Konzern","rawFilter":"yes","rawFilters":"title,empname,region"},...another group]}
   * @param campaign Campaign object after saved by transaction
   * @param filterText Filter text in json format
   * @param returnFilters boolean parameter used mainly in the campaign's transaction to return the formatted filters to use after.
   */
  async processFilterText(
    campaign: Campaigns,
    filterText: any,
    returnFilters: boolean = false,
    entityManager?: any,
  ): Promise<any> {
    const filtersToCreateOrUpdate: any = [];
    if (typeof filterText === "string") filterText = JSON.parse(filterText);

    const { id, type } = campaign;
    await this.validateCampaignFilter(type, filterText);
    for (const [key, value] of Object.entries<any>(filterText)) {
      if (key === "filters") {
        await this.processFilterGroups(value, id, filtersToCreateOrUpdate);
      } else {
        this.processSingleFilter(key, value, id, filtersToCreateOrUpdate);
      }
    }

    await this.createOrUpdateFilters(id, filtersToCreateOrUpdate, entityManager);
    if (returnFilters) return filtersToCreateOrUpdate;
  }

  /**
   * This is intended to validate the format of the filters' json sent to fill the campaign_filters table
   * @param campaignType Campaign Type
   * @param filterText Filter text in json format
   */
  async validateCampaignFilter(campaignType: string | null, filterText: any) {
    let dtoClass;

    switch (campaignType) {
      case "by_feedcode":
        dtoClass = ByFeedcodeFilterDto;
        break;
      case "by_location":
        dtoClass = ByLocationFilterDto;
        break;
      case "by_jobs":
        dtoClass = ByJobsFilterDto;
        break;
      case "by_tags":
        dtoClass = ByTagsFilterDto;
        break;
      case "by_filters":
        dtoClass = ByFiltersFilterDto;
        break;
      default:
        throw new HttpException("Invalid campaign type", HttpStatus.BAD_REQUEST);
    }

    const dtoInstance = plainToClassFromExist(new dtoClass(), filterText);
    const errors = await validate(dtoInstance);

    if (errors.length > 0) {
      const errorMessage = this.formatValidationErrors(errors);

      console.log(`Campaign filters validation failed: ${errorMessage}`);

      throw new HttpException(
        `Campaign filters validation failed: ${errorMessage}`,
        HttpStatus.BAD_REQUEST,
      );
    }

    return dtoInstance;
  }

  /**
   * Process all filter gruoups in order to know what is best to add. It calls processFilterGroup to process a single
   * group
   * @param filterGroups
   * @param campaignId
   * @param filtersToCreateOrUpdate used to acumulate the filters to crete/update
   */
  async processFilterGroups(
    filterGroups: any[],
    campaignId: number,
    filtersToCreateOrUpdate: any[],
  ): Promise<void> {
    let filterGroupValue = 0;
    for (const filterGroup of filterGroups) {
      filterGroupValue++;
      const filterGroupRecords = this.processFilterGroup(filterGroup, filterGroupValue, campaignId);
      filtersToCreateOrUpdate.push(...filterGroupRecords);
    }
  }

  /**
   * Process a single filter row
   * @param key filterType
   * @param value filterValue
   * @param campaignId
   * @param filtersToCreateOrUpdate used to acumulate the filters to crete/update
   */
  processSingleFilter(
    key: string,
    value: any,
    campaignId: number,
    filtersToCreateOrUpdate: any[],
  ): void {
    const exactMatch = key === "rawFilters" ? 1 : 0;
    //When tag or _id we must treat value as an array since it can come like ["tag1","tag2","tag3"] OR ["jobid1","jobid2","jobid3"]
    if (key === "tag" || key === "_id") {
      const quotedValues: string = value
        .map((value: string, index: number, array: Array<string> | any[]) => {
          if (index === array.length - 1) {
            return `"${value}"`;
          } else {
            return `"${value}",`;
          }
        })
        .join("");
      value = quotedValues;
    }

    filtersToCreateOrUpdate.push({
      campaignId,
      filterType: key,
      filterValue: value,
      filterPriority: 0,
      exactMatch,
      filterGroup: null,
    });
  }

  /**
   * Process a group of filters
   * @param filterGroup one siblge filter group
   * @param filterGroupValue this is the identifier used to know which filters must be chained by
   * AND or OR in sql clause
   * @param campaignId
   * @param filtersToCreateOrUpdate used to acumulate the filters to crete/update
   */
  processFilterGroup(filterGroup: any, filterGroupValue: number, campaignId: number): any[] {
    return Object.entries(filterGroup)
      .filter(([filterType]) => filterType !== "rawFilter" && filterType !== "rawFilters")
      .map(([filterType, filterValue]) => {
        let exactMatch = 0;
        if (
          (filterGroup.rawFilter === "yes" || filterGroup.rawFilter === 1) &&
          filterGroup.rawFilters
        ) {
          const rawFilters = filterGroup.rawFilters.split(",");
          if (rawFilters.includes(filterType)) {
            exactMatch = 1;
          }
        }
        return {
          campaignId,
          filterType,
          filterValue,
          filterPriority: 0,
          exactMatch,
          filterGroup: filterGroupValue,
        };
      });
  }

  /**
   * This is intended to insert/update the rows of filters into the campaign_filters table
   * @param array filters to be inserted/updated
   */
  async createOrUpdateFilters(id: number, filters: any[], entityManager?: any): Promise<any> {
    try {
      this.filtersRepository.updateFiltersByCampaign(id, entityManager);
      for (const filter of filters) {
        await this.filtersRepository.create(filter, entityManager);
      }
      return { result: true };
    } catch (error: any) {
      console.error("Error creating/updating filters:", error.message);
      return {
        result: false,
        message: `Error creating/updating filters:${error.message}`,
      };
    }
  }

  /**
   * Retrieves normalized geographical information (e.g., country and region)
   * by calling the external Geo API. This is used during campaign creation or update
   * to standardize user-provided location input. The method attempts the request once,
   * and retries once more in case of failure before returning an error.
   *
   * @param query - The geolocation query containing user input for country and/or region.
   * @param retry - Internal parameter to track if the request is a retry attempt (default is 0).
   * @returns A Promise resolving to a tuple: [GeoLocationResponse, Error|null].
   */
  private async getGeoCode(
    query: GeocodeServiceRequest,
    retry: number = 0,
  ): Promise<[GeoLocationResponse, Error | null]> {
    try {
      const response = await axios({
        method: "get",
        url: this.geoCodeUrl,
        data: query,
        headers: {
          "Content-Type": "application/json",
        },
      });

      return [response.data as GeoLocationResponse, null];
    } catch (error) {
      if (retry === 0) {
        console.log("Retrying for:", JSON.stringify(query));
        return await this.getGeoCode(query, 1);
      }

      console.log("Ultimately failed for:", JSON.stringify(query));
      return [{} as GeoLocationResponse, error as Error];
    }
  }

  /**
  /**
   * Wrapper method to create/update filters when a campaign transaction is initialized.
   * @param filters array containing CreateCampaignFilterDto objects to be inserted
   * @returns the filter objects after being saved.
   */
  async createOrUpdateFiltersTransaction(
    filterText: any,
    campaign: Campaigns,
    entityManager?: any,
  ): Promise<CampaignFilters[]> {
    const repository = entityManager
      ? entityManager.getRepository(CampaignFilters)
      : this.filtersRepository;
    try {
      // Extract the filter string into filter dto objects.
      const filters = await this.processFilterText(campaign, filterText, true, entityManager);
      // Retrieve all filters related to the campaignId
      const existingFilters = await repository.find({
        where: { campaignId: filters[0].campaignId, active: 1 },
      });

      // Find the country filter
      const countryFilter = filters.find((filter: any) => filter.filterType === "country");

      // Process the filters
      const promises = filters.map(async (filterData: any) => {
        const { filterType } = filterData;
        let filterValue = filterData.filterValue;
        // Check for array of tags/jobIds, since we need to properly transform it into an string of comma separated strings
        if (
          (filterType === "tag" || filterType === "_id") &&
          Array.isArray(filterData.filterValue)
        ) {
          filterValue = await this.transformFilterTag(filterData.filterValue);
        }

        if (filterType === "region") {
          const [geoData, error] = await this.getGeoCode({
            country: countryFilter?.filterValue,
            location: filterValue,
          });

          if (error) {
            Logger.log("Geocode failed:", error.message || error);
          }

          const regionName = geoData?.data?.administrative_area_level_1;
          Logger.log("Geo response:", regionName);

          filterData.geoLocation = regionName;
        } else if (filterType === "city") {
          const [geoData, error] = await this.getGeoCode({
            country: countryFilter?.filterValue,
            location: filterValue,
          });
          if (error) {
            Logger.log("Geocode failed:", error.message || error);
          }
          const cityName = geoData?.data?.locality;
          filterData.geoLocation = cityName;
        }

        // Select the correct filter to update. Note: sometimes filters can have more than one row with the same filterType.
        // That is why we are using filterPriority and group to complement the search.
        const existingFilter = existingFilters.find(
          (filter: { filterType: any; filterPriority: any; filterGroup: any }) =>
            filter.filterType === filterType &&
            filter.filterPriority === filterData.filterPriority &&
            filter.filterGroup === filterData.filterGroup,
        );

        if (existingFilter) {
          // If filter with the same filterType and filterValue exists, update its properties
          filterData.id = existingFilter.id;
          filterData.active = 1;
          return await repository.save(filterData);
        } else {
          filterData.filterValue = filterValue;
          // If filter doesn't exist, create a new one
          // const newFilter = await this.filtersRepository.create(
          //   filterData as CreateCampaignFilterDto,
          // );
          // return await this.filtersRepository.save(newFilter);

          return await repository.save(filterData);
        }
      });
      // Wait for all promises to resolve
      const savedFilters = await Promise.all(promises);
      return savedFilters;
    } catch (error: any) {
      console.error("Error creating/updating filters:", error.message);
      throw new HttpException(
        { error: error.error, message: ["filters: " + error.message] },
        HttpStatus.BAD_REQUEST,
        { cause: new SyntaxError() },
      );
    }
  }

  /**
   * Adds all the filters to filters group
   */
  addToFiltersGroups(filterGroups: any) {
    const keys = Object.keys(filterGroups);
    if (keys.length > 0) {
      this.jsonFilters.filters = Object.values(filterGroups);
    }
  }

  /**
   * Adds a single filter to the final result
   */
  addSingleFilter(filter: any) {
    if (filter.filterType == "tag" || filter.filterType == "_id") {
      const tagsValues =
        filter.filterValue
          .match(/(?:"([^"]+)"|([^,]+))/g) // Explicit grouping for tags and values
          ?.map((value: string) => {
            return value.trim().replace(/(^")|("$)/g, ""); // Remove surrounding quotes
          }) || [];
      filter.filterValue = tagsValues;
    }
    this.jsonFilters[filter.filterType] = filter.filterValue;
  }

  /**
   * Updates a record into the filters group
   */
  updateFilterGroups(filter: any, filterGroups: any) {
    // Add filter properties to an existing filter group object
    filterGroups[filter.filterGroup][filter.filterType] = filter.filterValue;
    //Validating exactness required in filters
    if (filter.exactMatch === 1) {
      filterGroups[filter.filterGroup].rawFilter = 1;
      filterGroups[filter.filterGroup].rawFilters = filterGroups[filter.filterGroup].rawFilters
        ? `${filterGroups[filter.filterGroup].rawFilters},${filter.filterType}`
        : filter.filterType;
    }
    return filterGroups;
  }
  /**
   * Adds a record in hte filters group
   */
  addFilterGroup(filter: any) {
    if (filter.filterType == "region" || filter.filterType == "city") {
      return {
        filter_group: filter.filterGroup,
        [filter.filterType]: filter.geoLocation,
        rawFilter: filter.exactMatch === 1 ? 1 : 0,
        rawFilters: filter.exactMatch === 1 ? filter.filterType : "",
      };
    }
    return {
      filter_group: filter.filterGroup,
      [filter.filterType]: filter.filterValue,
      rawFilter: filter.exactMatch === 1 ? 1 : 0,
      rawFilters: filter.exactMatch === 1 ? filter.filterType : "",
    };
  }

  /**
   * Gets all the filters related to a campaign and returns then in json format
   * @param campaignId int Campaign ID
   * @returns json with all the filters related to the campaign
   */
  async getCampaignFiltersByCampaignId(campaignId: number, format: string = "json"): Promise<any> {
    try {
      // Fetch all filter records for the specified campaignId
      const campaignFiltersRows = await this.filtersRepository.findBy(campaignId);

      switch (format) {
        case "json":
          return this.getFiltersInJsonFormat(campaignFiltersRows);
        case "jobs-endpoint":
          return this.getFiltersInEndpointsFormat(campaignFiltersRows);
      }
    } catch (error) {
      console.error(error);
      throw error;
    }
  }

  /**
   * Return campaigns filters records in the format of an array containing indexation jobs endpoint's queries
   */
  getFiltersInEndpointsFormat(filtersRows: CampaignFilters[]): Array<string> {
    const filtersJson = this.getFiltersInJsonFormat(filtersRows);
    return this.buildEndpointUrlsArray(filtersJson);
  }

  /**
   * Return campaigns filters records in json format
   */
  getFiltersInJsonFormat(filters: CampaignFilters[]): string {
    // Initialize JSON object
    this.jsonFilters = {};

    // Initialize an object to store filter groups
    let filterGroups: any = {};
    // Iterate over the retrieved filter records
    for (const filter of filters) {
      // Process filter based on filter group
      if (!filter.filterGroup || filter.filterGroup === 0) {
        // If filter_group is 0, add the filter directly to the JSON object
        this.addSingleFilter(filter);
      } else if (!filterGroups[filter.filterGroup]) {
        // Check if filter group exists in filterGroups object
        // Create a new filter group object
        filterGroups[filter.filterGroup] = this.addFilterGroup(filter);
      } else {
        // Update rawFilter and rawFilters properties
        filterGroups = this.updateFilterGroups(filter, filterGroups);
      }
    }
    //Adds all of the filters to filters group
    this.addToFiltersGroups(filterGroups);
    return this.jsonFilters;
  }

  /**
   * Return the queryString for the specified field
   */
  assignQueryString(key: string, value: string): string {
    const fieldValue = JobsFieldsForFilters[key as keyof typeof JobsFieldsForFilters]; // Access enum property dynamically
    return `${fieldValue}=${value}`;
  }

  /**
   * Get indexations urls for a campaign by_jobs
   */
  getUrlsByJobs(filtersArray: any): Array<string> {
    let filtersString: string = "";
    const endpointUrlsArray: Array<string> = [];
    const country = filtersArray.country ? `&enrich_geo_country=${filtersArray.country}` : ""; //country and feedcode should always come
    const feedcode = filtersArray.feedcode ? `&system_feedcode=${filtersArray.feedcode}` : "";
    //let's make an array so we don't repeat code doing searching next
    const arraySearchByJobsItems: Array<string> = [
      "_id",
      "title",
      "tag",
      "region1",
      "city",
      "reqid",
    ]; //"region2", "country" and "empname" seems to not be accepted for searching by indexation
    if (filtersArray.search && filtersArray.search != "") {
      //We have to form an array of urls since we need to simulate this construction
      //'id' OR 'title' OR 'client_tag' OR 'geo_country' OR 'geo_region1' OR 'geo_region2' OR 'geo_city' OR 'reqid'

      for (const searchItem of arraySearchByJobsItems) {
        filtersString = this.assignQueryString(searchItem, filtersArray.search);
        endpointUrlsArray.push(`${filtersString}${feedcode}${country}${this.jobsEndpointFields}`);
      }
    }

    if (filtersArray._id) {
      const stringified = JSON.stringify(filtersArray._id);
      filtersArray._id = stringified;
      // Remove square brackets and double quotes, and split the string into an array
      const idsArrayWithoutQuotes = filtersArray._id.replace(/[[\]"']+/g, "").split(",");

      // Join the array elements with commas
      const idsStringWithoutQuotes = idsArrayWithoutQuotes.join(",");
      //If it's only _id in the array
      filtersString +=
        filtersString == "" ? `ids=${idsStringWithoutQuotes}` : `&ids=${idsStringWithoutQuotes}`; //_ids can be more than one ["id1","id2","id3"]
      endpointUrlsArray.push(`${filtersString}${this.jobsEndpointFields}`);
    }
    return endpointUrlsArray;
  }

  /**
   * Get indexations urls for a campaign by_feedcode
   */
  getUrlsByFeedcode(filtersArray: any): Array<string> {
    let filtersString: string = "";
    const endpointUrlsArray: Array<string> = [];

    if (filtersArray.feedcode) {
      //if it's only feedcode in the array
      filtersString += this.assignQueryString("feedcode", filtersArray.feedcode);
      endpointUrlsArray.push(filtersString + this.jobsEndpointFields);
    }
    return endpointUrlsArray;
  }

  /**
   * Parsing different ways of tags to come
   */
  parseTags(tagString: string): string[] {
    // Remove enclosing quotes if present
    if (tagString.startsWith('"') && tagString.endsWith('"')) {
      tagString = tagString.slice(1, -1);
    }

    // Split by ::: if present
    if (tagString.includes(":::")) {
      return tagString.split(":::");
    }

    // Split by "," if present and remove extra quotes
    if (tagString.includes(",")) {
      return tagString.split(",").map((tag) => tag.replace(/(^")|("$)/g, "").trim());
    }

    // Handle single tag
    return [tagString];
  }

  /**
   * Get indexations urls for a campaign by_tags
   */
  getUrlsByTags(filtersArray: any): Array<string> {
    let filtersString: string = "";
    const endpointUrlsArray: Array<string> = [];
    const country = filtersArray.country ? `&enrich_geo_country=${filtersArray.country}` : "";
    const feedcode = filtersArray.feedcode ? `&system_feedcode=${filtersArray.feedcode}` : "";
    let arrayTags: Array<string>;

    let filtersArrayString;
    if (filtersArray.tag) {
      if (Array.isArray(filtersArray.tag)) {
        filtersArrayString = filtersArray.tag.join(",");
      } else {
        filtersArrayString = filtersArray.tag;
      }
      arrayTags = this.parseTags(filtersArrayString);
      for (const tagValue of arrayTags) {
        //tags can be more than one ["tag1","tag2","tag3"]
        filtersString = this.assignQueryString("tag", tagValue);
        endpointUrlsArray.push(`${filtersString}${feedcode}${country}${this.jobsEndpointFields}`);
      }
    }
    return endpointUrlsArray;
  }

  /**
   * Get indexations urls for a campaign by_location/country
   */
  getUrlsByLocation(filtersArray: any): Array<string> {
    let filtersString: string = "";
    const endpointUrlsArray: Array<string> = [];
    const feedcode = filtersArray.feedcode ? `&system_feedcode=${filtersArray.feedcode}` : "";
    if (filtersArray.country) {
      filtersString += this.assignQueryString("country", filtersArray.country);
      endpointUrlsArray.push(`${filtersString}${feedcode}${this.jobsEndpointFields}`);
    }
    return endpointUrlsArray;
  }

  /**
   * Get indexations urls for a campaign by_filters
   */
  getUrlsByFilters(filtersArray: any): Array<string> {
    let filtersString: string = "";
    let endpointUrlsArray: Array<string> = [];
    const feedcode = filtersArray.feedcode ? `system_feedcode=${filtersArray.feedcode}` : "";
    const country = filtersArray.country ? `&enrich_geo_country=${filtersArray.country}` : "";
    filtersString = `${feedcode}${country}`;

    if (filtersArray.filters) {
      endpointUrlsArray = this.buildFiltersArray(filtersString, filtersArray.filters);
    }
    return endpointUrlsArray;
  }

  /**
   * Shape the filters string based on the array given when exists
   */
  buildEndpointUrlsArray(filtersArray: any): Array<string> {
    let endpointUrlsArray: Array<string> = [];
    const type: string = filtersArray.type;

    switch (type) {
      case "by_feedcode":
      case "by_alljobs":
        endpointUrlsArray = this.getUrlsByFeedcode(filtersArray);
        break;
      case "by_jobs":
        endpointUrlsArray = this.getUrlsByJobs(filtersArray);
        break;
      case "by_tags":
        endpointUrlsArray = this.getUrlsByTags(filtersArray);
        break;
      case "by_location":
      case "by_country":
        endpointUrlsArray = this.getUrlsByLocation(filtersArray);
        break;
      case "by_filters":
      case "by_filter":
        endpointUrlsArray = this.getUrlsByFilters(filtersArray);
        break;
    }
    return endpointUrlsArray;
  }

  /**
   * This returns the array of Urls to call jobs indexation service
   */
  async getIndexationUrls(jobsFilters: SearchJobsDto | null, campaignID: number | null) {
    if (jobsFilters != null) {
      return this.buildEndpointUrlsArray(jobsFilters);
    } else if (campaignID != null) {
      return await this.getCampaignFiltersByCampaignId(campaignID, "jobs-endpoint");
    }
  }

  /**
   * Returns an array of filters which will be used to call the indexation service in order to get lists of jobs
   */
  buildFiltersArray(filtersString: string, filtersArray: any): Array<string> {
    const arrayOfFilters: Array<string> = [];
    for (const group of filtersArray) {
      arrayOfFilters.push(this.buildGroupOfFilters(filtersString, group) + this.jobsEndpointFields);
    }
    return arrayOfFilters;
  }

  /**
   * Build the queryStrings for each node of the filtersArray and put it into an Array
   */
  buildGroupOfFilters(filtersString: string, filtersArray: any): string {
    if (filtersArray.title) {
      filtersString += `&${this.assignQueryString("title", filtersArray.title)}`;
    }
    if (filtersArray.region) {
      filtersString += `&${this.assignQueryString("region1", filtersArray.region)}`;
    }
    if (filtersArray.city) {
      filtersString += `&${this.assignQueryString("city", filtersArray.city)}`;
    }
    if (filtersArray.empname) {
      filtersString += `&${this.assignQueryString("empname", filtersArray.empname)}`;
    }
    if (filtersArray.reqid) {
      filtersString += `&${this.assignQueryString("reqid", filtersArray.reqid)}`;
    }

    return filtersString;
  }

  /**
   * Reorders a campaign list to include filters by selector one by one, ordered by their internal priority.
   * Each internal filter has a different priority, and when normalized to the external filter priorities,
   * it defines which campaign will be used first.
   * @param {array} campaignList - List of campaigns that need to be ordered in case of filters by selector.
   * @return {array} - Ordered list of campaigns.
   */
  reorderCampaignsByFilters(campaignList: any[]): any[] {
    let newCampaignList: any[] = []; // Initialize a new array to store reordered campaigns
    let needToBeOrdered = false; // Flag to track if reordering is needed

    for (const campaign of campaignList) {
      try {
        // Check if campaign type is by_filer(indicating the need for reordering)
        if (campaign["type"] === "by_filters") {
          // Calculate priority of internal filters
          const campaignOrderedFilters = this.calculateInternalFiltersPriority(campaign);
          // Add ordered campaigns to the new list
          newCampaignList.push(...campaignOrderedFilters);
          // Set the flag to true, indicating reordering is needed
          needToBeOrdered = true;
        } else {
          const newCampaign = JSON.parse(JSON.stringify(campaign));
          newCampaign.campaignFilters = this.getFiltersInJsonFormat(newCampaign.campaignFilters);
          newCampaign.filterPriority = newCampaign.priority;
          newCampaign.campaignFilters = this.objectToString(newCampaign.campaignFilters);
          // Add campaign to the new list without reordering
          newCampaignList.push(newCampaign);
        }
      } catch (error: any) {
        console.log("There was an error processing this campaign: ", error.message, campaign.id);
      }
    }

    if (needToBeOrdered) {
      // If reordering is needed
      newCampaignList.sort((a, b) => {
        // Sort the new list
        if (a.filterPriority === b.filterPriority) {
          return b.campaignID - a.campaignID; // Sort by campaignID DESC
        }
        return a.filterPriority - b.filterPriority; // Sort by filterPriority ASC
      });
    }
    newCampaignList = newCampaignList.map(({ filterPriority, ...rest }) => rest); //Getting rid of the filterPriority filed since it's not needed in the final response to cache
    return newCampaignList; // Return the reordered list of campaigns
  }

  /**
   * Function to group by filterGroup and return as an array of arrays
   */
  groupByFilterGroup(data: CampaignFilters[]): CampaignFilters[][] {
    const groupedData: { [key: number]: CampaignFilters[] } = data.reduce(
      (acc, item) => {
        if (item.filterType !== "country") {
          if (!acc[item.filterGroup]) {
            acc[item.filterGroup] = [];
          }
          acc[item.filterGroup].push(item);
        }
        return acc;
      },
      {} as { [key: number]: CampaignFilters[] },
    );

    return Object.values(groupedData);
  }

  /**
   * Calculate the internal filters priority of each internal filter of the campaign.
   * @param {array} campaignData - Campaign data with filter type by selector to divide it one by one.
   * @return {array} - Array containing campaigns with internal filters priority calculated.
   */
  calculateInternalFiltersPriority(campaignData: any): any[] {
    const campaignsData: any[] = []; // Initialize an array to store campaign data with calculated priorities
    // If campaign has no filters or less than 1 filter, return the original campaign data
    if (!campaignData["campaignFilters"] || campaignData["campaignFilters"].length < 1) {
      //Set filters as an empty array
      campaignData["campaignFilters"] = "{}";
      return [campaignData];
    }

    // Destructure campaignFilters from campaignData
    const { campaignFilters, ...otherProps } = campaignData;

    const groupedData: CampaignFilters[][] = this.groupByFilterGroup(campaignFilters);

    // Let's catch the country of the campaigns' filters
    const countryTag = this.getFiltersCountryTag(campaignFilters);

    for (const group of groupedData) {
      let internalPriority = 0;
      // Calculate internal priority based on filter types and their priorities
      for (const [keyPriority, filterPriority] of Object.entries(this.internalFiltersPriorities)) {
        // Check if the specific internalFiltersPriorities item is within the group
        if (
          group.some(
            (item: { filterType: string }) => item.filterType.toLowerCase() === keyPriority,
          )
        ) {
          internalPriority += filterPriority;
        }
      }

      // Get the average of the priorities by the number of filters.
      internalPriority = internalPriority / Object.keys(this.internalFiltersPriorities).length;
      // Create a new campaign object with updated filter priorities
      const newCam = { ...otherProps };
      //Add the country to the each group of filters so that he becomes part of the whole filters setup
      group.unshift(countryTag);
      newCam["campaignFilters"] = this.getFiltersInJsonFormat(group);
      newCam.filterPriority = 80 - internalPriority; //this is to redorder by this field and then we get rid of it.
      newCam["campaignFilters"]["filterPriority"] = 80 - internalPriority; //this is to show the final calculation in the campaignFilters array
      newCam["campaignFilters"] = this.objectToString(newCam["campaignFilters"]);

      campaignsData.push(newCam);
    }

    return campaignsData; // Return the array of campaigns with internal filters priority calculated
  }

  /**
   * Searches among the filters and returns the country's tag associated to the campaigns filters
   * @param {array} campaignFilters - Array of campaign filters to search for the country tag.
   * @return {object} - Object representing the country tag associated with the campaign filters.
   */
  getFiltersCountryTag(campaignFilters: any): any {
    for (const filter of campaignFilters) {
      // Let's get the country because every filter needs to have it within
      if (filter.filterType === "country") {
        // Return an object representing the country tag
        return {
          filterType: "country",
          filterValue: filter.filterValue,
          filterPriority: 0, // Set priority to 0 as it's not relevant for the country tag
          exactMatch: 0, // Set exactMatch to 0 as it's not relevant for the country tag
          filterGroup: 0, // Set filterGroup to 0 as it's not relevant for the country tag
        };
      }
    }
  }

  /**
   * Converts filters' json object to its string representation in order to send it to processing jobs process
   * through the campaignUpdateRedisCache. So, something like this:
   * {"country": "US",
   *     "filters": [
   *                  {
   *                    "title": "phpmmmsss"
   *                  }
   *                ]
   * }, must become something like this: "{\"country\": \"US\", \"filters\": [{\"title\": \"phpmmmsss\"}]}"
   * @param obj The json object to convert.
   * @returns The string representation of the object.
   */
  objectToString(obj: any): string {
    const result = JSON.stringify(obj)
      .replace(/"(\w+)"\s*:/g, '"$1":') // Remove quotes around keys
      .replace(/:\s*"([^"]*)"/g, ': "$1"') // Make sure string values are within double quotes
      .replace(/\\"/g, ""); // Change "\\" to "\\", so there's only one backslash

    return result;
  }

  /**
   * Takes an array of strings and returns a string of comma separated strings enclosed in " "
   * @param currentValue the received tags request to be created/updated
   * @returns string of the transformed tags.
   */
  async transformFilterTag(currentValue: string[]) {
    const quotedValues: string = currentValue
      .map((value: string, index: number, array: Array<string> | any[]) => {
        if (index === array.length - 1) {
          return `"${value}"`;
        } else {
          return `"${value}",`;
        }
      })
      .join("");
    return quotedValues;
  }

  /**
   * Format validation filters dtos errors
   */
  formatValidationErrors(errors: ValidationError[]) {
    return errors
      .map((e) => {
        if (e.constraints) return Object.values(e.constraints);
      })
      .map((e) => {
        return e;
      })
      .join(",");
  }
}
