import { PartialType } from "@nestjs/mapped-types";
import { CreateCampaignSettingsDto } from "./create-campaigns-setting.dto";
import { IsNotEmpty, IsNumber, IsOptional } from "class-validator";

/**
 *
 */
export class UpdateCampaignSettingDto extends PartialType(CreateCampaignSettingsDto) {
  @IsNumber()
  @IsNotEmpty()
  updatedBy: number | undefined;

  @IsNumber()
  @IsOptional()
  campaignId: number;
}
