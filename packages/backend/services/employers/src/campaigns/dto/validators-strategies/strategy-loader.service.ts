import { Injectable } from "@nestjs/common";
import { ValidationMessage } from "./interfaces/campaigns-validation-strategy.interface";
import { CreateCampaignDto, UpdateCampaignDto } from "../../dto";
import { CampaignBasicValidationsStrategy } from "./strategies";
import { CampaignSponsorshipAdvancedValidationsStrategy } from "./strategies/campaigns-sponsorship-advanced-validations.strategy";
import { CampaignSettingsValidationStrategy } from "./strategies/campaign-settings-validations.strategy";
import { UpdateCampaignSettingDto } from "../update-campaigns-setting.dto";
import { Campaigns } from "../../entities/campaign.entity";
import { CampaignSettings } from "../../entities/campaigns-setting.entity";
import { CampaignObjectiveValidationsStrategy } from "./strategies/campaigns-objective-validations.strategy";
import { CampaignRoleActionsValidationStrategy } from "./strategies/campaigns-role-actions-validations.strategy";

/**
 * Load all of the strategies of validation
 */
@Injectable()
export class StrategyLoader {
  /**
   * Init the class
   */
  constructor(
    private readonly campaignBasicValidations: CampaignBasicValidationsStrategy,
    private readonly campaignSettingsValidations: CampaignSettingsValidationStrategy,
    private readonly campaignSponsorshipAdvanced: CampaignSponsorshipAdvancedValidationsStrategy,
    private readonly campaignObjective: CampaignObjectiveValidationsStrategy,
    private readonly campaignRoleActions: CampaignRoleActionsValidationStrategy,
  ) {}

  /**
   * Invoke all validations
   */
  async validateAll(
    campaign: CreateCampaignDto | UpdateCampaignDto,
    role: string,
    campaignId?: number,
  ): Promise<ValidationMessage[]> {
    const validationMessages: ValidationMessage[] = [];
    try {
      validationMessages.length = 0;
      const basicMessages = await this.campaignBasicValidations.validate(campaign, role, undefined, campaignId);
      validationMessages.push(...basicMessages);
      //Validate sponsorship advanced settings
      const advancedMessages = await this.campaignSponsorshipAdvanced.validate(campaign, role);
      validationMessages.push(...advancedMessages);
      //Validate campaign objective settings
      const objectiveMessages = await this.campaignObjective.validate(campaign, role);
      validationMessages.push(...objectiveMessages);
      // Make sure the current user can perform the actions found in the dto.
      const roleBasedActions = await this.campaignRoleActions.validate(campaign, role);
      validationMessages.push(...roleBasedActions);
      return validationMessages;
    } catch (error: any) {
      console.error(error.message);
      throw new Error(error);
    }
  }

  /**
   * Validates that the update operation complies with the business rules.
   * If valid, it will return an empty ValidationMessage object.
   * If not valid, it will throw an error with the specific business rules that failed.
   * @param settings Current settings object.
   * @param campaign Current campaign object.
   * @param settingsDto DTO with the updated values to compare.
   * @param updateCampaignDto Optional DTO with updated campaign values.
   * @returns A promise with the ValidationMessages array.
   * @throws Error if some business rules are invalid.
   */
  async validateUpdateCampaignSettings(
    settings: CampaignSettings,
    settingsDto: UpdateCampaignSettingDto,
    campaignBefore: Campaigns,
    campaignAfter: Campaigns,
  ): Promise<ValidationMessage[]> {
    try {
      return await this.campaignSettingsValidations.validate(
        settingsDto,
        settings,
        campaignBefore,
        campaignAfter,
      );
    } catch (error: any) {
      console.error(error.message);
      throw new Error(error);
    }
  }
}
