import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
  ValidationPipe,
} from "@nestjs/common";
import { CreateTagTypeDto } from "../dto/create-tag-type.dto";
import { TagsService } from "../services/tags.service";
import { UpdateTagTypeDto } from "../dto/update-tag-type.dto";
import { EntityManager } from "typeorm";
import { CampaignTagService } from "../../campaigns/services/campaign-tag-relation.service";
import { AccountTagRelationService } from "../../accounts/services/account-tag-relation.service";
import { CreateCampaignTagDto } from "../../campaigns/dto/create-campaign-tag-relation.dto";
import { AccountTagType, CampaignTagType } from "../../campaigns/dto/campaign-tag-type.enum";
import { UpdateCampaignTagDto } from "../../campaigns/dto/update-campaign-tag-relation.dto";
import { AccountTagRelationDto } from "../../accounts/dto/account-tag-relation.dto";
import { UpdateAccountTagDto } from "../../accounts/dto/update-account-tag-relations.dto";
import { CreateTagsDto } from "../dto/create-tag.dto";
import { UpdateTagDto } from "../dto/update-tag.dto";
import { SearchTagDto } from "../dto/search-tag.dto";
import { TestReconSftpDto } from "../dto/test-recon-sftp-conn.dto";
import { Public } from "../../auth/decorators/auth.decorator";
import { RequirePermissions } from "../../auth/decorators/permissions.decorator";
import { Permission } from "../../auth/resources/permission.enum";
import { User } from "../../auth/decorators/user.decorator";
import { UserAuthDto } from "../../common/dtos/user-auth.dto";
import { mapNestedToFlat } from "../services/mapper.service";

/**
 * Controller responsible for handling tags related endpoints.
 */
@Controller({
  path: "tags",
  version: "1",
})
export class TagsController {
  /**
   * Init the class objects
   */
  constructor(
    private readonly tagsService: TagsService,
    private readonly campaignTagService: CampaignTagService,
    private readonly accountTagService: AccountTagRelationService,
    private readonly entityManager: EntityManager,
  ) {}

  /**
   * Create new tag
   * @param data
   * @returns
   */
  @Post()
  @RequirePermissions(Permission.CreateTag)
  async createTag(@User() user: UserAuthDto, @Body() data: CreateTagsDto) {
    data.createdBy = user.user_id;
    await this.tagsService.createTag(data);
  }

  /**
   * Upadate tag type
   * @param id
   * @param data
   * @returns
   */
  @Patch(":id")
  @RequirePermissions(Permission.UpdateTagById)
  async updateTag(@Param("id") id: number, @Body() data: UpdateTagDto) {
    await this.tagsService.updateTag(+id, data);
  }

  /**
   * Upadate tag type
   * @param id
   * @param data
   * @returns
   */
  @Delete(":id")
  @HttpCode(HttpStatus.NO_CONTENT)
  async deleteTag(@Param("id", ParseIntPipe) id: number) {
    return await this.tagsService.delete(id);
  }

  /**
   *
   * @param credentials
   * @returns
   */
  @Public()
  @Get("test-reconsftp-connection")
  async testSftpConnection(
    @Query(new ValidationPipe({ transform: true })) credentials: TestReconSftpDto,
  ) {
    return await this.tagsService.testSftpConnection(credentials);
  }

  /**
   * Search Tags using filters
   * @returns
   */
  @Get("search")
  @RequirePermissions(Permission.GetAllTags)
  async search(@Query(new ValidationPipe({ transform: true })) filters: SearchTagDto) {
    return await this.tagsService.search(filters);
  }

  /**
   * Get all tags Types and its Tags
   * @returns
   */
  @Get("tag-types")
  @RequirePermissions(Permission.GetAllTags)
  async findAllTagTypes() {
    return await this.tagsService.findAllTagTypes();
  }

  /**
   * Get all active tag Types (Postback, Reconciliation, etc)
   * @returns
   */
  @Get("tag-types/list")
  @RequirePermissions(Permission.GetAllTags)
  async findTagTypesList() {
    return await this.tagsService.findTagTypesList();
  }

  /**
   * Gets a specific TAG based on its ID.
   * @param id - The unique identifier of the tag you want to obtain
   * @returns The requested account, along with its associated entities if includeEntities is true.
   */
  @Get(":id")
  @RequirePermissions(Permission.GetTagById)
  public async findOne(@Param("id") id: number) {
    return await this.tagsService.findOneTag(+id);
  }

  /**
   * Create new tag type to be used in Tags
   * @param data
   * @returns
   */
  @Post("tag-types")
  @RequirePermissions(Permission.CreateTag)
  async createTagType(@Body() data: CreateTagTypeDto) {
    await this.tagsService.createTagType(data);
  }

  /**
   * Upadate tag type
   * @param id
   * @param data
   * @returns
   */
  @Patch(":id/tag-types")
  @RequirePermissions(Permission.UpdateTagById)
  async updateTagType(@Param("id") id: number, @Body() data: UpdateTagTypeDto) {
    return await this.tagsService.updateTagType(+id, data);
  }

  /**
   * Upadate tag type
   * @param id
   * @param data
   * @returns void
   */
  @Delete(":id/campaign")
  @HttpCode(HttpStatus.NO_CONTENT)
  async deleteCampaignTag(@Param("id", ParseIntPipe) id: number) {
    return await this.campaignTagService.delete(id);
  }

  /**
   * Upadate tag type
   * @param id
   * @param data
   * @returns void
   */
  @Delete(":id/account")
  @HttpCode(HttpStatus.NO_CONTENT)
  async deleteAccountTag(@Param("id", ParseIntPipe) id: number) {
    return await this.accountTagService.delete(id);
  }

  /**
   * Return campaign tags.
   * @param id, it can be a campaign Id or the tag Id.
   * @param type: CampaignTagType, the type of the id provided, id by default.
   * @returns an array of campaign tags.
   */
  @Public()
  @Get("campaign/:id/:type?")
  async getCampaignTags(
    @Param("id", ParseIntPipe) id: number,
    @Param("type") type: CampaignTagType = CampaignTagType.id,
  ) {
    const where = await this.tagsService.getCampaignWhere(type, id);
    const tags = await this.campaignTagService.findBy(where);
    return mapNestedToFlat(tags);
  }

  /**
   * Return campaign tags.
   * @param id, it can be a campaign Id or the tag Id.
   * @param type: CampaignTagType, the type of the id provided, id by default.
   * @returns an array of campaign tags.
   */
  @Public()
  @Get("pixel-postback/campaign/:id/:type?")
  async getCampaignTagsFromPixelPostback(
    @Param("id", ParseIntPipe) id: number,
    @Param("type") type: CampaignTagType = CampaignTagType.id,
  ) {
    const where = await this.tagsService.getCampaignWhere(type, id);
    return await this.campaignTagService.findBy(where);
  }

  /**
   * Creates a new instance of campaign tag.
   * @param data CreateCampaignTransactional dto object with the data for creating a new campaign.
   * @returns void
   */
  @Post("campaign")
  @RequirePermissions(Permission.CreateTag)
  async createCampaignTag(@Body() campaignTagDto: CreateCampaignTagDto) {
    await this.campaignTagService.create(campaignTagDto);
  }

  /**
   * Updates an existing campaign tag using the primary id.
   * @param data CreateCampaignTransactional dto object with the data for creating a new campaign.
   * @returns void
   */
  @Patch("campaign/:id")
  @RequirePermissions(Permission.UpdateTagById)
  async updateCampaignTag(
    @Param("id", ParseIntPipe) id: number,
    @Body() campaignTagDto: UpdateCampaignTagDto,
  ) {
    campaignTagDto.id = id;
    await this.campaignTagService.update(campaignTagDto);
  }

  /**
   * Return account tags.
   * @param id, it can be a account Id or the tag Id.
   * @param type: CampaignTagType, the type of the id provided, id by default.
   * @param status A boolean indicating whether to filter active or inactive tag relations
   * @returns an array of account's tags found.
   */
  @Get("sales-module/account/:id/:type?")
  @RequirePermissions(Permission.GetAllTags)
  async getAccountTagsSales(
    @Param("id", ParseIntPipe) id: number,
    @Param("type") type: AccountTagType = AccountTagType.id,
    @Query("status") status: boolean = true
  ) {
    const where = await this.tagsService.getAccountWhere(type, id, undefined, status);
    const tags = await this.accountTagService.getAccountTags(where, true);
    return mapNestedToFlat(tags);
  }

  /**
   * Return account tags.
   * @param id, it can be a account Id or the tag Id.
   * @param type: CampaignTagType, the type of the id provided, id by default.
   * @returns an array of account's tags found.
   */
  @Get("account/:id/:type?")
  @RequirePermissions(Permission.GetAllTags)
  async getAccountTags(
    @Param("id", ParseIntPipe) id: number,
    @Param("type") type: AccountTagType = AccountTagType.id,
  ) {
    const where = await this.tagsService.getAccountWhere(type, id);
    return await this.accountTagService.getAccountTags(where, true);
  }

  /**
   * Creates a new instance of campaign tag.
   * @param data CreateCampaignTransactional dto object with the data for creating a new campaign.
   * @returns void
   */
  @Post("account")
  @RequirePermissions(Permission.CreateTag)
  async createAccountTag(@Body() accountTagDto: AccountTagRelationDto) {
    await this.accountTagService.createTagRelation(accountTagDto, this.entityManager);
  }

  /**
   * Updates an existing account tag using the id.
   * @param data CreateCampaignTransactional dto object with the data for creating a new campaign.
   * @returns void
   */
  @Patch("account/:id")
  @RequirePermissions(Permission.UpdateTagById)
  async updateAccountTag(
    @Param("id", ParseIntPipe) id: number,
    @Body() accountTagDto: UpdateAccountTagDto,
  ) {
    accountTagDto.id = id;
    await this.accountTagService.update(accountTagDto);
  }
}
