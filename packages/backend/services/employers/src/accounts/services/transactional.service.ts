import { BadRequestException, HttpException, HttpStatus, Injectable, Logger } from "@nestjs/common";
import { CreateAccountDto } from "../dto/create-account.dto";
import { AccountBudgetDto } from "../dto/account-budget.dto";
import { UpdateAccountBudgetDto } from "../dto/update-account-budget.dto";
import { AccountService } from "../services/accounts.service";
import { AccountTagRelationService } from "./account-tag-relation.service";
import { Accounts } from "../entities/account.entity";
import { AccountTag } from "../entities/account-tag-relation.entity";
import { DataSource, EntityManager } from "typeorm";
import { Billings } from "../../billings/entities/billings.entity";
import { BillingsService } from "../../billings/services/billings.service";
import { CreateBillingDto } from "../../billings/dto/create-billing.dto";
import { UserMockService } from "../../common/mocks/user.service.mock";
import {
  AccountSalesOwner,
  AccountSecondSalesOwner,
  AccountCSOwner,
  InsertResult,
  AccountContact,
} from "../../common/interfaces/user.service.interface";
import { CustomAxiosAdapter } from "../../common/adapters/axios.adapter";
import { AccountSettingsService } from "./account-settings.service";
import { AccountSettings } from "../entities/account-settings.entity";
import { UpdateAccountDto } from "../dto/update-account.dto";
import { UpdateBillingDto } from "../../billings/dto/update-billing.dto";
import { isToday } from "date-fns";
import { DealsService } from "../../deals/services/deals.service";
import { AccountsOwnersType, AccountType, ObserverEvenType } from "../../common/resources/enums";
import { CreateAccountSettingsDto } from "../dto/create-account-settings.dto";
import { UsersService } from "../../users/services/user.service";
import { Role } from "../../auth/resources/role.enum";
import { UpdateAccountSettingsDto } from "../dto/update-account-settings.dto";
import { CreateRequestBody, UpdateRequestBody } from "../../common/interfaces/accounts.interface";
import { AccountOwnersService } from "./account-owners.service";
import { AccountOwnersDto } from "../dto/account-owners.dto";
import { Users } from "../../users/entities/user.entity";
import { BillingUpdate } from "../../common/interfaces/billings.interface";
import * as privacy from "libs/privacy/src";

/**
 *
 */
@Injectable()
export class TransactionalService {
  private user: Users;

  /**
   *
   */
  constructor(
    private readonly accountService: AccountService,
    private readonly accountTagRelationService: AccountTagRelationService,
    private readonly entityManager: EntityManager,
    private readonly billingsService: BillingsService,
    private readonly mockUser: UserMockService,
    private readonly accontSettingsService: AccountSettingsService,
    private readonly http: CustomAxiosAdapter,
    private readonly dealsService: DealsService,
    private readonly dataSource: DataSource,
    private readonly usersService: UsersService,
    private readonly accountOwner: AccountOwnersService,
  ) {}

  /**
   * Function to initiate a trasaction instance using QueryRunner.
   * @returns
   */
  async initQueryRunner() {
    // Create a new query runner.
    const queryRunner = this.dataSource.createQueryRunner();

    // Establish real database connection using our new query runner.
    await queryRunner.connect();

    return queryRunner;
  }

  /**
   *
   * @param createAccountDto
   * @param createBillingDto
   * @param user
   * @param realiableConversion
   * @param tags
   * @param createAccountBudgetDto
   * @returns
   */
  async create(
    createAccountDto: CreateAccountDto,
    createBillingDto: CreateBillingDto,
    user: (AccountSalesOwner | AccountSecondSalesOwner | AccountCSOwner)[],
    settings?: CreateAccountSettingsDto,
    tags?: { [key: string]: number },
    createAccountBudgetDto?: AccountBudgetDto,
  ) {
    let createdAccount: any;
    let createdTagRelations: any;
    let createdBilling: any;
    let createdUserRelations: any;
    let createdBudget: any;
    let createdSettings: any;
    let accountId: number | undefined;
    // Lets now open a new transaction.
    const queryRunnerTransaction = await this.initQueryRunner();

    // Lets now open a new transaction.
    await queryRunnerTransaction.startTransaction();
    try {
      createdAccount = await this.validateAndCreateAccount(
        createAccountDto,
        queryRunnerTransaction.manager,
      );
      accountId = createdAccount.id;
      if (accountId) {
        createdUserRelations = await this.validateAndCreateUsers(
          createdAccount,
          user,
          queryRunnerTransaction.manager,
        );
        createdBilling = await this.createBilling(
          accountId,
          createBillingDto,
          createAccountDto,
          queryRunnerTransaction.manager,
        );
        if (tags) {
          createdTagRelations = await this.createTagRelations(
            accountId,
            tags,
            queryRunnerTransaction.manager,
          );
        }
        createdSettings = await this.createAccountSettings(
          accountId,
          createAccountDto,
          queryRunnerTransaction.manager,
          settings,
        );
      }

      if (accountId !== undefined) {
        if (!createAccountBudgetDto) {
          createAccountBudgetDto = new AccountBudgetDto();
          createAccountBudgetDto.id = accountId;
        }
        if (createAccountBudgetDto) {
          createdBudget = await this.createAccountBudget(accountId, createAccountBudgetDto);
        }
      }

      // Explicitly commit before releasing
      await queryRunnerTransaction.commitTransaction();
    } catch (error: any) {
      await queryRunnerTransaction.rollbackTransaction();
      Logger.error(new Error(`Error: ${error.message}`));
      throw error;
    } finally {
      await queryRunnerTransaction.release();
    }

    if (accountId !== undefined) {
      await this.usersService.addPrivilegeToLeadUsersForSpecificAccount({ accountId });
      //Account post process. Note: Execute after commitTransaction.
      this.accountPostProcess(createdAccount, undefined, ObserverEvenType.CREATE);
    }

    return {
      createdAccount,
      createdSettings,
      createdBilling,
      createdUserRelations,
      createdTagRelations,
      createdBudget,
    };
  }

  /**
   *
   * @param updateAccountDto
   * @param updateBillingDto
   * @param updateBudgetDto
   * @returns
   */
  async update(
    accountId: number,
    updateAccountDto?: UpdateAccountDto,
    updateBillingDto?: UpdateBillingDto,
    updateBudgetDto?: UpdateAccountBudgetDto,
    updateSettings?: UpdateAccountSettingsDto,
  ) {
    let updatedAccount: any;
    let billingUpdate: BillingUpdate = { oldBilling: null, newBilling: null };
    let updatedBudget: unknown;
    let updatedSettings: any;
    // Get account info
    const accountInfo = await this.accountService.findOne(
      accountId,
      ["accountsBudget", "accountsOwners.user", "accountSettings"],
      undefined,
      true,
    );
    // Lets now open a new transaction.
    const queryRunnerTransaction = await this.initQueryRunner();

    // Lets now open a new transaction.
    await queryRunnerTransaction.startTransaction();

    try {
      // Update Account
      if (updateAccountDto) {
        updateAccountDto.id = accountId;
        updatedAccount = await this.accountService.update(
          updateAccountDto,
          queryRunnerTransaction.manager,
        );
      }
      // Update Billing
      if (updateBillingDto) {
        billingUpdate = await this.updateBilling(
          updateBillingDto,
          updateAccountDto ?? accountInfo,
          queryRunnerTransaction.manager,
        );
      }
      // Update Settings
      if (updateSettings) {
        updateSettings.accountId = accountId;
        updatedSettings = await this.accontSettingsService.update(
          updateSettings,
          queryRunnerTransaction.manager,
        );
      }
      // Update Budget
      if (updateBudgetDto) {
        updatedBudget = await this.updateAccountBudget(accountId, updateBudgetDto);
      }

      //delete invoice-preview
      if (billingUpdate.newBilling) {
        await this.billingsService.onUpdateSuccess(billingUpdate.newBilling);
      }

      // Updates child billings if necessary
      await this.billingsService.updateBillingForChildrenIfNeeded(billingUpdate);
      await queryRunnerTransaction.commitTransaction();
      try {
        // you might be wondering why we have a try catch inside a try catch... PEW PEW
        //Account post process. Note: Execute after commitTransaction.
        await this.accountPostProcess(
          updatedAccount,
          accountInfo,
          ObserverEvenType.UPDATE,
          updateBudgetDto,
          updateSettings,
        );
      } catch (error: any) {
        Logger.error(new Error(`Non-transactional error occurred: ${error.message}`));
      }
    } catch (error: any) {
      await queryRunnerTransaction.rollbackTransaction();
      Logger.error(new Error(`Error: ${error.message}`));
      throw error;
    } finally {
      await queryRunnerTransaction.release();
    }

    return {
      updatedAccount,
      updatedBilling: billingUpdate.newBilling,
      updatedBudget,
      updatedSettings,
    };
  }

  /**
   *
   * @param updateBillingDto
   * @param account
   * @param manager
   * @returns
   */
  async updateBilling(
    updateBillingDto: UpdateBillingDto,
    account: UpdateAccountDto,
    manager?: EntityManager,
  ): Promise<BillingUpdate> {
    const isValid = await this.validateGroup(updateBillingDto, account);

    if (!isValid) {
      throw new HttpException(
        "Group error: groups have to be a parent feedcode. If you are creating a group, use the feedcode/account you are in to create a parent.",
        HttpStatus.BAD_REQUEST,
      );
    }

    try {
      const billing = await this.billingsService.findBy("accountId", account.id);

      const response = await this.billingsService.update(
        billing[0].id,
        updateBillingDto,
        manager,
        false,
      );
      return {
        newBilling: response,
        oldBilling: billing[0],
      };
    } catch (error) {
      throw new Error("No found billing");
    }
  }

  /**
   *
   * @param createAccountDto
   * @param manager
   * @returns
   */
  private async validateAndCreateAccount(
    createAccountDto: CreateAccountDto,
    manager: EntityManager,
  ) {
    try {
      if (createAccountDto.feedcode != null) {
        const account = await manager.getRepository(Accounts).findOne({
          where: { feedcode: createAccountDto.feedcode },
        });
        if (account) {
          throw new HttpException("Account already exists", HttpStatus.BAD_REQUEST);
        }
      } else {
        throw new HttpException("Feedcode is mandatory", HttpStatus.BAD_REQUEST);
      }
      return await this.accountService.create(createAccountDto, manager);
    } catch (error: any) {
      Logger.error(new Error(`Error: ${error.message}`));
      throw error;
    }
  }

  /**
   *
   * @param accountId
   * @param user
   * @param manager
   * @returns
   */
  private async validateAndCreateUsers(
    account: Accounts,
    user: (AccountSalesOwner | AccountSecondSalesOwner | AccountCSOwner | AccountContact)[],
    manager: EntityManager,
  ): Promise<InsertResult[]> {
    const accountId = this.validateAccount(account);
    const hubspotDealId = await this.getHubspotDealId(account.hubspotDealId);

    // Add the primary contact to the user array to process it into Hubspot and set privileges
    if (account.contactName && account.contactEmail) {
      user.push({
        name: account.contactName,
        email: privacy.cipher.decrypt(account.contactEmail),
        type: "account_contact",
      });
    }
    // Validate owners in Hubspot
    const createdUserRelations: InsertResult[] = [];
    let i: number = 0;
    const userIds: number[] = [];
    do {
      const currentUser = user[i];
      // Call the setHubspotAccessKey
      this.dealsService.setHubspotAccessKey();
      if (currentUser.email && currentUser.type != "account_contact") {
        await this.dealsService.getOwnersByEmail(currentUser.email);
      }
      if (currentUser.email && currentUser.name && currentUser.type == "account_contact") {
        await this.dealsService.dealContactProcess(
          currentUser.email,
          currentUser.name,
          hubspotDealId,
        );
      }
      let userResult: any;
      let userId: any;

      try {
        // Search user
        userResult = await this.usersService.findOneByEmail(currentUser.email);
        userId = userResult.id;
        // Create accountOwner
        if (currentUser.type !== "account_contact") {
          await this.createAccountOwner(accountId, userId, currentUser, manager);
        }
        if (!userIds.includes(userId)) {
          // Add User privilege
          const createdUserRelation = await this.usersService.addPrivilegeToUser(
            {
              userId: userId,
              accountId: accountId,
              privilege: Role.Admin,
            },
            manager,
          );
          createdUserRelations.push(createdUserRelation);
        }
        userIds.push(userId);
      } catch (error: any) {
        Logger.warn(
          `[AccountTransaction] [validateAndCreateUsers] ${error.message}: ${currentUser.email}`,
        );
      }
      i++;
    } while (i < user.length && i < 4);

    return createdUserRelations;
  }

  /**
   * Validates the account and returns the accountId.
   * @param {Accounts} account - The account object.
   * @returns {number} - The accountId.
   * @throws {HttpException} - If accountId is not present.
   */
  private validateAccount(account: Accounts): number {
    const accountId = account.id;
    if (!accountId) {
      throw new HttpException("accountId is required", HttpStatus.BAD_REQUEST);
    }
    return accountId;
  }

  /**
   * Retrieves the HubSpot deal ID and validates the dealId.
   * @param {string} dealId - The dealId from the account.
   * @returns {string} - The HubSpot deal ID.
   * @throws {HttpException} - If dealId is not present.
   */
  private async getHubspotDealId(dealId: number | null): Promise<string> {
    if (!dealId) {
      throw new HttpException("dealId is required", HttpStatus.BAD_REQUEST);
    }
    const hubspotDeal = await this.dealsService.findOne(dealId);
    return hubspotDeal.dealId;
  }

  /**
   *
   * @param billingDto
   * @param account
   * @returns
   */
  private async validateGroup(
    billingDto: CreateBillingDto | UpdateBillingDto,
    account: CreateAccountDto | UpdateAccountDto,
  ) {
    try {
      let isValid = false;
      let feedcode;
      if ("id" in account) {
        const accountInfo = await this.accountService.findOne(account?.id);
        feedcode = accountInfo?.feedcode;
      } else {
        feedcode = account.feedcode;
      }

      if (billingDto.group) {
        // Search for the billing group in the existing list
        const dataBillingGroup = await this.billingsService.listBillingGroups(
          0,
          1,
          billingDto.group,
          "group",
        );

        // Validate if the billing group exists
        if (dataBillingGroup.hits > 0) {
          isValid = true;
        }

        if (!isValid && billingDto.group === feedcode) {
          isValid = true;
        }

        const parent = await this.billingsService.getBillingAccountParentByGroup(billingDto.group);
        if (parent && "id" in account) {
          // Is an update
          // Find the billing information
          const billing = await this.billingsService.findBy("accountId", account.id);
          if (billing[0].currency !== parent.currency) {
            throw new BadRequestException(
              "Billing currency must be the same for all accounts in the same billing group.",
            );
          }
        } else if (parent) {
          // Is a create
          if (billingDto.currency !== parent.currency) {
            throw new BadRequestException(
              "Billing currency must be the same for all accounts in the same billing group.",
            );
          }
        }

        return isValid;
      }

      return true;
    } catch (error: any) {
      Logger.error(new Error(`Error: ${error.message}`));
      throw error;
    }
  }

  /**
   *
   * @param accountId
   * @param createBillingDto
   * @param createAccountDto
   * @param manager
   * @returns
   */
  private async createBilling(
    accountId: number,
    createBillingDto: CreateBillingDto,
    createAccountDto: CreateAccountDto,
    manager: EntityManager,
  ): Promise<Billings> {
    try {
      const isValid = await this.validateGroup(createBillingDto, createAccountDto);

      if (!isValid) {
        throw new HttpException(
          "Group error: groups have to be a parent feedcode. If you are creating a group, use the feedcode/account you are in to create a parent.",
          HttpStatus.BAD_REQUEST,
        );
      }

      if (createBillingDto.group && createBillingDto.group != createAccountDto.feedcode) {
        await this.handleDifferentGroup(createBillingDto);
      } else {
        await this.applyNextDiscount(createBillingDto);
        await this.setTalentCompanyBasedOnCountry(createBillingDto);
      }

      if (createBillingDto.group) {
        createBillingDto.invoiceOption = 1;
      }

      createBillingDto.accountId = accountId;
      return await this.billingsService.create(createBillingDto, manager);
    } catch (error: any) {
      Logger.error(new Error(`Error: ${error.message}`));
      throw error;
    }
  }

  /**
   *
   * @param accountId
   * @param tags
   * @param manager
   * @returns
   */
  private async createTagRelations(
    accountId: number,
    tags: { [key: string]: number },
    manager: EntityManager,
  ) {
    try {
      // Create tag relations
      const createdTagRelations: AccountTag[] = [];
      const tagsValues: { [key: string]: number } = {
        pixel: tags["pixel"],
        postback: tags["postback"],
        reconciliation: tags["reconciliation"],
        autoCampaign: tags["autoCampaign"],
      };

      for (const [, value] of Object.entries(tagsValues)) {
        if (value) {
          const accountTagRelation = new AccountTag();
          accountTagRelation.accountId = accountId;
          accountTagRelation.tagId = value;
          accountTagRelation.createdBy = this.user.id;

          // Create and save the tag relation
          const createdTagRelation = await this.accountTagRelationService.createTagRelation(
            accountTagRelation,
            manager,
          );
          createdTagRelations.push(createdTagRelation); // Store in the array
        }
      }
      return createdTagRelations;
    } catch (error: any) {
      Logger.error(new Error(`Error: ${error.message}`));
      throw error;
    }
  }

  /**
   *
   * @param accountId
   * @param createAccountDto
   * @param reliableConversion
   * @param manager
   * @returns
   */
  private async createAccountSettings(
    accountId: number,
    createAccountDto: CreateAccountDto,
    manager: EntityManager,
    settings?: CreateAccountSettingsDto,
  ): Promise<AccountSettings> {
    try {
      // Create account settings entity
      const settingsObject = new CreateAccountSettingsDto();
      settingsObject.accountId = accountId;
      settingsObject.frontLoadedStatus = true;
      if (createAccountDto.accountType == AccountType.Enterpost) {
        settingsObject.requiresQa = true;
      }
      (settingsObject as any).createdBy = this.user.id;
      if (settings) {
        Object.assign(settingsObject, settings);
      }
      return await this.accontSettingsService.createAccountSettings(settingsObject, manager);
    } catch (error: any) {
      Logger.error(new Error(`Error: ${error.message}`));
      throw error;
    }
  }

  /**
   *
   * @param accountId
   * @param createAccountBudgetDto
   * @returns
   */
  private async createAccountBudget(
    accountId: number,
    createAccountBudgetDto: AccountBudgetDto,
  ): Promise<any> {
    try {
      createAccountBudgetDto.id = accountId;

      const createdBudget = await this.http.post(
        `${process.env.URL_BUDGETING}/api/v1/account_budgets`,
        createAccountBudgetDto,
      );

      return createdBudget;
    } catch (error: any) {
      Logger.error(new Error(`Error: ${error.message}`));
      throw error;
    }
  }

  /**
   *
   * @param account_id
   * @param updateBudgetDto
   * @returns
   */
  async updateAccountBudget(
    accountId: number,
    updateBudgetDto: UpdateAccountBudgetDto,
  ): Promise<any> {
    try {
      const budgetInfo = await this.http.get(
        `${process.env.URL_BUDGETING}/api/v1/account_budgets/${accountId}`,
      );

      if (!budgetInfo) {
        throw new HttpException(
          `Account id not found in budgeting service ${accountId}`,
          HttpStatus.NOT_FOUND,
        );
      }

      if (updateBudgetDto.budget && updateBudgetDto.budget == -1) {
        updateBudgetDto.budget = null;
      }

      // Check the response from account-budget
      const updatedBudget: any = await this.http.patch(
        `${process.env.URL_BUDGETING}/api/v1/account_budgets/${budgetInfo.account_id}`,
        updateBudgetDto,
      );

      return updatedBudget;
    } catch (error: any) {
      if (error?.response?.data?.error === "No account budget rows affected") {
        return "No account budget changes identified";
      }

      // Improved error handling
      const errorMessage = error?.message || "Unknown error updating account budget";
      Logger.error(new Error(`Error updating account budget: ${errorMessage}`));

      // Return a clear error message instead of throwing undefined
      throw new HttpException(
        `Error updating account budget: ${errorMessage}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   *
   * @param billingDto
   */
  private async handleDifferentGroup(billingDto: CreateBillingDto) {
    const parent = await this.billingsService.getBillingAccountParentByGroup(billingDto.group);
    if (!parent) {
      billingDto.group = "";
    } else {
      this.updateBillingDtoWithParentData(billingDto, parent);
    }
  }

  /**
   *
   * @param billingDto
   * @param parent
   */
  private updateBillingDtoWithParentData(billingDto: CreateBillingDto, parent: Billings) {
    if (parent.qboId !== null) {
      billingDto.qboId = parent.qboId;
    }
    if (parent.taxId !== null) {
      billingDto.taxId = parent.taxId;
    }
    if (parent.talentCompany !== null) {
      billingDto.talentCompany = parent.talentCompany;
    }
    if (parent.currency !== null) {
      billingDto.currency = parent.currency;
    }
    billingDto.discount = parent.discount;
    billingDto.discountStartDate = parent.discountStartDate;
    billingDto.discountEndDate = parent.discountEndDate;
    billingDto.reason = parent.reason;
    billingDto.nextDiscountApply = parent.nextDiscountApply;
    billingDto.language = parent.language;
  }

  /**
   *
   * @param createBillingDto
   * @param createAccountDto
   */
  private async setTalentCompanyBasedOnCountry(createBillingDto: CreateBillingDto) {
    switch (createBillingDto.country) {
      case "br":
        createBillingDto.talentCompany = "GESTION NEUVOO";
        break;
      case "ca":
        createBillingDto.talentCompany = "NEUVOO INC";
        break;
      case "fr":
        createBillingDto.talentCompany = "NEUVOO FR";
        break;
      case "ch":
        createBillingDto.talentCompany = "NEUVOO SARL";
        break;
      case "gb":
        createBillingDto.talentCompany = "NEUVOO UK";
        break;
      case "us":
        createBillingDto.talentCompany = "NEUVOO USA";
        break;
      default:
        createBillingDto.talentCompany =
          createBillingDto.currency == "CAD" ? "NEUVOO INC" : "NEUVOO SARL";
        break;
    }
  }

  /**
   *
   * @param billingDto
   */
  private async applyNextDiscount(billingDto: CreateBillingDto) {
    if (billingDto.nextDiscountApply) {
      if (billingDto.discountStartDate !== null) {
        if (isToday(billingDto.discountStartDate)) {
          billingDto.discount = billingDto.nextDiscountApply;
          billingDto.nextDiscountApply = null;
        }
      }
    }
  }

  /**
   * Function to execute all the post process after creating a campaign.
   * @param campaign
   * @returns
   */
  public accountPostProcess(
    account: Accounts,
    accountInfo?: Accounts,
    evenType?: ObserverEvenType,
    updatedBudget?: any,
    updateSettings?: UpdateAccountSettingsDto,
  ) {
    try {
      // Notifies the observer with the changes made.
      this.accountService.accountSubject.next({
        account: account,
        accountInfo: accountInfo,
        evenType: evenType,
        updatedBudget,
        accountSettings: updateSettings,
      });
    } catch (err: any) {
      Logger.error(new Error(`Error: ${err.message}`));
      throw err;
    }
    return account;
  }

  /**
   * Grabs the user id that came with the token and adds it to the entities to see who created/updated it.
   * @param user user_id
   * @param data transaction's request data.
   */
  async addUserToEntities(
    userId: number,
    data: CreateRequestBody | UpdateRequestBody,
    method: string,
  ) {
    const userResult = await this.usersService.findOneBy(userId);
    if (!userResult) {
      throw new HttpException(`User not found for userId ${userId}`, HttpStatus.NOT_FOUND);
    }
    this.user = userResult;
    if (method === "POST") {
      (data.account as any).createdBy = userId;
    } else if (method === "PATCH") {
      if (data.account) {
        (data.account as any).updatedBy = userId;
      }
    }
  }

  /**
   * Creates an account owner record based on the provided account and user IDs, and the type of the current user.
   * @param accountId - The ID of the account to associate with the owner.
   * @param userId - The ID of the user who will be set as the account owner.
   * @param currentUser - The current user whose type will determine the owner type.
   * @param entityManager - Optional. The entity manager to use for transactional operations. If not provided, the default entity manager will be used.
   */
  async createAccountOwner(
    accountId: number,
    userId: number,
    currentUser: AccountSalesOwner | AccountSecondSalesOwner | AccountCSOwner | AccountContact,
    entityManager: EntityManager,
  ) {
    type AccountOwnerTypes =
      | AccountSalesOwner["type"]
      | AccountSecondSalesOwner["type"]
      | AccountCSOwner["type"];

    const accountOwnerTypes: Record<AccountOwnerTypes, string> = {
      account_sales_owner: "sales",
      account_second_sales_owner: "secondary_sales",
      account_cs_owner: "cs",
    };

    // Validate that the current user's type is recognized
    if (!accountOwnerTypes[currentUser.type.trim() as AccountOwnerTypes]) {
      throw new HttpException(
        `User type not found for user ${currentUser.email}`,
        HttpStatus.BAD_REQUEST,
      );
    }

    // Determine the account owner type based on the user's type
    const accountOwnerType = accountOwnerTypes[
      currentUser.type.trim() as AccountOwnerTypes
    ] as AccountsOwnersType;

    // Create a new AccountOwnersDto and populate it with the relevant data
    const ownerCreate = new AccountOwnersDto();
    ownerCreate.accountId = accountId;
    ownerCreate.userId = userId;
    ownerCreate.accountOwnerType = accountOwnerType;
    //////

    //////

    // Save the new account owner record
    await this.accountOwner.create(ownerCreate, entityManager);
  }
}
