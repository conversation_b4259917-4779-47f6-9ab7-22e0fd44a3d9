import { HttpException, HttpStatus, Injectable } from "@nestjs/common";
import { UserRepository } from "../repositories/user.repository";
import { CreateUserDto } from "../dto/create-user.dto";
import { CreateUserPrivilegeDto } from "../dto/create-user-privileges.dto";
import { AccountService } from "../../accounts/services/accounts.service";
import { UserSetPassword, UserStatus, LoginType, IsPwned } from "../../common/resources/enums";
import { UserPrivilegeRepository } from "../repositories/user-privileges.repository";
import { SearchUserPrivilegeDto } from "../dto/search-user-privileges.dto";
import { SearchUserDto } from "../dto/search-user.dto";
import { UpdateUserDto } from "../dto/update-user.dto";
import { DataSource, EntityManager } from "typeorm";
import { CreateUserPrivilegeForAllAccountsDto } from "../dto/create-user-privileges-for-all-accounts.dto";
import {
  InternalGlobalRole,
  InternalRole,
  Role,
  UserMainType,
} from "../../auth/resources/role.enum";
import { CreateUserSsoPrivilegeDto } from "../dto/create-user-sso-privileges.dto";
import { CreateUserSsoDto } from "../dto/create-user-sso.dto";
import { ChangeUserPasswordDto } from "../dto/change-user-password.dto";
import { PrivacyService } from "../../common/resources/privacy.service";
import { ResetUserPasswordDto } from "../../auth/dto/reset-user-password.dto";
import { UpdateUserInfoAndPrivilegeDto } from "../dto/update-user-info-and-privilege.dto";
import * as privacy from "libs/privacy/src";
import { CreateUserPrivilegesForSpecificAccountDto } from "../dto/create-user-privileges-for-specific-account.dto";
import { UserPrivilege } from "../entities/user-privileges.entity";
import { NotificationService } from "../../notification/services/notification.service";
import { Users } from "../entities/user.entity";

/**
 *
 */
@Injectable()
export class UsersService {
  /**
   * Init the class' objects
   */
  constructor(
    private readonly userRepository: UserRepository,
    private readonly accountService: AccountService,
    private readonly userPrivilegeRepository: UserPrivilegeRepository,
    private readonly dataSource: DataSource,
    private readonly privacyService: PrivacyService,
    private readonly notificationService: NotificationService,
  ) {}

  /**
   * Creates a new user in the database and associate an accountID to it.
   * You cannot create a user without associating an accountID.
   * @param userParams
   * @param privilegeParams
   */
  async create(userParams: CreateUserDto, privilegeParams: CreateUserPrivilegeDto) {
    // Lets now open a new transaction.
    const queryRunnerTransaction = await this.initQueryRunner();

    // Lets now open a new transaction.
    await queryRunnerTransaction.startTransaction();

    try {
      // Check if the email is external, else return error.
      if (userParams.email.endsWith("@talent.com"))
        throw new HttpException("The user is not external", HttpStatus.BAD_REQUEST);

      // Check if some wants to create an account with internal role, if yes, then return error.
      if (
        Object.values(InternalRole).includes(privilegeParams.privilege as unknown as InternalRole)
      )
        throw new HttpException("Invalid privilege role.", HttpStatus.BAD_REQUEST);

      const accountInfo = await this.accountService.findOne(privilegeParams.accountId);
      // Account should exist
      if (!accountInfo) {
        throw new HttpException("Account not found", HttpStatus.NOT_FOUND);
      }

      const result = await this.userRepository.findOneByEmail(userParams.email);
      if (result) {
        throw new HttpException("User already exists", HttpStatus.BAD_REQUEST);
      }

      // Check if password is valid, else return error
      this.validatePasswordStrength(userParams.password);

      // User Params
      const userCreate: any = {};
      userCreate.email = userParams.email;
      userCreate.password = userParams.password;
      userCreate.setPassword = UserSetPassword.YES;
      userCreate.loginType = LoginType.CREDENTIALS;
      userCreate.status = UserStatus.active;
      userCreate.userType = UserMainType.External;
      if (userParams.firstName) userCreate.firstName = userParams.firstName;
      if (userParams.lastName) userCreate.lastName = userParams.lastName;

      // Create user
      const resultUser = await this.userRepository.create(
        userCreate,
        queryRunnerTransaction.manager,
      );

      // User Privilege Account Params
      const privilegeCreate: any = {};
      privilegeCreate.userId = resultUser.id;
      privilegeCreate.privilege = privilegeParams.privilege;
      privilegeCreate.accountId = privilegeParams.accountId;

      // Create User Privilege Account
      const resultUserPrivileges = await this.addPrivilegeToUser(
        privilegeCreate,
        queryRunnerTransaction.manager,
      );
      if (!resultUserPrivileges.status) {
        throw new HttpException("Error inserting privileges", HttpStatus.INTERNAL_SERVER_ERROR);
      }

      // Commit of trasanction
      await queryRunnerTransaction.commitTransaction();

      return { resultUser };
    } catch (error) {
      // If any error occurs, rollback the transaction.
      await queryRunnerTransaction.rollbackTransaction();
      throw error;
    } finally {
      // Release the query runner.
      await queryRunnerTransaction.release();
    }
  }

  /**
   * Creates a new internal user in the database and associate an accountID to it.
   * The user needs to have an email that ends "@talent.com" and the Role needs to be an internal role.
   * @param userParams
   * @param privilegeParams
   * @returns
   */
  async createUserSso(userParams: CreateUserSsoDto, privilegeParams: CreateUserSsoPrivilegeDto) {
    // Lets now open a new transaction.
    const queryRunnerTransaction = await this.initQueryRunner();

    // Lets now open a new transaction.
    await queryRunnerTransaction.startTransaction();

    try {
      // Check if the email ends in @talent.com, else return error.
      if (!userParams.email.endsWith("@talent.com")) {
        throw new HttpException("Email must end with @talent.com", HttpStatus.BAD_REQUEST);
      }

      const result = await this.userRepository.findOneByEmail(userParams.email);
      if (result) {
        throw new HttpException("User already exists", HttpStatus.BAD_REQUEST);
      }

      // User Params
      const userCreate: any = {};
      userCreate.email = userParams.email;
      userCreate.setPassword = UserSetPassword.NO;
      userCreate.loginType = LoginType.SSO_MICROSOFT;
      userCreate.status = UserStatus.active;
      userCreate.userType = UserMainType.Internal;
      if (userParams.firstName) userCreate.firstName = userParams.firstName;
      if (userParams.lastName) userCreate.lastName = userParams.lastName;

      // Create user
      const resultUser = await this.userRepository.create(
        userCreate,
        queryRunnerTransaction.manager,
      );
      const privilegeCreate: any = {};

      if (
        Object.values(InternalGlobalRole).includes(
          privilegeParams.privilege as unknown as InternalGlobalRole,
        )
      ) {
        // User Privilege Account Params
        privilegeCreate.userId = resultUser.id;
        privilegeCreate.privilege = privilegeParams.privilege;

        // Create User Privilege Account
        await this.addPrivilegeToUserForAllAccounts(
          privilegeCreate,
          queryRunnerTransaction.manager,
        );
      } else {
        // If no accountId return error
        if (!privilegeParams.accountId)
          throw new HttpException("accountId is required.", HttpStatus.BAD_REQUEST);

        // Check Account Id
        const accountInfo = await this.accountService.findOne(privilegeParams.accountId);

        // Account should exist
        if (!accountInfo) throw new HttpException("Account not found", HttpStatus.NOT_FOUND);

        // User Privilege Account Params
        privilegeCreate.userId = resultUser.id;
        privilegeCreate.privilege = privilegeParams.privilege;
        privilegeCreate.accountId = privilegeParams.accountId;

        // Create User Privilege Account
        await this.addPrivilegeToUser(privilegeCreate, queryRunnerTransaction.manager);
      }

      // Commit of trasanction
      await queryRunnerTransaction.commitTransaction();

      return { resultUser };
    } catch (error) {
      // If any error occurs, rollback the transaction.
      await queryRunnerTransaction.rollbackTransaction();
      throw error;
    } finally {
      // Release the query runner.
      await queryRunnerTransaction.release();
    }
  }

  /**
   * Add a Privilege to User for all the Account Ids and Update those that already have Privilege for.
   * @param privilegeForAllAccountsParams
   * @returns
   */
  async addPrivilegeToUserForAllAccounts(
    privilegeForAllAccountsParams: CreateUserPrivilegeForAllAccountsDto,
    entityManager?: EntityManager,
  ) {
    try {
      // Chunksize
      const chunkSize = 10000;

      // Get all AccountIds
      const accountsInfo = await this.accountService.findAll();

      // Account should exist
      if (!accountsInfo.length) throw new HttpException("Account not found", HttpStatus.NOT_FOUND);

      // Prepare batch for insert or update
      const batchOfAccountIdsToInsert = accountsInfo.map((account) => account.id as number);

      // Perform insert or update in batches
      await this.userPrivilegeRepository.createOrUpdateBatch(
        batchOfAccountIdsToInsert,
        privilegeForAllAccountsParams.userId,
        privilegeForAllAccountsParams.privilege as unknown as Role,
        chunkSize,
        entityManager,
      );

      return;
    } catch (error: any) {
      // Handle the error
      throw new HttpException(error.message, error.status || HttpStatus.BAD_REQUEST);
    }
  }

  /**
   * Add a Privilege to all Users that have a Lead Role for a specific Account Id.
   * @param createUserPrivilegesForSpecificAccountParams
   * @returns
   */
  async addPrivilegeToLeadUsersForSpecificAccount(
    createUserPrivilegesForSpecificAccountParams: CreateUserPrivilegesForSpecificAccountDto,
  ) {
    try {
      // Chunksize
      const chunkSize = 10000;

      // Roles
      const roleValues = Object.values(InternalGlobalRole);

      // Get all UserIds
      const usersInfo =
        await this.userPrivilegeRepository.findDistinctUserIdsByPrivileges(roleValues);

      // Users should exist
      if (!usersInfo.length) throw new HttpException("Users not found", HttpStatus.NOT_FOUND);

      // Prepare batch for insert or update
      await this.userPrivilegeRepository.createOrUpdateBatchByAccountId(
        usersInfo,
        createUserPrivilegesForSpecificAccountParams.accountId,
        chunkSize,
      );

      return;
    } catch (error: any) {
      // Handle the error
      throw new HttpException(error.message, error.status || HttpStatus.BAD_REQUEST);
    }
  }

  /**
   * Updates a user FirstName, LastName or password (we cannot change the email)
   * @param id
   * @param userParams
   */
  async update(id: number, userParams: UpdateUserDto) {
    const checkUser = await this.userRepository.findOneBy(id);
    if (!checkUser) {
      throw new HttpException("User not found", HttpStatus.NOT_FOUND);
    }

    if (userParams.password) {
      this.validatePasswordStrength(userParams.password);
    }

    if (userParams.firstName) {
      this.validateStringNameAndLastName(userParams.firstName, "First name");
    }

    if (userParams.lastName) {
      this.validateStringNameAndLastName(userParams.lastName, "Last name");
    }

    return await this.userRepository.update(id, userParams);
  }

  /**
   * Assign, remove or update the user's authorization on an specific account.
   * AuthorizationTypes are [user, admin, removed, cs, sales]
   * @param privilegeParams
   */
  async addPrivilegeToUser(privilegeParams: CreateUserPrivilegeDto, entityManager?: EntityManager) {
    const out: any = {};

    // Check if user id exists.
    if (!privilegeParams.userId) {
      throw new HttpException("User id is required", HttpStatus.BAD_REQUEST);
    }
    //await this.findOneBy(privilegeParams.userId); --> is it needed?

    //check if account id exist
    //await this.accountService.findOne(privilegeParams.accountId, undefined, entityManager); --> is it needed?

    // Checking existing record in DB
    const searchPrivileges: SearchUserPrivilegeDto = {
      accountId: privilegeParams.accountId,
      userId: privilegeParams.userId,
    };

    const rPrivileges = await this.userPrivilegeRepository.search(
      searchPrivileges,
      true,
      true,
      entityManager,
    );
    if (rPrivileges.userPrivilege.length > 0) {
      if (rPrivileges.userPrivilege[0].privilege == privilegeParams.privilege) {
        throw new HttpException(
          `User already related to the account with ${privilegeParams.privilege} privilege`,
          HttpStatus.BAD_REQUEST,
        );
      }
      // Before update we are going to check if the new privilege is 'remove' or 'user'
      if (privilegeParams.privilege != "admin") {
        // we have to check if the account has associated another admin
        await this.validateAccountAdmin(privilegeParams, entityManager);
      }
      // All went well, so let's update the record!
      await this.userPrivilegeRepository.update(
        rPrivileges.userPrivilege[0].id,
        {
          privilege: privilegeParams.privilege,
        },
        entityManager,
      );
      // Setting output array.
      out.status = "Updated";
      out.privilege = privilegeParams.privilege;
      out.accountId = privilegeParams.accountId;
      out.userId = privilegeParams.userId;
    } else {
      await this.userPrivilegeRepository.create(privilegeParams, entityManager);
      // Setting output array.
      out.status = "Inserted";
      out.privilege = privilegeParams.privilege;
      out.accountId = privilegeParams.accountId;
      out.userId = privilegeParams.userId;
    }

    return out;
  }

  /**
   * Validate if the account has associated another user admin
   * the account cannot be left without an admin.
   * @param privilegeParams
   */
  async validateAccountAdmin(
    privilegeParams: CreateUserPrivilegeDto,
    entityManager?: EntityManager,
  ) {
    const userList = await this.userPrivilegeRepository.search(
      {
        accountId: privilegeParams.accountId,
      },
      true,
      true,
      entityManager,
    );

    if (userList.userPrivilege.length == 0) {
      throw new HttpException("A problem occurred", HttpStatus.INTERNAL_SERVER_ERROR);
    }

    let canRemoveUser = false;
    for (const user of userList.userPrivilege) {
      if (user.id !== privilegeParams.userId && user.privilege === "admin") {
        canRemoveUser = true;
        break;
      }
    }

    if (!canRemoveUser) {
      throw new HttpException(
        "Please select a new admin before updating this one.",
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  /**
   * Gets a specific user based on its email.
   * @param email - The email address of the user you want to obtain.
   */
  async findOneByEmail(email: string) {
    const result = await this.userRepository.findOneByEmail(email);
    if (!result) {
      throw new HttpException("User not found", HttpStatus.NOT_FOUND);
    }
    return result;
  }

  /**
   * Returns all users with provided filters.
   */
  async search(filters: SearchUserDto, withRelations: boolean = true) {
    const usersResult = await this.userRepository.search(filters, withRelations);
    // if no results, throw error, otherwiser return the results of the query
    if (usersResult.users.length === 0)
      throw new HttpException(`User not found`, HttpStatus.NOT_FOUND);

    return usersResult;
  }

  /**
   * Returns all user privileges with the provided filters.
   */
  async searchUserPrivileges(
    filters: SearchUserPrivilegeDto,
    addLimit: boolean = true,
    withRelations: boolean = true,
  ) {
    const usersResult = await this.userPrivilegeRepository.search(filters, addLimit, withRelations);
    // if no results, throw error, otherwiser return the results of the query
    if (usersResult.userPrivilege.length === 0)
      throw new HttpException(`User privileges not found`, HttpStatus.NOT_FOUND);

    return usersResult;
  }

  /**
   * Retrieves a list of privileges associated with users whose email hashes match the provided list.
   * @param emails - An array of email hashes to filter the users.
   * @param accountId - The ID of the account to filter the privileges.
   */
  async searchUserPrivilegesByEmails(emails: string[], accountId: number) {
    const privileges = await this.userPrivilegeRepository.findByEmails(emails, accountId);
    return privileges;
  }

  /**
   * General function to validate a password
   * these passwords must have at least 6 characters
   * strength.
   * @param password
   */
  validatePasswordStrength(password: string) {
    if (password.length < 6) {
      throw new HttpException("Your password must be 6 characters or more", HttpStatus.BAD_REQUEST);
    }
    return { valid: "yes" };
  }

  /**
   * Validates that the string provided only contains letters, spaces, and the following symbols: '-´
   * @param {string} str - String that wants to be validated
   */
  validateStringNameAndLastName(str: string, field: string) {
    // Validating that the name/last name is longer than two characters
    if (str.trim().length < 2) {
      throw new HttpException(
        `${field} should have two or more characters`,
        HttpStatus.BAD_REQUEST,
      );
    }
    // Validating that there are only letters and spaces in the name
    const regex = /^[-'a-zA-Z \p{L}]+$/u;
    if (!regex.test(str)) {
      throw new HttpException(
        `${field} must only contain letters and spaces`,
        HttpStatus.BAD_REQUEST,
      );
    }
    return { valid: "yes" };
  }

  /**
   * This method will be used when purging the user. It does not delete the database record,
   * it only updates the fields that contain sensitive information by replacing them with
   * the word "deleted".
   * @param id userId
   */
  async delete(id: number) {
    const userInfo = await this.userRepository.findOneBy(id);
    if (!userInfo) {
      throw new HttpException("User not found", HttpStatus.NOT_FOUND);
    }
    await this.userRepository.delete(id);
  }

  /**
   * Function to initiate a trasaction instance using QueryRunner.
   * @returns
   */
  async initQueryRunner() {
    // Create a new query runner.
    const queryRunner = this.dataSource.createQueryRunner();

    // Establish real database connection using our new query runner.
    await queryRunner.connect();

    return queryRunner;
  }

  /**
   * Get id from users table by email.
   * @param email
   * @returns
   */
  async getIdByEmail(email: string) {
    return await this.userRepository.getIdByEmail(email);
  }

  /**
   * Get User Id and Account Roles information by User Id
   * Clean and Restructure data to return it
   * @param userId
   * @returns
   */
  async getUserInfoWithAccountRoles(userId: number) {
    const userInfo = await this.searchUserPrivileges({ userId: userId }, false, false);
    if (!userInfo) throw new HttpException("Invalid User Id.", HttpStatus.UNAUTHORIZED);

    const accountRoles: any[] = [];
    userInfo.userPrivilege.forEach((userPrivilege: any) => {
      accountRoles.push({ account_id: userPrivilege.accountId, role: userPrivilege.privilege });
    });

    return { user_id: userId, user_roles: accountRoles };
  }

  /**
   ** Gets a specific user based on its ID.
   * @param id - The unique identifier of the user you want to obtain.
   * @param includeEntities - An optional boolean flag that specifies whether to load entities associated with the user. By default, it is set to false.
   */
  async findOneBy(id: number, includeEntities: boolean = false) {
    // Execute the query with the specified options
    return await this.userRepository.findOneBy(id, includeEntities);
  }

  /**
   * Change user password when is logged, providing old password and new password
   * @param id
   * @param data
   * @returns
   */
  async changeUserPassword(id: number, data: ChangeUserPasswordDto) {
    try {
      // Check user ID
      const userInfo = await this.userRepository.findOneBy(id);
      if (!userInfo) throw new HttpException("User not found", HttpStatus.NOT_FOUND);

      // Only External users can change password
      if (userInfo.userType != UserMainType.External)
        throw new HttpException(
          "Change password is only allowed for external users",
          HttpStatus.BAD_REQUEST,
        );

      // Validate inputs
      if (!data.oldPassword)
        throw new HttpException("oldPassword cannot be empty", HttpStatus.BAD_REQUEST);
      if (!data.newPassword)
        throw new HttpException("newPassword cannot be empty", HttpStatus.BAD_REQUEST);
      this.validatePasswordStrength(data.newPassword);

      // Check if user has an normal password, if not check against old legacy password
      if (userInfo.password) {
        // Validate User password
        await this.privacyService.checkPassword(userInfo.password as string, data.oldPassword);
      } else if (userInfo.oldPassword) {
        // Validate old user password
        const validPwd = this.privacyService.comparePassword(
          data.oldPassword,
          userInfo.oldPassword,
        );
        if (!validPwd) throw new HttpException("Invalid Credentials.", HttpStatus.UNAUTHORIZED);
      } else {
        throw new HttpException("Something went wrong", HttpStatus.INTERNAL_SERVER_ERROR);
      }

      // Check if password is weak or vulnerable (Pwned)
      const checkIfPwned = await this.privacyService.isPasswordPwned(data.newPassword);

      // By default is NO, else if result is true then change it to YES
      let isPasswordPwned = IsPwned.NO;
      if (checkIfPwned) isPasswordPwned = IsPwned.YES;

      // Update User password
      await this.update(id, { password: data.newPassword, isPwned: isPasswordPwned });

      return true;
    } catch (error: any) {
      throw new HttpException(
        error.message || "Something went wrong",
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Reset user's password
   * @param id
   * @param data
   * @returns
   */
  async resetUserPassword(id: number, data: ResetUserPasswordDto) {
    try {
      // Check user ID
      const userInfo = await this.userRepository.findOneBy(id);
      if (!userInfo) throw new HttpException("User not found", HttpStatus.NOT_FOUND);

      // Only External users can change password
      if (userInfo.userType != UserMainType.External)
        throw new HttpException(
          "Change password is only allowed for external users",
          HttpStatus.BAD_REQUEST,
        );

      // Validate inputs
      if (!data.password)
        throw new HttpException("Password cannot be empty", HttpStatus.BAD_REQUEST);

      data.password = privacy.cipher.decrypt(data.password);
      this.validatePasswordStrength(data.password);

      // Check if password is weak or vulnerable (Pwned)
      const checkIfPwned = await this.privacyService.isPasswordPwned(data.password);

      // By default is NO, else if result is true then change it to YES
      let isPasswordPwned = IsPwned.NO;
      if (checkIfPwned) isPasswordPwned = IsPwned.YES;

      // Update User password and pwned field
      await this.update(id, { password: data.password, isPwned: isPasswordPwned });

      // Send email notification (tmpl name: employer_activated) to the user
      await this.notificationService.sendEmail(privacy.cipher.decrypt(userInfo.email), 15, {});

      return true;
    } catch (error: any) {
      throw new HttpException(
        error.message || "Something went wrong",
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get user info and roles info
   * @param id
   * @returns
   */
  async getUserInfo(id: number) {
    try {
      let userPrivInfo;
      // Get user info
      const userInfo = await this.userRepository.findOneBy(id);
      if (!userInfo) throw new HttpException("User not found", HttpStatus.NOT_FOUND);

      if (userInfo.superUser == "no") {
        // Get user info roles
        userPrivInfo = await this.getUserInfoWithAccountRoles(id);
      } else {
        userPrivInfo = {
          user_roles: [],
        };
      }

      // Decrypt Email
      let email;
      if (userInfo.email) email = privacy.cipher.decrypt(userInfo.email);

      // Decrypt LastName
      let lastName;
      if (userInfo.lastName) lastName = privacy.cipher.decrypt(userInfo.lastName);

      // Cleaning output
      const userResult = {
        id: userInfo.id,
        email: email,
        firstName: userInfo.firstName,
        lastName: lastName,
        status: userInfo.status,
        userType: userInfo.userType,
        created: userInfo.created,
        updated: userInfo.updated,
      };

      // Return
      const result = {
        user: userResult,
        user_roles: userPrivInfo.user_roles,
      };

      return result;
    } catch (error: any) {
      throw new HttpException(
        error.message || "Something went wrong",
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Update User Info and Privilege
   * @param id
   * @param params
   * @returns
   */
  async updateUserInfoAndPrivilege(id: number, params: UpdateUserInfoAndPrivilegeDto) {
    try {
      // Get user info
      const userInfo = await this.userRepository.findOneBy(id);
      if (!userInfo) throw new HttpException("User not found", HttpStatus.NOT_FOUND);

      // Check if params is empty
      if (Object.keys(params).length === 0)
        throw new HttpException("Nothing to update", HttpStatus.BAD_REQUEST);

      // Update Users info
      if (params.firstName || params.lastName) {
        // Fields to update
        const updateParams: { firstName?: string; lastName?: string } = {};
        if (params.firstName) updateParams.firstName = params.firstName;
        if (params.lastName) updateParams.lastName = params.lastName;

        // Update User info
        await this.update(id, updateParams);
      }

      // If we want to update privilege for a particular account, then do.
      if (params.privilege || params.accountId) {
        // Check if accountId and privilege was provided
        if (!params.privilege)
          throw new HttpException(
            "If you want to update an accountId permission, you need to specify privilege input.",
            HttpStatus.BAD_REQUEST,
          );
        if (!params.accountId)
          throw new HttpException(
            "If you want to update an privilege permission, you need to specify accountId input.",
            HttpStatus.BAD_REQUEST,
          );

        // Add or Update User privilege
        await this.userPrivilegeRepository.createOrUpdateBatch(
          [params.accountId],
          id,
          params.privilege,
        );
      }

      return true;
    } catch (error: any) {
      throw new HttpException(error.message, error.status);
    }
  }

  /**
   * Helper method to extract the role of the current user.
   * @param user
   * @param accountId
   */
  async getUserPrivilege(userId: number, accountId: number | undefined): Promise<string> {
    let privilege = "";
    const user = await this.findOneBy(userId, true);
    // Validate the user so that we avoid typescript errors.
    if (!user) {
      throw new HttpException(`User with id: ${userId} not found`, HttpStatus.NOT_FOUND);
    }

    if (user.userPrivileges) {
      const role = user.userPrivileges.filter(
        (userPrivilege: UserPrivilege) => userPrivilege.accountId === accountId,
      );
      if (role[0]) {
        privilege = role[0].privilege;
      }
    }
    return privilege;
  }

  /**
   * Helper method to extract the role of the current user.
   * @param user
   * @param accountId
   */
  async getUserPrivilegeByAccount(userId: number, accountId: number): Promise<string> {
    const user = await this.userRepository.findUserRoleByAccount(userId, accountId);
    // Validate the user so that we avoid typescript errors.
    if (!user) {
      throw new HttpException(`User with id: ${userId} not found`, HttpStatus.NOT_FOUND);
    }
    return user?.role ?? "";
  }
}
