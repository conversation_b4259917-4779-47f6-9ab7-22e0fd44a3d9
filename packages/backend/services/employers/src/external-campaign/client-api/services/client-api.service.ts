import { HttpException, HttpStatus, Inject, Injectable, Logger } from "@nestjs/common";
import { ExternalCampaignService } from "../../services/external-campaign.service";
import { suggestFeedCodeName } from "../../../common/resources/utils";
import { ExternalCampaignIngestionService } from "../../services/abstract/external-campaign-ingestion.service";
import { compareKeys } from "../../common/constants";
import { ExternalCampaign } from "../../entities/external-campaign.entity";
import { CACHE_MANAGER } from "@nestjs/cache-manager";
import { Cache } from "cache-manager";
import { createHash } from "crypto";

/**
 *
 */
@Injectable()
export class ClientApiService {
  private source = "client-api";
  private apiClientName = "appcast";
  private sourceKeys = [
    "api_client_key",
    "api_client_name",
    "publisher",
    "employer",
    "campaign",
    "prodOwnerEmail",
  ];
  private compareKeys = [...compareKeys, "notified"];
  /**
   *
   */
  constructor(
    @Inject(ExternalCampaignService)
    protected readonly externalCampaignService: ExternalCampaignService,
    @Inject(ExternalCampaignIngestionService)
    private readonly externalIngestion: ExternalCampaignIngestionService,
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
  ) {}

  /**
   * Remove special characters from a string. Example:
   * "RN - <60 day old posts" it's going to be returned as "RN - 60 day old posts" removing the < character.
   * @param string Name to be cleaned
   * @return string
   */
  cleanParam(line: string): string {
    const trimmedline = line.trim();
    if (trimmedline == "") {
      // PENDING: Replace with proper error handling
    }
    // Remove all angle brackets, square brackets, curly brackets and .js extensions, iframes and
    // Directory traversal attacks in strings, iframe, onmouseover events
    const customRegex =
      /[{}[]]|iframe|onmouseover|onclick|onchange|undefined|alert\([\S\s]*\)|\/\.\.\/|\.\.\/|src=|[+=']|&#39;/gi;

    return trimmedline.replace(customRegex, "");
  }

  /**
   * Method for making sure the most important variables for this type of ingestion exists at the moment of the creation of a campaign
   */
  private validateCriticalParams(publisher: any, employer: any, campaign: any): void {
    if (typeof publisher === "undefined") {
      throw new Error("publisher is undefined");
    }
    if (typeof employer === "undefined") {
      throw new Error("employer is empty");
    }
    if (typeof campaign === "undefined") {
      throw new Error("campaign is empty");
    }
  }

  /**
   * Generates an API campaign ID based on publisher information.
   */
  private generateApiCampaignId(publisher: any, campaign: any): string {
    return `${publisher["tag"]}_${publisher["publisher_id"]}_${campaign["campaign_id"]}`;
  }

  /**
   * Generates a talent campaign name based on employer, publisher, and campaign information.
   */
  private generateTalentCampaignName(employer: any, publisher: any, campaign: any): string {
    return `${employer["employer_name"]} - ${publisher["publisher_name"]} - ${campaign["campaign_id"]} - ${campaign["campaign_name"]}`;
  }

  /**
   * Generates a feed code name based on publisher and employer information.
   */
  private async generateFeedCodeName(
    apiClientName: string,
    publisher: any,
    employer: any,
  ): Promise<string> {
    let publisherInfo: string = "";
    // Checks if the enterprise name is "Appcast Exchanges" and formats the publisher name accordingly.
    if (publisher["enterprise_name"] === "Appcast Exchanges") {
      // Remove the 'Talent.com' from the publisher info
      const dataPublisher: string[] = (publisher["publisher_name"] ?? "")
        .replace(/Talent.com/i, "")
        .split(" ")
        .filter((str: string | any[]) => str.length > 0); // Remove empty positions in array

      // Implode all the data with dash
      publisherInfo = `-${dataPublisher.join("-")}`;
    }
    // Clean the enterprise name
    const enterpriseName = publisher["enterprise_name"]?.toLowerCase().replace(/ /g, "-");

    // Constructs the feed code name using various information including API client name, enterprise name, and currency name.
    let posibleFeedcode = `${apiClientName}-${enterpriseName}-${employer["currency_name"]?.toLowerCase()}${publisherInfo}`;

    // Evaluate the company name and suggest a feedCode
    posibleFeedcode = suggestFeedCodeName(posibleFeedcode);

    // Check if the feed code is already in use and suggest a new one if necessary
    const validateFeedcode = await this.externalCampaignService.validateFeedcodeCreation(
      `${publisher["publisher_id"]}`,
      `${employer["currency_name"]}`,
      `${apiClientName}`,
    );

    if (validateFeedcode && validateFeedcode != "") {
      const currentFeedCode = validateFeedcode;
      posibleFeedcode = posibleFeedcode != currentFeedCode ? currentFeedCode : posibleFeedcode;
    }

    return posibleFeedcode;
  }

  /**
   * Asynchronously preprocesses the data before further processing.
   * Cleans the names of publisher, employer, and campaign.
   * Generates API campaign ID, feed code name, and talent campaign name.
   * Checks if the campaign is selected as non-automatic campaigns and throws an error if so.
   * Checks if feed code, account, or campaign exist on Talent and generates the correct action and related link.
   * Validates the result of the generated action.
   * Builds a new entry based on the action.
   *
   * @returns A promise that resolves with the new entry.
   */
  async preProcess(sourceFields: Map<string, any>): Promise<{
    newEntry: any;
    lastEntry: any;
    talentCampaignID: string;
    isFullyAutoCampaign: boolean;
  }> {
    const totalStartTime = performance.now();
    let stepStartTime: number;
    const timings: Record<string, number> = {};

    try {
      // Validate correctly the apiClientName
      this.apiClientName = "appcast";

      // Parse JSON data
      stepStartTime = performance.now();
      const { publisher, employer, campaign } = this.getPrimaryData(sourceFields);
      timings["getPrimaryData"] = performance.now() - stepStartTime;

      stepStartTime = performance.now();
      this.validateCriticalParams(publisher, employer, campaign);
      timings["validateCriticalParams"] = performance.now() - stepStartTime;

      publisher["publisher_name"] = this.cleanParam(publisher["publisher_name"] ?? "");
      employer["employer_name"] = this.cleanParam(employer["employer_name"] ?? "");
      campaign["campaign_name"] = this.cleanParam(campaign["campaign_name"] ?? "");

      // Generate IDs and names
      stepStartTime = performance.now();
      const apiCampaignId = this.generateApiCampaignId(publisher, campaign);
      timings["generateApiCampaignId"] = performance.now() - stepStartTime;

      /**
       * ####################################################################################################
       * LAST ENTRY VS NEW ENTRY
       * ####################################################################################################
       */
      const hashLastEntry = await this.cacheManager.get<any>(apiCampaignId);

      const hashNewEntry = createHash("sha256")
        .update(JSON.stringify([...sourceFields]))
        .digest("hex");

      if (hashLastEntry) {
        if (hashNewEntry == hashLastEntry) {
          Logger.log(
            `[Client-api] [processIngestion] Campaign '${apiCampaignId}' skipped. Hash already exists.`,
          );
          return {
            newEntry: null,
            lastEntry: null,
            talentCampaignID: "",
            isFullyAutoCampaign: false,
          };
        }
      }
      await this.cacheManager.set(apiCampaignId, hashNewEntry, 0);

      //####################################################################################################

      // Start these promises in parallel
      stepStartTime = performance.now();
      const [talentFeedcodeName, isNonExternalCampaign] = await Promise.all([
        this.generateFeedCodeName(this.apiClientName, publisher, employer),
        this.externalIngestion.isNonExternalCampaigns(apiCampaignId),
      ]);
      timings["parallel_feedCodeAndNonExternalCheck"] = performance.now() - stepStartTime;

      // Check if the campaign is selected as non-automatic campaigns and throw an error if so
      if (isNonExternalCampaign) {
        // Log the error and throw an exception
        throw new HttpException(
          "The campaign is excluded from the automation process",
          HttpStatus.NOT_FOUND,
        );
      }

      stepStartTime = performance.now();
      const talentCampaignName = this.generateTalentCampaignName(employer, publisher, campaign);
      timings["generateTalentCampaignName"] = performance.now() - stepStartTime;

      // Start the generateLinks call and lastEntry lookup in parallel
      stepStartTime = performance.now();
      const [{ action, talentCampaignID, isFullyAutoCampaign }, lastEntry] = await Promise.all([
        this.externalCampaignService.generateLinks(
          talentFeedcodeName,
          talentCampaignName,
          apiCampaignId,
          publisher["currency_name"] ?? "",
        ),
        this.externalCampaignService.findOne("apiCampaignId", apiCampaignId),
      ]);
      timings["parallel_generateLinksAndFindOne"] = performance.now() - stepStartTime;

      // Start stopDuplicationProcess (async)
      stepStartTime = performance.now();
      const stopDuplicationPromise = this.stopDuplicationProcess(
        action,
        apiCampaignId,
        campaign.campaign_id,
      );

      // While that's running, build the new entry
      const buildEntryStartTime = performance.now();
      const newEntryMap = this.buildNewEntry(
        action,
        campaign,
        sourceFields,
        talentCampaignID,
        isFullyAutoCampaign,
        apiCampaignId,
        talentCampaignName,
        talentFeedcodeName,
      );
      timings["buildNewEntry"] = performance.now() - buildEntryStartTime;

      // Wait for the stopDuplication process to complete
      await stopDuplicationPromise;
      timings["stopDuplicationProcess"] = performance.now() - stepStartTime;

      stepStartTime = performance.now();
      let newEntry: Partial<ExternalCampaign> = Object.fromEntries(newEntryMap);
      timings["createRepoInstance"] = performance.now() - stepStartTime;

      // Calculate the correct notified value
      stepStartTime = performance.now();
      const notified = sourceFields.get("campaign_id") == "Talent.com US CPA" ? "no" : "toSend";
      newEntry = this.externalIngestion.calculateNotifyAndStatus(
        notified,
        lastEntry,
        newEntry,
        true,
        talentCampaignID,
      );
      timings["calculateNotifyAndStatus"] = performance.now() - stepStartTime;

      // Calculate and log total time
      const totalTime = performance.now() - totalStartTime;
      Logger.log(`[Performance] preProcess total time: ${totalTime.toFixed(2)}ms`);

      // Log all step timings
      Object.entries(timings).forEach(([step, time]) => {
        Logger.log(
          `[Performance] preProcess - ${step}: ${time.toFixed(2)}ms (${((time / totalTime) * 100).toFixed(1)}%)`,
        );
      });

      return { newEntry, lastEntry, talentCampaignID, isFullyAutoCampaign };
    } catch (error) {
      // Still log timing info even if there's an error
      const totalTime = performance.now() - totalStartTime;
      Logger.log(`[Performance] preProcess failed after ${totalTime.toFixed(2)}ms`);
      Object.entries(timings).forEach(([step, time]) => {
        Logger.log(
          `[Performance] preProcess - ${step}: ${time.toFixed(2)}ms (${((time / totalTime) * 100).toFixed(1)}%)`,
        );
      });
      throw error;
    }
  }

  /**
   * Builds a new entry object based on the provided action.
   *
   * @param action The action to be performed.
   * @returns An object representing the new entry.
   */
  buildNewEntry(
    action: string,
    campaign: any,
    sourceFields: Map<string, any>,
    talentCampaignID: string,
    isFullyAutoCampaign: boolean,
    apiCampaignId: string,
    talentCampaignName: string,
    talentFeedcodeName: string,
  ): Map<string, string> {
    const totalStartTime = performance.now();
    const timings: Record<string, number> = {};
    let stepStartTime: number;

    try {
      // Build budget type and value
      stepStartTime = performance.now();
      const { budgetType, budgetValue } = this.buildBudgetTypeAndValue(campaign);
      timings["buildBudgetTypeAndValue"] = performance.now() - stepStartTime;

      // Validate the budget
      stepStartTime = performance.now();
      const validateBudget = this.externalIngestion.handleValidateBudget(
        action,
        campaign["campaign_status"] ?? "",
        budgetType,
        budgetValue,
        sourceFields,
        talentCampaignID,
        isFullyAutoCampaign,
      );
      timings["handleValidateBudget"] = performance.now() - stepStartTime;

      action = validateBudget.get("action") ?? "";

      // Populate the new entry
      stepStartTime = performance.now();
      const newEntry = this.populateNewEntry(
        action,
        budgetType,
        budgetValue,
        validateBudget,
        apiCampaignId,
        sourceFields,
        talentCampaignName,
        talentFeedcodeName,
      );
      timings["populateNewEntry"] = performance.now() - stepStartTime;

      // Log performance data
      const totalTime = performance.now() - totalStartTime;
      Logger.log(`[Performance] buildNewEntry total time: ${totalTime.toFixed(2)}ms`);

      // Log all step timings
      Object.entries(timings).forEach(([step, time]) => {
        Logger.log(
          `[Performance] buildNewEntry - ${step}: ${time.toFixed(2)}ms (${((time / totalTime) * 100).toFixed(1)}%)`,
        );
      });

      return newEntry;
    } catch (error) {
      // Still log timing info even if there's an error
      const totalTime = performance.now() - totalStartTime;
      Logger.log(`[Performance] buildNewEntry failed after ${totalTime.toFixed(2)}ms`);
      Object.entries(timings).forEach(([step, time]) => {
        Logger.log(
          `[Performance] buildNewEntry - ${step}: ${time.toFixed(2)}ms (${((time / totalTime) * 100).toFixed(1)}%)`,
        );
      });
      throw error;
    }
  }

  /**
   *
   * @returns
   */
  private buildBudgetTypeAndValue(campaign: any): { budgetType: string; budgetValue: string } {
    // Build budget type and value
    let budgetType: string = "";
    let budgetValue: string = "";

    if (
      (campaign.publisher_weekly_budget && campaign.publisher_weekly_budget !== "no limit") ||
      (Number(campaign.publisher_weekly_budget) === 0 &&
        parseFloat(campaign.publisher_monthly_budget ?? "0") <= 0 &&
        campaign.publisher_monthly_budget !== "no limit")
    ) {
      budgetType = "weekly";
      budgetValue = `${parseFloat(campaign["publisher_weekly_budget"] ?? "0")}`;
    } else if (
      campaign["publisher_monthly_budget"] ||
      campaign["publisher_monthly_budget"] === "0"
    ) {
      budgetType = "monthly";
      budgetValue =
        campaign["publisher_monthly_budget"] === "no limit"
          ? "no limit"
          : `${parseFloat(campaign["publisher_monthly_budget"] ?? "0")}`;
    }
    return { budgetType, budgetValue };
  }

  /**
   *
   * @param validateBudget
   * @returns
   */
  private populateBasicFields(
    validateBudget: Map<string, string>,
    apiCampaignId: string,
    sourceFields: Map<string, any>,
  ): Map<string, string> {
    const { publisher, employer, campaign } = this.getPrimaryData(sourceFields);
    const newEntry = this.populateComplexFields(campaign);
    newEntry.set("api_campaign_id", apiCampaignId);
    newEntry.set("api_client_key", sourceFields?.get("api_client_key") ?? "");
    newEntry.set("api_client_name", this.apiClientName ?? "");
    newEntry.set("account_id", publisher["enterprise_id"] ?? "");
    newEntry.set("account_name", publisher["enterprise_name"] ?? "");
    newEntry.set("account_currency_name", publisher["currency_name"] ?? "");
    newEntry.set("company_id", employer["employer_id"] ?? "");
    newEntry.set("company_name", employer["employer_name"] ?? "");
    newEntry.set("company_currency_name", employer["currency_name"] ?? "");
    newEntry.set("company_country", employer["countries"] ?? "");
    newEntry.set("company_monthly_budget", employer["publisher_monthly_budget"] ?? "");
    newEntry.set("xml_feed_link", employer["xml_feed"] ?? "");
    newEntry.set("campaign_id", campaign["campaign_id"] ?? "");
    newEntry.set("campaign_name", campaign["campaign_name"] ?? "");
    newEntry.set("campaign_status", validateBudget.get("campaignStatus") ?? "");
    newEntry.set("campaign_country", employer["countries"] ?? "");

    return newEntry;
  }

  /**
   *
   */
  private populateComplexFields(campaign: any): Map<string, string> {
    const newEntry: Map<string, string> = new Map();

    newEntry.set(
      "estimated_budget_days_remaining",
      campaign["estimated_budget_days_remaining"] ?? "",
    );
    if (campaign["bid"] && campaign["bid"] != "N/A") {
      newEntry.set("campaign_cpc", campaign["bid"]);
    }
    newEntry.set("xml_num_jobs", campaign["exported_jobs_count"] ?? "");
    newEntry.set("campaign_target_cost", campaign["paid_cpa_goal"] ?? "");
    newEntry.set("campaign_sponsorship_ended", campaign["sponsorship_ended"] ?? "");

    return newEntry;
  }
  /**
   *
   */
  private populateNewEntry(
    action: string,
    budgetType: string,
    budgetValue: string,
    validateBudget: Map<string, string>,
    apiCampaignId: string,
    sourceFields: Map<string, any>,
    talentCampaignName: string,
    talentFeedcodeName: string,
  ): Map<string, string> {
    const newEntry = this.populateBasicFields(validateBudget, apiCampaignId, sourceFields);
    const { campaign, publisher } = this.getPrimaryData(sourceFields);

    newEntry.set("talent_campaign", talentCampaignName ?? "");
    newEntry.set("talent_feedcode", talentFeedcodeName ?? "");
    newEntry.set("action", action);
    newEntry.set("source", this.source);
    newEntry.set(
      "campaign_delivery",
      parseFloat(budgetValue ?? "") < 300 && budgetType === "monthly"
        ? ""
        : (campaign["campaign_delivery"] ?? ""),
    );
    newEntry.set("campaign_conversion_type", campaign["campaign_conversion_type"] ?? "");

    // Asing the new values to the new Entry
    newEntry.set("campaign_budget_type", budgetType ?? "");
    newEntry.set("campaign_budget_value", budgetValue ?? "");
    newEntry.set("publisher_id", publisher.publisher_id);

    return newEntry;
  }

  /**
   * This method is responsible for processing and handling campaign data received from Kafka.
   * It receives a message containing the campaign data, validates it, sets relevant fields,
   * and performs any necessary pre-processing steps.
   *
   * @param message - The message received from Kafka containing the campaign data.
   */
  async campaignsConsumer(message: any) {
    try {
      // Validate and extract the campaign data from the received message
      const data = this.validateProdMessage(message);

      const dataNew: Map<string, string> = new Map();
      dataNew.set("publisher", data.publisher);
      dataNew.set("employer", data.employer);
      dataNew.set("campaign", data.campaign);
      // Set relevant fields based on the extracted data
      const sourceFields = this.setFields(dataNew);
      // Process the external campaign data (logic in processExternalCampaign)
      await this.processIngestion(sourceFields);
    } catch (error: any) {
      // Log the error and rethrow it
      const dataError = new Map<string, any>();
      dataError.set("type", "error-external-campaigns");
      dataError.set("entityId", this.apiClientName);
      dataError.set("messageStatus", error);
      await this.externalIngestion.saveLog(
        dataError,
        `consumer-unexpected-error-${this.source}`,
        `no data here`,
        "80010",
      );
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  /**
   * This method is responsible for validating and parsing messages received from a producer (e.g., Kafka).
   * It checks if the message contains a value, converts it to a string, and then parses it as a JSON object.
   *
   * @param message - The message received from the producer.
   * @returns The parsed JSON object extracted from the message, or throws an error if the message is invalid.
   */
  validateProdMessage(message: any) {
    try {
      // Check if the message has a value property, and convert it to a string
      const prodMessage = message?.value ? message.value.toString() : "";

      // If the message doesn't have a value, throw an error
      if (!prodMessage) {
        throw new HttpException("Couldn't read a value from the producer", HttpStatus.BAD_REQUEST);
      }

      // Convert the message buffer to a string
      const rawData = prodMessage.toString();

      // Parse the string as a JSON object
      const data = JSON.parse(rawData);

      // Return the parsed JSON object
      return data;
    } catch (error: any) {
      // Log the error and rethrow it
      const dataError = new Map<string, any>();
      dataError.set("type", "error-validate-message");
      dataError.set("entityId", this.apiClientName);
      dataError.set("messageStatus", error);
      this.externalIngestion.saveLog(
        dataError,
        `consumer-unexpected-error-${this.source}`,
        `no data here`,
        "80010",
      );
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  /**
   * Stops the duplication process based on certain conditions.
   *
   * @param action - The action to be performed, e.g., "Create Campaign", "Update Campaign", etc.
   * @returns A promise that resolves to an empty object if certain conditions are met, otherwise no return value.
   */
  async stopDuplicationProcess(action: string, apiCampaignId: string, campaignId: string) {
    // Check if the campaign's client name is "Appcast" and the API campaign ID starts with "appcast_12492_%".
    if (
      this.apiClientName === "appcast" &&
      /appcast_12492_/i.test(apiCampaignId) &&
      action == "-"
    ) {
      // If the current campaign is already live on Talent (indicated by action "-") and receives an update:
      // Check if there is another active campaign with the same campaign ID in the API bucket.
      const duplicatedCampaign = await this.externalCampaignService.duplicatedCampaign(
        String(campaignId) ?? "",
        apiCampaignId,
      );

      // Pause the campaign in Talent whose API campaign ID starts with "appcast_12492_%",
      // but only if there is a duplicated campaign.
      if (duplicatedCampaign) {
        await this.externalCampaignService.pausedCampaignTalent(apiCampaignId, "paused");
      }
    }
  }

  /**
   *
   * @param data
   * @param strict
   */
  setFields(data: Map<string, string>, strict: boolean = true): Map<any, any> {
    const sourceFields = new Map();
    // Defining the properties at class level according to what we got from the job
    for (const [key, value] of data) {
      if (strict && !this.sourceKeys?.includes(key)) {
        continue;
      }
      sourceFields?.set(key, value);
    }

    return sourceFields;
  }

  /**
   *
   * @param sourceFields
   * @returns
   */
  getPrimaryData(sourceFields: Map<string, any>) {
    const publisher = sourceFields?.get("publisher");
    const employer = sourceFields?.get("employer");
    const campaign = sourceFields?.get("campaign");

    return { publisher, employer, campaign };
  }

  /**
   *
   * @param sourceFields
   */
  async processIngestion(sourceFields: Map<string, any>) {
    try {
      const preProcessStartTime = performance.now();
      const { newEntry, lastEntry, talentCampaignID, isFullyAutoCampaign } =
        await this.preProcess(sourceFields);
      const preProcessEndTime = performance.now();
      const preProcessDuration = preProcessEndTime - preProcessStartTime;
      Logger.log(`[Performance] preProcess took ${preProcessDuration.toFixed(2)}ms`);

      if (typeof newEntry === "object" && newEntry !== null) {
        if (newEntry.campaign_sponsorship_ended && lastEntry?.campaignSponsorshipEnded == "true") {
          return;
        }

        const processExternalStartTime = performance.now();
        await this.externalIngestion.processExternalCampaign(
          sourceFields,
          newEntry.api_campaign_id,
          newEntry.source,
          this.compareKeys,
          newEntry,
          lastEntry,
          talentCampaignID,
          isFullyAutoCampaign,
        );
        const processExternalEndTime = performance.now();
        const processExternalDuration = processExternalEndTime - processExternalStartTime;
        Logger.log(
          `[Performance] processExternalCampaign took ${processExternalDuration.toFixed(2)}ms`,
        );
      }
    } catch (error: any) {
      Logger.error(new Error(`[Client-api] [processIngestion] ${error.message}`));
      throw error;
    }
  }
}
