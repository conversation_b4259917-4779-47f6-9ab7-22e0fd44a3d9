import { Modu<PERSON> } from "@nestjs/common";
import { XmlModule } from "./xml/xml.module";
import { ClientApiModule } from "./client-api/client-api.module";
import { TalentApiModule } from "./talent-api/talent-api.module";
import { ExternalCampaignController } from "./controllers/external-campaign.controller";
import { ExternalCampaignService } from "./services/external-campaign.service";
import { ExternalCampaign } from "./entities/external-campaign.entity";
import { TypeOrmModule } from "@nestjs/typeorm";
import { ExternalCampaignRepository } from "./repositories/external-campaign.repository";
import { CampaignsModule } from "../campaigns/campaigns.module";
import { NotUpdatedService } from "./services/not-updated.service";
import { NotUpdatedRepository } from "./repositories/not-updated.repository";
import { NotUpdated } from "./entities/not-updated.entity";
import { AccountsModule } from "../accounts/accounts.module";
import { CustomAxiosAdapter } from "../common/adapters/axios.adapter";
import { HttpModule } from "@nestjs/axios";
import { TagsModule } from "../tags/tags.module";
import { Metrics } from "@talent-back-libs/metrics";
import { MetricsService } from "../common/services/metrics.service";
import { ExternalCampaignOverridesService } from "./services/external-campaign-overrides.service";
import { ExternalCampaignOverridesRepository } from "./repositories/external-campaign-overrides.repository";
import { Override } from "./entities/override.entity";
import { ExternalCampaignOverridesController } from "./controllers/external-campaign-overrides.controller";
import { TalentApiService } from "./talent-api/services/talent-api.service";
import { CampaignsService } from "../campaigns/services/campaigns.service";
import { CampaignRepository } from "../campaigns/repositories/campaign.repository";
import { CampaignValidationService } from "../campaigns/services/campaigns-validations.service";
import { KpisTargetService } from "../campaigns/services/kpis-target.service";
import { Campaigns } from "../campaigns/entities/campaign.entity";
import { PrivacyService } from "../common/resources/privacy.service";
import { StrategyLoader } from "../campaigns/dto/validators-strategies/strategy-loader.service";
import { KpisTargetRepository } from "../campaigns/repositories/kpis-target.repository";
import { CurrencyService } from "../common/resources/currencies";
import { CampaignObjectiveValidationsStrategy } from "../campaigns/dto/validators-strategies/strategies/campaigns-objective-validations.strategy";
import { CampaignBasicValidationsStrategy } from "../campaigns/dto/validators-strategies/strategies";
import { CampaignSettingsValidationStrategy } from "../campaigns/dto/validators-strategies/strategies/campaign-settings-validations.strategy";
import { CampaignSponsorshipAdvancedValidationsStrategy } from "../campaigns/dto/validators-strategies/strategies/campaigns-sponsorship-advanced-validations.strategy";
import { CampaignRoleActionsValidationStrategy } from "../campaigns/dto/validators-strategies/strategies/campaigns-role-actions-validations.strategy";
import { KpiTarget } from "../campaigns/entities/kpi-target.entity";
import { DealsModule } from "../deals/deals.module";
import { UsersModule } from "../users/users.module";
import { CampaignsStatsSummaryView } from "../campaigns/entities/campaigns-summary-view.entity";
import { CacheModule } from "@nestjs/cache-manager";
import type { RedisClientOptions } from "redis";
import { redisStore } from "cache-manager-redis-yet";
import { ScheduleModule } from "@nestjs/schedule";
import { ExternalCampaignCronService } from "./services/external-campaign-crons.service";
import { ExternalCampaignHelperService } from "./services/external-campaign-helper.service";
import { EmployerErrorLogsModule } from "../common/employer-error-logs/employer-error-logs.module";
import { ToolsTrackingLog } from "../common/employer-error-logs/entities/tools-tracking.entity";
import { ConsumersModule } from "./consumers/consumers.module";
import { ExternalCampaignIngestionService } from "./services/abstract/external-campaign-ingestion.service";
import { NotUpdatedController } from "./controllers/not-updated.controller";
import { ScreeningQuestionsModule } from "../screening-questions/screening-questions.module";
const isProd = process.env.ENVIRONMENT === "prod" || process.env.ENVIRONMENT === "dev";

/**
 * Auto Campaign's entire functionalities module, for specific features, please access the submodules of this one
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([
      ExternalCampaign,
      NotUpdated,
      ExternalCampaignRepository,
      Override,
      Campaigns,
      KpiTarget,
      KpisTargetRepository,
      ToolsTrackingLog,
    ]),
    ScheduleModule.forRoot(),
    XmlModule,
    ClientApiModule,
    TalentApiModule,
    CampaignsModule,
    AccountsModule,
    HttpModule,
    TagsModule,
    DealsModule,
    UsersModule,
    ScreeningQuestionsModule,
    EmployerErrorLogsModule,
    ...(isProd ? [ConsumersModule] : []),
    TypeOrmModule.forFeature([CampaignsStatsSummaryView], "employersPostgres"),
    CacheModule.register<RedisClientOptions>({
      isGlobal: true,
      store: redisStore,
      url: process.env.REDIS_URL,
    }),
  ],
  exports: [
    ExternalCampaignService,
    NotUpdatedService,
    ExternalCampaignOverridesService,
    ExternalCampaignIngestionService,
  ],
  providers: [
    ExternalCampaignCronService,
    ExternalCampaignService,
    NotUpdatedService,
    ExternalCampaignRepository,
    NotUpdatedRepository,
    CustomAxiosAdapter,
    MetricsService,
    Metrics,
    ExternalCampaignOverridesService,
    ExternalCampaignOverridesRepository,
    TalentApiService,
    CampaignsService,
    CampaignRepository,
    CampaignValidationService,
    KpisTargetService,
    KpisTargetRepository,
    PrivacyService,
    StrategyLoader,
    CurrencyService,
    CampaignObjectiveValidationsStrategy,
    CampaignBasicValidationsStrategy,
    CampaignSettingsValidationStrategy,
    CampaignSponsorshipAdvancedValidationsStrategy,
    CampaignRoleActionsValidationStrategy,
    ExternalCampaignHelperService,
    ExternalCampaignIngestionService,
  ],
  controllers: [
    ExternalCampaignController,
    ExternalCampaignOverridesController,
    NotUpdatedController,
  ],
})
export class ExternalCampaignModule {}
