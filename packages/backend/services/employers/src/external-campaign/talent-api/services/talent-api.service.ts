/* eslint-disable @typescript-eslint/no-explicit-any */
import { HttpException, HttpStatus, Inject, Injectable, Logger } from "@nestjs/common";
import { ExternalCampaignService } from "../../services/external-campaign.service";
import { compareKeys, sourceKeys } from "./talent-api.constants";
import { suggestFeedCodeName } from "../../../common/resources/utils";
import { CampaignStatus } from "../../../common/resources/enums";
import { CampaignsService } from "../../../campaigns/services/campaigns.service";
import { CustomAxiosAdapter } from "../../../common/adapters/axios.adapter";
import { AccountService } from "../../../accounts/services/accounts.service";
import { Campaigns } from "../../../campaigns/entities/campaign.entity";
import * as privacy from "libs/privacy/src";
import { TagsService } from "../../../tags/services/tags.service";
import { UpdateCampaignTalentApiDto } from "../dto/update-campaign-talent-api.dto";
import { ExternalCampaignIngestionService } from "../../services/abstract/external-campaign-ingestion.service";
import { ExternalCampaign } from "../../entities/external-campaign.entity";
import { CACHE_MANAGER } from "@nestjs/cache-manager";
import { Cache } from "cache-manager";
import { createHash } from "crypto";

/**
 *
 */
@Injectable()
export class TalentApiService {
  private source = "talent-api";
  private sourceKeys = [...sourceKeys];
  private compareKeys = compareKeys;
  public version: number = 1;
  /**
   *
   */
  constructor(
    @Inject(ExternalCampaignService)
    protected readonly externalCampaignService: ExternalCampaignService,
    @Inject(ExternalCampaignIngestionService)
    private readonly externalIngestion: ExternalCampaignIngestionService,
    @Inject(CampaignsService) private readonly campaignsService: CampaignsService,
    @Inject(CustomAxiosAdapter) private readonly axios: CustomAxiosAdapter,
    @Inject(AccountService) private readonly accountService: AccountService,
    @Inject(TagsService) private readonly tagsService: TagsService,
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
  ) {}

  /**
   *
   * @param apiClientName
   * @param sourceFields
   * @returns
   */
  generateApiCampaignId(
    apiClientName: string | undefined,
    sourceFields: Map<string, string>,
  ): string {
    if (!apiClientName || !sourceFields.get("account_id") || !sourceFields.get("campaign_id")) {
      return "";
    }
    const apiCampaignId = `${apiClientName}_${sourceFields.get("account_id")}_${sourceFields.get("campaign_id")}`;
    return apiCampaignId;
  }

  /**
   *
   * @param apiClientName
   * @param sourceFields
   * @returns
   */
  async generateFeedCodeName(
    apiClientName: string,
    sourceFields: Map<string, string>,
  ): Promise<string> {
    if (
      !apiClientName ||
      !sourceFields.get("account_name") ||
      !sourceFields.get("account_currency_name")
    ) {
      return "";
    }

    const accountName = sourceFields.get("account_name") ?? "";
    let posibleFeedcode = "";

    if (new RegExp(`^${apiClientName}\\b`, "i").test(accountName.toLowerCase().trim())) {
      posibleFeedcode = `${accountName}-${sourceFields.get("account_currency_name")}`;
    } else {
      posibleFeedcode = `${apiClientName}-${accountName}-${sourceFields.get("account_currency_name")}`;
    }

    // Evaluar el nombre de la empresa y sugerir un feedCode
    posibleFeedcode = suggestFeedCodeName(posibleFeedcode);

    // Validar feedcode según el publisher_id, tag y currency
    const validateFeedcode = await this.externalCampaignService.validateFeedcodeCreation(
      sourceFields.get("account_id") ?? "",
      sourceFields.get("account_currency_name") ?? "",
      apiClientName,
    );

    if (validateFeedcode) {
      posibleFeedcode = posibleFeedcode !== validateFeedcode ? validateFeedcode : posibleFeedcode;
    }

    return posibleFeedcode;
  }

  /**
   *
   * @param sourceFields
   * @returns
   */
  generateTalentCampaignName(sourceFields: Map<string, string>): string {
    const companyName = sourceFields.get("company_name") ?? "";
    const campaignName = sourceFields.get("campaign_name") ?? "";
    const campaignId = sourceFields.get("campaign_id") ?? "";

    if (!companyName || !campaignName || !campaignId) {
      console.error("Error: missing values");
      return "";
    }

    return new RegExp(`^${companyName}\\b`, "i").test(campaignName.trim())
      ? `${campaignId} - ${campaignName}`
      : `${companyName} - ${campaignId} - ${campaignName}`;
  }

  /**
   *
   * @returns
   */
  async preProcess(
    sourceFields: Map<string, string>,
    key: string,
    version?: number,
  ): Promise<{
    newEntry: any;
    lastEntry: any;
    talentCampaignID: string;
    isFullyAutoCampaign: boolean;
  }> {
    const apiClientName = await this.getValidatedClientTagName(key);
    // if the request comes from the v2 of the update
    /*if (version == 2) {
      const apiCampaignID = this.generateApiCampaignId(apiClientName, sourceFields);
      await this.updateV2(apiCampaignID, sourceFields);
    }*/

    // 1. Validate user action
    const userAction = sourceFields.get("user_action");
    const useId = sourceFields.get("user_id");
    if (userAction && userAction.trim() === "" && useId && useId.trim() === "") {
      throw new Error("Error, client name wasn't defined");
    }

    // 2. Preprocess source fields
    this.preprocessSourceFields(sourceFields);

    // 3. Generate campaign and talent feedcode variables
    const apiCampaignId = this.generateApiCampaignId(apiClientName, sourceFields);

    /**
     * ####################################################################################################
     * LAST ENTRY VS NEW ENTRY
     * ####################################################################################################
     */
    const hashLastEntry = await this.cacheManager.get<any>(apiCampaignId);

    const hashNewEntry = createHash("sha256")
      .update(JSON.stringify([...sourceFields]))
      .digest("hex");

    if (hashLastEntry) {
      if (hashNewEntry == hashLastEntry) {
        Logger.log(
          `[Talent-api] [processIngestion] Campaign '${apiCampaignId}' skipped. Hash already exists.`,
        );
        throw new HttpException("Campaign already exists with this data", HttpStatus.BAD_REQUEST);
      }
    }
    await this.cacheManager.set(apiCampaignId, hashNewEntry, 0);

    //####################################################################################################

    const talentCampaignName = this.generateTalentCampaignName(sourceFields);
    const talentFeedcodeName = await this.generateFeedCodeName(apiClientName ?? "", sourceFields);

    const accountCurrencyName = sourceFields.get("account_currency_name") ?? "";

    // 4. Generate action based on existing data
    const { action, talentCampaignID, isFullyAutoCampaign } =
      await this.externalCampaignService.generateLinks(
        talentFeedcodeName,
        talentCampaignName,
        apiCampaignId,
        accountCurrencyName,
      );

    const isAccountCreated: boolean = action !== "feedcode" && action !== "account";

    // 6. Build new entry
    let newEntry: Partial<ExternalCampaign> = this.buildNewEntry(
      action,
      accountCurrencyName,
      sourceFields,
      talentCampaignID,
      isFullyAutoCampaign,
      apiCampaignId,
      talentCampaignName,
      talentFeedcodeName,
      apiClientName,
    );

    // Perform some validations if the campaign already exists
    if (talentCampaignID && talentCampaignID != "") {
      await this.ifCampaignExistValidations(
        parseFloat(newEntry.campaignBudgetValue as string),
        sourceFields,
        apiCampaignId,
      );
    }

    const lastEntry =
      (await this.externalCampaignService.findOne("apiCampaignId", apiCampaignId)) ?? "";

    // Give the correct notified value depending on last entry and action
    const notified = "toSend";
    newEntry = this.calculateNotifyAndStatus(
      notified,
      lastEntry,
      newEntry,
      isAccountCreated,
      talentCampaignID,
    );

    return { newEntry, lastEntry, talentCampaignID, isFullyAutoCampaign };
  }

  /**
   * Preprocesses the source fields by trimming values, transforming specific keys to lowercase,
   * and normalizing values based on specified keys.
   *
   * This method performs the following steps:
   * 1. Trims all values in the `sourceFields` map.
   * 2. Converts specified keys to lowercase.
   * 3. Normalizes values by converting them to lowercase for keys not specified to be preserved.
   */
  private preprocessSourceFields(sourceFields: Map<string, string>) {
    // List of keys to transform to lowercase
    const fieldsToLowercase = ["account_name", "company_name", "campaign_name"];

    /**
     * Trim all values in the sourceFields map,
     * Convert specified keys to lowercase
     * and trim values and Normalize values by converting to lowercase for keys not in keysToPreserve
     */
    sourceFields.forEach((value, key) => {
      const transformedKey = fieldsToLowercase.includes(key) ? key.toLowerCase() : key;
      let transformedValue = typeof value === "string" ? value.trim() : value;
      if (!fieldsToLowercase.includes(key) && typeof value === "string") {
        transformedValue = value.toLowerCase();
      }
      sourceFields.set(transformedKey, transformedValue);
    });
  }

  /**
   * Build new entry
   * @param action
   * @param accountCurrencyName
   * @returns
   */
  buildNewEntry(
    action: string,
    accountCurrencyName: string,
    sourceFields: Map<string, string>,
    talentCampaignID: string,
    isFullyAutoCampaign: boolean,
    apiCampaignId: string,
    talentCampaignName: string,
    talentFeedcodeName: string,
    apiClientName: string,
  ): Map<string, string> {
    // Validate the budget
    const campaignBugetType = sourceFields.get("campaign_budget_type") ?? "";
    const campaignBugetValue = sourceFields.get("campaign_budget_value") ?? "";
    const campaignStatus = sourceFields.get("campaign_status") ?? "";

    // Validate the budget
    const validateBudget = this.externalIngestion.handleValidateBudget(
      action,
      campaignStatus,
      campaignBugetType,
      campaignBugetValue,
      sourceFields,
      talentCampaignID,
      isFullyAutoCampaign,
    );

    action =
      validateBudget.has("action") && validateBudget.get("action") !== ""
        ? (validateBudget.get("action") ?? "-")
        : "-";
    const status =
      validateBudget.has("campaignStatus") && validateBudget.get("campaignStatus") !== ""
        ? (validateBudget.get("campaignStatus") ?? "")
        : "";
    // Assign variables from sourceFields that we are using in compareKeys
    const newEntry = this.createBasicFields(
      action,
      accountCurrencyName,
      status,
      sourceFields,
      apiCampaignId,
      talentCampaignName,
      talentFeedcodeName,
      apiClientName,
    );

    // Asing the empty values to the unused fields for this ingestions
    newEntry["campaign_target_cost"] = newEntry["campaign_target_cost"]
      ? `${newEntry["campaign_target_cost"]}`
      : "";
    newEntry["campaign_daily_budget"] = "";
    newEntry["campaign_monthly_budget"] = "";
    newEntry["campaign_sponsorship_ended"] = "";
    newEntry["company_monthly_budget"] = "";
    newEntry["notified"] = "";
    newEntry["campaign_weekly_budget"] = "";

    return newEntry;
  }

  /**
   * Create basic fields
   * @param action
   * @param accountCurrencyName
   * @param status
   * @returns
   */
  private createBasicFields(
    action: string,
    accountCurrencyName: string,
    status: string,
    sourceFields: Map<string, string>,
    apiCampaignId: string,
    talentCampaignName: string,
    talentFeedcodeName: string,
    apiClientName: string,
  ): any {
    const newEntry: any = {}; // Use any type if the structure of newEntry is not known beforehand
    newEntry["campaign_start_date"] = sourceFields.get("campaign_start_date") ?? "";
    newEntry["campaign_end_date"] = sourceFields.get("campaign_end_date") ?? "";
    newEntry["campaign_budget_type"] = sourceFields.get("campaign_budget_type");
    newEntry["campaign_budget_value"] = `${sourceFields.get("campaign_budget_value")}`;
    newEntry["apply_email"] = sourceFields.get("apply_email");
    newEntry["account_budget"] = sourceFields.has("account_budget")
      ? sourceFields.get("account_budget")
      : "";
    newEntry["account_country"] = sourceFields.get("account_country");

    // We assign to the main array the variables with all the necessary fields to create the record in the bucket
    newEntry["api_campaign_id"] = apiCampaignId;
    newEntry["api_client_key"] = sourceFields.get("client_id") ?? "";
    newEntry["api_client_name"] = apiClientName;

    newEntry["account_id"] = sourceFields.get("account_id");
    newEntry["account_name"] = sourceFields.get("account_name");
    newEntry["account_currency_name"] = accountCurrencyName;

    newEntry["company_id"] = sourceFields.get("company_id");
    newEntry["company_name"] = sourceFields.get("company_name");
    newEntry["company_currency_name"] = accountCurrencyName;
    newEntry["company_country"] = sourceFields.get("account_country");
    newEntry["xml_feed_link"] = sourceFields.get("source_jobs");

    newEntry["campaign_id"] = sourceFields.get("campaign_id");
    newEntry["campaign_name"] = sourceFields.get("campaign_name");
    newEntry["campaign_status"] = status;
    newEntry["campaign_country"] = sourceFields.get("campaign_country");
    newEntry["campaign_delivery"] = sourceFields.get("campaign_delivery");
    newEntry["campaign_sponsorship_ended"] =
      sourceFields.get("campaign_status") === "active" ? "" : "1";
    newEntry["estimated_budget_days_remaining"] = sourceFields.has(
      "estimated_budget_days_remaining",
    )
      ? `${sourceFields.get("estimated_budget_days_remaining")}`
      : "";
    if (sourceFields.has("campaign_ppc") || sourceFields.get("campaign_ppc")) {
      newEntry["campaign_cpc"] = `${sourceFields.get("campaign_ppc")}`;
    }

    const applytype = sourceFields.get("apply_type") ?? "";
    const deliveryType = sourceFields.get("application_delivery_type") ?? "";

    newEntry["campaign_apply_type"] = applytype.toLowerCase();
    newEntry["campaign_application_delivery_type"] = deliveryType.toLowerCase();
    newEntry["campaign_target_cost"] = sourceFields.get("campaign_target_cost") ?? "";

    newEntry["xml_num_jobs"] = `${sourceFields.get("estimated_num_jobs")}`;
    newEntry["talent_campaign"] = talentCampaignName;
    newEntry["talent_feedcode"] = talentFeedcodeName;
    newEntry["action"] = action;
    newEntry["source"] = "talent-api";

    newEntry["date"] = new Date().toISOString();

    const dateToEnd = new Date(newEntry["campaign_end_date"]).getTime();
    const difference = dateToEnd - Date.now();
    const daysRemaining = Math.floor(difference / (1000 * 60 * 60 * 24));

    // Calculate the estimated_budget_days_remaining when we have an end date
    if (newEntry["campaign_end_date"] && !newEntry["estimated_budget_days_remaining"]) {
      // Look for the difference, 1000 * 60 * 60 * 24 represents the number of milliseconds in a day
      newEntry["estimated_budget_days_remaining"] = daysRemaining;
    }

    // Chek if the campaign_apply_type is not talentApply
    if (newEntry["campaign_apply_type"].trim() != "talentapply") {
      newEntry["campaign_application_delivery_type"] = "";
      newEntry["apply_email"] = "";
    }

    // Set the correct format for apply_email field
    if (newEntry["apply_email"]) {
      if (newEntry["apply_email"].includes(", ")) {
        newEntry["apply_email"] = newEntry["apply_email"].split(", ");
      }
    }
    return newEntry;
  }

  /**
   *
   */
  trimMapValues(map: Map<string, any>): Map<string, any> {
    const trimmedMap = new Map<string, any>();
    map.forEach((value, key) => {
      if (typeof value === "string") {
        trimmedMap.set(key, value.trim());
      } else {
        trimmedMap.set(key, value);
      }
    });
    return trimmedMap;
  }

  /**
   * Validates the existence and budget constraints of a campaign before allowing updates.
   *
   * @param newEntryBudget - The new budget value being proposed for the campaign.
   * @throws HttpException - If the request is made via the wrong endpoint, or if the new budget is invalid.
   */
  async ifCampaignExistValidations(
    newEntryBudget: number,
    sourceFields: Map<string, string>,
    apiCampaignId: string,
  ) {
    // Ensure that the update is not being attempted through the "create" endpoint.
    if (sourceFields.get("user_action") === "create") {
      const dataToLog = new Map<string, any>();
      dataToLog.set("apiCampaignId", apiCampaignId);
      dataToLog.set("type", "error-external-campaigns");
      dataToLog.set("entityId", apiCampaignId);
      dataToLog.set("messageStatus", "invalid_endpoint");
      await this.externalIngestion.saveLog(
        dataToLog,
        "talent-api-invalid_endpoint",
        apiCampaignId,
        sourceFields.get("user_id") ?? "80010",
      );
      throw new HttpException(
        "Please use the correct endpoint to make an update",
        HttpStatus.BAD_REQUEST,
      );
    }

    // Retrieve the current spent budget of the existing campaign.
    let actualBudget;
    try {
      actualBudget = await this.externalCampaignService.getCampaignBudgetSpent(apiCampaignId);
    } catch (error) {
      sourceFields.set("error", "Unexpected error in getCampaignBudgetSpent");
      sourceFields.set("type", "error-external-campaigns");
      sourceFields.set("entityId", apiCampaignId);
      sourceFields.set("messageStatus", "Unexpected error in getCampaignBudgetSpent");
      await this.externalIngestion.saveLog(
        sourceFields,
        `getCampaignBudgetSpent-error-${this.source}`,
        `${apiCampaignId}`,
        sourceFields.get("user_id") ?? "80010",
      );
      throw new HttpException("Unexpected error in getCampaignBudgetSpent", HttpStatus.BAD_REQUEST);
    }

    // Ensure that the new budget is not less than the amount already spent.
    if (newEntryBudget < actualBudget) {
      sourceFields.set(
        "error",
        "The campaign budget cannot be lower than the current amount spent. Please change it to a higher value.",
      );
      sourceFields.set("type", "error-external-campaigns");
      sourceFields.set("entityId", apiCampaignId);
      sourceFields.set(
        "messageStatus",
        "The campaign budget cannot be lower than the current amount spent. Please change it to a higher value.",
      );
      await this.externalIngestion.saveLog(
        sourceFields,
        `invalid_budget-error-${this.source}`,
        apiCampaignId,
        sourceFields.get("user_id") ?? "80010",
      );
      throw new HttpException(
        "The campaign budget cannot be lower than the current amount spent. Please change it to a higher value.",
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  /**
   * Calculates the notification status and updates the new entry based on certain conditions.
   *
   * @param notified - The current notification status.
   * @param lastEntry - The last entry to compare against.
   * @param newEntry - The new entry that is being evaluated and updated.
   * @param isAccountCreated - A boolean indicating if the account is already created.
   * @returns The updated newEntry with the calculated notification status.
   */
  calculateNotifyAndStatus(
    notified: string,
    lastEntry: any,
    newEntry: any,
    isAccountCreated: boolean,
    talentCampaignID: string,
  ) {
    // If there is no previous entry (i.e., lastEntry is empty):
    if (Object.keys(lastEntry).length == 0) {
      // Check if the new entry action is "not allowed" and it is defined.
      if (newEntry?.action == "not allowed" && newEntry?.action != undefined) {
        /**
         * Set the notification status to "toSend" if:
         * - The campaign's budget is below the minimum (action is "not allowed").
         * - The account already exists (isAccountCreated is true).
         * Otherwise, set the notification status to "no".
         */
        newEntry.notified = isAccountCreated ? "toSend" : "no";
      } else {
        // If the action is not "not allowed", retain the current notification status.
        newEntry.notified = notified;
      }
    } else if (newEntry.action == "not allowed") {
      // If the new entry action is "not allowed", handle it with a custom method.
      newEntry = this.handleNotAllowedAction(
        newEntry,
        isAccountCreated,
        lastEntry,
        talentCampaignID,
      );
    } else if (
      // If the action is "account", or it's a "campaign" action and the last entry was notified with "yesAccount",
      // or the last entry was notified with "no", retain the current notification status.
      newEntry.action == "account" ||
      (newEntry.action == "campaign" && lastEntry.notified == "yesAccount") ||
      lastEntry.notified == "no"
    ) {
      newEntry.notified = notified;
    } else {
      // In all other cases, retain the last entry's notification status.
      newEntry.notified = lastEntry.notified ?? "";
    }

    // Return the updated new entry.
    return newEntry;
  }

  /**
   * Handles the "not allowed" action for a campaign entry and determines its status and notification needs.
   *
   * @param newEntry - The new entry being evaluated.
   * @param isAccountCreated - A boolean indicating if the account is already created.
   * @param lastEntry - The previous entry for comparison.
   * @returns The updated newEntry with the appropriate status and notification settings.
   */
  handleNotAllowedAction(
    newEntry: any,
    isAccountCreated: boolean,
    lastEntry: any,
    talentCampaignID: string,
  ) {
    // If the campaign exists in Talent (indicated by a non-empty talentCampaignID), pause the campaign.
    if (talentCampaignID != "") {
      newEntry.status = CampaignStatus.paused;
    }

    /**
     * Rules for sending notifications when a campaign is not allowed:
     * - If the account already exists, determine if a notification should be sent.
     * - If a notification was already sent ("yesValidation") and the budget hasn't changed,
     *   maintain the existing notification status. Otherwise, set it to "toSend".
     */
    if (isAccountCreated) {
      if (
        lastEntry.notified == "yesValidation" &&
        lastEntry.campaign_budget_value == newEntry.campaign_budget_value &&
        lastEntry.campaign_budget_type == newEntry.campaign_budget_type
      ) {
        // Maintain the notification status if the budget hasn't changed and the notification was already sent.
        newEntry.notified = "yesValidation";
      } else {
        // Set the notification status to "toSend" if the budget has changed or no notification was sent.
        newEntry.notified = "toSend";
      }
    } else {
      // If the account is not created, no notification is needed.
      newEntry.notified = "no";
    }

    // Return the updated new entry.
    return newEntry;
  }

  /**
   * Get the information of a specific campaign based on the apiCampaignId,
   * even if it only exists in external_campaigns database.
   * The client_id that is a valid and active tag is also validated.
   * @param apiCampaignId
   * @returns
   */
  async getCampaign(apiCampaignId: string, userId: number, key: string): Promise<any> {
    // Get campaign info from Talent
    let talentCampaign: any = {},
      externalCampaign: any = {};

    try {
      const apiClientName = await this.getValidatedClientTagName(key);

      talentCampaign = await this.campaignsService.findOneByApiCampaignId(apiCampaignId);

      // If there's no campaign created in Talent, get it from External Campaigns
      if (talentCampaign === undefined) {
        externalCampaign = await this.externalCampaignService.findOne(
          "apiCampaignId",
          apiCampaignId,
        );
      } else {
        // We need to get info from campaign_budget to add it to the talentCampaign
        talentCampaign = await this.addBudgetCampaign(talentCampaign);
      }

      // Check if we found the campaign in Talent or in External Campaigns
      this.validateTalentOrExternalCampaign(talentCampaign, externalCampaign);

      // Prepare the result when is talentCampaign or externalCampaign
      return this.prepareResultTalentOrExternalCampaign(
        talentCampaign,
        externalCampaign,
        apiClientName,
      );
    } catch (error) {
      const dataError = new Map<string, any>();
      dataError.set("apiCampaignId", apiCampaignId);
      dataError.set("type", "error-external-campaigns");
      dataError.set("entityId", apiCampaignId);
      dataError.set("messageStatus", error);
      await this.externalIngestion.saveLog(
        dataError,
        "talent-api-get-campaign",
        apiCampaignId,
        String(userId),
      );
      throw error;
    }
  }

  /**
   * Get budgetCampaign's info and add it to the campaign
   * @param campaign
   * @returns
   */
  async addBudgetCampaign(campaign: any) {
    const url = `${process.env.URL_CAMPAIGN_BUDGET}/${campaign.id}`;
    const budgetCampaign = await this.axios.get(url);
    delete budgetCampaign.campaign_id;
    campaign = { ...campaign, ...budgetCampaign };
    return campaign;
  }

  /**
   * Validate if we have talentCampaign or externalCampaign for getCampaign()
   * @param talentCampaign
   * @param externalCampaign
   */
  validateTalentOrExternalCampaign(talentCampaign: any, externalCampaign: any) {
    if (talentCampaign === undefined && externalCampaign === undefined) {
      throw new HttpException(
        "Campaign not found in Talent nor External Campaigns",
        HttpStatus.NOT_FOUND,
      );
    }
  }

  /**
   * Prepare the result to return info about talentCampaign or externalCampaign for getCampaign()
   * @param talentCampaign
   * @param externalCampaign
   * @returns
   */
  async prepareResultTalentOrExternalCampaign(
    talentCampaign: any,
    externalCampaign: any,
    apiClientName: string,
  ) {
    if (
      talentCampaign !== undefined &&
      typeof talentCampaign === "object" &&
      Object.keys(talentCampaign).length > 0
    ) {
      // Validate if clientTag is the same at account level (only for Talent Campaigns)
      await this.validateClientNameAtAccountLevel(talentCampaign, apiClientName);

      // Return the result for Talent Campaign
      return this.resultTalentCampaign(talentCampaign);
    }
    if (
      externalCampaign !== undefined &&
      typeof externalCampaign === "object" &&
      Object.keys(externalCampaign).length > 0
    ) {
      // Return the result for External Campaign
      return this.resultExternalCampaign(externalCampaign);
    }
  }

  /**
   * Validate validatedClientTagName for getCampaign() at account level
   * @param talentCampaign
   */
  async validateClientNameAtAccountLevel(talentCampaign: Campaigns, apiClientName: string) {
    // accountId from talentCampaign.accountId, only when is from talentCampaign
    const accountId = talentCampaign.accountId;
    const account = await this.accountService.findBy("id", accountId, true);
    if (account.length === 0) {
      throw new HttpException("Account not found", HttpStatus.NOT_FOUND);
    }
    // Validate if apiClientName is the same than account's autoCampaignApiTagName
    const tagName = this.externalCampaignService.getTagNameByid(account[0]);

    if (apiClientName !== tagName) {
      throw new HttpException(
        "No results found when try to validate the client id at account level",
        HttpStatus.NOT_FOUND,
      );
    }
  }

  /**
   * Return the result when the campaing is from Talent for getCampaign()
   * @param talentCampaign
   * @returns
   */
  resultTalentCampaign(talentCampaign: any) {
    return {
      campaignName: talentCampaign.campaignName,
      dateStart: talentCampaign.dateStart,
      dateEnd: talentCampaign.dateEnd,
      budgetType: talentCampaign.budget_type,
      budgetDay: talentCampaign.budget_day,
      budgetMonth: talentCampaign.budget_month,
      spentToday: talentCampaign.spent_today,
      monthlySpent: talentCampaign.monthly_spent,
      flexibleSpent: talentCampaign.flexible_spent,
      hasPacing: talentCampaign.hasPacing,
      applyType: talentCampaign.applyType,
      dateCreated: talentCampaign.created,
      dateUpdated: talentCampaign.updated,
      active: talentCampaign.active,
      paused: talentCampaign.paused,
      removed: talentCampaign.removed,
      country: talentCampaign.country,
      autoCampaignApi: talentCampaign.autoCampaignApi,
    };
  }

  /**
   * Return the result when the campaign is from External Campaigns for getCampaign()
   * @param externalCampaign
   * @returns
   */
  resultExternalCampaign(externalCampaign: any) {
    /**
     *
     */
    const addProp = (obj: any, key: string, value: any) => {
      if (value !== undefined && value !== null && value !== "") {
        obj[key] = value;
      }
    };

    const result: any = {};

    addProp(result, "account_id", externalCampaign.accountId);
    addProp(result, "account_name", externalCampaign.accountName);
    addProp(result, "account_currency_name", externalCampaign.accountCurrencyName);
    addProp(result, "account_budget", externalCampaign.accountBudget);
    addProp(result, "account_country", externalCampaign.accountCountry);
    addProp(result, "company_id", externalCampaign.companyId);
    addProp(result, "company_name", externalCampaign.companyName);
    addProp(result, "campaign_id", externalCampaign.campaignId);
    addProp(result, "campaign_name", externalCampaign.campaignName);
    addProp(result, "campaign_status", externalCampaign.campaignStatus);
    addProp(result, "campaign_country", externalCampaign.campaignCountry);
    addProp(result, "campaign_budget_type", externalCampaign.campaignBudgetType);
    addProp(result, "campaign_budget_value", externalCampaign.campaignBudgetValue);
    addProp(
      result,
      "estimated_budget_days_remaining",
      externalCampaign.estimatedBudgetDaysRemaining,
    );
    addProp(result, "campaign_start_date", externalCampaign.campaignStartDate);
    addProp(result, "campaign_end_date", externalCampaign.campaignEndDate);
    addProp(result, "campaign_delivery", externalCampaign.campaignDelivery);
    addProp(result, "campaign_cpc", externalCampaign.campaignCpc);
    addProp(result, "source_jobs", externalCampaign.xmlFeedLink);
    addProp(result, "estimated_num_jobs", externalCampaign.xmlNumJobs);
    addProp(result, "campaign_apply_type", externalCampaign.campaignApplyType);
    addProp(
      result,
      "campaign_application_delivery_type",
      externalCampaign.campaignApplicationDeliveryType,
    );
    addProp(result, "campaign_target_cost", externalCampaign.campaignTargetCost);
    addProp(result, "campaign_conversion_type", externalCampaign.campaignConversionType);
    addProp(result, "apply_email", externalCampaign.applyEmail);

    result.campaign_status_on_talent =
      "This campaign has been registered but is still pending creation. Please contact Talent.com's account management team for more information.";

    return result;
  }

  /**
   * Get the tagName through the x-api-key
   * @param apiKey
   * @returns
   */
  async getValidatedClientTagName(apiKey: string) {
    // Validate the xApiKey
    if (!apiKey || apiKey === undefined || apiKey === "") {
      throw new HttpException("x-api-key missing or not valid", HttpStatus.UNAUTHORIZED);
    }

    // Make the decrypt for the clientId
    const key = privacy.legacy.flo(`${apiKey}`);

    // Validate the decrypted key
    if (typeof key !== "string" || key.trim() === "") {
      throw new HttpException("x-api-key missing or not valid", HttpStatus.UNAUTHORIZED);
    }

    // Extract the API client name from the decrypted key and retrieve the corresponding talent key
    const apiClientName = key.split("-");
    if (apiClientName[0] !== "auto") {
      throw new HttpException("x-api-key missing or not valid", HttpStatus.UNAUTHORIZED);
    }
    const clientName = apiClientName.slice(1).join("-");

    // Check if the apiClientName is empty
    if (clientName === "") {
      throw new HttpException("x-api-key missing or not valid", HttpStatus.UNAUTHORIZED);
    }

    // Get the tag info for status active and type 'Auto campaign api' (typeId = 7)
    const result = await this.tagsService.getTagByName(clientName, 7);
    if (result?.length === 1 && result[0]?.name === clientName) {
      // Setting the clientName
      //this.setApiClientName(clientName);
      return clientName;
    } else {
      return "";
    }
  }

  /**
   * Retrieves account information based on the account name and currency. Throws an error if the parameters are invalid.
   * It checks for a matching talent or non-talent account and returns relevant fields for the user.
   *
   * @param accountName - The name of the account.
   * @param accountCurrency - The currency of the account.
   * @returns A Promise resolving to an object containing the relevant account information.
   * @throws HttpException if the account name or currency is invalid or if the account is not found.
   */
  async getAccount(
    accountName: string,
    accountCurrency: string,
    userId: number,
    key: string,
    clientId?: string,
  ): Promise<any> {
    const apiClientName = this.getValidatedClientTagName(key);
    // Validate the account name and currency inputs
    if (!accountName || accountName === "" || !accountCurrency || accountCurrency === "") {
      throw new HttpException(
        "apiCampaignId or accountCurrency not valid or empty.",
        HttpStatus.BAD_REQUEST,
      );
    }

    // Initialize the talentAccount object and the isNotTalent flag
    let talentAccount: any = {};
    let isNotTalent: boolean = false;

    // Creating a "feedcode" based on the provided account name and currency
    const accountWords: string[] = accountName.split(" "); // Split account name into words
    const accountNameRefactor: string = accountWords.join("-").toLocaleLowerCase(); // Join words into a lowercased string with hyphens
    const feedcode = `${apiClientName}-${accountNameRefactor}-${accountCurrency}`; // Construct the feedcode using the API client name, refactored account name, and currency

    try {
      // Fetch the talent account from the account service using the generated feedcode
      talentAccount = await this.accountService.findBy("feedcode", feedcode, true);
      talentAccount = talentAccount[0]; // Get the first result

      // If version 2 is used, check for non-talent accounts if no talent account is found
      if (this.version == 2) {
        if (!talentAccount || talentAccount === undefined || talentAccount.length < 1) {
          // Fetch the non-talent account from the external campaign service
          talentAccount = await this.externalCampaignService.findOne("talentFeedcode", feedcode);
          isNotTalent = true; // Mark the account as non-talent
        }
      }

      // If no account is found, throw an exception
      if (!talentAccount || talentAccount === undefined || talentAccount.length < 1) {
        throw new HttpException(
          "This account does not exist or has not been created yet. Please contact your Talent.com Account Management Team for more details.",
          HttpStatus.NOT_FOUND,
        );
      }

      // Define what account fields will be shown to the user using the showAccountFields method
      const response = this.showAccountFields(talentAccount, isNotTalent);

      // Return the account fields to the user
      return response;
    } catch (error) {
      const dataError = new Map<string, any>();
      dataError.set("clientId", clientId);
      dataError.set("accountName", accountName);
      dataError.set("accountCurrency", accountCurrency);
      dataError.set("feedcode", feedcode);
      dataError.set("type", "error-external-campaigns");
      dataError.set("entityId", feedcode);
      dataError.set("messageStatus", error);
      dataError.set("error", error);
      await this.externalIngestion.saveLog(
        dataError,
        "talent-api-get-account-error",
        feedcode,
        String(userId),
      );
      throw error;
    }
  }

  /**
   * Retrieves and returns specific fields from a talent account based on the account type (talent or non-talent).
   *
   * @param talentAccount - The object representing the talent account.
   * @param isNotTalent - A boolean indicating if the account is not a talent account. If true, it's a non-talent account.
   * @returns An object containing country, status, dateCreated, accountCurrency, and dateEdited.
   */
  showAccountFields(talentAccount: any, isNotTalent: boolean) {
    // Declare variables for the account fields
    let country: string;
    let status: string;
    let dateCreated: string;
    let accountCurrency: string;
    let dateEdited: string;

    // If the account is a talent account, retrieve the fields accordingly
    if (!isNotTalent) {
      country = talentAccount.country; // Get the country from the talent account
      status = talentAccount.status; // Get the account status
      dateCreated = talentAccount.created; // Get the creation date
      accountCurrency = talentAccount.accountCurrency; // Get the account's currency
      dateEdited = talentAccount.updated; // Get the last edited date
    } else {
      // If the account is not a talent account, retrieve the fields differently
      country = talentAccount.accountCountry; // Get the country from the non-talent account
      status = talentAccount.campaignStatus; // Get the campaign status
      dateCreated = talentAccount.date; // Get the date (which could represent creation or another date)
      accountCurrency = talentAccount.accountCurrencyName; // Get the name of the account's currency
      dateEdited = talentAccount.date; // Get the date again (this could be the same or different from creation date)
    }

    // Return an object containing the relevant account information
    return {
      country,
      status,
      dateCreated,
      accountCurrency,
      dateEdited,
    };
  }

  /**
   * Updates an existing campaign by setting fields and preprocessing the data.
   * This method performs the following steps:
   * 1. Retrieves the current campaign data from the database.
   * 2. Converts the keys of the current campaign data to snake_case.
   * 3. Assigns new values to the existing campaign data.
   * 4. Sets the updated fields for further processing.
   */
  async updateV2(apiCampaignId: string, sourceFields: Map<string, string>) {
    // Retrieve the latest campaign data from the database
    const actualCampaign = await this.externalCampaignService.findOne(
      "apiCampaignId",
      apiCampaignId,
    );

    // Convert the keys of the current campaign data to snake_case
    const lastEntry = this.convertKeysToSnakeCase(actualCampaign ?? {});

    // Assign new values to the existing campaign data
    const newEntry = this.asingNewValuesToOldRecord(lastEntry, Object.fromEntries(sourceFields));

    // Create a map of the updated campaign data
    const finalFields = new Map(Object.entries(newEntry));

    // Set the updated fields for further processing
    return finalFields;
  }

  /**
   * Converts the keys of an object to snake_case.
   *
   * This method takes an object with camelCase keys and returns a new object
   * where all keys are converted to snake_case.
   *
   * @param obj - The object whose keys are to be converted to snake_case.
   * @returns A new object with snake_case keys.
   */
  convertKeysToSnakeCase<T extends object>(obj: T): { [key: string]: any } {
    return Object.keys(obj).reduce(
      (acc, key) => {
        // Convert camelCase key to snake_case
        const newKey = key.replace(/([A-Z])/g, "_$1").toLowerCase();
        // Assign the value to the new key in the accumulator object
        acc[newKey] = (obj as any)[key];
        return acc;
      },
      // Initialize the accumulator object
      {} as { [key: string]: any },
    );
  }

  /**
   * Assigns new values from the update DTO to the last entry record, ensuring all fields are updated appropriately.
   *
   * This method merges the last entry object with the update fields, then explicitly assigns values to specific fields
   * to create a new record with the updated values.
   *
   * @param lastEntry - The last entry record to be updated.
   * @param fields - The update fields to be assigned to the last entry.
   * @returns A new record with the updated values.
   */
  asingNewValuesToOldRecord(lastEntry: any, fields: UpdateCampaignTalentApiDto) {
    // Merge the last entry with the update fields
    const updatedFields = { ...lastEntry, ...fields };

    // Return a new object with updated values for specific fields
    return {
      account_id: updatedFields.account_id,
      account_name: updatedFields.account_name,
      account_currency_name: updatedFields.account_currency_name,
      account_budget: updatedFields.account_budget,
      account_country: updatedFields.account_country,
      company_id: updatedFields.company_id,
      company_name: updatedFields.company_name,
      campaign_id: updatedFields.campaign_id,
      campaign_name: updatedFields.campaign_name,
      campaign_status: updatedFields.campaign_status,
      campaign_country: updatedFields.campaign_country,
      campaign_budget_type: updatedFields.campaign_budget_type,
      campaign_budget_value: updatedFields.campaign_budget_value,
      estimated_budget_days_remaining: updatedFields.estimated_budget_days_remaining,
      campaign_start_date: updatedFields.campaign_start_date,
      campaign_end_date: updatedFields.campaign_end_date,
      campaign_delivery: updatedFields.campaign_delivery,
      // Use `campaign_ppc` if available, otherwise use `campaign_cpc`
      campaign_ppc: updatedFields.campaign_ppc ?? updatedFields.campaign_cpc,
      campaign_target_cost: updatedFields.campaign_target_cost,
      // Use `source_jobs` if available, otherwise use `xml_feed_link`
      source_jobs: updatedFields.source_jobs ?? updatedFields.xml_feed_link,
      // Use `estimated_num_jobs` if available, otherwise use `xml_num_jobs`
      estimated_num_jobs: updatedFields.estimated_num_jobs ?? updatedFields.xml_num_jobs,
      // Use `apply_type` if available, otherwise use `campaign_apply_type`
      apply_type: updatedFields.apply_type ?? updatedFields.campaign_apply_type,
      // Use `application_delivery_type` if available, otherwise use `campaign_application_delivery_type`
      application_delivery_type:
        updatedFields.application_delivery_type ?? updatedFields.campaign_application_delivery_type,
      apply_email: updatedFields.apply_email,
    };
  }

  /**
   * Retrieves campaign information based on the provided API campaign ID.
   *
   * @param apiCampaignId - The unique identifier of the campaign in the API.
   * @returns An object containing detailed information about the campaign, such as budget, status, and dates.
   * @throws HttpException if the apiCampaignId is invalid, empty, or if no campaign is found with the provided ID.
   */
  async getCampaignV1(apiCampaignId: string, userId: number, clientId: string) {
    try {
      // Fetch the campaign details from the service using the API campaign ID
      const campaign = await this.campaignsService.findOneByApiCampaignId(apiCampaignId, [
        "campaignsBudget",
      ]);

      // If no campaign is found, throw an exception
      if (!campaign || Object.keys(campaign).length < 1) {
        throw new HttpException("No results found", HttpStatus.BAD_REQUEST);
      }

      // Map the campaign details to the response object
      const result = {
        campaignName: campaign?.campaignName,
        dateStart: campaign?.dateStart,
        dateEnd: campaign?.dateEnd,
        budgetType: campaign?.campaignsBudget?.budgetType,
        budgetDay: campaign?.campaignsBudget?.budgetDay,
        budgetMonth: campaign?.campaignsBudget?.budgetMonth,
        spentToday: campaign?.campaignsBudget?.spentToday,
        monthlySpent: campaign?.campaignsBudget?.monthlySpent,
        flexibleSpent: campaign?.campaignsBudget?.flexibleSpent,
        hasPacing: campaign?.campaignsBudget?.hasPacing,
        applyType: campaign?.applyType,
        dateCreated: campaign?.created,
        dateUpdated: campaign?.updated,
        active: campaign?.active,
        paused: campaign?.paused,
        removed: campaign?.removed,
        cpaTarget: campaign?.conversionTargetCost,
        country: campaign?.country,
        autoCampaignApi: campaign?.autoCampaignApi,
      };

      return result; // Return the campaign details
    } catch (error) {
      const dataError = new Map<string, any>();
      dataError.set("apiCampaignId", apiCampaignId);
      dataError.set("clientId", clientId);
      dataError.set("error", error);
      dataError.set("type", "error-external-campaigns");
      dataError.set("entityId", apiCampaignId);
      dataError.set("messageStatus", error);
      await this.externalIngestion.saveLog(
        dataError,
        "talent-api-get-campaign-error",
        apiCampaignId,
        String(userId),
      );
      throw error;
    }
  }

  /**
   *
   * @param data
   * @param strict
   */
  setFields(data: Map<string, string>, strict: boolean = true): Map<any, any> {
    const sourceFields = new Map();
    // Defining the properties at class level according to what we got from the job
    for (const [key, value] of data) {
      if (strict && !this.sourceKeys?.includes(key)) {
        continue;
      }
      sourceFields?.set(key, value);
    }

    return sourceFields;
  }

  /**
   *
   */
  async processIngestion(sourceFields: Map<string, string>, key: string, version?: number) {
    try {
      // this.validate(sourceFields);

      const { newEntry, lastEntry, talentCampaignID, isFullyAutoCampaign } = await this.preProcess(
        sourceFields,
        key,
        version,
      );
      if (typeof newEntry === "object" && newEntry !== null) {
        await this.externalIngestion.processExternalCampaign(
          sourceFields,
          newEntry.api_campaign_id,
          newEntry.source,
          this.compareKeys,
          newEntry,
          lastEntry,
          talentCampaignID,
          isFullyAutoCampaign,
        );
      }
    } catch (error: any) {
      Logger.error(new Error(`[Talent-api] [processIngestion] error: ${error.message}`));
      throw error;
    }
  }
}
