import {
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Query,
  UseInterceptors,
  ValidationPipe,
} from "@nestjs/common";
import { QuickbooksService } from "../services/quickbooks.service";
import {
  Customer,
  FormattedCreditMemo,
  FormattedPayment,
} from "../../common/interfaces/quickbooks.interface";
import { CreateQuickBooksCustomerDto } from "../dto/create-quickbooks-customer.dto";
import { AccountIdDto } from "../dto/accountId.dto";
import { User } from "../../auth/decorators/user.decorator";
import { UserAuthDto } from "../../common/dtos/user-auth.dto";
import { RequirePermissions } from "../../auth/decorators/permissions.decorator";
import { Permission } from "../../auth/resources/permission.enum";
import { BillingPermissionService } from "../services/billing-permission.service";
import { BillingActionInterceptor } from "../../common/interceptors/billing-actions.interceptor";

/**
 * Controller for handling QuickBooks-related operations.
 *
 * @class
 */
@Controller({
  path: "quickbooks",
  version: "1",
})
@UseInterceptors(BillingActionInterceptor)
export class QuickbooksController {
  /**
   * Creates an instance of QuickbooksController.
   *
   * @param {QuickbookService} quickbooksService - The service used for QuickBooks  operations.
   * @param billingPermissionService The billing permission service to handle billing permissions.
   */
  constructor(
    private readonly quickbooksService: QuickbooksService,
    private readonly billingPermissionService: BillingPermissionService,
  ) {}

  /**
   * Quickbooks endpoint to get a formatted credit memo by its id in the provided company.
   * @param {number} id - The id of the credit memo.
   * @param {string} company - The company name.
   * @returns {Promise<string>} - The pdf in base64 credit memo.
   */
  @Get("credit-memo/:id")
  @HttpCode(HttpStatus.OK)
  async getCreditMemoById(
    @User() user: UserAuthDto,
    @Param("id") id: number,
    @Query("company") company: string,
  ): Promise<string> {
    await this.billingPermissionService.checkPrivilagesByUserId();
    return await this.quickbooksService.getCompanyFormattedDocumentById(id, company, "creditMemo");
  }

  /**
   * Quickbooks endpoint to get a formatted payment by its id in the provided company.
   * @param {number} id - The id of the payment.
   * @param {string} company - The company name.
   * @returns {Promise<string>} - The pdf in base64 payment
   */
  @Get("payment/:id")
  @HttpCode(HttpStatus.OK)
  async getPaymentById(
    @User() user: UserAuthDto,
    @Param("id") id: number,
    @Query("company") company: string,
  ): Promise<string> {
    await this.billingPermissionService.checkPrivilagesByUserId();
    return await this.quickbooksService.getCompanyFormattedDocumentById(id, company, "payment");
  }

  /**
   * Creates a new QuickBooks customer.
   *
   * @param {CreateQuickBooksCustomerDto} data - The data needed to create a QuickBooks customer.
   * @returns {Promise<Customer>} The created QuickBooks customer.
   */
  @Post("customer")
  @RequirePermissions(Permission.operationsQuickbooks)
  @HttpCode(HttpStatus.CREATED)
  async createClientQbo(
    @User() user: UserAuthDto,
    @Body() data: CreateQuickBooksCustomerDto,
    @Query(new ValidationPipe()) filters: AccountIdDto,
  ): Promise<void> {
    return await this.quickbooksService.createCompanyCustomer(data);
  }
}
