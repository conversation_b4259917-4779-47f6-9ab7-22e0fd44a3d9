import { HttpException, HttpStatus } from "@nestjs/common";
import { AccountsOwners } from "../../accounts/entities/account-owner.entity";
import { StatusOptions } from "../interfaces/types";
import * as crypto from "crypto";
import * as privacy from "libs/privacy/src";

/**
 *
 * @param str
 * @returns
 */
export function decodeHtmlEntities(str: string): string {
  return str.replace(/&#(\d+);/g, function (match, dec) {
    return String.fromCharCode(dec);
  });
}

/**
 *
 * @param str
 * @returns
 */
export function removeAccents(str: string): string {
  str = str.replace(/[\u00C0-\u00C5\u00E0-\u00E5\u0100-\u0105\u0180-\u0185]/g, "A");
  str = str.replace(/[\u00C8-\u00CB\u00E8-\u00EB\u0112-\u0115]/g, "E");
  str = str.replace(/[\u00CC-\u00CF\u00EC-\u00EF\u0128-\u012F]/g, "I");
  str = str.replace(/[\u00D2-\u00D6\u00F2-\u00F6\u014C-\u0151]/g, "O");
  str = str.replace(/[\u00D9-\u00DC\u00F9-\u00FC\u0168-\u0171]/g, "U");
  str = str.replace(/[\u00D1\u00F1]/g, "N");
  str = str.replace(/[\u00C7\u00E7]/g, "C");
  return str;
}

/**
 * Returns possible states according to the status received.
 * @param string $status Current campaign status
 * @param string $type Type to decide which data return ['post, 'enterpost']
 * @return false|string
 */
export function campaignStatusOptions(status: string, type: string = ""): StatusOptions | false {
  const output: StatusOptions = {
    validating: {
      validated: "Validate",
      removed: "Remove",
    },
    active: {
      paused: "Pause",
      removed: "Remove",
    },
    limited_by_monthly_budget: {
      paused: "Pause",
      removed: "Remove",
    },
    limited_by_daily_budget: {
      paused: "Pause",
      removed: "Remove",
    },
    pending: {
      paused: "Pause",
      removed: "Remove",
    },
    ended: {
      paused: "Pause",
      removed: "Remove",
    },
    paused: {
      active: "UnPause",
      removed: "Remove",
    },
    limited_by_monthly_pacing: {
      paused: "Pause",
      removed: "Remove",
    },
    limited_by_daily_pacing: {
      paused: "Pause",
      removed: "Remove",
    },
  };

  if (type === "enterpost") {
    delete output.pending.paused;
    output["draft"] = { removed: "remove" };
  }

  return output;
}

/**
 * Rounds a number to the specified number of decimal places.
 * @param num The number to round.
 * @param decimals The number of decimals to round.
 * @returns The rounded number.
 */
export function roundNumber(num: number, decimals: number): number {
  const factor = Math.pow(10, decimals);
  return Math.round(num * factor) / factor;
}

/**
 * Sets the given date to the first day of the month.
 * @param date The date to update to the first day of the month.
 * @returns The date set to the first day of the month.
 */
export function setFirstDayOfMonth(date: Date): Date {
  const firstDayOfMonth = new Date(date);
  firstDayOfMonth.setDate(1);
  return firstDayOfMonth;
}

/**
 * Checks if the provided string is a valid JSON string.
 *
 * @param {any} str - The string to validate.
 * @returns {boolean} - True if the string is a valid JSON string, false otherwise.
 */
export function isValidJSONString(str: any): boolean {
  try {
    const parsed = JSON.parse(str);
    return parsed && (typeof parsed === 'object' || Array.isArray(parsed));
  } catch (error) {
    return false;
  }
}

/**
 * Generates a default password using a secure hashing algorithm.
 * @returns {string} A SHA-256 hashed unique password.
 */
export function generateDefaultPassword(): string {
  // Generate a secure random value
  const uniqueId = crypto.randomBytes(16).toString("hex");

  // Hash the random value with SHA-256
  const defaultPass = crypto.createHash("sha256").update(uniqueId).digest("hex");

  return defaultPass;
}

/**
 * Converts a Date object to a MySQL datetime string format (YYYY-MM-DD HH:mm:ss).
 * @param date - The Date object to be converted.
 * @returns A string representing the date in MySQL datetime format.
 */
export function formatDateToMySQL(date: Date): string {
  /**
   * Helper function to add leading zero if needed
   */
  const pad = (n: number) => (n < 10 ? `0${n}` : n);

  const year = date.getFullYear();
  const month = pad(date.getMonth() + 1); // Months are zero-indexed, so add 1
  const day = pad(date.getDate());
  const hours = pad(date.getHours());
  const minutes = pad(date.getMinutes());
  const seconds = pad(date.getSeconds());

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

/**
 *
 * @param talentCompany Load the billing banking data by company.
 * @returns
 */
export function billingBankingData(talentCompany: string): any {
  const bankingData = {
    "NEUVOO SARL": {
      name: "Talent.com Europe Sàrl",
      address: [
        "Pl. de la Gare 12",
        "1003 Lausanne, Switzerland",
        "<EMAIL>",
        "UID : CHE-163.383.061",
      ],
      "*": {
        RUB: [
          "Credit Suisse",
          "Talent.com Europe Sàrl",
          "IBAN CH08 0483 ************** 2",
          "Compte: 0517 - 1463929 - 71",
          "BIC: CRESCHZZ80A",
        ],
        EUR: [
          "Credit Suisse",
          "Talent.com Europe Sàrl",
          "IBAN EUR: CH62 0483 ************** 0",
          "Compte : 0517 - 1463929 - 72",
          "BIC: CRESCHZZ80A",
        ],
        CHF: [
          "Credit Suisse",
          "Talent.com Europe Sàrl",
          "IBAN CHF: CH96 0483 ************** 0",
          "Compte : 0517 - 1463929 - 71",
          "BIC: CRESCHZZ80A",
        ],
        GBP: [
          "Credit Suisse",
          "Talent.com Europe Sàrl",
          "IBAN GBP: CH35 0483 ************** 1",
          "Compte : 0517 - 1463929 - 72 - 1",
          "BIC: CRESCHZZ80A",
        ],
      },
      bankNotice: ["Steuerschuldnerschaft des Leistungsempfängers"],
    },
    "NEUVOO INC": {
      name: "TALENT.COM INC",
      address: [
        "5800 Rue Saint Denis, Suite 604",
        "Montreal, QC, Canada, H2S 3L5",
        "<EMAIL>",
        "GST No.: 84603 3108",
        "QST No.: 12186 75391",
      ],
      "*": {
        CAD: [
          "Bank of Montreal",
          "119 rue Saint-Jacques, Montreal, QC, H2Y 1L6",
          "Financial Institution: 001",
          "Transit Number: 00011",
          "Account Number: 1899429",
          "Beneficiary Customer: ***********",
          "S.W.I.F.T. BIC Code: BOFMCAM2",
        ],
        USD: [
          "Bank of Montreal",
          "119 rue Saint-Jacques, Montreal, QC, H2Y 1L6",
          "Financial Institution: 001",
          "Transit Number: 00011",
          "Account Number: 4742098",
          "Beneficiary Customer: ***********",
          "S.W.I.F.T. BIC Code: BOFMCAM2",
        ],
        EUR: [
          "Beneficiary's Bank BMO Bank N.A.",
          "TALENT.COM INC",
          "Account Number: 30089-4254868",
          "S.W.I.F.T. BIC Code: HATRUS44GTM",
          "Intermediary Bank: Deutsche Bank, Frankfurt",
          "Account Number: ************",
          "S.W.I.F.T. BIC Code: DEUTDEFF",
        ],
        GBP: [
          "Beneficiary's Bank BMO Bank N.A.",
          "TALENT.COM INC",
          "Account Number: 77755-4254868",
          "S.W.I.F.T. BIC Code: HATRUS44GTM",
          "Intermediary Bank: Royal Bank of Scotland, London",
          "Account Number: *************",
          "S.W.I.F.T. BIC Code: NWBKGB2L",
        ],
      },
      US: {
        USD: [
          "BMO Harris Bank",
          "111 W. Monroe St., Chicago, IL, 60603",
          "Account Number: 4254868",
          "Routing Number (ABA): *********",
          "SWIFT Code: HATRUS44",
        ],
      },
      lockboxInfo: {
        us: {
          USD: {
            en: [
              "<b>Directing lockbox items: </b>",
              "Talent.com Inc. - USD",
              "P.O. Box 95729",
              "Chicago, IL 60694-5729",
            ],
            es: [
              "<b>Envíos a la caja de seguridad:</b>",
              "Talent.com Inc. – USD",
              "Apartado de correos 95729",
              "Chicago, IL  60694-5729",
            ],
            fr: [
              "<b>Pour l'envoi d'effets au coffre:</b>",
              "Talent.com USA Inc. – USD",
              "Boîte postale 95729",
              "Chicago, IL  60694-5729",
            ],
          },
          CAD: {
            en: [
              "LBX ID: M12610C",
              "Talent.com Inc.",
              "PO Box 12522",
              "c/o M12610",
              "Succursale Centre-Ville",
              "Montreal, QC  H3C 6R1",
            ],
            es: [
              "<b>ID de la caja de seguridad:</b> M12610C",
              "Talent.com Inc.",
              "Apartado de correos 12522",
              "a/a M12610",
              "Succursale Centre-Ville",
              "Montreal, QC  H3C 6R1",
            ],
            fr: [
              "<b>Identifiant coffre-fort:</b> M12610C",
              "Talent.com Inc.",
              "Boîte postale 12522",
              "c/o M12610",
              "Succursale Centre-Ville",
              "Montreal, QC  H3C 6R1",
            ],
          },
        },
        ca: {
          CAD: {
            en: [
              "LBX ID: M12610C",
              "Talent.com Inc.",
              "PO Box 12522",
              "c/o M12610",
              "Succursale Centre-Ville",
              "Montreal, QC  H3C 6R1",
            ],
            es: [
              "<b>ID de la caja de seguridad:</b> M12610C",
              "Talent.com Inc.",
              "Apartado de correos 12522",
              "a/a M12610",
              "Succursale Centre-Ville",
              "Montreal, QC  H3C 6R1",
            ],
            fr: [
              "<b>Identifiant coffre-fort:</b> M12610C",
              "Talent.com Inc.",
              "Boîte postale 12522",
              "c/o M12610",
              "Succursale Centre-Ville",
              "Montreal, QC  H3C 6R1",
            ],
          },
          USD: {
            en: [
              "LBX ID: M12610U",
              "Talent.com Inc.",
              "PO Box 12522",
              "c/o M12610",
              "Succursale Centre-Ville",
              "Montreal, QC  H3C 6R1",
            ],
            es: [
              "<b>ID de la caja de seguridad:</b> M12610U",
              "Talent.com Inc.",
              "Apartado de correos 12522",
              "a/a M12610",
              "Succursale Centre-Ville",
              "Montreal, QC  H3C 6R1",
            ],
            fr: [
              "<b>Identifiant coffre-fort:</b> M12610U",
              "Talent.com Inc.",
              "Boîte postale 12522",
              "c/o M12610",
              "Succursale Centre-Ville",
              "Montreal, QC  H3C 6R1",
            ],
          },
        },
        DEFAULT: {
          CAD: {
            en: [
              "LBX ID: M12610C",
              "Talent.com Inc.",
              "PO Box 12522",
              "c/o M12610",
              "Succursale Centre-Ville",
              "Montreal, QC  H3C 6R1",
            ],
            es: [
              "<b>ID de la caja de seguridad:</b> M12610C",
              "Talent.com Inc.",
              "Apartado de correos 12522",
              "a/a M12610",
              "Succursale Centre-Ville",
              "Montreal, QC  H3C 6R1",
            ],
            fr: [
              "<b>Identifiant coffre-fort:</b> M12610C",
              "Talent.com Inc.",
              "Boîte postale 12522",
              "c/o M12610",
              "Succursale Centre-Ville",
              "Montreal, QC  H3C 6R1",
            ],
          },
          USD: {
            en: [
              "LBX ID: M12610U",
              "Talent.com Inc.",
              "PO Box 12522",
              "c/o M12610",
              "Succursale Centre-Ville",
              "Montreal, QC  H3C 6R1",
            ],
            es: [
              "<b>ID de la caja de seguridad:</b> M12610U",
              "Talent.com Inc.",
              "Apartado de correos 12522",
              "a/a M12610",
              "Succursale Centre-Ville",
              "Montreal, QC  H3C 6R1",
            ],
            fr: [
              "<b>Identifiant coffre-fort:</b> M12610U",
              "Talent.com Inc.",
              "Boîte postale 12522",
              "c/o M12610",
              "Succursale Centre-Ville",
              "Montreal, QC  H3C 6R1",
            ],
          },
        },
      },
    },
    "NEUVOO FR": {
      name: "Talent.com France SARL",
      address: [
        "124 rue Réaumur",
        "75002, Paris 2",
        "France",
        "<EMAIL>",
        "https://fr.talent.com/",
      ],
      "*": {
        EUR: [
          "Banque Populaire",
          "Talent.com France SARL",
          "IBAN : FR 76 1350 7000 2031 3347 0218 725",
          "RIB : 13507 - 00020 - 31334702187 - 25",
          "BIC : CCBPFRPPLIL",
          "Veuillez noter que nous n’acceptons pas les paiements par chèque mais uniquement par virement. Merci de votre compréhension.",
        ],
      },
      legal: [
        "SARL au capital social de 10,000.00 € - N° RCS Lille Metropole B 822 839 924 - N° Siret 822*********** - N° de TVA : FR 55 822 839 924",
        "Pénalités de retard (taux annuel) : 10,05 %",
        "Escompte pour paiement anticipé (taux mensuel) : 1,5 %",
        "Indemnité forfaitaire pour frais de recouvrement en cas de retard de paiement : 40,00 €",
      ],
    },
    "NEUVOO USA": {
      name: "Talent.com USA Inc",
      address: [
        "2915 Ogletown Road, Newark",
        "New Castle, DE 19713 USA",
        "<EMAIL>",
      ],
      "*": {
        USD: [
          "BMO Harris Bank",
          "111 W. Monroe St., Chicago, IL, 60603",
          "Account Number: 3552304",
          "Routing Number (ABA): *********",
          "SWIFT Code: HATRUS44",
        ],
      },
      lockboxInfo: {
        us: {
          USD: {
            en: [
              "Directing lockbox items:",
              "Talent.com USA Inc. - USD",
              "P.O. Box 95538",
              "Chicago, IL  60694-5538",
            ],
            es: [
              "<b>ID de la caja de seguridad:</b> M12610C",
              "Talent.com USA Inc. - USD",
              "Apartado de correos 95538",
              "Chicago, IL  60694-5538",
            ],
            fr: [
              "<b>Pour l'envoi d'effets au coffre:</b>",
              "Talent.com USA Inc. – USD",
              "Boîte postale 95538",
              "Chicago, IL  60694-5538",
            ],
          },
        },
      },
    },
    "GESTION NEUVOO": {
      name: "Talent.com Brasil Pesquisa de Empregos LTDA",
      address: [
        "CNPJ: 40.798.404/0001-97",
        "AV Brig Faria Lima 4055",
        "SALA 05-108 04.538-133",
        "ITAIM BIBI",
        "SAO PAULO",
        "<EMAIL>",
      ],
      "*": {
        BRL: ["ITAU", "Agência: 0445", "Conta: 99865-1", "CNPJ: 40.798.404/0001-97"],
      },
    },
    "NEUVOO UK": {
      name: "TALENT.COM JOBS LIMITED",
      address: [
        "5 New Street Square",
        "London EC4A 3TW",
        "United Kingdom",
        "<EMAIL>",
        "VAT number: *********",
      ],
      "*": {
        GBP: [
          "JPMorgan  Chase  Bank, N.A. London",
          "TALENT.COM  JOBS  LIMITED",
          "Bank SWIFT: CHASGB2L",
          "Sort Code: 60-92-42",
          "Account: ********",
          "IBAN: **********************",
        ],
        EUR: [
          "JPMorgan  Chase  Bank, N.A. London",
          "TALENT.COM  JOBS  LIMITED",
          "Bank SWIFT: CHASGB2L",
          "Account: ********",
          "IBAN: **********************",
        ],
      },
    },
  };

  if (bankingData[talentCompany as keyof typeof bankingData] === undefined) {
    return [];
  }

  return bankingData[talentCompany as keyof typeof bankingData];
}

/**
 *
 * @param paymentTerm Load the payment terms for the billing.
 * @returns
 */
export function billingPaymentTerms(
  paymentTerm: number,
): { [key: string]: number | string } | undefined {
  const paymentTermsData = {
    30: {
      0: 30,
      1: "thirty_days",
      "NEUVOO INC": 3,
      "NEUVOO FR": 3,
      "NEUVOO USA": 3,
      "NEUVOO UK": 3,
      "NEUVOO SARL": 9,
      "NEUVOO BRA": 3,
      "GESTION NEUVOO": 4,
    },
    45: {
      0: 45,
      1: "fourty_five_days",
      "NEUVOO INC": 6,
      "NEUVOO FR": 6,
      "NEUVOO USA": 6,
      "NEUVOO UK": 6,
      "NEUVOO SARL": 15,
      "GESTION NEUVOO": 7,
    },
    60: {
      0: 60,
      1: "sixty_days",
      "NEUVOO INC": 4,
      "NEUVOO FR": 4,
      "NEUVOO USA": 4,
      "NEUVOO UK": 4,
      "NEUVOO SARL": 14,
      "NEUVOO BRA": 4,
      "GESTION NEUVOO": 5,
    },
    90: {
      0: 90,
      1: "ninety_days",
      "NEUVOO INC": 9,
      "NEUVOO FR": 7,
      "NEUVOO USA": 7,
      "NEUVOO UK": 7,
      "NEUVOO SARL": 18,
      "NEUVOO BRA": 6,
      "GESTION NEUVOO": 8,
    },
  };

  if (paymentTermsData[paymentTerm as keyof typeof paymentTermsData] === undefined) {
    return undefined;
  }

  return paymentTermsData[paymentTerm as keyof typeof paymentTermsData];
}

/**
 *
 * @param talentCompany Load the billing currency by company.
 * @returns
 */
export function billingCurrencyByCompany(talentCompany: string): string {
  const defaultCompanyCurrency: { [key: string]: string } = {
    "NEUVOO SARL": "EUR",
    "NEUVOO INC": "USD",
    "NEUVOO FR": "EUR",
    "NEUVOO USA": "USD",
    "NEUVOO BRA": "BRL",
    "GESTION NEUVOO": "BRL",
    "NEUVOO UK": "GBP",
  };

  const companyCurrency = defaultCompanyCurrency[talentCompany];

  if (companyCurrency) {
    return companyCurrency;
  }

  throw new Error(`Company ${talentCompany} not found.`);
}

/**
 *
 * @param segment Load the billing department reference by segment.
 * @returns
 */
export function billingDeparmentRefBysegment(segment: string): any {
  const locationData: { [key: string]: { [key: string]: number } } = {
    Agency: {
      "NEUVOO INC": 16,
      "NEUVOO FR": 7,
      "NEUVOO USA": 7,
      "NEUVOO UK": 2,
      "NEUVOO SARL": 9,
      "NEUVOO BRA": 10,
      "GESTION NEUVOO": 2,
    },
    Enterprise: {
      "NEUVOO INC": 17,
      "NEUVOO FR": 8,
      "NEUVOO USA": 8,
      "NEUVOO UK": 3,
      "NEUVOO SARL": 10,
      "NEUVOO BRA": 11,
      "GESTION NEUVOO": 3,
    },
    "Job Board": {
      "NEUVOO INC": 18,
      "NEUVOO FR": 9,
      "NEUVOO USA": 9,
      "NEUVOO UK": 4,
      "NEUVOO SARL": 11,
      "NEUVOO BRA": 12,
      "GESTION NEUVOO": 4,
    },
    "Mid Market": {
      "NEUVOO INC": 19,
      "NEUVOO FR": 10,
      "NEUVOO USA": 10,
      "NEUVOO UK": 5,
      "NEUVOO SARL": 12,
      "NEUVOO BRA": 13,
      "GESTION NEUVOO": 5,
    },
  };

  const segmentData = locationData[segment];

  if (segmentData) {
    return segmentData;
  }

  throw new Error(`Segment ${segment} not found.`);
}

/**
 * Decrypts and organize account owners to set up email_address and cc_addresses
 * @param owners
 * @returns
 */
export function formatAccountOwners(owners: AccountsOwners[]): {
  email_address: string;
  cc_addresses: string[];
} {
  // Find owner with type sales
  const salesOwner = owners?.find((owner) => owner.accountOwnerType === "sales");

  // Decrypt its email and assign it into email_address variable
  const email_address =
    salesOwner && salesOwner.user && salesOwner.user.email
      ? privacy.cipher.decrypt(salesOwner.user.email)
      : "";

  // Get CC and secondary sales to send email, excluding QA and sales
  const cc_addresses =
    owners
      ?.filter(
        (owner) =>
          owner.accountOwnerType !== "sales" &&
          owner.accountOwnerType !== "qa" &&
          owner.user?.email, // Ensure the user and email exist
      )
      .map((owner) => privacy.cipher.decrypt(owner.user.email)) || [];

  // Return result
  return { email_address, cc_addresses };
}

/**
 * Returns a date in the Zulu timezone (UTC+0) as a string in the ISO format.
 * If a timestamp is provided, it will be used to generate the date, otherwise the current date will be used.
 * @param {number} [timestamp] - The timestamp to use for the date, in seconds since the Unix epoch.
 * @returns {string} - The date in the Zulu timezone as a string in the ISO format.
 * @example
 * zuluDate(**********); // "2021-04-23T00:00:00Z"
 * zuluDate(); // The current date in the Zulu timezone
 */
export function zuluDate(timestamp: number | null = null): string {
  const date = timestamp ? new Date(timestamp * 1000) : new Date();
  return date.toISOString().replace(/\.\d{3}Z$/, "Z"); // Ensures the correct Zulu format
}

/**
 * Evaluate the company name and suggest a feedCode
 * @param companyName Company name
 * @returns feedCode or an error message
 */
export function suggestFeedCodeName(feedCodeName: string): string {
  let feedCode = removeAccents(feedCodeName);
  // Convert '&' into ' and '
  feedCode = feedCode.replace(/&/g, " and ");
  feedCode = feedCode.trim();
  feedCode = feedCode.replace(/[^a-z\d |-]/gi, "");
  feedCode = feedCode.toLowerCase().replace(/\s+/g, "-");
  // Replace multiple hyphens with a single hyphen
  feedCode = feedCode.replace(/-+/g, "-");
  feedCode = feedCode.replace(/^-+|-+$/g, "");

  return feedCode;
}

export const countryArguments: {
  [key: string]: {
    country_label: string;
    sub_domain?: string;
    country: string;
    languages?: string[];
    jobs_languages?: string[];
    api_languages?: string[];
    default_language?: string;
    active?: number;
    distance?: string;
    group?: string;
    momo?: number;
    geo_continent?: string;
    currency?: string;
    cpc_mask?: string;
    cookie?: number;
    seo_url?: number;
    Employers?: number;
    advice_blog?: number;
    franchise_page?: number;
    postjobs?: {
      active?: number;
      freemium?: number;
    };
    tier_type?: string;
    billing: {
      currency?: string;
      currency_symbol?: string;
      symbol_position?: string;
    };
    minAmount: number;
    salary_data: {
      currency_symbol?: string;
      currency_text?: string;
      currency_symbol_ar?: string;
      currency_symbol_fr?: string;
      currency_symbol_en?: string;
      symbol_position?: string;
      thousand_separate?: string;
      weekly_hours?: string;
      hour_min_wage?: string;
      month_median_wage?: string;
      min_salary?: string;
      max_salary?: string;
      step?: string;
      active?: number;
      active_languages?: string[];
    };
    tax_cal?: { active: number };
    force_registration?: number;
    consent_banner?: number;
    consent_banner_exceptions?: {
      display?: number; // It shows or hides the banner in the defined regions
      region?: string[];
    };
    countryHubspot?: string;
    talentpedia?: { active?: number };
  };
} = {
  ae: {
    country_label: "United Arab Emirates",
    sub_domain: "ae",
    country: "ae",
    languages: ["ar"],
    jobs_languages: ["ar"],
    default_language: "ar",
    active: 1,
    distance: "km",
    group: "europa",
    momo: 1,
    geo_continent: "Asia Oceania",
    currency: "AED",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: " د.إ",
      symbol_position: "after",
      thousand_separate: ",",
      weekly_hours: "48",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "7500",
      max_salary: "92500",
      step: "2000",
      active: 1,
    },
    tax_cal: {
      active: 1,
    },
    force_registration: 1,
    consent_banner: 1,
  },
  ao: {
    country_label: "Angola",
    sub_domain: "ao",
    country: "ao",
    languages: ["pt"],
    jobs_languages: ["pt"],
    default_language: "pt",
    active: 1,
    distance: "km",
    group: "Africa",
    momo: 1,
    geo_continent: "Atlántica de Africa",
    currency: "AOA",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: " Kz",
      symbol_position: "after",
      thousand_separate: "",
      weekly_hours: "",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "100000",
      max_salary: "930000",
      step: "18000",
      active: 0,
    },
    tax_cal: {
      active: 1,
    },
    force_registration: 1,
    consent_banner: 1,
  },
  ar: {
    country_label: "Argentina",
    sub_domain: "ar",
    country: "ar",
    languages: ["es"],
    jobs_languages: ["es"],
    default_language: "es",
    active: 1,
    distance: "km",
    group: "america",
    momo: 1,
    geo_continent: "America",
    currency: "ARS",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: "$ ",
      symbol_position: "before",
      thousand_separate: ".",
      weekly_hours: "40",
      hour_min_wage: "59.375",
      month_median_wage: "124608",
      min_salary: "40000",
      max_salary: "470000",
      step: "10000",
      active: 1,
    },
    tax_cal: {
      active: 1,
    },
    force_registration: 1,
    consent_banner: 1,
  },
  at: {
    country_label: "Austria",
    sub_domain: "at",
    country: "at",
    languages: ["de"],
    jobs_languages: ["de"],
    default_language: "de",
    active: 1,
    distance: "km",
    group: "europa",
    cookie: 1,
    momo: 1,
    geo_continent: "Europa",
    currency: "EUR",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: "€ ",
      symbol_position: "before",
      thousand_separate: ".",
      weekly_hours: "40",
      hour_min_wage: "6.92",
      month_median_wage: "22068",
      min_salary: "1000",
      max_salary: "11750",
      step: "250",
      active: 1,
    },
    tax_cal: {
      active: 1,
    },
    Employers: 1,
    force_registration: 1,
    consent_banner: 1,
  },
  au: {
    country_label: "Australia",
    sub_domain: "au",
    country: "au",
    languages: ["en"],
    jobs_languages: ["en"],
    default_language: "en",
    active: 1,
    distance: "km",
    group: "oceania",
    momo: 1,
    geo_continent: "Asia Oceania",
    currency: "AUD",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: "$",
      symbol_position: "before",
      thousand_separate: ",",
      weekly_hours: "38",
      hour_min_wage: "17.29",
      month_median_wage: "59571",
      min_salary: "16000",
      max_salary: "190000",
      step: "4000",
      active: 1,
    },
    tax_cal: {
      active: 1,
    },
    force_registration: 1,
    consent_banner: 1,
  },
  be: {
    country_label: "Belgium",
    sub_domain: "be",
    country: "be",
    languages: ["nl", "fr"],
    jobs_languages: ["nl", "fr"],
    default_language: "nl",
    active: 1,
    distance: "km",
    group: "europa",
    cookie: 1,
    momo: 1,
    geo_continent: "Europa",
    currency: "EUR",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: "€ ",
      symbol_position: "before",
      thousand_separate: " ",
      weekly_hours: "38",
      hour_min_wage: "9.12",
      month_median_wage: "33600",
      min_salary: "1300",
      max_salary: "14500",
      step: "500",
      active: 1,
    },
    tax_cal: {
      active: 1,
    },
    postjobs: {
      active: 1,
      freemium: 0,
    },
    force_registration: 1,
    consent_banner: 1,
  },
  bh: {
    country_label: "Bahrain",
    sub_domain: "bh",
    country: "bh",
    languages: ["ar", "en"],
    jobs_languages: ["ar", "en"],
    default_language: "ar",
    active: 1,
    distance: "km",
    group: "europa",
    momo: 1,
    geo_continent: "Asia Oceania",
    currency: "BHD",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: " BHD",
      currency_symbol_ar: " د.ب",
      symbol_position: "after",
      thousand_separate: "",
      weekly_hours: "",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "6000",
      max_salary: "68500",
      step: "1500",
      active: 0,
    },
    tax_cal: {
      active: 1,
    },
    force_registration: 1,
    consent_banner: 1,
  },
  br: {
    country_label: "Brazil",
    sub_domain: "br",
    country: "br",
    languages: ["pt"],
    jobs_languages: ["pt"],
    default_language: "pt",
    active: 1,
    distance: "km",
    group: "america",
    momo: 1,
    geo_continent: "America",
    currency: "BRL",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: "R$",
      symbol_position: "before",
      thousand_separate: ".",
      weekly_hours: "40",
      hour_min_wage: "5.08",
      month_median_wage: "26730",
      min_salary: "4000",
      max_salary: "47000",
      step: "1000",
      active: 1,
    },
    tax_cal: {
      active: 1,
    },
    force_registration: 1,
    consent_banner: 1,
  },
  ca: {
    country_label: "Canada",
    sub_domain: "ca",
    country: "ca",
    tier_type: "1",
    languages: ["fr", "en"],
    jobs_languages: ["fr", "en"],
    api_languages: ["fr", "en"],
    default_language: "en",
    active: 1,
    distance: "km",
    group: "america",
    momo: 1,
    geo_continent: "America",
    currency: "CAD",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: "$",
      currency_text: "CAD",
      symbol_position: "before",
      thousand_separate: ",",
      weekly_hours: "37.5",
      hour_min_wage: "10.45",
      month_median_wage: "32790",
      min_salary: "15000",
      max_salary: "232000",
      step: "3000",
      active: 1,
    },
    tax_cal: {
      active: 1,
    },
    postjobs: {
      active: 1,
      freemium: 1,
    },
    Employers: 1,
    force_registration: 1,
    advice_blog: 1,
    franchise_page: 1,
    consent_banner: 1,
  },
  ch: {
    country_label: "Switzerland",
    sub_domain: "ch",
    country: "ch",
    tier_type: "1",
    languages: ["fr", "de", "en", "it"],
    jobs_languages: ["fr", "de", "en", "it"],
    default_language: "de",
    active: 1,
    distance: "km",
    group: "europa",
    cookie: 1,
    momo: 1,
    geo_continent: "Europa",
    currency: "CHF",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: "CHF ",
      symbol_position: "before",
      thousand_separate: "'",
      weekly_hours: "42.5",
      month_median_wage: "74268",
      hour_min_wage: "16.5",
      min_salary: "15000",
      max_salary: "230000",
      step: "2000",
      active: 1,
    },
    tax_cal: {
      active: 1,
    },
    Employers: 1,
    force_registration: 1,
    consent_banner: 1,
  },
  ci: {
    country_label: "Côte d'Ivoire",
    sub_domain: "ci",
    country: "ci",
    languages: ["fr"],
    jobs_languages: ["fr"],
    default_language: "fr",
    active: 1,
    distance: "km",
    group: "europa",
    cookie: 1,
    momo: 1,
    geo_continent: "Africa",
    currency: "XOF",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: " CFA",
      symbol_position: "after",
      thousand_separate: "",
      weekly_hours: "",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "100000",
      max_salary: "900000",
      step: "20000",
      active: 0,
    },
    tax_cal: {
      active: 1,
    },
    force_registration: 1,
    consent_banner: 1,
  },
  cn: {
    country_label: "China",
    sub_domain: "cn",
    country: "cn",
    languages: ["cn", "en"],
    jobs_languages: ["cn", "en"],
    default_language: "cn",
    active: 1,
    distance: "km",
    group: "asia",
    cookie: 0,
    momo: 1,
    geo_continent: "Asia Oceania",
    currency: "CNY",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: " 元",
      symbol_position: "after",
      thousand_separate: "",
      weekly_hours: "",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "100000",
      max_salary: "1200000",
      step: "15000",
      active: 0,
    },
    tax_cal: {
      active: 1,
    },
    force_registration: 1,
    consent_banner: 1,
  },
  cm: {
    country_label: "Cameroon",
    sub_domain: "cm",
    country: "cm",
    languages: ["fr", "en"],
    jobs_languages: ["fr", "en"],
    default_language: "fr",
    active: 1,
    distance: "km",
    group: "europa",
    cookie: 0,
    momo: 1,
    geo_continent: "Africa",
    currency: "XAF",
    seo_url: 1,
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: " FCFA",
      symbol_position: "after",
      thousand_separate: "",
      weekly_hours: "",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "130000",
      max_salary: "1450000",
      step: "30000",
      active: 0,
    },
    tax_cal: {
      active: 1,
    },
    force_registration: 1,
    consent_banner: 1,
  },
  cl: {
    country_label: "Chile",
    sub_domain: "cl",
    country: "cl",
    languages: ["es"],
    jobs_languages: ["es"],
    default_language: "es",
    active: 1,
    distance: "km",
    group: "america",
    momo: 1,
    geo_continent: "America",
    currency: "CLP",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: "$",
      symbol_position: "before",
      thousand_separate: ".",
      weekly_hours: "45",
      month_median_wage: "4080000",
      hour_min_wage: "1725",
      min_salary: "130000",
      max_salary: "1300000",
      step: "30000",
      active: 1,
    },
    tax_cal: {
      active: 1,
    },
    force_registration: 1,
  },
  co: {
    country_label: "Colombia",
    sub_domain: "co",
    country: "co",
    languages: ["es"],
    jobs_languages: ["es", "en"],
    default_language: "es",
    active: 1,
    distance: "km",
    group: "america",
    momo: 1,
    geo_continent: "America",
    currency: "COP",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: "$",
      symbol_position: "before",
      thousand_separate: ".",
      weekly_hours: "42",
      hour_min_wage: "4882.77",
      month_median_wage: "781242",
      min_salary: "650000",
      max_salary: "9000000",
      step: "200000",
      active: 1,
    },
    tax_cal: {
      active: 1,
    },
    force_registration: 1,
    consent_banner: 1,
  },
  cr: {
    country_label: "Costa Rica",
    sub_domain: "cr",
    country: "cr",
    languages: ["es"],
    jobs_languages: ["es"],
    default_language: "es",
    active: 1,
    distance: "km",
    group: "america",
    momo: 1,
    geo_continent: "America",
    currency: "CRC",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: "₡ ",
      symbol_position: "before",
      thousand_separate: "",
      weekly_hours: "",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "200000",
      max_salary: "2350000",
      step: "50000",
      active: 0,
    },
    tax_cal: {
      active: 1,
    },
    force_registration: 1,
    consent_banner: 1,
  },
  cz: {
    country_label: "Czechia",
    sub_domain: "cz",
    country: "cz",
    languages: ["cs", "en"],
    jobs_languages: ["cs", "en"],
    default_language: "cs",
    active: 1,
    distance: "km",
    group: "europa",
    cookie: 1,
    geo_continent: "Europa",
    currency: "CZK",
    momo: 1,
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: " Kč",
      symbol_position: "after",
      thousand_separate: "",
      weekly_hours: "",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "120000",
      max_salary: "900000",
      step: "20000",
      active: 1,
    },
    tax_cal: {
      active: 1,
    },
    force_registration: 1,
    consent_banner: 1,
  },
  dk: {
    country_label: "Denmark",
    sub_domain: "dk",
    country: "dk",
    languages: ["da", "en"],
    jobs_languages: ["da", "en"],
    default_language: "da",
    active: 1,
    distance: "km",
    group: "europa",
    cookie: 1,
    momo: 1,
    geo_continent: "Europa",
    currency: "DKK",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: " kr",
      symbol_position: "after",
      thousand_separate: "",
      weekly_hours: "37",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "100000",
      max_salary: "1500000",
      step: "10000",
      active: 0,
    },
    tax_cal: {
      active: 1,
    },
    force_registration: 1,
    consent_banner: 1,
  },
  de: {
    country_label: "Germany",
    sub_domain: "de",
    country: "de",
    tier_type: "1",
    languages: ["de"],
    jobs_languages: ["de", "en"],
    default_language: "de",
    active: 1,
    distance: "km",
    group: "europa",
    cookie: 1,
    momo: 1,
    geo_continent: "Europa",
    currency: "EUR",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: "€ ",
      symbol_position: "before",
      thousand_separate: " ",
      weekly_hours: "40",
      month_median_wage: "21264",
      hour_min_wage: "8.5",
      min_salary: "1000",
      max_salary: "13500",
      step: "150",
      active: 1,
    },
    tax_cal: {
      active: 1,
    },
    Employers: 1,
    force_registration: 1,
    consent_banner: 1,
  },
  do: {
    country_label: "Dominican Republic",
    sub_domain: "do",
    country: "do",
    languages: ["es"],
    jobs_languages: ["es"],
    default_language: "es",
    active: 0,
    distance: "km",
    group: "america",
    cookie: 1,
    momo: 1,
    geo_continent: "America",
    currency: "DOP",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: " $",
      symbol_position: "after",
      thousand_separate: " ",
      weekly_hours: "40",
      month_median_wage: "",
      hour_min_wage: "8.5",
      active: 0,
    },
    force_registration: 1,
    consent_banner: 1,
  },
  dz: {
    country_label: "Algeria",
    sub_domain: "dz",
    country: "dz",
    languages: ["ar", "fr", "en"],
    jobs_languages: ["ar", "fr", "en"],
    default_language: "ar",
    active: 1,
    distance: "km",
    group: "europa",
    cookie: 1,
    momo: 1,
    geo_continent: "Africa",
    currency: "DZD",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: " دج",
      currency_symbol_fr: " DA",
      symbol_position: "after",
      thousand_separate: "",
      weekly_hours: "",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "170000",
      max_salary: "2250000",
      step: "50000",
      active: 0,
    },
    tax_cal: {
      active: 1,
    },
    force_registration: 1,
    consent_banner: 1,
  },
  ec: {
    country_label: "Ecuador",
    sub_domain: "ec",
    country: "ec",
    languages: ["es"],
    jobs_languages: ["es"],
    default_language: "es",
    active: 1,
    distance: "km",
    group: "america",
    momo: 1,
    geo_continent: "America",
    currency: "USD",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: "$",
      symbol_position: "before",
      thousand_separate: "",
      weekly_hours: "",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "250",
      max_salary: "3250",
      step: "70",
      active: 0,
    },
    tax_cal: {
      active: 1,
    },
    force_registration: 1,
    consent_banner: 1,
  },
  eg: {
    country_label: "Egypt",
    sub_domain: "eg",
    country: "eg",
    languages: ["ar", "en"],
    jobs_languages: ["ar", "en"],
    default_language: "ar",
    active: 1,
    distance: "km",
    group: "europa",
    momo: 1,
    geo_continent: "Africa",
    currency: "EGP",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: "E£ ",
      currency_symbol_ar: "ج.م ",
      symbol_position: "before",
      thousand_separate: "",
      weekly_hours: "",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "35000",
      max_salary: "410000",
      step: "10000",
      active: 0,
    },
    tax_cal: {
      active: 1,
    },
    force_registration: 1,
    consent_banner: 1,
  },
  es: {
    country_label: "Spain",
    sub_domain: "es",
    country: "es",
    languages: ["es"],
    jobs_languages: ["es"],
    default_language: "es",
    active: 1,
    distance: "km",
    group: "europa",
    cookie: 1,
    momo: 1,
    geo_continent: "Europa",
    currency: "EUR",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: "€ ",
      symbol_position: "before",
      thousand_separate: ".",
      weekly_hours: "40",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "1200",
      max_salary: "15500",
      step: "300",
      active: 1,
    },
    tax_cal: {
      active: 1,
    },
    force_registration: 1,
    consent_banner: 1,
  },
  fi: {
    country_label: "Finland",
    sub_domain: "fi",
    country: "fi",
    languages: ["fi", "en"],
    jobs_languages: ["fi", "en"],
    default_language: "fi",
    active: 1,
    distance: "km",
    group: "europa",
    cookie: 1,
    momo: 1,
    geo_continent: "Europa",
    currency: "EUR",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: "€ ",
      symbol_position: "before",
      thousand_separate: "",
      weekly_hours: "",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "13000",
      max_salary: "180000",
      step: "4000",
      active: 1,
      active_languages: ["en"],
    },
    tax_cal: {
      active: 1,
    },
    force_registration: 1,
    consent_banner: 1,
  },
  fr: {
    country_label: "France",
    sub_domain: "fr",
    country: "fr",
    tier_type: "1",
    languages: ["fr"],
    jobs_languages: ["fr"],
    default_language: "fr",
    active: 1,
    distance: "km",
    group: "europa",
    cookie: 1,
    momo: 1,
    geo_continent: "Europa",
    currency: "EUR",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: "€ ",
      symbol_position: "before",
      thousand_separate: " ",
      weekly_hours: "35",
      month_median_wage: "21264",
      hour_min_wage: "9.61",
      min_salary: "1000",
      max_salary: "13000",
      step: "250",
      active: 1,
    },
    tax_cal: {
      active: 1,
    },
    postjobs: {
      active: 1,
      freemium: 0,
    },
    Employers: 1,
    force_registration: 1,
    consent_banner: 1,
  },
  uk: {
    country_label: "United Kingdom",
    sub_domain: "uk",
    country: "gb",
    tier_type: "1",
    languages: ["en"],
    jobs_languages: ["en"],
    api_languages: ["en"],
    default_language: "en",
    active: 1,
    distance: "mi",
    group: "europa",
    cookie: 1,
    momo: 1,
    geo_continent: "Europa",
    currency: "GBP",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: "£",
      symbol_position: "before",
      thousand_separate: ",",
      weekly_hours: "40",
      hour_min_wage: "6.7",
      month_median_wage: "27600",
      min_salary: "15000",
      max_salary: "230000",
      step: "4000",
      active: 1,
    },
    tax_cal: {
      active: 1,
    },
    Employers: 1,
    force_registration: 1,
    consent_banner: 1,
  },
  gr: {
    country_label: "Greece",
    sub_domain: "gr",
    country: "gr",
    languages: ["el", "en"],
    jobs_languages: ["el", "en"],
    default_language: "el",
    active: 1,
    distance: "km",
    group: "europa",
    cookie: 1,
    momo: 1,
    geo_continent: "Europa",
    currency: "EUR",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: "€ ",
      symbol_position: "before",
      thousand_separate: "",
      weekly_hours: "",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "500",
      max_salary: "6750",
      step: "150",
      active: 0,
    },
    tax_cal: {
      active: 1,
    },
    force_registration: 1,
    consent_banner: 1,
  },
  gh: {
    country_label: "Ghana",
    sub_domain: "gh",
    country: "gh",
    languages: ["en"],
    jobs_languages: ["en"],
    default_language: "en",
    active: 1,
    distance: "km",
    group: "europa",
    cookie: 1,
    momo: 1,
    geo_continent: "Africa",
    currency: "GHS",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: "GH₵ ",
      symbol_position: "before",
      thousand_separate: "",
      weekly_hours: "",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "170000",
      max_salary: "1900000",
      step: "40000",
      active: 0,
    },
    tax_cal: {
      active: 1,
    },
    force_registration: 1,
    consent_banner: 1,
  },
  gt: {
    country_label: "Guatemala",
    sub_domain: "gt",
    country: "gt",
    languages: ["es"],
    jobs_languages: ["es"],
    default_language: "es",
    active: 1,
    distance: "km",
    group: "america",
    cookie: 1,
    momo: 1,
    geo_continent: "America",
    currency: "GTQ",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: "Q ",
      symbol_position: "before",
      thousand_separate: "",
      weekly_hours: "",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "170000",
      max_salary: "2250000",
      step: "50000",
      active: 0,
    },
    tax_cal: {
      active: 1,
    },
    force_registration: 1,
    consent_banner: 1,
  },
  hk: {
    country_label: "Hong Kong",
    sub_domain: "hk",
    country: "hk",
    languages: ["en", "zh", "cn"],
    jobs_languages: ["en", "zh", "cn"],
    default_language: "zh",
    active: 1,
    distance: "km",
    group: "asia",
    cookie: 0,
    momo: 1,
    geo_continent: "Asia Oceania",
    currency: "HKD",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: "$ ",
      symbol_position: "before",
      thousand_separate: "",
      weekly_hours: "",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "100000",
      max_salary: "1400000",
      step: "20000",
      active: 1,
      active_languages: ["en"],
    },
    tax_cal: {
      active: 1,
    },
    force_registration: 1,
    consent_banner: 1,
  },
  hu: {
    country_label: "Hungary",
    sub_domain: "hu",
    country: "hu",
    languages: ["hu", "en"],
    jobs_languages: ["hu", "en"],
    default_language: "hu",
    active: 1,
    distance: "km",
    group: "europa",
    cookie: 1,
    momo: 1,
    geo_continent: "Europa",
    currency: "HUF",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: "Ft ",
      symbol_position: "before",
      thousand_separate: "",
      weekly_hours: "",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "15000",
      max_salary: "210000",
      step: "4000",
      active: 1,
      active_languages: ["en"],
    },
    tax_cal: {
      active: 1,
    },
    force_registration: 1,
    consent_banner: 1,
  },
  id: {
    country_label: "Indonesia",
    sub_domain: "id",
    country: "id",
    languages: ["id", "en"],
    jobs_languages: ["id", "en"],
    default_language: "id",
    active: 1,
    distance: "km",
    group: "asia",
    cookie: 0,
    momo: 1,
    geo_continent: "Asia Oceania",
    currency: "IDR",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: "Rp. ",
      symbol_position: "before",
      thousand_separate: "",
      weekly_hours: "",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "17000000",
      max_salary: "*********",
      step: "4000000",
      active: 0,
    },
    tax_cal: {
      active: 1,
    },
    force_registration: 1,
    consent_banner: 1,
  },
  ie: {
    country_label: "Ireland",
    sub_domain: "ie",
    country: "ie",
    languages: ["en"],
    jobs_languages: ["en"],
    default_language: "en",
    active: 1,
    distance: "km",
    group: "europa",
    cookie: 1,
    momo: 1,
    geo_continent: "Europa",
    currency: "EUR",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: "€ ",
      symbol_position: "before",
      thousand_separate: " ",
      weekly_hours: "39",
      hour_min_wage: "9.15",
      month_median_wage: "19728",
      min_salary: "1400",
      max_salary: "18200",
      step: "400",
      active: 1,
    },
    tax_cal: {
      active: 1,
    },
    force_registration: 1,
    consent_banner: 1,
  },
  il: {
    country_label: "Israel",
    sub_domain: "il",
    country: "il",
    languages: ["he", "ar", "en"],
    jobs_languages: ["he", "ar", "en"],
    default_language: "he",
    active: 1,
    distance: "km",
    group: "asia",
    cookie: 0,
    momo: 1,
    geo_continent: "Asia Oceania",
    currency: "ILS",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: "₪ ",
      symbol_position: "before",
      thousand_separate: "",
      weekly_hours: "",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "15000",
      max_salary: "225000",
      step: "5000",
      active: 0,
    },
    tax_cal: {
      active: 1,
    },
    force_registration: 1,
    consent_banner: 1,
  },
  in: {
    country_label: "India",
    sub_domain: "in",
    country: "in",
    languages: ["en"],
    jobs_languages: ["en"],
    default_language: "en",
    active: 1,
    distance: "km",
    group: "asia",
    momo: 1,
    geo_continent: "Asia Oceania",
    currency: "INR",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: "₹ ",
      symbol_position: "before",
      thousand_separate: "",
      weekly_hours: "48",
      hour_min_wage: "100",
      month_median_wage: "100000",
      min_salary: "170000",
      max_salary: "2250000",
      step: "50000",
      active: 1,
    },
    tax_cal: {
      active: 1,
    },
    postjobs: {
      active: 1,
      freemium: 1,
    },
    Employers: 1,
    force_registration: 1,
    consent_banner: 0,
  },
  it: {
    country_label: "Italy",
    sub_domain: "it",
    country: "it",
    languages: ["it"],
    jobs_languages: ["it"],
    default_language: "it",
    active: 1,
    distance: "km",
    group: "europa",
    cookie: 1,
    momo: 1,
    geo_continent: "Europa",
    currency: "EUR",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: "€ ",
      symbol_position: "before",
      thousand_separate: " ",
      weekly_hours: "40",
      month_median_wage: "16032",
      hour_min_wage: "6",
      min_salary: "1400",
      max_salary: "22200",
      step: "200",
      active: 1,
    },
    Employers: 1,
    tax_cal: {
      active: 1,
    },
    postjobs: {
      active: 1,
      freemium: 1,
    },
    force_registration: 1,
    consent_banner: 1,
  },
  jp: {
    country_label: "Japan",
    sub_domain: "jp",
    country: "jp",
    languages: ["ja", "en"],
    jobs_languages: ["ja", "en"],
    default_language: "ja",
    active: 1,
    distance: "km",
    group: "oceania",
    momo: 1,
    geo_continent: "Asia Oceania",
    currency: "JPY",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: " 円",
      symbol_position: "after",
      thousand_separate: "",
      weekly_hours: "",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "2000000",
      max_salary: "26000000",
      step: "400000",
      active: 1,
      active_languages: ["en"],
    },
    tax_cal: {
      active: 1,
    },
    force_registration: 1,
    consent_banner: 1,
  },
  ke: {
    country_label: "Kenya",
    sub_domain: "ke",
    country: "ke",
    languages: ["en"],
    jobs_languages: ["en"],
    default_language: "en",
    active: 1,
    distance: "km",
    group: "europa",
    momo: 1,
    geo_continent: "Africa",
    currency: "KES",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: "KSh ",
      symbol_position: "before",
      thousand_separate: "",
      weekly_hours: "",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "170000",
      max_salary: "2250000",
      step: "50000",
      active: 0,
    },
    tax_cal: {
      active: 1,
    },
    force_registration: 1,
    consent_banner: 1,
  },
  kr: {
    country_label: "Korea, Republic of",
    sub_domain: "kr",
    country: "kr",
    languages: ["ko", "en"],
    jobs_languages: ["ko", "en"],
    default_language: "ko",
    active: 1,
    distance: "km",
    group: "asia",
    momo: 1,
    geo_continent: "Asia Oceania",
    currency: "KRW",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: " 원",
      symbol_position: "after",
      thousand_separate: "",
      weekly_hours: "",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "18000000",
      max_salary: "*********",
      step: "5000000",
      active: 0,
    },
    tax_cal: {
      active: 1,
    },
    force_registration: 1,
    consent_banner: 1,
  },
  kw: {
    country_label: "Kuwait",
    sub_domain: "kw",
    country: "kw",
    languages: ["ar", "en"],
    jobs_languages: ["ar", "en"],
    default_language: "ar",
    active: 1,
    distance: "km",
    group: "europa",
    momo: 1,
    geo_continent: "Asia Oceania",
    currency: "KWD",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: " KWD",
      currency_symbol_ar: " د.ك.",
      symbol_position: "after",
      thousand_separate: "",
      weekly_hours: "",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "3000",
      max_salary: "45000",
      step: "1000",
      active: 0,
    },
    tax_cal: {
      active: 1,
    },
    force_registration: 1,
    consent_banner: 1,
  },
  kz: {
    country_label: "Kazakhstan",
    sub_domain: "kz",
    country: "kz",
    languages: ["ru", "en"],
    jobs_languages: ["ru", "en"],
    default_language: "ru",
    active: 1,
    distance: "км",
    group: "asia",
    momo: 1,
    geo_continent: "Asia Oceania",
    currency: "KZT",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: " ₸",
      symbol_position: "after",
      thousand_separate: "",
      weekly_hours: "",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "50000",
      max_salary: "500000",
      step: "10000",
      active: 0,
    },
    tax_cal: {
      active: 1,
    },
    force_registration: 1,
    consent_banner: 1,
  },
  lb: {
    country_label: "Lebanon",
    sub_domain: "lb",
    country: "lb",
    languages: ["ar", "en", "fr"],
    jobs_languages: ["ar", "en", "fr"],
    default_language: "ar",
    active: 1,
    distance: "km",
    group: "asia",
    cookie: 0,
    momo: 1,
    geo_continent: "Asia Oceania",
    currency: "LBP",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: " ل.ل.‎",
      currency_symbol_fr: " LL",
      currency_symbol_en: " LL",
      symbol_position: "after",
      thousand_separate: "",
      weekly_hours: "",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "150000",
      max_salary: "4000000",
      step: "100000",
      active: 0,
    },
    tax_cal: {
      active: 1,
    },
    force_registration: 1,
    consent_banner: 1,
  },
  lu: {
    country_label: "Luxembourg",
    sub_domain: "lu",
    country: "lu",
    languages: ["de", "fr", "en"],
    jobs_languages: ["de", "fr", "en"],
    default_language: "fr",
    active: 1,
    distance: "km",
    group: "europa",
    cookie: 1,
    momo: 1,
    geo_continent: "Europa",
    currency: "EUR",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: "€ ",
      symbol_position: "before",
      thousand_separate: ",",
      weekly_hours: "40",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "15000",
      max_salary: "400000",
      step: "10000",
      active: 1,
    },
    tax_cal: {
      active: 1,
    },
    force_registration: 1,
    consent_banner: 1,
  },
  ma: {
    country_label: "Morocco",
    sub_domain: "ma",
    country: "ma",
    languages: ["ar", "fr", "en"],
    jobs_languages: ["ar", "fr", "en"],
    default_language: "fr",
    active: 1,
    distance: "km",
    group: "europa",
    cookie: 0,
    momo: 1,
    geo_continent: "Africa",
    currency: "MAD",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: " MAD",
      currency_symbol_ar: " درهم",
      symbol_position: "after",
      thousand_separate: "",
      weekly_hours: "",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "15000",
      max_salary: "400000",
      step: "10000",
      active: 0,
    },
    tax_cal: {
      active: 1,
    },
    force_registration: 1,
    consent_banner: 1,
  },
  mx: {
    country_label: "Mexico",
    sub_domain: "mx",
    country: "mx",
    languages: ["es"],
    jobs_languages: ["es"],
    default_language: "es",
    active: 1,
    distance: "km",
    group: "america",
    momo: 1,
    geo_continent: "America",
    currency: "MXN",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: "$ ",
      symbol_position: "before",
      thousand_separate: ",",
      weekly_hours: "48",
      hour_min_wage: "7.61",
      month_median_wage: "26244",
      min_salary: "3000",
      max_salary: "45000",
      step: "1000",
      active: 1,
    },
    tax_cal: {
      active: 1,
    },
    force_registration: 1,
    consent_banner: 1,
  },
  my: {
    country_label: "Malaysia",
    sub_domain: "my",
    country: "my",
    languages: ["ms", "en"],
    jobs_languages: ["ms", "en"],
    default_language: "ms",
    active: 1,
    distance: "km",
    group: "asia",
    cookie: 0,
    momo: 1,
    geo_continent: "Asia Oceania",
    currency: "MYR",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: "RM ",
      symbol_position: "before",
      thousand_separate: "",
      weekly_hours: "45",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "23000",
      max_salary: "280000",
      step: "6000",
      active: 1,
      active_languages: ["en"],
    },
    tax_cal: {
      active: 1,
    },
    force_registration: 1,
    consent_banner: 1,
  },
  mz: {
    country_label: "Mozambique",
    sub_domain: "mz",
    country: "mz",
    languages: ["pt"],
    jobs_languages: ["pt"],
    default_language: "pt",
    active: 1,
    distance: "km",
    group: "europa",
    momo: 1,
    geo_continent: "Africa",
    currency: "MZN",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: " MTn",
      symbol_position: "after",
      thousand_separate: "",
      weekly_hours: "",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "170000",
      max_salary: "2250000",
      step: "50000",
      active: 0,
    },
    tax_cal: {
      active: 1,
    },
    force_registration: 1,
    consent_banner: 1,
  },
  ng: {
    country_label: "Nigeria",
    sub_domain: "ng",
    country: "ng",
    languages: ["en"],
    jobs_languages: ["en"],
    default_language: "en",
    active: 1,
    distance: "km",
    group: "europa",
    momo: 1,
    geo_continent: "Africa",
    currency: "NGN",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: "₦",
      symbol_position: "before",
      thousand_separate: "",
      weekly_hours: "",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "50000",
      max_salary: "500000",
      step: "10000",
      active: 0,
    },
    tax_cal: {
      active: 1,
    },
    force_registration: 1,
    consent_banner: 1,
  },
  nl: {
    country_label: "Netherlands",
    sub_domain: "nl",
    country: "nl",
    languages: ["nl"],
    jobs_languages: ["nl"],
    default_language: "nl",
    active: 1,
    distance: "km",
    group: "europa",
    momo: 1,
    geo_continent: "Europa",
    currency: "EUR",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: "€ ",
      symbol_position: "before",
      thousand_separate: ".",
      weekly_hours: "38",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "13000",
      max_salary: "180000",
      step: "4000",
      active: 1,
    },
    tax_cal: {
      active: 1,
    },
    force_registration: 1,
    consent_banner: 1,
  },
  no: {
    country_label: "Norway",
    sub_domain: "no",
    country: "no",
    languages: ["no", "en"],
    jobs_languages: ["no", "en"],
    default_language: "no",
    active: 1,
    distance: "km",
    group: "europa",
    cookie: 1,
    momo: 1,
    geo_continent: "Europa",
    currency: "NOK",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: " kr",
      symbol_position: "after",
      thousand_separate: "",
      weekly_hours: "",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "170000",
      max_salary: "4000000",
      step: "100000",
      active: 0,
    },
    tax_cal: {
      active: 1,
    },
    force_registration: 1,
    consent_banner: 1,
  },
  nz: {
    country_label: "New Zealand",
    sub_domain: "nz",
    country: "nz",
    languages: ["en"],
    jobs_languages: ["en"],
    api_languages: ["en"],
    default_language: "en",
    active: 1,
    distance: "km",
    group: "oceania",
    momo: 1,
    geo_continent: "Asia Oceania",
    currency: "NZD",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: "$",
      symbol_position: "before",
      thousand_separate: "",
      weekly_hours: "",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "15000",
      max_salary: "400000",
      step: "10000",
      active: 1,
    },
    tax_cal: {
      active: 1,
    },
    force_registration: 1,
    consent_banner: 1,
  },
  om: {
    country_label: "Oman",
    sub_domain: "om",
    country: "om",
    languages: ["ar", "en"],
    jobs_languages: ["ar", "en"],
    default_language: "ar",
    active: 1,
    distance: "km",
    group: "europa",
    momo: 1,
    geo_continent: "Asia Oceania",
    currency: "OMR",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: " OMR",
      currency_symbol_ar: "	ر.ع.",
      symbol_position: "after",
      thousand_separate: "",
      weekly_hours: "",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "5000",
      max_salary: "68500",
      step: "1500",
      active: 0,
    },
    tax_cal: {
      active: 1,
    },
    force_registration: 1,
    consent_banner: 1,
  },
  pa: {
    country_label: "Panama",
    sub_domain: "pa",
    country: "pa",
    languages: ["es"],
    jobs_languages: ["es"],
    default_language: "es",
    active: 1,
    distance: "km",
    group: "america",
    momo: 1,
    geo_continent: "America",
    currency: "PAB",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: "B/. ",
      symbol_position: "before",
      thousand_separate: "",
      weekly_hours: "",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "700",
      max_salary: "9000",
      step: "200",
      active: 0,
    },
    tax_cal: {
      active: 1,
    },
    force_registration: 1,
    consent_banner: 1,
  },
  pe: {
    country_label: "Peru",
    sub_domain: "pe",
    country: "pe",
    languages: ["es"],
    jobs_languages: ["es"],
    default_language: "es",
    active: 1,
    distance: "km",
    group: "america",
    momo: 1,
    geo_continent: "America",
    currency: "PEN",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: "S/ ",
      symbol_position: "before",
      thousand_separate: "",
      weekly_hours: "",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "700",
      max_salary: "9000",
      step: "200",
      active: 0,
    },
    tax_cal: {
      active: 1,
    },
    force_registration: 1,
    consent_banner: 1,
  },
  ph: {
    country_label: "Philippines",
    sub_domain: "ph",
    country: "ph",
    languages: ["en"],
    jobs_languages: ["en"],
    default_language: "en",
    active: 1,
    distance: "km",
    group: "asia",
    momo: 1,
    geo_continent: "Asia Oceania",
    currency: "PHP",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: "₱ ",
      symbol_position: "before",
      thousand_separate: "",
      weekly_hours: "48",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "130000",
      max_salary: "1780000",
      step: "40000",
      active: 1,
    },
    tax_cal: {
      active: 1,
    },
    force_registration: 1,
    consent_banner: 1,
  },
  pk: {
    country_label: "Pakistan",
    sub_domain: "pk",
    country: "pk",
    languages: ["en"],
    jobs_languages: ["en"],
    default_language: "en",
    active: 1,
    distance: "km",
    group: "asia",
    momo: 1,
    geo_continent: "Asia Oceania",
    currency: "PKR",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: "₨ ",
      symbol_position: "before",
      thousand_separate: "",
      weekly_hours: "",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "7000",
      max_salary: "75000",
      step: "1000",
      active: 0,
    },
    tax_cal: {
      active: 1,
    },
    force_registration: 1,
    consent_banner: 1,
  },
  pl: {
    country_label: "Poland",
    sub_domain: "pl",
    country: "pl",
    languages: ["pl", "en"],
    jobs_languages: ["pl", "en"],
    default_language: "pl",
    active: 1,
    distance: "km",
    group: "europa",
    cookie: 1,
    momo: 1,
    geo_continent: "Europa",
    currency: "PLN",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: " zł",
      symbol_position: "after",
      thousand_separate: " ",
      weekly_hours: "40",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "15000",
      max_salary: "250000",
      step: "5000",
      active: 1,
    },
    tax_cal: {
      active: 1,
    },
    force_registration: 1,
    consent_banner: 1,
  },
  pt: {
    country_label: "Portugal",
    sub_domain: "pt",
    country: "pt",
    languages: ["pt"],
    jobs_languages: ["pt"],
    default_language: "pt",
    active: 1,
    distance: "km",
    group: "europa",
    cookie: 1,
    momo: 1,
    geo_continent: "Europa",
    currency: "EUR",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: "€ ",
      symbol_position: "before",
      thousand_separate: "",
      weekly_hours: "",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "700",
      max_salary: "9000",
      step: "200",
      active: 1,
    },
    tax_cal: {
      active: 1,
    },
    force_registration: 1,
    consent_banner: 1,
  },
  pr: {
    country_label: "Puerto Rico",
    sub_domain: "pr",
    country: "pr",
    languages: ["es"],
    jobs_languages: ["es"],
    default_language: "es",
    active: 1,
    distance: "km",
    group: "america",
    momo: 1,
    geo_continent: "America",
    currency: "USD",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: "$",
      symbol_position: "before",
      thousand_separate: "",
      weekly_hours: "",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "10000",
      max_salary: "150000",
      step: "3000",
      active: 1,
      active_languages: ["es"],
    },
    tax_cal: {
      active: 1,
    },
    force_registration: 1,
    consent_banner: 1,
  },
  qa: {
    country_label: "Qatar",
    sub_domain: "qa",
    country: "qa",
    languages: ["ar", "en"],
    jobs_languages: ["ar", "en"],
    default_language: "ar",
    active: 1,
    distance: "km",
    group: "europa",
    momo: 1,
    geo_continent: "Asia Oceania",
    currency: "QAR",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: " QAR",
      currency_symbol_ar: " ر. ق",
      symbol_position: "after",
      thousand_separate: "",
      weekly_hours: "48",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "170000",
      max_salary: "2100000",
      step: "40000",
      active: 0,
    },
    tax_cal: {
      active: 1,
    },
    force_registration: 1,
    consent_banner: 1,
  },
  ro: {
    country_label: "Romania",
    sub_domain: "ro",
    country: "ro",
    languages: ["ro", "en"],
    jobs_languages: ["ro", "en"],
    default_language: "ro",
    active: 1,
    distance: "km",
    group: "europa",
    cookie: 1,
    momo: 1,
    geo_continent: "Europa",
    currency: "RON",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: " lei",
      symbol_position: "after",
      thousand_separate: "",
      weekly_hours: "",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "13000",
      max_salary: "160000",
      step: "3000",
      active: 0,
    },
    tax_cal: {
      active: 1,
    },
    force_registration: 1,
    consent_banner: 1,
  },
  ru: {
    country_label: "Russian Federation",
    sub_domain: "ru",
    country: "ru",
    languages: ["ru", "en"],
    jobs_languages: ["ru", "en"],
    default_language: "ru",
    active: 1,
    distance: "км",
    group: "europa",
    momo: 1,
    geo_continent: "Europa",
    currency: "RUB",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: " ₽",
      symbol_position: "after",
      thousand_separate: "",
      weekly_hours: "",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "250000",
      max_salary: "3150000",
      step: "60000",
      active: 0,
    },
    tax_cal: {
      active: 1,
    },
    force_registration: 1,
    consent_banner: 1,
  },
  sa: {
    country_label: "Saudi Arabia",
    sub_domain: "sa",
    country: "sa",
    languages: ["ar", "en"],
    jobs_languages: ["ar", "en"],
    default_language: "ar",
    active: 1,
    distance: "km",
    group: "europa",
    momo: 1,
    geo_continent: "Asia Oceania",
    currency: "SAR",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: " SAR",
      currency_symbol_ar: " ر. س",
      symbol_position: "after",
      thousand_separate: "",
      weekly_hours: "48",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "40000",
      max_salary: "540000",
      step: "10000",
      active: 1,
    },
    tax_cal: {
      active: 1,
    },
    force_registration: 1,
    consent_banner: 1,
  },
  se: {
    country_label: "Sweden",
    sub_domain: "se",
    country: "se",
    languages: ["sv", "en"],
    jobs_languages: ["sv", "en"],
    default_language: "sv",
    active: 1,
    distance: "km",
    group: "europa",
    cookie: 1,
    momo: 1,
    geo_continent: "Europa",
    currency: "SEK",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: " kr",
      symbol_position: "after",
      thousand_separate: " ",
      weekly_hours: "40",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "13000",
      max_salary: "160000",
      step: "3000",
      active: 1,
    },
    tax_cal: {
      active: 1,
    },
    force_registration: 1,
    consent_banner: 1,
  },
  sg: {
    country_label: "Singapore",
    sub_domain: "sg",
    country: "sg",
    languages: ["en"],
    jobs_languages: ["en"],
    default_language: "en",
    active: 1,
    distance: "km",
    group: "asia",
    cookie: 0,
    momo: 1,
    geo_continent: "Asia Oceania",
    currency: "SGD",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: "S$",
      symbol_position: "before",
      thousand_separate: ",",
      weekly_hours: "44",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "2300",
      max_salary: "27100",
      step: "500",
      active: 1,
    },
    tax_cal: {
      active: 1,
    },
    force_registration: 1,
    consent_banner: 1,
  },
  sn: {
    country_label: "Senegal",
    sub_domain: "sn",
    country: "sn",
    languages: ["fr"],
    jobs_languages: ["fr"],
    default_language: "fr",
    active: 1,
    distance: "km",
    group: "Africa",
    cookie: 0,
    momo: 1,
    geo_continent: "Africa",
    currency: "XOF",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: " CFA",
      symbol_position: "after",
      thousand_separate: "",
      weekly_hours: "",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "170000",
      max_salary: "2500000",
      step: "50000",
      active: 0,
    },
    tax_cal: {
      active: 1,
    },
    force_registration: 1,
    consent_banner: 1,
  },
  tr: {
    country_label: "Turkey",
    sub_domain: "tr",
    country: "tr",
    languages: ["tr", "en"],
    jobs_languages: ["tr", "en"],
    default_language: "tr",
    active: 1,
    distance: "km",
    group: "europa",
    momo: 1,
    geo_continent: "Asia Oceania",
    currency: "TRY",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: " TL",
      symbol_position: "after",
      thousand_separate: "",
      weekly_hours: "",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "10000",
      max_salary: "150000",
      step: "3000",
      active: 0,
    },
    tax_cal: {
      active: 1,
    },
    force_registration: 1,
    consent_banner: 1,
  },
  th: {
    country_label: "Thailand",
    sub_domain: "th",
    country: "th",
    languages: ["th", "en"],
    jobs_languages: ["th", "en"],
    default_language: "th",
    active: 1,
    distance: "km",
    group: "asia",
    momo: 1,
    geo_continent: "Asia Oceania",
    currency: "THB",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: " บาท",
      symbol_position: "after",
      thousand_separate: "",
      weekly_hours: "",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "40000",
      max_salary: "520000",
      step: "10000",
      active: 0,
    },
    tax_cal: {
      active: 1,
    },
    force_registration: 1,
    consent_banner: 1,
  },
  tn: {
    country_label: "Tunisia",
    sub_domain: "tn",
    country: "tn",
    languages: ["ar", "fr", "en"],
    jobs_languages: ["ar", "fr", "en"],
    default_language: "ar",
    active: 1,
    distance: "km",
    group: "europa",
    cookie: 1,
    momo: 1,
    geo_continent: "Africa",
    currency: "TND",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: "د.ت",
      currency_symbol_fr: " DT",
      symbol_position: "after",
      thousand_separate: "",
      weekly_hours: "",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "170000",
      max_salary: "2100000",
      step: "40000",
      active: 0,
    },
    tax_cal: {
      active: 1,
    },
    force_registration: 1,
    consent_banner: 1,
  },
  tw: {
    country_label: "Taiwan, Province of China",
    sub_domain: "tw",
    country: "tw",
    languages: ["zh", "en"],
    jobs_languages: ["zh", "en"],
    default_language: "zh",
    active: 1,
    distance: "km",
    group: "asia",
    momo: 1,
    geo_continent: "Asia Oceania",
    currency: "TWD",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: "$ ",
      symbol_position: "before",
      thousand_separate: "",
      weekly_hours: "",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "400000",
      max_salary: "5200000",
      step: "100000",
      active: 0,
    },
    tax_cal: {
      active: 1,
    },
    force_registration: 1,
    consent_banner: 1,
  },
  ua: {
    country_label: "Ukraine",
    sub_domain: "ua",
    country: "ua",
    languages: ["ru", "uk", "en"],
    jobs_languages: ["ru", "uk", "en"],
    default_language: "uk",
    active: 1,
    distance: "км",
    group: "europa",
    momo: 1,
    geo_continent: "Europa",
    currency: "UAH",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: " грн",
      symbol_position: "after",
      thousand_separate: "",
      weekly_hours: "",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "15000",
      max_salary: "210000",
      step: "4000",
      active: 0,
    },
    tax_cal: {
      active: 1,
    },
    force_registration: 1,
    consent_banner: 1,
  },
  sca: {
    country_label: "United States of America",
    sub_domain: "www",
    country: "us",
    tier_type: "1",
    languages: ["en", "es"],
    jobs_languages: ["en", "es"],
    default_language: "en",
    active: 1,
    distance: "mi",
    group: "america",
    momo: 1,
    geo_continent: "America",
    currency: "USD",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: "$",
      symbol_position: "before",
      thousand_separate: ",",
      weekly_hours: "37.5",
      hour_min_wage: "7.25",
      month_median_wage: "28851",
      min_salary: "18000",
      max_salary: "335000",
      step: "2000",
      active: 1,
    },
    tax_cal: {
      active: 1,
    },
    talentpedia: {
      active: 1,
    },
    postjobs: {
      active: 1,
      freemium: 1,
    },
    Employers: 1,
    force_registration: 1,
    consent_banner: 0,
  },
  www: {
    country_label: "United States of America",
    sub_domain: "www",
    country: "us",
    tier_type: "1",
    languages: ["en", "es"],
    jobs_languages: ["en", "es"],
    api_languages: ["en", "es"],
    default_language: "en",
    active: 1,
    distance: "mi",
    group: "america",
    momo: 1,
    geo_continent: "America",
    currency: "USD",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: "$",
      symbol_position: "before",
      thousand_separate: ",",
      weekly_hours: "40",
      hour_min_wage: "7.25",
      month_median_wage: "28851",
      min_salary: "18000",
      max_salary: "335000",
      step: "2000",
      active: 1,
    },
    tax_cal: {
      active: 1,
    },
    talentpedia: {
      active: 1,
    },
    postjobs: {
      active: 1,
      freemium: 1,
    },
    Employers: 1,
    force_registration: 1,
    consent_banner: 1,
    consent_banner_exceptions: {
      display: 1, // It shows or hides the banner in the defined regions
      region: [
        "Rhode Island",
        "New Jersey",
        "Oregon",
        "Utah",
        "Maryland",
        "Iowa",
        "Colorado",
        "Nebraska",
        "Minnesota",
        "Montana",
        "Indiana",
        "Conneticut",
        "Texas",
        "Virginia",
        "Kentucky",
        "Tenessee",
        "Delaware",
        "New Hampshire",
      ],
    },
  },
  uy: {
    country_label: "Uruguay",
    sub_domain: "uy",
    country: "uy",
    languages: ["es"],
    jobs_languages: ["es"],
    default_language: "es",
    active: 1,
    distance: "km",
    group: "america",
    momo: 1,
    geo_continent: "America",
    currency: "UYU",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: "$ ",
      symbol_position: "before",
      thousand_separate: "",
      weekly_hours: "",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "13000",
      max_salary: "180000",
      step: "4000",
      active: 0,
    },
    tax_cal: {
      active: 1,
    },
    force_registration: 1,
    consent_banner: 1,
  },
  ug: {
    country_label: "Uganda",
    sub_domain: "ug",
    country: "ug",
    languages: ["en"],
    jobs_languages: ["en"],
    default_language: "en",
    active: 1,
    distance: "km",
    group: "Africa",
    momo: 1,
    geo_continent: "Africa",
    currency: "UGX",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: "USh ",
      symbol_position: "before",
      thousand_separate: "",
      weekly_hours: "",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "300000",
      max_salary: "4500000",
      step: "100000",
      active: 0,
    },
    tax_cal: {
      active: 1,
    },
    force_registration: 1,
    consent_banner: 1,
  },
  ve: {
    country_label: "Venezuela (Bolivarian Republic of)",
    sub_domain: "ve",
    country: "ve",
    languages: ["es"],
    jobs_languages: ["es"],
    default_language: "es",
    active: 1,
    distance: "km",
    group: "america",
    momo: 1,
    geo_continent: "America",
    currency: "VEF",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: " Bs",
      symbol_position: "after",
      thousand_separate: ".",
      weekly_hours: "40",
      hour_min_wage: "86.83",
      month_median_wage: "303504",
      min_salary: "650000",
      max_salary: "9000000",
      step: "200000",
      active: 0,
    },
    tax_cal: {
      active: 1,
    },
    force_registration: 1,
    consent_banner: 1,
  },
  vn: {
    country_label: "Viet Nam",
    sub_domain: "vn",
    country: "vn",
    languages: ["vi", "en"],
    jobs_languages: ["vi", "en"],
    default_language: "vi",
    active: 1,
    distance: "km",
    group: "asia",
    cookie: 0,
    momo: 1,
    geo_continent: "Asia Oceania",
    currency: "VND",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: " ₫",
      symbol_position: "after",
      thousand_separate: "",
      weekly_hours: "",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "15000000",
      max_salary: "*********",
      step: "4000000",
      active: 1,
      active_languages: ["en"],
    },
    tax_cal: {
      active: 1,
    },
    force_registration: 1,
    consent_banner: 1,
  },
  za: {
    country_label: "South Africa",
    sub_domain: "za",
    country: "za",
    languages: ["en"],
    jobs_languages: ["en"],
    default_language: "en",
    active: 1,
    distance: "km",
    group: "europa",
    momo: 1,
    geo_continent: "Africa",
    currency: "ZAR",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: "R ",
      symbol_position: "before",
      thousand_separate: " ",
      weekly_hours: "40",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "7000",
      max_salary: "90000",
      step: "2000",
      active: 1,
    },
    tax_cal: {
      active: 1,
    },
    postjobs: {
      active: 1,
      freemium: 1,
    },
    Employers: 1,
    force_registration: 1,
    consent_banner: 1,
  },
  zm: {
    country_label: "Zambia",
    sub_domain: "zm",
    country: "zm",
    languages: ["en"],
    jobs_languages: ["en"],
    default_language: "en",
    active: 1,
    distance: "km",
    group: "europa",
    momo: 1,
    geo_continent: "Africa",
    currency: "ZMW",
    billing: {},
    minAmount: -1,
    salary_data: {
      currency_symbol: "K ",
      symbol_position: "before",
      thousand_separate: "",
      weekly_hours: "",
      hour_min_wage: "",
      month_median_wage: "",
      min_salary: "30000",
      max_salary: "450000",
      step: "10000",
      active: 0,
    },
    tax_cal: {
      active: 1,
    },
    force_registration: 1,
    consent_banner: 1,
  },
};

/**
 * Validates a given token by checking its base64 format, decoding it, and verifying its structure and decryption.
 * @param token The token string to validate. It is expected to be a base64-encoded string.
 * @returns The decrypted token if all checks pass.
 */
export function validateToken(token: string) {
  const base64Regex = /^[A-Za-z0-9+/=]*$/;
  const isBase64Valid = base64Regex.test(token) && token.length % 4 === 0;
  if (!isBase64Valid) throw new HttpException("Invalid Request", HttpStatus.BAD_REQUEST);

  // Decode the base64 string, handling URL-safe encoding
  const decodedPayload = atob(token.replace(/-/g, "+").replace(/_/g, "/"));
  if (!decodedPayload) throw new HttpException("Invalid Request", HttpStatus.BAD_REQUEST);

  // Check the validity of the decoded payload
  const tokenFormat = /^[a-f0-9]{32}:[a-f0-9]+:[a-f0-9]{32}$/;
  const isEncryptionValid = tokenFormat.test(decodedPayload);
  if (!isEncryptionValid) throw new HttpException("Invalid Request", HttpStatus.BAD_REQUEST);

  // Check the validity of the excryption if not throw the exception
  const decryptedToken = privacy.cipher.decrypt(decodedPayload);
  if (!decryptedToken) throw new HttpException("Invalid Request", HttpStatus.BAD_REQUEST);

  return decryptedToken;
}
