import { CampaignRepository } from "../../../src/campaigns/repositories/campaign.repository";
import { Campaigns } from "../../../src/campaigns/entities/campaign.entity";
import { CreateCampaignDto } from "../../../src/campaigns/dto/create-campaign.dto";
import { SearchCampaignDto } from "../../../src/campaigns/dto/search-campaign.dto";
import {
  campaignCreatedDataMock,
  campaignLitsDataMock,
  campaignNewDataMock,
  campaignSearchDataMock,
  campaignSearchedListDataMock,
} from "../../../src/common/mocks/campaigns.data.mock";
import { DataSource } from "typeorm";
import { PrivacyService } from "../../../src/common/resources/privacy.service";

describe("CampaignRepository", () => {
  let campaignRepository: CampaignRepository;
  let privacy: PrivacyService;
  let dataSource: DataSource;

  const mockRepository = {
    find: jest.fn(),
    findOne: jest.fn(),
    create: jest.fn(),
    save: jest.fn(),
    search: jest.fn(),
    delete: jest.fn(),
    createQueryBuilder: jest.fn().mockReturnThis(),
    innerJoinAndSelect: jest.fn().mockReturnThis(),
    leftJoin: jest.fn().mockReturnThis(),
    innerJoin: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    andWhere: jest.fn().mockReturnThis(),
    take: jest.fn().mockReturnThis(),
    skip: jest.fn().mockReturnThis(),
    getManyAndCount: jest.fn(),
    getMany: jest.fn(),
    getRawMany: jest.fn(),
    addOrderBy: jest.fn(),
  };

  const mockCampaignsStatsSummaryViewRepository = {
    find: jest.fn(),
    save: jest.fn(),
    create: jest.fn(),
    createQueryBuilder: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    groupBy: jest.fn().mockReturnThis(),
    setParameters: jest.fn().mockReturnThis(),
    having: jest.fn().mockReturnThis(),
    andHaving: jest.fn().mockReturnThis(),
    getRawMany: jest.fn(),
    innerJoin: jest.fn().mockReturnThis(),
    andWhere: jest.fn().mockReturnThis(),
    addOrderBy: jest.fn().mockReturnThis(),
  };
  beforeEach(() => {
    campaignRepository = new CampaignRepository(
      mockRepository as any, // Mocked campaign repository
      privacy, // Mock or real privacy service
      dataSource, // Mock or real data source
      mockCampaignsStatsSummaryViewRepository as any, // Mocked campaigns stats summary view repository
    );
  });

  describe("find", () => {
    it("should return all campaigns", async () => {
      const mockCampaigns: Campaigns[] = campaignLitsDataMock;

      mockRepository.find.mockResolvedValue(mockCampaigns);

      expect(await campaignRepository.find()).toEqual(mockCampaigns);
    });
  });

  describe("findOne", () => {
    it("should return a campaign by ID", async () => {
      const mockCampaignId = 1;
      const mockCampaign: Campaigns = campaignCreatedDataMock;

      mockRepository.findOne.mockResolvedValue(mockCampaign);

      expect(await campaignRepository.findOne(mockCampaignId)).toEqual(mockCampaign);
    });
  });

  describe("create", () => {
    it("should create a new campaign", async () => {
      const createCampaignDto: CreateCampaignDto = campaignNewDataMock;
      const createdCampaign: Campaigns = campaignCreatedDataMock;

      mockRepository.create.mockReturnValue(createCampaignDto);
      mockRepository.save.mockResolvedValue(createdCampaign);

      expect(await campaignRepository.create(createCampaignDto)).toEqual(createdCampaign);
    });
  });

  describe("delete", () => {
    it("should delete a campaign", async () => {
      mockRepository.delete.mockReturnValue({ affected: 1, raw: true });

      expect(await campaignRepository.delete(1)).toEqual({ affected: 1, raw: true });
    });

    it("should return affected 0 if a campaign wasn't deleted", async () => {
      mockRepository.delete.mockReturnValue({ affected: 0, raw: true });

      expect(await campaignRepository.delete(1)).toEqual({ affected: 0, raw: true });
    });
  });

  describe("update", () => {
    it("should update a campaign by ID", async () => {
      const mockUpdatedCampaign: Campaigns = campaignCreatedDataMock;

      mockRepository.save.mockResolvedValue(mockUpdatedCampaign);

      expect(await campaignRepository.update(mockUpdatedCampaign)).toEqual(mockUpdatedCampaign);
    });

    it("should throw NotFoundException if campaign is not found", async () => {
      const mockUpdatedCampaign: Campaigns = campaignCreatedDataMock;

      // Simulate campaign not found by rejecting the save operation with a NotFoundException
      mockRepository.save.mockRejectedValue(new TypeError());

      await expect(campaignRepository.update(mockUpdatedCampaign)).rejects.toThrow(TypeError);
    });
  });

  describe("search", () => {
    it("should return all campaigns with provided filters", async () => {
      // mockFilters containing the query params
      const mockFilters: SearchCampaignDto = campaignSearchDataMock;

      // mockSearchedList containing the results of repository search
      const mockSearchedList: Campaigns[] = campaignSearchedListDataMock.campaigns;

      // mocked repository.search
      mockRepository.search = jest.fn().mockResolvedValue(mockSearchedList);

      expect(await mockRepository.search(mockFilters)).toEqual(mockSearchedList);
    });
  });

  describe("findAllAccountsWithCampaigns", () => {
    it("should return accounts without the 2-hour filter", async () => {
      const expectedResult = [{ account_id: 1690 }, { account_id: 1700 }];
      mockRepository.createQueryBuilder().getRawMany.mockResolvedValue(expectedResult);

      const result = await campaignRepository.findAllAccountsWithCampaigns(false);

      expect(mockRepository.createQueryBuilder).toHaveBeenCalled();
      expect(result).toEqual([1690, 1700]);
    });
  });
});
