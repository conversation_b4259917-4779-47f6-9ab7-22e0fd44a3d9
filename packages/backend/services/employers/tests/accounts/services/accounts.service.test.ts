/* eslint-disable @typescript-eslint/no-explicit-any */
import { Test, TestingModule } from "@nestjs/testing";
import { AccountService } from "../../../src/accounts/services/accounts.service";
import { AccountsRepositoryMock } from "../../../src/common/mocks/accounts.repository.mock"; // Importing mock
import { DataSource, EntityManager, QueryRunner } from "typeorm";
import { UserMockService } from "../../../src/common/mocks/user.service.mock";
import { AccountRepository } from "../../../src/accounts/repositories/account.repository";
import { Accounts } from "../../../src/accounts/entities/account.entity";
import {
  accountNewDataMock,
  accountCreateResponseDataMock,
  accountsListMock,
  mockAccountsEntity,
  expectedAccountSettings,
} from "../../../src/common/mocks/accounts.data.mock";
import { DealsService } from "../../../src/deals/services/deals.service";
import { accounts } from "../../../src/common/mocks/job-queue-data.mock";
import { CampaignFiltersService } from "../../../src/campaigns/services/campaign-filters.service";
import { CustomAxiosAdapter } from "../../../src/common/adapters/axios.adapter";
import { BadRequestException, HttpException, HttpStatus, Logger } from "@nestjs/common";
import { SearchAccountDto } from "../../../src/accounts/dto/search-account.dto";
import {
  AccountType,
  CampaignObjective,
  ConversionTypesFeaturesTags,
  Status,
} from "../../../src/common/resources/enums";
import { UserPrivilegeRepository } from "../../../src/users/repositories/user-privileges.repository";
import { userPrivilegeRepositoryMockFactory } from "../repositories/user-privileges.test";
import { MetricsService } from "../../../src/common/services/metrics.service";
import { Metrics } from "@talent-back-libs/metrics";
import { AccountSettingsService } from "../../../src/accounts/services/account-settings.service";
import { AccountSettings } from "../../../src/accounts/entities/account-settings.entity";
import * as privacy from "libs/privacy/src";
import { of } from "rxjs/internal/observable/of";
import { PrivacyService } from "../../../src/common/resources/privacy.service";
import { AccountConversionStepsRepository } from "../../../src/accounts/repositories/account-conversion-steps.repository";
import { PpcLegacyRepository } from "../../../src/accounts/repositories/account-ppc-legacy.repository";
import { PpcLegacy } from "../../../src/accounts/entities/account-ppc-legacy.entity";
import { AccountTagRelationService } from "../../../src/accounts/services/account-tag-relation.service";
import { CampaignsObjectiveService } from "../../../src/campaigns/services/campaigns-objective.service";
import { ConversionSteps } from "../../../src/accounts/entities/conversion-steps.entity";
import { mockAccount } from "../../../src/common/mocks/invoice.data.mock";
import { CurrencyService } from "../../../src/common/resources/currencies";
import { Deal } from "../../../src/deals/entities/deal.entity";
import { UpdateAccountSettingsSalesDto } from "../../../src/accounts/dto/update-account-settings-sales.dto";
import { CampaignsAccountsSettingsService } from "../../../src/campaigns/services/campaign-account-settings.service";
import { CampaignSettingsRepository } from "../../../src/campaigns/repositories/campaign-settings.repository";
import { CampaignRepository } from "../../../src/campaigns/repositories/campaign.repository";
import { UpdateCampaignSettingDto } from "../../../src/campaigns/dto/update-campaigns-setting.dto";
import { UpdateAccountDto } from "../../../src/accounts/dto/update-account.dto";
import { AccountOwnersService } from "../../../src/accounts/services/account-owners.service";
import { UserRepository } from "../../../src/users/repositories/user.repository";
import { Users } from "../../../src/users/entities/user.entity";
import { UserMainType } from "../../../src/auth/resources/role.enum";

describe("AccountService", () => {
  let service: AccountService;
  let repository: AccountsRepositoryMock;
  let manager: EntityManager;
  let dealService: DealsService;
  let filtersService: CampaignFiltersService;
  let http: CustomAxiosAdapter;
  let dataSourceMock: Partial<DataSource>;
  let queryRunner: QueryRunner;
  let metrics: Metrics;
  let settingsService: AccountSettingsService;
  let accountRepository: AccountRepository;
  let accountTagRelationService: AccountTagRelationService;
  let campaignObjectiveService: CampaignsObjectiveService;
  let userPrivilegeRepository: UserPrivilegeRepository;
  let accountConversionStepsRepository: AccountConversionStepsRepository;
  let campaignsAccountsSettingsService: CampaignsAccountsSettingsService;
  let accountOwnersService: AccountOwnersService;
  let userRepository: UserRepository;

  beforeEach(async () => {
    queryRunner = {
      connect: jest.fn().mockResolvedValue(undefined),
    } as unknown as QueryRunner;

    dataSourceMock = {
      createQueryRunner: jest.fn(() => ({
        connect: jest.fn().mockResolvedValue({}),
        startTransaction: jest.fn().mockResolvedValue({}),
        commitTransaction: jest.fn().mockResolvedValue({}),
        rollbackTransaction: jest.fn().mockResolvedValue({}),
        release: jest.fn().mockResolvedValue({}),
      })) as any,
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        // setting instances of the required providers
        AccountService,
        UserMockService,
        AccountRepository,
        EntityManager,
        CampaignsAccountsSettingsService,
        {
          provide: UserRepository,
          useValue: {
            findOneByEmail: jest.fn(),
          },
        },
        {
          provide: AccountOwnersService,
          useValue: {
            updateAccountOwners: jest.fn(),
          },
        },
        {
          provide: CampaignRepository,
          useValue: {
            findBy: jest.fn(),
          },
        },
        {
          provide: CampaignSettingsRepository,
          useValue: {
            update: jest.fn(),
            create: jest.fn(),
          },
        },
        {
          provide: UserPrivilegeRepository,
          useFactory: userPrivilegeRepositoryMockFactory,
        },
        {
          provide: DataSource,
          useValue: dataSourceMock,
        },
        Accounts,
        {
          provide: "AccountsRepository", //Repository token
          useClass: AccountsRepositoryMock,
          useValue: {
            subscribeToCampaignChanges: jest.fn(),
            findBy: jest.fn(),
            autoSuggestAccountAgencyNames: jest.fn(),
            findAccountsToSendToRedis: jest.fn(),
            isAutoCampaignAccount: jest.fn(),
            setEntityManagerInstance: jest.fn(),
          }, // Using mock instead of actual repository
        },
        {
          provide: DealsService,
          useValue: {
            findOneWithFilters: jest.fn(),
            setHubspotAccessKey: jest.fn(),
            getDeal: jest.fn(),
            getOwnersByEmail: jest.fn(),
            updateDealInHubSpot: jest.fn(),
          },
        },
        {
          provide: CampaignFiltersService,
          useValue: {
            filtersService,
            getCampaignFiltersByCampaignId: jest.fn(),
            reorderCampaignsByFilters: jest.fn(),
          },
        },
        {
          provide: AccountSettingsService,
          useValue: {
            getAccountSettings: jest.fn(),
            update: jest.fn(),
          },
        },
        {
          provide: CustomAxiosAdapter,
          useValue: {
            post: jest.fn(),
            get: jest.fn(),
            http,
          },
        },
        PrivacyService,
        {
          provide: Metrics,
          useValue: {
            initializeMetrics: jest.fn(),
            setMeter: jest.fn(),
            getMeter: jest.fn(),
            createCounter: jest.fn(),
            createObservableGauge: jest.fn(),
            addObservable: jest.fn(),
            addValueToCounter: jest.fn(),
            createHistogram: jest.fn(),
            startHistogramMeasure: jest.fn(),
            finishHistogramMeasure: jest.fn(),
          },
        },
        {
          provide: MetricsService,
          useValue: {
            setupMetricsVariables: jest.fn(),
          },
        },
        {
          provide: AccountConversionStepsRepository,
          useValue: {
            // Mock implementation if needed
          },
        },
        {
          provide: PpcLegacyRepository,
          useValue: {
            // Mock implementation if needed
            findOneByEmpcode: jest.fn(),
          },
        },
        {
          provide: AccountTagRelationService,
          useValue: {
            // Mock implementation if needed
            updateAccountTags: jest.fn(),
          },
        },
        {
          provide: CampaignsObjectiveService,
          useValue: {
            // Mock implementation if needed
            updateCampaignsConversionTypesByAccount: jest.fn(),
            setCampaignObjectiveByAccount: jest.fn(),
          },
        },
        {
          provide: AccountConversionStepsRepository,
          useValue: {
            deleteByAccountId: jest.fn(),
            createStep: jest.fn(),
          },
        },
        {
          provide: CurrencyService,
          useValue: {
            getCurrencies: jest.fn(),
            getCurrencyRate: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<AccountService>(AccountService);
    repository = module.get<AccountsRepositoryMock>(AccountRepository); // Get the mock
    dealService = module.get<DealsService>(DealsService);
    http = module.get<CustomAxiosAdapter>(CustomAxiosAdapter);
    metrics = module.get<Metrics>(Metrics);
    filtersService = module.get<CampaignFiltersService>(CampaignFiltersService);
    accountRepository = module.get<AccountRepository>(AccountRepository);
    settingsService = module.get<AccountSettingsService>(AccountSettingsService);
    accountTagRelationService = module.get<AccountTagRelationService>(AccountTagRelationService);
    campaignObjectiveService = module.get<CampaignsObjectiveService>(CampaignsObjectiveService);
    userPrivilegeRepository = module.get<UserPrivilegeRepository>(UserPrivilegeRepository);
    accountConversionStepsRepository = module.get<AccountConversionStepsRepository>(
      AccountConversionStepsRepository,
    );
    campaignsAccountsSettingsService = module.get<CampaignsAccountsSettingsService>(
      CampaignsAccountsSettingsService,
    );
    accountOwnersService = module.get<AccountOwnersService>(AccountOwnersService);
    userRepository = module.get<UserRepository>(UserRepository);
    manager = module.get<EntityManager>(EntityManager);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe(":id/role", () => {
    it("should create and connect a new query runner", async () => {
      jest.spyOn(dataSourceMock, "createQueryRunner").mockReturnValue(queryRunner);
      const result = await service.initQueryRunner();

      expect(dataSourceMock.createQueryRunner).toHaveBeenCalledTimes(1);
      expect(queryRunner.connect).toHaveBeenCalledTimes(1);
      expect(result).toBe(queryRunner);
    });
    // Test for successful user registration
    it("get role for a user id account id combination - meant to be called from a secure backend", async () => {
      const userId = 23;
      const accountId = 1;

      const addValueToCounterMock = jest
        .spyOn(metrics, "addValueToCounter")
        .mockImplementation(() => {});

      const startHistogramMeasureMock = jest.spyOn(metrics, "startHistogramMeasure");
      const finishHistogramMeasureMock = jest.spyOn(metrics, "finishHistogramMeasure");

      const result = await service.findAccountRoleByUser(userId, accountId);
      expect(result?.accountId).toBe(accountId);
      expect(result?.userId).toBe(userId);
      expect(result?.privilege).toBe("admin");
      expect(addValueToCounterMock).toHaveBeenCalledWith(
        service["statsCounters"]["employers.accounts.find_account_role_by_user.request"],
        1,
      );
      expect(startHistogramMeasureMock).toHaveBeenCalled();
      expect(finishHistogramMeasureMock).toHaveBeenCalled();
    });

    it("get all accounts for a user-account", async () => {
      const userId = 23;

      const addValueToCounterMock = jest
        .spyOn(metrics, "addValueToCounter")
        .mockImplementation(() => {});
      const startHistogramMeasureMock = jest.spyOn(metrics, "startHistogramMeasure");
      const finishHistogramMeasureMock = jest.spyOn(metrics, "finishHistogramMeasure");

      (userPrivilegeRepository.findAccountIdsByUser as jest.Mock).mockResolvedValue([
        { id: 1, companyLabel: "companyLabel1", feedcode: "feedcode1" },
        { id: 2, companyLabel: "companyLabel2", feedcode: "feedcode2" },
      ]);

      const result = await service.findAllByUser(userId);
      expect(result?.length).toBe(2);
      expect(result?.at(0)?.feedcode).toBe("feedcode1");
      expect(result?.at(0)?.companyLabel).toBe("companyLabel1");
      expect(addValueToCounterMock).toHaveBeenCalledWith(
        service["statsCounters"]["employers.accounts.find_all_by_user.request"],
        1,
      );
      expect(startHistogramMeasureMock).toHaveBeenCalled();
      expect(finishHistogramMeasureMock).toHaveBeenCalled();
    });
  });

  // Testing service is up
  it("should be defined", () => {
    expect(service).toBeDefined();
  });

  //Testing methods definition
  it("should have a method called createAccount", () => {
    expect(service.create).toBeDefined(); // Check the method to exist.
  });

  // Testing accounts creation
  it("should create an account", async () => {
    const accountData = accountNewDataMock; //from the accounts.data.mock
    manager = {} as EntityManager;

    // Simulate value returned by createAccount
    jest.spyOn(dealService, "findOneWithFilters").mockResolvedValue({ id: 123 } as Deal);
    jest.spyOn(repository, "create").mockResolvedValue(accountData);
    const result = await service.create(accountData, manager);
    expect(result).toEqual(accountData); // check if the expected value is as defined
    expect(repository.create).toHaveBeenCalledWith(accountData, manager); // Check whether method is called with the account data
  });

  it("should return all accounts without campaigns when loadCampaigns is false", async () => {
    // Simulate returned value by getAccounts
    jest.spyOn(repository, "findAll").mockResolvedValue(accountsListMock);

    const addValueToCounterMock = jest
      .spyOn(metrics, "addValueToCounter")
      .mockImplementation(() => {});
    const startHistogramMeasureMock = jest.spyOn(metrics, "startHistogramMeasure");
    const finishHistogramMeasureMock = jest.spyOn(metrics, "finishHistogramMeasure");

    const result = await service.findAll(false, false); // Pass false for loadCampaigns

    expect(result).toEqual(accountsListMock); // check if the expected value is as defined
    expect(repository.findAll).toHaveBeenCalledWith(false, false); // check if the getAccounts is called with false
    expect(addValueToCounterMock).toHaveBeenCalledWith(
      service["statsCounters"]["employers.accounts.find_all.request"],
      1,
    );
    expect(startHistogramMeasureMock).toHaveBeenCalled();
    expect(finishHistogramMeasureMock).toHaveBeenCalled();
  });

  it("should return all accounts with campaigns when loadCampaigns is true", async () => {
    // Simulate returned value by getAccounts
    jest.spyOn(repository, "findAll").mockResolvedValue([accounts] as any);

    const result = await service.findAll(true, true); // Pass true for loadCampaigns

    expect(result).toEqual([accounts]);
    expect(repository.findAll).toHaveBeenCalledWith(true, true); // check if the getAccounts is called with true
  });

  it("should return only one account per ID when relations are empty", async () => {
    const accountId = 1;
    const accountData = accountCreateResponseDataMock; //from the accounts.data.mock
    // Simulate returned value by findOne
    jest.spyOn(repository, "findOne").mockResolvedValue(accountData);

    const addValueToCounterMock = jest
      .spyOn(metrics, "addValueToCounter")
      .mockImplementation(() => {});
    const startHistogramMeasureMock = jest.spyOn(metrics, "startHistogramMeasure");
    const finishHistogramMeasureMock = jest.spyOn(metrics, "finishHistogramMeasure");

    const result = await service.findOne(accountId);
    expect(result).toEqual(accountData); // check if the expected value is as defined
    expect(repository.findOne).toHaveBeenCalledWith(accountId, undefined); // check if the findOne is called
    expect(addValueToCounterMock).toHaveBeenCalledWith(
      service["statsCounters"]["employers.accounts.find_one.request"],
      1,
    );
    expect(startHistogramMeasureMock).toHaveBeenCalled();
    expect(finishHistogramMeasureMock).toHaveBeenCalled();
  });

  it("should update an account by ID", async () => {
    const accountId = 1;

    const accountData = { id: accountId, ...accountNewDataMock }; //from the accounts.data.mock
    jest.spyOn(service.accountSubject, "next");
    jest.spyOn(dealService, "findOneWithFilters").mockResolvedValue({ id: 123 } as Deal);
    jest.spyOn(repository, "update").mockResolvedValue(accountData);
    const result = await service.update(accountData);
    expect(result).toEqual(accountData); // check if the expected value is as defined
  });

  describe("getAccountHubspotDealIDByAccountID", () => {
    it("should return hubspotDealId when findOne is successful", async () => {
      const accountId = 1;
      const accountData = accountCreateResponseDataMock; //from the accounts.data.mock

      jest.spyOn(repository, "findOne").mockResolvedValue(accountData);
      const result = await service.findOne(accountId);
      expect(result).toEqual(accountData); // check if the expected value is as defined
      expect(repository.findOne).toHaveBeenCalledWith(accountId, undefined); // check if the findOne is called
      const hubspotDealId = 1;
      const result2 = await service.getAccountHubspotDealIDByAccountID(accountId);

      expect(result2).toEqual(hubspotDealId);
    });
    it("should throw an error if id is empty", async () => {
      const accountId: number = -1; // Simulamos un id vacío

      await expect(service.getAccountHubspotDealIDByAccountID(accountId)).rejects.toThrow(
        "id is empty",
      );
    });
    it("should throw an error if an error occurs during account fetching", async () => {
      const accountId: number = 123; // Supongamos un ID válido para la cuenta

      jest.spyOn(service, "findOne").mockRejectedValue(new Error("Error: Sample error message"));

      await expect(service.getAccountHubspotDealIDByAccountID(accountId)).rejects.toThrow(
        "Error fetching account ID: Error: Error: Sample error message",
      );
    });
  });

  describe("registerAccountsToRedis", () => {
    it("should register accounts to Redis", async () => {
      // Mock de findAll
      const findAllMock = jest
        .spyOn(service, "findAccountsToSendToRedis")
        .mockResolvedValueOnce([]);
      // Mock groupAccountsByFeedcode
      const groupAccountsByFeedcodeMock = jest
        .spyOn(service, "groupAccountsByFeedcode")
        .mockResolvedValueOnce([]);
      // Mock  storeDataInRedis
      const storeDataInRedisMock = jest.spyOn(service, "storeDataInRedis").mockResolvedValueOnce();
      await service.registerAccountsToRedis();
      expect(findAllMock).toHaveBeenCalled();
      expect(groupAccountsByFeedcodeMock).toHaveBeenCalled();
      expect(storeDataInRedisMock).toHaveBeenCalled();
    });

    it("should register accounts to Redis successfully", async () => {
      const mockAccounts = accountsListMock;

      jest.spyOn(service, "findAccountsToSendToRedis").mockResolvedValue(accountsListMock);
      jest.spyOn(service, "groupAccountsByFeedcode").mockResolvedValue(accountsListMock);
      jest.spyOn(service, "storeDataInRedis").mockResolvedValue();

      await service.registerAccountsToRedis();

      expect(service.findAccountsToSendToRedis).toHaveBeenCalledWith();

      expect(service.storeDataInRedis).toHaveBeenCalledWith(mockAccounts);
    });

    it("should handle errors correctly", async () => {
      jest
        .spyOn(service, "findAccountsToSendToRedis")
        .mockRejectedValue(new Error("Database error"));
      jest.spyOn(service, "groupAccountsByFeedcode").mockResolvedValue([]);
      jest.spyOn(service, "storeDataInRedis").mockResolvedValue();

      await expect(service.registerAccountsToRedis()).rejects.toThrow("Database error");

      expect(service.findAccountsToSendToRedis).toHaveBeenCalledWith();

      expect(service.groupAccountsByFeedcode).not.toHaveBeenCalled();
      expect(service.storeDataInRedis).not.toHaveBeenCalled();
    });
  });

  describe("groupAccountsByFeedcode", () => {
    it("should return empty array when no accounts are provided", async () => {
      const result = await service.groupAccountsByFeedcode([]);
      expect(result).toEqual([]);
    });

    it("should not include accounts without feedcodes", async () => {
      const accounts: Accounts[] = mockAccountsEntity();

      jest.spyOn(service, "getPpcDiscountsFromLegacy").mockResolvedValue([]);

      const result = await service.groupAccountsByFeedcode(accounts);
      expect(result).toHaveLength(1);
      expect(result[0].feedcode).toEqual("ABC123");
    });

    it("should set ppcDiscount to 0 if no discount is found", async () => {
      const accounts: Accounts[] = mockAccountsEntity();

      jest.spyOn(service, "getPpcDiscountsFromLegacy").mockResolvedValue([]);
      (filtersService.reorderCampaignsByFilters as jest.Mock).mockReturnValue([]);

      const result = await service.groupAccountsByFeedcode(accounts);
      expect(result[0].ppcDiscount).toEqual(0.0);
    });

    it("should correctly set ppcDiscount based on feedcode", async () => {
      const accounts: Accounts[] = mockAccountsEntity();
      // Mocking discount data with only the necessary property
      const mockDiscountData = new PpcLegacy();
      mockDiscountData.empcode = "ABC123";
      mockDiscountData.billing_discount = "10";

      jest.spyOn(service, "getPpcDiscountsFromLegacy").mockResolvedValue([mockDiscountData]);
      (filtersService.reorderCampaignsByFilters as jest.Mock).mockReturnValue([]);

      const result = await service.groupAccountsByFeedcode(accounts);
      expect(result[0].ppcDiscount).toEqual(10);
    });

    it("should reorder campaigns", async () => {
      const accounts: Accounts[] = mockAccountsEntity();
      const mockDiscountData = new PpcLegacy();
      mockDiscountData.empcode = "ABC123";
      mockDiscountData.billing_discount = "10";
      const reorderedCampaigns = [{ id: 2 }, { id: 1 }];

      jest.spyOn(service, "getPpcDiscountsFromLegacy").mockResolvedValue([mockDiscountData]);
      (filtersService.reorderCampaignsByFilters as jest.Mock).mockReturnValue(reorderedCampaigns);

      const result = await service.groupAccountsByFeedcode(accounts);
      expect(result[0].campaigns).toEqual(reorderedCampaigns);
    });
  });
  describe("storeDataInRedis", () => {
    it("should store data in Redis successfully", async () => {
      const mockData = mockAccountsEntity();

      const result = await service.storeDataInRedis(mockData);

      expect(result).toEqual(undefined);
    });
    it("should throw HttpException on failure to store data", async () => {
      await expect(service.storeDataInRedis([])).rejects.toThrow(HttpException);
    });

    it("should process accounts and campaigns correctly", async () => {
      const mockData = mockAccountsEntity();
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      jest.spyOn(http, "post").mockImplementation((url, data) => of({}).toPromise());

      await service.storeDataInRedis(mockData);

      expect(http.post).toHaveBeenCalledTimes(1);
    });

    it("should throw an exception for empty data", async () => {
      await expect(service.storeDataInRedis([])).rejects.toThrow(
        new HttpException("Failed to store data", HttpStatus.BAD_REQUEST),
      );
    });

    it("should exclude campaigns with empty filters", async () => {
      const mockData = mockAccountsEntity();
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      jest.spyOn(http, "post").mockImplementation((url, data) => of({}).toPromise());

      await service.storeDataInRedis(mockData);

      expect(http.post).toHaveBeenCalledTimes(1);
    });

    it("should handle errors properly and log them", async () => {
      const mockData = mockAccountsEntity();
      jest.spyOn(http, "post").mockImplementation(() => {
        throw new Error("Network error");
      });
      const consoleSpy = jest.spyOn(Logger, "error").mockImplementation();
      //const consoleErrorSpy = jest.spyOn(Logger, "error").mockImplementation();

      await service.storeDataInRedis(mockData);

      expect(consoleSpy).toHaveBeenCalledWith(
        "[AccountService] [storeDataInRedis] Error processing account. Network error",
      );
    });

    it("should filter out campaigns with empty filters and increase the counter", async () => {
      jest.spyOn(http, "post").mockReturnValue(of({}).toPromise());

      const data = [
        {
          id: 1002292,
          feedcode: "agencesynergie",
          accountCurrency: "USD",
          campaigns: [
            {
              id: 85266,
              campaignName: "Test Campaign",
              ppc: 2.5,
              cpcModifier: 1.2,
              sourcePpc: 1,
              sponsored: 1,
              type: "by_feedcode",
              applyType: "jobredirect",
              priority: 100,
              country: "US",
              status: "validating",
              campaignFilters: [],
            },
          ],
        },
      ];

      const spyHasEmptyFilters = jest.spyOn(service, "hasEmptyFilters");

      await service.storeDataInRedis(data);

      expect(spyHasEmptyFilters).toHaveBeenCalledWith(data[0].campaigns[0]);

      expect(http.post).toHaveBeenCalledWith(
        `${process.env.URL_JOB_PROCESING}cache-account`,
        data[0],
      );

      spyHasEmptyFilters.mockRestore();
    });

    it("should skip processing the account if all campaigns have empty filters and increase the counter", async () => {
      jest.spyOn(http, "post").mockReturnValue(of({}).toPromise());

      const data = [
        {
          id: 1002292,
          feedcode: "agencesynergie",
          accountCurrency: "USD",
          campaigns: [
            {
              id: 85266,
              campaignName: "Test Campaign",
              ppc: 2.5,
              cpcModifier: 1.2,
              sourcePpc: 1,
              sponsored: 1,
              type: "by_feedcode",
              applyType: "jobredirect",
              priority: 100,
              country: "US",
              status: "validating",
              campaignFilters: {},
            },
          ],
        },
      ];
      data[0].campaigns[0].campaignFilters = JSON.stringify(data[0].campaigns[0].campaignFilters);
      jest.spyOn(Logger, "log").mockImplementation(); // mock console.log to prevent actual logging

      await service.storeDataInRedis(data);

      expect(http.post).not.toHaveBeenCalled();
      expect(Logger.log).toHaveBeenCalledWith(
        `[AccountService] [storeDataInRedis] Skipped account with ID: 1002292 due to no valid campaigns.`,
      );
    });
  });

  describe("subscribeToAccountChanges", () => {
    it("should return an observable", () => {
      const observable = service.subscribeToAccountChanges();

      // Verificar si el observable es una instancia de Observable de RxJS
      expect(observable).toBeDefined();
      expect(observable.subscribe).toBeDefined();
      expect(typeof observable.subscribe).toBe("function");
    });
  });

  describe("findBy", () => {
    it("should find accounts by a specified field and value", async () => {
      // Mock data
      const mockAccounts: Accounts[] = accountsListMock;
      const field = "id";
      const value = 1;

      // Mock repository method to return mock data
      jest.spyOn(repository, "findBy").mockResolvedValue(mockAccounts as any);

      // Call the method with the specified field and value
      const results = await service.findBy(field, value, false);

      // Assert that the repository method was called with the correct arguments
      expect(repository.findBy).toHaveBeenCalledWith({ [field]: value }, false);

      // Assert that the results match the mock data
      expect(results).toEqual(mockAccounts);
    });

    it("should throw a BadRequestException when both field and value are undefined", async () => {
      // Call the method with undefined field and value
      const field = "id";
      const value = 1;
      jest.spyOn(service, "findBy").mockRejectedValue(new BadRequestException());
      await expect(service.findBy(field, value)).rejects.toThrow(new BadRequestException());
    });

    it("should throw a NotFoundException when an Error is caught", async () => {
      // Mock repository method to throw an Error
      jest.spyOn(repository, "findBy").mockRejectedValue(new Error("Account not found"));

      // Call the method
      await expect(service.findBy("id", 1)).rejects.toThrow(HttpException);
    });

    it("should throw an InternalServerErrorException when an unknown error occurs", async () => {
      // Mock repository method to throw an unknown error
      jest.spyOn(repository, "findBy").mockRejectedValue("unknown error");

      // Call the method
      await expect(service.findBy("id", 1)).rejects.toThrow(HttpException);
    });
    it("should return a value of error", async () => {
      await expect(service.findBy("accountCurrency", undefined)).rejects.toThrowError(
        HttpException,
      );
    });
  });

  describe("search", () => {
    it("should call repository search with provided filters", async () => {
      // mockFilters containing the query params
      const AccountSearchDataMock = {
        country: "ca",
        accountType: AccountType.Post,
        companyLabel: "test",
        status: "active",
        owner: "<EMAIL>",
        limit: 10,
        from: 0,
        vipAccount: true,
        isAutoCampaign: true,
      };
      const mockFilters: SearchAccountDto = AccountSearchDataMock;

      // mockSearchedList containing the results of repository search
      const accountSearchedListDataMock = {
        total: 1,
        accounts: [
          {
            id: 1,
            ...accountsListMock,
          },
        ],
      };

      // Mock the response from the HTTP call
      jest.spyOn(http, "get").mockResolvedValue({
        account_budget: 0,
        account_spent: 0,
      });

      // mocked repository.search
      repository.search = jest.fn().mockResolvedValue(accountSearchedListDataMock);

      const addValueToCounterMock = jest
        .spyOn(metrics, "addValueToCounter")
        .mockImplementation(() => {});
      const startHistogramMeasureMock = jest.spyOn(metrics, "startHistogramMeasure");
      const finishHistogramMeasureMock = jest.spyOn(metrics, "finishHistogramMeasure");

      // executes the service.search and compares with mock data
      expect(await service.search(mockFilters, 123)).toEqual(accountSearchedListDataMock);
      expect(addValueToCounterMock).toHaveBeenCalledWith(
        service["statsCounters"]["employers.accounts.search.request"],
        1,
      );
      expect(startHistogramMeasureMock).toHaveBeenCalled();
      expect(finishHistogramMeasureMock).toHaveBeenCalled();
    });

    it("should throw an error if search fails", async () => {
      // mockFilters containing the query params
      const AccountSearchDataMock = {
        country: "ca",
        accountType: AccountType.Post,
        companyLabel: "test",
        status: "active",
        owner: "<EMAIL>",
        limit: 10,
        from: 0,
        vipAccount: true,
        isAutoCampaign: true,
      };
      const mockFilters: SearchAccountDto = AccountSearchDataMock;
      // error message
      const errorMessage = "Acounts not found";
      // Execute repository function
      jest
        .spyOn(repository, "search")
        .mockRejectedValue(new HttpException(errorMessage, HttpStatus.NOT_FOUND));
      // Search functio
      try {
        await service.search(mockFilters, 123);
      } catch (error: any) {
        expect(error.message).toEqual(errorMessage);
      }
    });
  });

  describe("autoSuggestAccountAgencyNames", () => {
    it("should get list of the agency names suggested", async () => {
      // Mock service method
      const response: string[] = [
        "pandologic agency",
        "white label agency",
        "reddot media agency ",
        "appcast agency",
      ];
      const mockService = jest
        .spyOn(repository, "autoSuggestAccountAgencyNames")
        .mockResolvedValue(response);

      const addValueToCounterMock = jest
        .spyOn(metrics, "addValueToCounter")
        .mockImplementation(() => {});
      const startHistogramMeasureMock = jest.spyOn(metrics, "startHistogramMeasure");
      const finishHistogramMeasureMock = jest.spyOn(metrics, "finishHistogramMeasure");

      // Execute controller method
      const result = await service.autoSuggestAccountAgencyNames("felipe");

      // Check if service method was called with correct parameters
      expect(mockService).toHaveBeenCalledWith("felipe");

      console.log("result", result);
      // Check if result matches the expected output
      expect(result).toEqual(response);
      expect(addValueToCounterMock).toHaveBeenCalledWith(
        service["statsCounters"]["employers.accounts.autosuggest_account_agency_names.request"],
        1,
      );
      expect(startHistogramMeasureMock).toHaveBeenCalled();
      expect(finishHistogramMeasureMock).toHaveBeenCalled();
    });
  });
  describe("getBudgetReachedInfo", () => {
    it("should return the expected budget reached info", async () => {
      // Mock data
      const mockAccounts = [
        {
          account: {
            campaigns: [
              {
                status: "campaign_monthly_budget_reached",
                campaignsBudget: { budgetType: "monthly" },
                dateEnd: new Date().toISOString(),
                campaignID: 123,
                campaignName: "Test Campaign",
                country: "US",
              },
            ],
            userPrivileges: [
              {
                privilege: "admin",
                user: {
                  email: "<EMAIL>",
                  id: 1,
                  user_token: "token123",
                },
              },
            ],
            language: "en",
          },
        },
      ];

      const mockEmailDataArray = [
        {
          user_id: 1,
          user_token: "token123",
          email: "<EMAIL>",
          language: "en",
          campaignId: 123,
          campaignName: "Test Campaign",
          country: "US",
        },
      ];

      // Mock the accountSettingsService and buildBudgetReachedInfo method
      jest.spyOn(settingsService, "getAccountSettings").mockResolvedValue(mockAccounts as any);
      jest.spyOn(service, "buildBudgetReachedInfo").mockResolvedValue(mockEmailDataArray);

      const result = await service.getBudgetReachedInfo();

      expect(settingsService.getAccountSettings).toHaveBeenCalledWith(
        { sendBudgetEmail: true },
        [
          "account",
          "account.campaigns",
          "account.campaigns.campaignsBudget",
          "account.accountsBudget",
          "account.userPrivileges",
          "account.userPrivileges.user",
        ],
        true,
      );

      expect(service.buildBudgetReachedInfo).toHaveBeenCalledWith(mockAccounts);
      expect(result).toEqual(mockEmailDataArray);
    });

    it("should return an empty array when no campaigns match criteria", async () => {
      const mockAccounts = [
        {
          account: {
            campaigns: [
              {
                status: "active", // Not 'campaign_monthly_budget_reached'
                campaignsBudget: { budgetType: "monthly" },
                dateEnd: new Date(new Date().getTime() + 3 * 24 * 60 * 60 * 1000).toISOString(),
                campaignID: 123,
                campaignName: "Test Campaign",
                country: "US",
              },
            ],
            userPrivileges: [
              {
                privilege: "admin",
                user: {
                  email: "<EMAIL>",
                  id: 1,
                  user_token: "token123",
                },
              },
            ],
            language: "en",
          },
        },
      ];

      jest.spyOn(settingsService, "getAccountSettings").mockResolvedValue(mockAccounts as any);
      jest.spyOn(service, "buildBudgetReachedInfo").mockResolvedValue([]);

      const result = await service.getBudgetReachedInfo();

      expect(result).toEqual([]);
    });

    it("should return an empty array when campaign end date is less than two days away", async () => {
      const mockAccounts = [
        {
          account: {
            campaigns: [
              {
                status: "campaign_monthly_budget_reached",
                campaignsBudget: { budgetType: "monthly" },
                dateEnd: new Date().toISOString(), // End date is today
                campaignID: 123,
                campaignName: "Test Campaign",
                country: "US",
              },
            ],
            userPrivileges: [
              {
                privilege: "admin",
                user: {
                  email: "<EMAIL>",
                  id: 1,
                  user_token: "token123",
                },
              },
            ],
            language: "en",
          },
        },
      ];

      jest.spyOn(settingsService, "getAccountSettings").mockResolvedValue(mockAccounts as any);
      jest.spyOn(service, "buildBudgetReachedInfo").mockResolvedValue([]);

      const result = await service.getBudgetReachedInfo();

      expect(result).toEqual([]);
    });

    it("should return an empty array when there are no admin users", async () => {
      const mockAccounts = [
        {
          account: {
            campaigns: [
              {
                status: "campaign_monthly_budget_reached",
                campaignsBudget: { budgetType: "monthly" },
                dateEnd: new Date(new Date().getTime() + 3 * 24 * 60 * 60 * 1000).toISOString(),
                campaignID: 123,
                campaignName: "Test Campaign",
                country: "US",
              },
            ],
            userPrivileges: [
              {
                privilege: "user", // Not 'admin'
                user: {
                  email: "<EMAIL>",
                  id: 1,
                  user_token: "token123",
                },
              },
            ],
            language: "en",
          },
        },
      ];

      jest.spyOn(settingsService, "getAccountSettings").mockResolvedValue(mockAccounts as any);
      jest.spyOn(service, "buildBudgetReachedInfo").mockResolvedValue([]);

      const result = await service.getBudgetReachedInfo();

      expect(result).toEqual([]);
    });

    it("should handle admin users with null email correctly", async () => {
      const mockAccounts = [
        {
          account: {
            campaigns: [
              {
                status: "campaign_monthly_budget_reached",
                campaignsBudget: { budgetType: "monthly" },
                dateEnd: new Date(new Date().getTime() + 3 * 24 * 60 * 60 * 1000).toISOString(),
                campaignID: 123,
                campaignName: "Test Campaign",
                country: "US",
              },
            ],
            userPrivileges: [
              {
                privilege: "admin",
                user: {
                  email: null, // Email is null
                  id: 1,
                  user_token: "token123",
                },
              },
            ],
            language: "en",
          },
        },
      ];

      jest.spyOn(settingsService, "getAccountSettings").mockResolvedValue(mockAccounts as any);
      jest.spyOn(service, "buildBudgetReachedInfo").mockResolvedValue([]);

      const result = await service.getBudgetReachedInfo();

      expect(result).toEqual([]);
    });
  });

  describe("sendEmailBudgetReached", () => {
    it("should send emails successfully", async () => {
      // Mock data
      const mockUsers = [
        {
          id: 1,
          email: "<EMAIL>",
        },
        {
          id: 2,
          email: "<EMAIL>",
        },
      ];

      // Mock the getBudgetReachedInfo method
      jest.spyOn(service, "getBudgetReachedInfo").mockResolvedValue(mockUsers);

      // Mock HTTP service to simulate successful email send
      jest.spyOn(http, "post").mockResolvedValue({});
      const addValueToCounterMock = jest
        .spyOn(metrics, "addValueToCounter")
        .mockImplementation(() => {});
      const startHistogramMeasureMock = jest.spyOn(metrics, "startHistogramMeasure");
      const finishHistogramMeasureMock = jest.spyOn(metrics, "finishHistogramMeasure");

      await service.sendEmailBudgetReached();

      // Verify HTTP post calls
      expect(http.post).toHaveBeenCalledTimes(mockUsers.length);
      mockUsers.forEach((user) => {
        expect(http.post).toHaveBeenCalledWith(
          `${process.env.URL_SEND_EMAILS}`,
          expect.objectContaining({
            email_address: user.email, // Expect the dynamic email address here
            template_id: 18,
            data: expect.objectContaining({
              user_id: user.id,
            }),
          }),
        );
      });
      expect(addValueToCounterMock).toHaveBeenCalledWith(
        service["statsCounters"]["employers.accounts.send_email_budget_reached.request"],
        1,
      );
      expect(startHistogramMeasureMock).toHaveBeenCalled();
      expect(finishHistogramMeasureMock).toHaveBeenCalled();
    });

    it("should handle errors during email sending", async () => {
      // Mock data
      const mockUsers = [
        {
          id: 1,
          email: "<EMAIL>",
        },
      ];

      // Mock the getBudgetReachedInfo method
      jest.spyOn(service, "getBudgetReachedInfo").mockResolvedValue(mockUsers);

      // Mock HTTP service to simulate error
      jest.spyOn(http, "post").mockRejectedValue("Email send failed");

      // Spy on console.error to check if errors are logged
      const consoleErrorSpy = jest.spyOn(console, "error").mockImplementation();

      await service.sendEmailBudgetReached();

      // Verify HTTP post calls
      expect(http.post).toHaveBeenCalledTimes(1);
      expect(http.post).toHaveBeenCalledWith(`${process.env.URL_SEND_EMAILS}`, expect.any(Object));

      // Verify error was logged
      expect(consoleErrorSpy).toHaveBeenCalled();
      expect(consoleErrorSpy.mock.calls[0][0]).toBe("Email send failed");

      consoleErrorSpy.mockRestore();
    });
  });

  describe("buildBudgetReachedInfo", () => {
    it("should handle multiple accounts with different scenarios", async () => {
      const mockAccounts = [
        {
          account: {
            campaigns: [
              {
                status: "campaign_monthly_budget_reached",
                campaignsBudget: { budgetType: "monthly", hasPacing: 0 },
                dateEnd: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(),
                id: 123,
                campaignName: "Test Campaign 1",
                country: "US",
              },
            ],
            userPrivileges: [
              {
                privilege: "admin",
                user: {
                  email: "<EMAIL>",
                  id: 1,
                  user_token: "token123",
                },
              },
            ],
            language: "en",
          },
        },
        {
          account: {
            campaigns: [
              {
                status: "campaign_monthly_budget_reached",
                campaignsBudget: { budgetType: "monthly", hasPacing: 0 },
                dateEnd: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(),
                id: 456,
                campaignName: "Test Campaign 2",
                country: "UK",
              },
            ],
            userPrivileges: [
              {
                privilege: "admin",
                user: {
                  email: "<EMAIL>",
                  id: 2,
                  user_token: "token456",
                },
              },
            ],
            language: "fr",
          },
        },
      ] as unknown as AccountSettings[];

      jest
        .spyOn(privacy.cipher, "decrypt")
        .mockReturnValueOnce("<EMAIL>")
        .mockReturnValueOnce("<EMAIL>");

      const result = await service.buildBudgetReachedInfo(mockAccounts);

      expect(result).toHaveLength(2);
      expect(result).toEqual(
        expect.arrayContaining([
          {
            user_id: 1,
            email: "<EMAIL>",
            language: "en",
            campaignId: 123,
            campaignName: "Test Campaign 1",
            country: "US",
          },
          {
            user_id: 2,
            email: "<EMAIL>",
            language: "fr",
            campaignId: 456,
            campaignName: "Test Campaign 2",
            country: "UK",
          },
        ]),
      );

      expect(privacy.cipher.decrypt).toHaveBeenNthCalledWith(1, "<EMAIL>");
      expect(privacy.cipher.decrypt).toHaveBeenNthCalledWith(2, "<EMAIL>");
    });
    it("should build budget reached info with filtered campaigns and admin users", async () => {
      const mockAccounts = [
        {
          account: {
            campaigns: [
              {
                id: 123,
                status: "campaign_monthly_budget_reached",
                campaignsBudget: { budgetType: "monthly", hasPacing: 0 },
                dateEnd: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(),
                campaignName: "Test Campaign",
                country: "US",
              },
            ],
            userPrivileges: [
              {
                privilege: "admin",
                user: {
                  email: "<EMAIL>",
                  id: 1,
                  user_token: "token123",
                },
              },
            ],
            language: "en",
          },
        } as unknown as AccountSettings,
      ] as unknown as AccountSettings[];

      jest.spyOn(privacy.cipher, "decrypt").mockReturnValueOnce("<EMAIL>");

      const result = await service.buildBudgetReachedInfo(mockAccounts);

      expect(result).toEqual([
        {
          user_id: 1,
          email: "<EMAIL>",
          language: "en",
          campaignId: 123,
          campaignName: "Test Campaign",
          country: "US",
        },
      ]);

      expect(privacy.cipher.decrypt).toHaveBeenCalledWith("<EMAIL>");
    });

    it("should handle no matching campaigns", async () => {
      const mockAccounts = [
        {
          account: {
            campaigns: [
              {
                status: "active",
                campaignsBudget: { budgetType: "monthly", hasPacing: 0 },
                dateEnd: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(),
                campaignName: "Test Campaign",
                country: "US",
              },
            ],
            userPrivileges: [
              {
                privilege: "admin",
                user: {
                  email: "<EMAIL>",
                  id: 1,
                  user_token: "token123",
                },
              },
            ],
            language: "en",
          },
        },
      ] as unknown as AccountSettings[];

      const result = await service.buildBudgetReachedInfo(mockAccounts);

      expect(result).toEqual([]);
    });

    it("should handle no admin users", async () => {
      const mockAccounts = [
        {
          account: {
            campaigns: [
              {
                status: "campaign_monthly_budget_reached",
                campaignsBudget: { budgetType: "monthly", hasPacing: 0 },
                dateEnd: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(),
                campaignName: "Test Campaign",
                country: "US",
              },
            ],
            userPrivileges: [
              {
                privilege: "user",
                user: {
                  email: "<EMAIL>",
                  id: 1,
                  user_token: "token123",
                },
              },
            ],
            language: "en",
          },
        },
      ] as unknown as AccountSettings[];

      const result = await service.buildBudgetReachedInfo(mockAccounts);

      expect(result).toEqual([]);
    });

    it("should handle admin user with null email", async () => {
      const mockAccounts = [
        {
          account: {
            campaigns: [
              {
                status: "campaign_monthly_budget_reached",
                campaignsBudget: { budgetType: "monthly", hasPacing: 0 },
                dateEnd: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(),
                campaignName: "Test Campaign",
                country: "US",
              },
            ],
            userPrivileges: [
              {
                privilege: "admin",
                user: {
                  email: null,
                  id: 1,
                  user_token: "token123",
                },
              },
            ],
            language: "en",
          },
        },
      ] as unknown as AccountSettings[];

      const result = await service.buildBudgetReachedInfo(mockAccounts);

      expect(result).toEqual([]);
    });

    it("should handle campaigns with end dates less than or equal to 2 days", async () => {
      const mockAccounts = [
        {
          account: {
            campaigns: [
              {
                status: "campaign_monthly_budget_reached",
                campaignsBudget: { budgetType: "monthly", hasPacing: 0 },
                dateEnd: new Date(Date.now() + 1 * 24 * 60 * 60 * 1000).toISOString(),
                campaignName: "Test Campaign",
                country: "US",
              },
            ],
            userPrivileges: [
              {
                privilege: "admin",
                user: {
                  email: "<EMAIL>",
                  id: 1,
                  user_token: "token123",
                },
              },
            ],
            language: "en",
          },
        },
      ] as unknown as AccountSettings[];

      const result = await service.buildBudgetReachedInfo(mockAccounts);

      expect(result).toEqual([]);
    });
  });

  describe("findAccountsToSendToRedis", () => {
    it("should return accounts if found", async () => {
      const mockAccounts = accountsListMock;

      jest.spyOn(service, "findAccountsToSendToRedis").mockResolvedValue(accountsListMock);
      jest.spyOn(service, "groupAccountsByFeedcode").mockResolvedValue(accountsListMock);
      jest.spyOn(accountRepository, "findAccountsToSendToRedis").mockResolvedValue(mockAccounts);

      const result = await service.findAccountsToSendToRedis();
      expect(result).toEqual(mockAccounts);
    });

    it("should throw HttpException if no accounts are found", async () => {
      jest
        .spyOn(accountRepository, "findAccountsToSendToRedis")
        .mockResolvedValue(null as unknown as Accounts[]);

      await expect(service.findAccountsToSendToRedis()).rejects.toThrow(HttpException);
      await expect(service.findAccountsToSendToRedis()).rejects.toMatchObject({
        status: HttpStatus.NOT_FOUND,
      });
    });

    it("should log error and rethrow it", async () => {
      const error = new Error("Some error");
      jest.spyOn(accountRepository, "findAccountsToSendToRedis").mockRejectedValue(error);
      jest.spyOn(Logger, "error").mockImplementation(); // mock console.log to prevent actual logging

      await expect(service.findAccountsToSendToRedis()).rejects.toThrow("Some error");
      expect(Logger.error).toHaveBeenCalledWith(
        "[AccountService] [findAccountsToSendToRedis] Error message: Some error",
      );
    });
  });

  describe("accountOutOfRedisCache", () => {
    it("should delete account cache successfully", async () => {
      const feedcode = "test-feedcode";

      // Mock the http.post method to return a successful response
      //jest.spyOn(http, 'post').mockReturnValue(of(response));
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      jest.spyOn(http, "post").mockImplementation((url, data) => of({}).toPromise());
      jest.spyOn(Logger, "log").mockImplementation(); // mock console.log to prevent actual logging

      // Call the method
      const result = await service.accountOutOfRedisCache(feedcode);

      // Expectations
      expect(http.post).toHaveBeenCalledWith(
        `${process.env.URL_JOB_PROCESING}delete-cache-account`,
        { feedcode },
      );
      expect(result).toBe(true);
      expect(Logger.log).toHaveBeenCalledWith("ACCOUNT's REDIS CACHE DELETED");
    });

    it("should handle error when deleting account cache", async () => {
      const feedcode = "test-feedcode";
      const error = new Error("Failed to delete cache");

      // Mock the http.post method to throw an error
      jest.spyOn(http, "post").mockRejectedValue(error);

      // Mock console.log to prevent actual logging during tests
      jest.spyOn(Logger, "error").mockImplementation(); // mock console.log to prevent actual logging

      // Call the method and expect it to throw
      await expect(service.accountOutOfRedisCache(feedcode)).rejects.toThrow(error);

      // Expectations
      expect(http.post).toHaveBeenCalledWith(
        `${process.env.URL_JOB_PROCESING}delete-cache-account`,
        { feedcode },
      );
      expect(Logger.error).toHaveBeenCalledWith(
        `Error updating account's data in cache. Failed to delete cache`,
      );
    });

    it("should not make a request if feedcode is not provided", async () => {
      // Mock the http.post method
      const postSpy = jest.spyOn(http, "post");

      // Call the method with no feedcode
      const result = await service.accountOutOfRedisCache("");

      // Expectations
      expect(postSpy).not.toHaveBeenCalled();
      expect(result).toBeUndefined();
    });
  });
  describe("isAutoCampaignAccount", () => {
    it("should throw an HttpException if the account is auto-campaign", async () => {
      // Mock repository to return true, meaning the account is auto-campaign
      jest.spyOn(accountRepository, "isAutoCampaignAccount").mockResolvedValue(true);
      await expect(service.isAutoCampaignAccount(123)).rejects.toThrow(
        new HttpException(
          "Auto campaign accounts are forbidden from manually creating/updating campaigns",
          HttpStatus.UNAUTHORIZED,
        ),
      );

      expect(accountRepository.isAutoCampaignAccount).toHaveBeenCalledWith(123);
    });

    it("should pass if the account is not auto-campaign", async () => {
      // Mock repository to return false, meaning the account is not auto-campaign
      jest.spyOn(accountRepository, "isAutoCampaignAccount").mockResolvedValue(false);
      await expect(service.isAutoCampaignAccount(123)).resolves.not.toThrow();

      expect(accountRepository.isAutoCampaignAccount).toHaveBeenCalledWith(123);
    });
  });

  describe("updateFeatureTags", () => {
    it("should call updateAccountTags and updateAccountSettings", async () => {
      // Arrange
      const accountId = 123;
      const data = {
        multistepTraking: "yes",
        flagUpdateConversionTypes: 1,
        conversionTypeAccountLevel: ConversionTypesFeaturesTags.applystart,
        updateCampaignsObjective: CampaignObjective.no_objective,
      };
      jest.spyOn(accountRepository, "findOne").mockResolvedValue(mockAccount);
      jest.spyOn(service, "updateAccountTags").mockResolvedValue("Done");
      jest.spyOn(service, "setUpConversionSteps").mockResolvedValue("Done");
      jest.spyOn(service, "updateMultistepTracking").mockResolvedValue("Done");
      jest
        .spyOn(campaignObjectiveService, "updateCampaignsConversionTypesByAccount")
        .mockResolvedValue({
          success: true,
        });
      jest.spyOn(campaignObjectiveService, "setCampaignObjectiveByAccount").mockResolvedValue({
        success: true,
      });

      // Act
      const result = await service.updateFeatureTags(accountId, data, 80010);

      expect(result).toEqual({
        result: {
          updateAccountTags: "Done",
          updateConversionSteps: "Done",
          updateAccountSettings: "Done",
          updateConversionTypes: "Done",
          updateCampaignObjective: "",
        },
      });
    });

    it("should return an error for campaign conversion types", async () => {
      // Arrange
      const accountId = 123;
      const data = {
        multistepTraking: "yes",
        flagUpdateConversionTypes: 1,
        conversionTypeAccountLevel: ConversionTypesFeaturesTags.applystart,
        Pixel: "",
        Postback: "",
      };

      jest.spyOn(accountRepository, "findOne").mockResolvedValue(mockAccount);
      jest
        .spyOn(campaignObjectiveService, "updateCampaignsConversionTypesByAccount")
        .mockResolvedValue({
          success: false,
        });

      // Act
      const result = await service.updateFeatureTags(accountId, data, 80010);

      // Assert
      expect(result.result.updateConversionTypes).toEqual("Error updating campaign's KPIs");
    });

    it("should throw an error for invalid multistep tracking", async () => {
      // Arrange
      const data = {
        multistepTraking: "yes",
        stepsMain: "",
        conversionTypesMain: ConversionTypesFeaturesTags.no_conversion,
      };
      jest.spyOn(accountRepository, "findOne").mockResolvedValue(mockAccount);

      // Act & Assert
      await expect(service.updateFeatureTags(123, data, 80010)).rejects.toThrow(
        new HttpException(
          {
            message: ["Mainstep and its conversion type are required"],
            error: "Bad Request",
          },
          HttpStatus.BAD_REQUEST,
        ),
      );
    });
  });

  describe("validateMultistepTracking", () => {
    it("should throw an exception if validation fails", () => {
      const data = {
        stepsMain: "",
        multistepTraking: "yes",
        conversionTypesMain: "N/A",
      };

      expect(() => service.validateMultistepTracking(data)).toThrow(
        new HttpException(
          {
            message: ["Mainstep and its conversion type are required"],
            error: "Bad Request",
          },
          HttpStatus.BAD_REQUEST,
        ),
      );
    });

    it("should not throw if validation passes", () => {
      const data = {
        stepsMain: "mainstep",
        multistepTraking: "yes",
        conversionTypesMain: "applystart",
      };

      expect(() => service.validateMultistepTracking(data)).not.toThrow();
    });
  });

  describe("updateCampaignConversionTypes", () => {
    it('should return "Done" if update succeeds', async () => {
      const accountId = 123;
      const data = {
        flagUpdateConversionTypes: true,
        conversionTypeAccountLevel: "someType",
      };
      jest
        .spyOn(campaignObjectiveService, "updateCampaignsConversionTypesByAccount")
        .mockResolvedValue({
          success: true,
        });

      const result = await service.updateCampaignConversionTypes(accountId, data);

      expect(result).toBe("Done");
    });

    it("should return an error if update fails", async () => {
      const accountId = 123;
      const data = {
        flagUpdateConversionTypes: true,
        conversionTypeAccountLevel: "someType",
      };
      jest
        .spyOn(campaignObjectiveService, "updateCampaignsConversionTypesByAccount")
        .mockResolvedValue({
          success: false,
        });

      const result = await service.updateCampaignConversionTypes(accountId, data);

      expect(result).toBe("Error updating campaign's KPIs");
    });
  });

  describe("updateCampaignObjective", () => {
    it('should return "Done" when campaign objective is updated successfully', async () => {
      const accountId = 1;
      const data = { updateCampaignsObjective: {} };

      // Mock the result of setCampaignObjectiveByAccount
      jest.spyOn(campaignObjectiveService, "setCampaignObjectiveByAccount").mockResolvedValue({
        success: true,
      });

      const result = await service.updateCampaignObjective(accountId, data);

      expect(campaignObjectiveService.setCampaignObjectiveByAccount).toHaveBeenCalledWith(
        accountId,
        data.updateCampaignsObjective,
      );
      expect(result).toBe("Done");
    });

    it('should return "Error updating campaign\'s KPIs" when the update fails', async () => {
      const accountId = 1;
      const data = { updateCampaignsObjective: {} };

      // Mock the result of setCampaignObjectiveByAccount
      jest.spyOn(campaignObjectiveService, "setCampaignObjectiveByAccount").mockResolvedValue({
        success: false,
      });

      const result = await service.updateCampaignObjective(accountId, data);

      expect(campaignObjectiveService.setCampaignObjectiveByAccount).toHaveBeenCalledWith(
        accountId,
        data.updateCampaignsObjective,
      );
      expect(result).toBe("Error updating campaign's KPIs");
    });

    it("should return an empty string if updateCampaignsObjective is not provided", async () => {
      const accountId = 1;
      const data = {};

      const result = await service.updateCampaignObjective(accountId, data);

      expect(campaignObjectiveService.setCampaignObjectiveByAccount).not.toHaveBeenCalled();
      expect(result).toBe("");
    });

    it('should return "Error updating campaign\'s KPIs" if an exception is thrown', async () => {
      const accountId = 1;
      const data = { updateCampaignsObjective: {} };

      // Mock the method to throw an error
      jest
        .spyOn(campaignObjectiveService, "setCampaignObjectiveByAccount")
        .mockRejectedValue(new Error("some error"));

      const result = await service.updateCampaignObjective(accountId, data);

      expect(campaignObjectiveService.setCampaignObjectiveByAccount).toHaveBeenCalledWith(
        accountId,
        data.updateCampaignsObjective,
      );
      expect(result).toBe("Error updating campaign's KPIs");
    });
  });

  describe("updateWebhookTag", () => {
    it("should update the webhook tag when webhookTagId is provided", async () => {
      const accountId = 1;
      const tags = { webhookTagId: 123 };

      const updateAccountDto = {
        id: accountId,
        webhookTagId: 123,
      };
      jest.spyOn(service, "update").mockResolvedValue(accountCreateResponseDataMock);

      await service.updateWebhookTag(accountId, tags);

      expect(service.update).toHaveBeenCalledWith(updateAccountDto);
    });

    it("should not update the webhook tag when webhookTagId is not provided", async () => {
      const accountId = 1;
      const tags = {};

      jest.spyOn(service, "update").mockResolvedValue(accountCreateResponseDataMock);
      await service.updateWebhookTag(accountId, tags);

      expect(service.update).not.toHaveBeenCalled();
    });
  });

  describe("updateAccountTags", () => {
    it('should return "Done" when account tags are updated successfully', async () => {
      const accountId = 1;
      const data = { featureTag: "new_tag" };

      jest.spyOn(accountTagRelationService, "updateAccountTags").mockResolvedValueOnce(undefined);

      const result = await service.updateAccountTags(accountId, data, 80010);

      expect(accountTagRelationService.updateAccountTags).toHaveBeenCalledWith(
        accountId,
        data,
        80010,
      );
      expect(result).toBe("Done");
    });

    it('should return "Error updating account tags" when there is an error', async () => {
      const accountId = 1;
      const data = { featureTag: "new_tag" };

      jest
        .spyOn(accountTagRelationService, "updateAccountTags")
        .mockRejectedValueOnce(new Error("Update failed"));

      const result = await service.updateAccountTags(accountId, data, 80010);

      expect(accountTagRelationService.updateAccountTags).toHaveBeenCalledWith(
        accountId,
        data,
        80010,
      );
      expect(result).toBe("Error updating account tags");
    });
  });

  describe("updateMultistepTracking", () => {
    it('should return "Done" when multistep tracking is updated successfully', async () => {
      const accountId = 1;
      const data = { multistepTraking: true };

      const multistepSetting = {
        multiStepTracking: data.multistepTraking,
        accountId: accountId,
      };

      jest.spyOn(settingsService, "update").mockResolvedValueOnce(expectedAccountSettings);

      const result = await service.updateMultistepTracking(accountId, data);

      expect(settingsService.update).toHaveBeenCalledWith(multistepSetting);
      expect(result).toBe("Done");
    });
  });

  describe("setUpConversionSteps", () => {
    it("should set up multi-step conversion tracking", async () => {
      const accountId = 1;
      const data = {
        Postback: "yes",
        multistepTraking: "yes",
        conversionTypesMain: "MainConversionType",
        stepsMain: "MainStep",
        steps: [
          { conversionType: "StepConversionType1", step: "Step1" },
          { conversionType: "StepConversionType2", step: "Step2" },
        ],
      };

      const createConversionStepsSpy = jest
        .spyOn(service, "createConversionSteps")
        .mockResolvedValue([]);

      await service.setUpConversionSteps(accountId, data);

      expect(createConversionStepsSpy).toHaveBeenCalledWith([
        {
          conversionType: "MainConversionType",
          accountId: accountId,
          conversionName: "MainStep",
          stepOrder: 1,
        },
        {
          conversionType: "StepConversionType1",
          accountId: accountId,
          conversionName: "Step1",
          stepOrder: 2,
        },
        {
          conversionType: "StepConversionType2",
          accountId: accountId,
          conversionName: "Step2",
          stepOrder: 3,
        },
      ]);
    });

    it("should set up single-step conversion tracking", async () => {
      const accountId = 1;
      const data = {
        Postback: "yes",
        Pixel: "yes",
        multistepTraking: "no",
        conversionTypesNoMultistep: "SingleStepType",
      };

      const createConversionStepsSpy = jest
        .spyOn(service, "createConversionSteps")
        .mockResolvedValue([]);

      await service.setUpConversionSteps(accountId, data);

      expect(createConversionStepsSpy).toHaveBeenCalledWith([
        {
          conversionType: "SingleStepType",
          accountId: accountId,
          conversionName: "apply_step",
          stepOrder: 1,
        },
      ]);
    });

    it("should call createConversionSteps even if postback and pixel data are empty", async () => {
      const accountId = 1;
      const data = { Postback: "", multistepTraking: "no", Pixel: "" };

      const createConversionStepsSpy = jest
        .spyOn(service, "createConversionSteps")
        .mockResolvedValue([]);

      await service.setUpConversionSteps(accountId, data);

      expect(createConversionStepsSpy).toHaveBeenCalled();
    });
  });

  describe("createConversionSteps", () => {
    it("should delete existing steps and create new ones", async () => {
      const accountId = 1;
      const conversionSteps = [
        {
          accountId,
          conversionName: "Step1",
          conversionType: "Type1",
          stepOrder: 1,
        },
        {
          accountId,
          conversionName: "Step2",
          conversionType: "Type2",
          stepOrder: 2,
        },
      ];

      jest
        .spyOn(accountConversionStepsRepository, "deleteByAccountId")
        .mockResolvedValue(undefined);
      // Mock createStep to return a full ConversionSteps object
      const createStepMock = jest
        .spyOn(accountConversionStepsRepository, "createStep")
        .mockImplementation((step: Partial<ConversionSteps>) =>
          Promise.resolve({
            ...step,
            id: Math.floor(Math.random() * 1000), // Mock ID generation
            accountId: accountId, // Ensure accountId matches
            conversionName: step.conversionName || "Mock Conversion",
            conversionType: step.conversionType || "application",
            stepOrder: step.stepOrder || 1,
          } as ConversionSteps),
        );
      const result = await service.createConversionSteps(conversionSteps);

      expect(accountConversionStepsRepository.deleteByAccountId).toHaveBeenCalledWith(accountId);
      expect(createStepMock).toHaveBeenCalledTimes(2);
      expect(createStepMock).toHaveBeenNthCalledWith(
        1,
        expect.objectContaining(conversionSteps[0]),
      );
      expect(createStepMock).toHaveBeenNthCalledWith(
        2,
        expect.objectContaining(conversionSteps[1]),
      );
      // Using array map to ignore the `id` field in each item of the result array
      expect(result).toEqual(
        expect.arrayContaining(conversionSteps.map((step) => expect.objectContaining(step))),
      );
    });
  });

  describe("getAccountBudgetStatus", () => {
    it("should return 'active' status if no issues are found", async () => {
      const accountInfo: Partial<Accounts> = {
        id: 1,
        status: Status.active,
        accountsBudget: {
          accountId: 1,
          budget: 1000,
          spentBudget: 500,
          account: { id: 1, status: Status.active } as Accounts,
        },
      };

      const result = await service.getAccountBudgetStatus(accountInfo);

      expect(result.status).toBe("active");
      expect(result.account_msg).toBeUndefined();
    });

    it("should fetch budget data if accountsBudget is not provided", async () => {
      const accountInfo: Partial<Accounts> = { id: 1, status: Status.active };

      // Mock the response from the HTTP call
      jest.spyOn(http, "get").mockResolvedValue({
        account_budget: 1000,
        account_spent: 500,
      });
      const result = await service.getAccountBudgetStatus(accountInfo);

      expect(http.get).toHaveBeenCalledWith(
        `${process.env.URL_BUDGETING}/api/v1/account_budgets/1`,
      );
      expect(result.status).toBe("active");
    });

    it("should update status to 'limited_by_account_budget' when spent budget exceeds the limit", async () => {
      const accountInfo: Partial<Accounts> = { id: 1, status: Status.active };

      // Mock the response from the HTTP call
      jest.spyOn(http, "get").mockResolvedValue({
        account_budget: 1000,
        account_spent: 1500,
      });

      const result = await service.getAccountBudgetStatus(accountInfo);

      expect(result.status).toBe("limited_by_account_budget");
      expect(result.account_msg).toBe("Account monthly budget exceeded.");
    });

    it("should update status to 'limited_by_account_status' when account status is not 'active'", async () => {
      const accountInfo: Partial<Accounts> = { id: 1, status: Status.inactive };
      jest.spyOn(http, "get").mockResolvedValue({
        account_budget: 0,
        account_spent: 0,
      });
      const result = await service.getAccountBudgetStatus(accountInfo);

      expect(result.status).toBe("limited_by_account_status");
      expect(result.account_msg).toBe("Account status is inactive");
    });

    it("should handle when account has no budget data and returns default status", async () => {
      const accountInfo: Partial<Accounts> = { id: 1, status: Status.active };

      // Mock the response from the HTTP call
      jest.spyOn(http, "get").mockResolvedValue({
        account_budget: 0,
        account_spent: 0,
      });

      const result = await service.getAccountBudgetStatus(accountInfo);

      expect(result.status).toBe("active");
    });
  });

  describe("accountListByPerformanceOverview", () => {
    it("should throw an error if performance overview is empty", async () => {
      await expect(service.accountListByPerformanceOverview("")).rejects.toThrow(
        new HttpException("Performance overview value is required", HttpStatus.BAD_REQUEST),
      );
    });

    it("should return the correct account list for a valid performance overview", async () => {
      const mockPerformanceOverview = "pandologic agency";
      const mockAccountSettings = [
        {
          accountId: "123",
          account: { feedcode: "feed1", status: "active", companyLabel: "Company A" },
        },
        {
          accountId: "456",
          account: { feedcode: "feed2", status: "inactive", companyLabel: "Company B" },
        },
      ];

      // Mock the getAccountSettings method to return the mock data
      jest.spyOn(settingsService, "getAccountSettings").mockResolvedValueOnce(mockAccountSettings);

      const result = await service.accountListByPerformanceOverview(mockPerformanceOverview);

      expect(result.total).toBe(2); // There should be 2 accounts
      expect(result.accounts["123"]).toEqual({
        feedCode: "feed1",
        status: "active",
        companyLabel: "Company A",
      });
      expect(result.accounts["456"]).toEqual({
        feedCode: "feed2",
        status: "inactive",
        companyLabel: "Company B",
      });
    });

    it("should handle the case when no accounts are returned", async () => {
      const mockPerformanceOverview = "non-existing overview";
      jest.spyOn(settingsService, "getAccountSettings").mockResolvedValueOnce([]);

      const result = await service.accountListByPerformanceOverview(mockPerformanceOverview);

      expect(result.total).toBe(0); // No accounts should be returned
      expect(result.accounts).toEqual({}); // No accounts in the list
    });
  });

  describe("prepareCampaignSettingstoUpdate", () => {
    it("should return an empty object when no changes are detected", () => {
      const data = {
        reliableConversion: true,
        status24hCommitment: true,
        endOfMonthExposure: false,
        externalConversionData: true,
        cem: true,
        spikeManager: true,
        smartBidding: true,
        accountId: 123,
      } as unknown as UpdateAccountSettingsSalesDto;

      const accountInfo = {
        reliableConversion: true,
        status24hCommitment: true,
        endOfMonthExposure: false,
        externalConversionData: true,
        cem: true,
        spikeManager: true,
        smartBidding: true,
        // other properties...
      } as unknown as AccountSettings;

      const result = service.prepareCampaignSettingstoUpdate(data, accountInfo);
      expect(result).toEqual({});
    });

    it("should return an object with updated properties when changes are detected", () => {
      const data = {
        reliableConversion: false,
        cctModifier: 1, // <= 1 so should be set to -1
        status24hCommitment: false,
        endOfMonthExposure: true,
        externalConversionData: true,
        cem: true,
        spikeManager: true,
        smartBidding: false,
      } as unknown as UpdateAccountSettingsSalesDto;

      const accountInfo = {
        reliableConversion: true,
        cctModifier: 2,
        status24hCommitment: true,
        endOfMonthExposure: false,
        externalConversionData: false,
        cem: false,
        spikeManager: false,
        smartBidding: true,
        // other properties...
      } as unknown as AccountSettings;

      const expected: Partial<UpdateCampaignSettingDto> = {
        reliableConversions: false,
        cctModifier: -1,
        status24hCommitment: false,
        endOfMonthExposure: true,
        externalConversionData: true,
        spikeManager: true,
        smartBidding: false,
      };

      const result = service.prepareCampaignSettingstoUpdate(data, accountInfo);
      expect(result).toEqual(expected);
    });

    // Additional tests to improve coverage

    it("should update only reliableConversions when reliableConversion is changed", () => {
      const data = { reliableConversion: false } as unknown as UpdateAccountSettingsSalesDto;
      const accountInfo = { reliableConversion: true } as unknown as AccountSettings;

      const result = service.prepareCampaignSettingstoUpdate(data, accountInfo);
      expect(result).toEqual({ reliableConversions: false });
    });

    it("should update cctModifier correctly when value is greater than 1", () => {
      const data = { cctModifier: 2 } as unknown as UpdateAccountSettingsSalesDto;
      // accountInfo value is irrelevant since cctModifier is always updated if defined
      const accountInfo = { cctModifier: 5 } as unknown as AccountSettings;

      const result = service.prepareCampaignSettingstoUpdate(data, accountInfo);
      expect(result).toEqual({ cctModifier: 2 });
    });

    it("should update cem when changed and externalConversionData is falsy", () => {
      const data = {
        cem: true,
        externalConversionData: false, // falsy so cem should be updated
      } as unknown as UpdateAccountSettingsSalesDto;
      const accountInfo = { cem: false } as unknown as AccountSettings;

      const result = service.prepareCampaignSettingstoUpdate(data, accountInfo);
      expect(result).toEqual({ cem: true, externalConversionData: false });
    });

    it("should not update cem when changed but externalConversionData is truthy", () => {
      const data = {
        cem: true,
        externalConversionData: true, // truthy so skip updating cem
      } as unknown as UpdateAccountSettingsSalesDto;
      const accountInfo = { cem: false } as unknown as AccountSettings;

      const result = service.prepareCampaignSettingstoUpdate(data, accountInfo);
      expect(result).toEqual({ externalConversionData: true });
    });

    it("should update multiple fields independently", () => {
      const data = {
        reliableConversion: false,
        cctModifier: 0, // 0 <= 1 so should be set to -1
        status24hCommitment: false,
        endOfMonthExposure: true,
        externalConversionData: false, // changed from accountInfo value
        cem: true, // should update because externalConversionData is falsy
        spikeManager: true,
        smartBidding: false,
      } as unknown as UpdateAccountSettingsSalesDto;
      const accountInfo = {
        reliableConversion: true,
        cctModifier: 5,
        status24hCommitment: true,
        endOfMonthExposure: false,
        externalConversionData: true,
        cem: false,
        spikeManager: false,
        smartBidding: true,
      } as unknown as AccountSettings;

      const expected: Partial<UpdateCampaignSettingDto> = {
        reliableConversions: false,
        cctModifier: -1,
        status24hCommitment: false,
        endOfMonthExposure: true,
        externalConversionData: false,
        cem: true,
        spikeManager: true,
        smartBidding: false,
      };

      const result = service.prepareCampaignSettingstoUpdate(data, accountInfo);
      expect(result).toEqual(expected);
    });
  });

  describe("updateSettings", () => {
    const accountId = 1;
    const userId = 123;

    const accountInfoEnterpost = {
      account: { accountType: "enterpost" },
      reliableConversion: true,
      status24hCommitment: true,
      endOfMonthExposure: false,
      externalConversionData: "oldData",
      cem: "oldCem",
      spikeManager: "oldManager",
      smartBidding: true,
    } as any;

    const accountInfoNonEnterpost = {
      account: { accountType: "other" },
      reliableConversion: true,
      status24hCommitment: true,
      endOfMonthExposure: false,
      externalConversionData: "oldData",
      cem: "oldCem",
      spikeManager: "oldManager",
      smartBidding: true,
    } as any;

    beforeEach(() => {
      jest.clearAllMocks();
    });

    it("should throw HttpException when account is not found", async () => {
      // Simulate getAccountSettings returning null (account not found)
      jest.spyOn(settingsService, "getAccountSettings").mockResolvedValueOnce(null);

      const data = {} as UpdateAccountSettingsSalesDto;
      await expect(service.updateSettings(accountId, data, userId)).rejects.toThrow(
        new HttpException("Account not found", HttpStatus.NOT_FOUND),
      );
    });

    it("should update settings when account is found (enterpost account) with provided autoCampaignApiTagName", async () => {
      // Simulate a valid account with an "enterpost" accountType
      jest.spyOn(settingsService, "getAccountSettings").mockResolvedValueOnce(accountInfoEnterpost);

      const updateSpy = jest.spyOn(service, "update").mockResolvedValueOnce({
        id: accountId,
        accountType: "newType",
        updatedBy: userId,
      } as unknown as UpdateAccountDto);
      const updateAccountTagsSpy = jest
        .spyOn(service, "updateAccountTags")
        .mockResolvedValueOnce("tag updated");
      const updateCampaignSpy = jest
        .spyOn(campaignsAccountsSettingsService, "updateCampaignSettingsByAccount")
        .mockResolvedValueOnce([]);
      const settingsUpdateSpy = jest
        .spyOn(settingsService, "update")
        .mockResolvedValueOnce({ id: 123, accountId } as unknown as AccountSettings);
      // Simulate that no campaign settings change is needed
      jest.spyOn(service, "prepareCampaignSettingstoUpdate").mockReturnValueOnce({});

      const data: UpdateAccountSettingsSalesDto = {
        accountType: "newType",
        autoCampaignApiTagName: "123",
        requiresQa: "value", // For enterpost accounts, this value is kept as is.
      } as any;

      await service.updateSettings(accountId, data, userId);

      // Verify account update
      expect(updateSpy).toHaveBeenCalledWith(
        expect.objectContaining({ id: accountId, accountType: "newType", updatedBy: userId }),
      );
      // Verify that updateAccountTags was called with the provided autoCampaignApiTagName
      expect(updateAccountTagsSpy).toHaveBeenCalledWith(
        accountId,
        { autoCampaignApi: "123" },
        userId,
      );
      // Verify that campaigns settings update is not called (since prepareCampaignSettingstoUpdate returned empty)
      expect(updateCampaignSpy).not.toHaveBeenCalled();
      // Verify that accountSettingsService.update was called with the proper payload
      expect(settingsUpdateSpy).toHaveBeenCalledWith(
        expect.objectContaining({ accountId, updated: expect.any(Date) }),
      );
    });

    it("should set requiresQa to null for non-enterpost accounts", async () => {
      // Simulate a valid account with a non-enterpost accountType.
      jest
        .spyOn(settingsService, "getAccountSettings")
        .mockResolvedValueOnce(accountInfoNonEnterpost);

      jest.spyOn(service, "update").mockResolvedValueOnce({
        id: accountId,
        accountType: "newType",
        updatedBy: userId,
      } as unknown as UpdateAccountDto);
      jest.spyOn(service, "updateAccountTags").mockResolvedValueOnce("tag updated");
      jest
        .spyOn(campaignsAccountsSettingsService, "updateCampaignSettingsByAccount")
        .mockResolvedValueOnce([]);
      jest
        .spyOn(settingsService, "update")
        .mockResolvedValueOnce({ id: 123, accountId } as unknown as AccountSettings);
      // Assume no changes in campaign settings
      jest.spyOn(service, "prepareCampaignSettingstoUpdate").mockReturnValueOnce({});

      const data: UpdateAccountSettingsSalesDto = {
        accountType: "newType",
        autoCampaignApiTagName: "tagValue",
        requiresQa: "shouldBeNull", // Should be nulled out for non-enterpost accounts.
      } as any;

      await service.updateSettings(accountId, data, userId);

      expect(data.requiresQa).toBeNull();
    });

    it("should update account tags with an empty string when autoCampaignApiTagName is not provided", async () => {
      jest.spyOn(settingsService, "getAccountSettings").mockResolvedValueOnce(accountInfoEnterpost);

      jest.spyOn(service, "update").mockResolvedValueOnce({
        id: accountId,
        accountType: "newType",
        updatedBy: userId,
      } as unknown as UpdateAccountDto);
      const updateAccountTagsSpy = jest
        .spyOn(service, "updateAccountTags")
        .mockResolvedValueOnce("tag updated");
      jest
        .spyOn(campaignsAccountsSettingsService, "updateCampaignSettingsByAccount")
        .mockResolvedValueOnce([]);
      jest
        .spyOn(settingsService, "update")
        .mockResolvedValueOnce({ id: 123, accountId } as unknown as AccountSettings);
      jest.spyOn(service, "prepareCampaignSettingstoUpdate").mockReturnValueOnce({});

      const data: UpdateAccountSettingsSalesDto = {
        accountType: "newType",
        // No autoCampaignApiTagName provided
      } as any;

      await service.updateSettings(accountId, data, userId);

      expect(updateAccountTagsSpy).toHaveBeenCalledWith(accountId, { autoCampaignApi: "" }, userId);
    });

    it("should call campaignsAccountsSettingsService.updateCampaignSettingsByAccount when campaign settings update is not empty", async () => {
      jest.spyOn(settingsService, "getAccountSettings").mockResolvedValueOnce(accountInfoEnterpost);

      jest.spyOn(service, "update").mockResolvedValueOnce({
        id: accountId,
        accountType: "newType",
        updatedBy: userId,
      } as unknown as UpdateAccountDto);
      jest.spyOn(service, "updateAccountTags").mockResolvedValueOnce("tag updated");
      const updateCampaignSpy = jest
        .spyOn(campaignsAccountsSettingsService, "updateCampaignSettingsByAccount")
        .mockResolvedValueOnce([]);
      jest
        .spyOn(settingsService, "update")
        .mockResolvedValueOnce({ id: 123, accountId } as unknown as AccountSettings);
      // Simulate a non-empty update object
      jest
        .spyOn(service, "prepareCampaignSettingstoUpdate")
        .mockReturnValueOnce({ reliableConversion: true } as Partial<UpdateCampaignSettingDto>);

      const data: UpdateAccountSettingsSalesDto = {
        accountType: "newType",
        autoCampaignApiTagName: "tagValue",
      } as any;

      await service.updateSettings(accountId, data, userId);

      expect(updateCampaignSpy).toHaveBeenCalledWith(accountId, { reliableConversion: true });
    });
  });

  describe("updateOwners", () => {
    it("should update owners successfully", async () => {
      const data = {
        salesOwner: "<EMAIL>",
        secondarySalesOwner: "<EMAIL>",
        csOwner: "<EMAIL>",
        hubspotDealId: "deal123",
      };
      const resultUser = new Users();
      resultUser.id = 1;
      resultUser.email = "V001u8VxSRcpNJX9e52H-y-ryrwzxES8q1NrmCdxRzrf18HdtqML8uPEFBHGE4XlwUdX";
      resultUser.password = "718b286768840c1f122feeecc2dae76c1aee55f13042d137cc757f10b64fc44b";
      resultUser.firstName = "Talent";
      resultUser.lastName = "V001TI3QU1Ii_fdAdEzT2yz2vhcepKRuzo_4Kycy7EkXKf4*";
      resultUser.status = "active";
      resultUser.emailHash = "9c8e6bf810e1942754860ef7be320c7882ae2c6b26f8da08239fbc10086b03ed";
      resultUser.tags = [];
      resultUser.userType = UserMainType.Internal;
      resultUser.setPassword = "yes";
      resultUser.creditRecordsRequested = [];
      resultUser.creditRecordsApproved = [];
      jest.spyOn(userRepository, "findOneByEmail").mockResolvedValue(resultUser);
      const accountData = accountCreateResponseDataMock; //from the accounts.data.mock
      jest.spyOn(repository, "findOne").mockResolvedValue(accountData);
      await service.updateOwners(1, data);
      expect(dealService.updateDealInHubSpot).toHaveBeenCalled();
      expect(accountOwnersService.updateAccountOwners).toHaveBeenCalled();
    });
  });

  describe("validateTalentEmails", () => {
    it("should throw an error for invalid emails", () => {
      const data = { salesOwner: "<EMAIL>", hubspotDealId: "123" };
      expect(() => service.validateTalentEmails(data)).toThrow(HttpException);
    });

    it("should pass for valid emails", () => {
      const data = { salesOwner: "<EMAIL>", hubspotDealId: "123" };
      expect(() => service.validateTalentEmails(data)).not.toThrow();
    });
  });

  describe("validateOwner", () => {
    it("should return an owner ID if found", async () => {
      const resultUser = new Users();
      resultUser.id = 1;
      resultUser.email = "V001u8VxSRcpNJX9e52H-y-ryrwzxES8q1NrmCdxRzrf18HdtqML8uPEFBHGE4XlwUdX";
      resultUser.password = "718b286768840c1f122feeecc2dae76c1aee55f13042d137cc757f10b64fc44b";
      resultUser.firstName = "Talent";
      resultUser.lastName = "V001TI3QU1Ii_fdAdEzT2yz2vhcepKRuzo_4Kycy7EkXKf4*";
      resultUser.status = "active";
      resultUser.emailHash = "9c8e6bf810e1942754860ef7be320c7882ae2c6b26f8da08239fbc10086b03ed";
      resultUser.tags = [];
      resultUser.userType = UserMainType.Internal;
      resultUser.setPassword = "yes";
      resultUser.creditRecordsRequested = [];
      resultUser.creditRecordsApproved = [];
      jest.spyOn(userRepository, "findOneByEmail").mockResolvedValue(resultUser);
      jest.spyOn(dealService, "getOwnersByEmail").mockResolvedValue({ results: [{ id: "123" }] });
      const result = await service.validateOwner("<EMAIL>", "primary sales owner");
      expect(result).toBe("123");
    });

    it("should throw an error if owner is not found", async () => {
      userRepository.findOneByEmail = jest.fn().mockResolvedValue(null);
      await expect(
        service.validateOwner("<EMAIL>", "primary sales owner"),
      ).rejects.toThrow(HttpException);
    });
  });

  describe("updateCampaignDeals", () => {
    it("should update campaign deals correctly", async () => {
      service.findOne = jest.fn().mockResolvedValue({
        campaigns: [{ deal: { dealId: "deal456" }, hubspotDealID: "deal789" }],
      });
      jest.spyOn(dealService, "getDeal").mockResolvedValue({ properties: { dealstage: 12345 } });
      await service.updateCampaignDeals("deal123", {}, 1);
      expect(dealService.getDeal).toHaveBeenCalled();
      expect(dealService.updateDealInHubSpot).toHaveBeenCalled();
    });
  });

  describe("getAccountInfoForTransaction", () => {
    it("should return account when found", async () => {
      const accountData = accountCreateResponseDataMock; //from the accounts.data.mock
      jest.spyOn(repository, "findOne").mockResolvedValue(accountData);

      const result = await service.getAccountInfoForTransaction(1);

      expect(result).toEqual(accountData);
      expect(repository.findOne).toHaveBeenCalledWith(1, undefined);
    });

    it("should throw HttpException when account is not found", async () => {
      jest.spyOn(repository, "findOne").mockResolvedValue(null as any);

      await expect(service.getAccountInfoForTransaction(1)).rejects.toThrow(
        new HttpException("Account not found", HttpStatus.NOT_FOUND),
      );
    });

    it("should log error and throw HttpException on unexpected error", async () => {
      const error = new Error("Database error");
      jest.spyOn(Logger, "error").mockImplementation(() => {});
      jest.spyOn(repository, "findOne").mockRejectedValue(error);

      await expect(service.getAccountInfoForTransaction(1)).rejects.toThrow(
        new HttpException(error.message, HttpStatus.NOT_FOUND),
      );

      expect(Logger.error).toHaveBeenCalledWith(
        `Error in getAccountInfoForTransaction: ${JSON.stringify(error)}`,
      );
    });
  });
});
