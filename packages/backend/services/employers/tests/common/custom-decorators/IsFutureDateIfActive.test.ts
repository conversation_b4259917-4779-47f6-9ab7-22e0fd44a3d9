import { validate } from "class-validator";
import { IsFutureDateIfActive } from "../../../src/common/custom-decorators/IsFutureDateIfActive"; // Adjust path as needed

class TestDto {
  status: string;

  @IsFutureDateIfActive({
    message: "You cannot activate the campaign. Campaign Date Start cannot be in the future",
  })
  dateStart: string | Date | null;
}

describe("IsFutureDateIfActive Decorator", () => {
  it("should pass when status is not 'active'", async () => {
    const dto = new TestDto();
    dto.status = "inactive";
    dto.dateStart = "2050-01-01";

    const errors = await validate(dto);
    expect(errors).toHaveLength(0);
  });

  it("should pass when status is 'active' and date is today", async () => {
    const dto = new TestDto();
    dto.status = "active";
    dto.dateStart = new Date().toISOString().split("T")[0]; // Today's date in YYYY-MM-DD

    const errors = await validate(dto);
    expect(errors).toHaveLength(0);
  });

  it("should pass when status is 'active' and date is in the past", async () => {
    const dto = new TestDto();
    dto.status = "active";
    dto.dateStart = "2020-01-01";

    const errors = await validate(dto);
    expect(errors).toHaveLength(0);
  });

  it("should fail when status is 'active' and date is in the future", async () => {
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + 1); // Tomorrow

    const dto = new TestDto();
    dto.status = "active";
    dto.dateStart = futureDate.toISOString().split("T")[0];

    const errors = await validate(dto);
    expect(errors).toHaveLength(1);
  });

  it("should handle string date inputs correctly (convert to UTC)", async () => {
    const dto = new TestDto();
    dto.status = "active";
    dto.dateStart = "2023-12-31"; // A past date

    const errors = await validate(dto);
    expect(errors).toHaveLength(0);
  });
});
