import { Accounts } from "../../src/accounts/entities/account.entity";
import { AccountCurrency, AccountType, CompanyType, LeadType, Status } from "../../src/common/resources/enums";
import { CreateDealDto } from "../../src/deals/dto/create-deal.dto";
import { UpdateDealDto } from "../../src/deals/dto/update-deal.dto";
import { Deal } from "../../src/deals/entities/deal.entity";

export const createDealDto: CreateDealDto = {
  dealName: "dealtest1",
  primaryContact: "Test Contact",
  primaryContactEmail: "<EMAIL>",
  dealOwnerId: "*********",
  dealOwner: "Test Owner",
  dealOwnerEmail: "<EMAIL>",
  secondaryDealOwner: "Test Two",
  secondaryDoEmail: "<EMAIL>",
  customerSuccessOwner: "Test success owner",
  customerSoEmail: "<EMAIL>",
  budgetHolderName: "Talent.com  | CA",
  billedClientName: "Talent.com  | CA",
  secondaryDealId: "",
  customerSuccessId: "",
  mapped: "no",
  validDeal: "0",
  emptyProperties: "",
  dealStage: "",
  pipeline: "",
  openBudget: false,
  amount: 0,
  dealCurrencyCode: "",
  atsApplicantTracking: "",
  budgetHolderType: "",
  budgetHolderCountry: "",
  budgetHolderState: "",
  budgetHolderIndustry: "",
  billedClientType: "",
  billedClientCountry: "",
  billedClientState: "",
  billedClientIndustry: "",
  apiClientName: "",
  accountName: "",
  associationsId: "",
  mergeDealId: "",
  pandaDocStatus: "",
  budgetHolderParentId: "",
  budgetHolderParentName: "",
  billedClientParentId: "",
  billedClientParentName: "",
  closeDate: null,
  lastInteractionDate: null,
};

export const updateDealDto: UpdateDealDto = {
  dealName: "dealtest1",
  primaryContact: "Test Contact",
  primaryContactEmail: "<EMAIL>",
  dealOwnerId: "*********",
  dealOwner: "Test Owner",
  dealOwnerEmail: "<EMAIL>",
  secondaryDealOwner: "Test Two",
  secondaryDoEmail: "<EMAIL>",
  customerSuccessOwner: "Test success owner",
  customerSoEmail: "<EMAIL>",
  budgetHolderName: "Talent.com  | CA",
  billedClientName: "Talent.com  | CA",
};

export const expectedDeal: Deal = {
  id: 1,
  dealId: "21232",
  ...createDealDto,
  dealName: null,
  primaryContact: null,
  primaryContactEmail: null,
  dealOwnerId: null,
  dealOwner: null,
  dealOwnerEmail: null,
  secondaryDealOwner: null,
  secondaryDoEmail: null,
  customerSuccessOwner: null,
  customerSoEmail: null,
  budgetHolderName: null,
  billedClientName: null,
  secondaryDealId: null,
  customerSuccessId: null,
  mapped: "no",
  validDeal: "0",
  emptyProperties: null,
  dealStage: null,
  pipeline: null,
  openBudget: null,
  amount: null,
  dealCurrencyCode: null,
  atsApplicantTracking: null,
  budgetHolderType: null,
  budgetHolderCountry: null,
  budgetHolderState: null,
  budgetHolderIndustry: null,
  billedClientType: null,
  billedClientCountry: null,
  billedClientState: null,
  billedClientIndustry: null,
  apiClientName: null,
  accountName: null,
  associationsId: null,
  mergeDealId: null,
  pandaDocStatus: null,
  budgetHolderParentId: null,
  budgetHolderParentName: null,
  billedClientParentId: null,
  billedClientParentName: null,
  closeDate: null,
  lastInteractionDate: null,
  campaigns: [],
};

export const expectsAccoutnsFromObserverMock = [
  {
    id: 1002292,
    country: "us",
    language: "es",
    status: Status.active,
    created: new Date(),
    accountType: AccountType.Enterprise,
    companyLabel: "Test Account",
    companyType: CompanyType.job_board,
    companyWebsite: "https://www.linkedin.com/mynetwork/catch-up/all/",
    feedcode: "agencesynergie",
    contactEmail: "<EMAIL>",
    contactName: "Person to contact",
    leadType: LeadType.company,
    accountCurrency: AccountCurrency.AED,
    updated: new Date(),
    contactHsid: null,
    agencyTag: null,
    autoCampaignApiTagName: "tagNest",
    hubspotDealId: 2687,
    webhookTagId: null,
    accountsBudget: {
      id: 54,
      accountId: 1002292,
      budget: 100,
      spentBudget: 10.0,
      account: new Accounts(),
    },
  },
];


export const fakeCampaignMock = [
  {
    id: 1,
    budgetType: "monthly",
    active: 1,
    validated: 1,
    paused: 0,
    removed: false,
    budget: 500,
  },
  {
    id: 2,
    budgetType: "daily",
    active: 1,
    paused: 1,
    validated: 1,
    removed: false,
    budget: 500,
  },
  {
    id: 3,
    budgetType: "monthly",
    active: 1,
    validated: 1,
    paused: 0,
    removed: false,
    budget: 1000,
  },
]
