package models

import (
	"context"
	"os"
	"testing"
)

func TestInitMetricsLocal(t *testing.T) {
	// Initialize the metrics
	InitMetrics(context.Background())

	// Check if the metrics are initialized
	if TotalRequests == nil {
		t.<PERSON><PERSON>rf("TotalSearchRequests is nil")
	}
	if RequestDurationTotal == nil {
		t.<PERSON><PERSON>("TotalSearchRequestsDuration is nil")
	}
	if Uptime == nil {
		t.<PERSON><PERSON>rf("TotalSearchRequestsErrors is nil")
	}
}

func TestInitMetrics(t *testing.T) {
	os.Setenv("OTEL_EXPORTER_OTLP_METRICS_ENDPOINT", "http://localhost:4319")
	// Initialize the metrics
	InitMetrics(context.Background())

	// Check if the metrics are initialized
	if TotalRequests == nil {
		t.<PERSON><PERSON>rf("TotalSearchRequests is nil")
	}
	if RequestDurationTotal == nil {
		t.<PERSON><PERSON>("TotalSearchRequestsDuration is nil")
	}
	if Uptime == nil {
		t.<PERSON><PERSON><PERSON>("TotalSearchRequestsErrors is nil")
	}
}
