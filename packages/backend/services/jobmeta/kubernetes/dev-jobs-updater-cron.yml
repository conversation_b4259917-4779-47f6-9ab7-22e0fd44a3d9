apiVersion: batch/v1
kind: CronJob
metadata:
  name: jobs-delta-updater
spec:
  schedule: "*/30 * * * *" # every 30 minutes
  concurrencyPolicy: Forbid # Do not overlap run by any chance
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            cj2job: jdu-cronjob
          annotations:
            prometheus.io/scrape: "true"
            prometheus.io/port: "9090"
            prometheus.io/path: "/metrics"
            sidecar.istio.io/inject: "false"
            sidecar.opentelemetry.io/inject: "opentelemetry-apps-no-istio-collector"
        spec:
          serviceAccountName: aws-role-s3
          nodeSelector:
            app: migration-svc
          tolerations:
            - key: app
              operator: Equal
              value: migration-svc
              effect: NoSchedule
          shareProcessNamespace: true
          containers:
          - name: jobs-delta-updater
            image: "************.dkr.ecr.us-east-1.amazonaws.com/services/jobssync:latest"
            imagePullPolicy: Always
            command: ["/bin/sh","-c"]
            args:
            - |
              echo "Running Go program if exists:"
              if [ -f src/jobs-updater/jobsupdater ]; then
                src/jobs-updater/jobsupdater
                echo "Sleeping for 90 seconds before stopping opentelemetry collector"
                sleep 90
                echo "Stopping opentelemetry collector sidecar"
                kill $(pidof otelcol-contrib)
              else
                echo "Go file not found"
              fi
            env:
              - name: KAFKA_TOPIC_PREFIX
                value: "job_ppc_processed"
              # - name: QUERY_OVERWRITE
              #   value: "flag_active:*"
              - name: SNAPSHOT_PATH
                value: "/home/<USER>/"
              - name: ES_SHARDS_DIGITS
                value: "2"
              - name: STORE_SNAP_REMOTE
                value: "1"
              - name: JOBS_INDEX
                value: "jobs"
              - name: TABLE_SYNC
                value: "jobs"
              - name: LOG_LEVEL
                value: "info"
              - name: ES_HOST
                value: "http://************:9200"
              - name: ES_USER
                value: "elastic"
              - name: ES_PASSWORD
                value: "C7G2FpFXYbfnpTsW"
              - name: CASS_HOST
                value: "jobs-dc1-all-pods-service.k8ssandra-operator.svc"
              - name: CASS_USER
                value: "jobs0-superuser"
              - name: CASS_PASSWORD
                value: "vnlLv6JWRK3aJvMEUTi1"
              - name: KAFKA_HOST
                value: "kafka-broker-employers-job-stream-0.talent.private:9092,kafka-broker-employers-job-stream-1.talent.private:9092,kafka-broker-employers-job-stream-2.talent.private:9092"
              - name: PG_HOST
                value: "postgres-spiders.talent.private"
              - name: PG_USER
                value: "spiders"
              - name: PG_PASSWORD
                value: "p0stgr3st4r0r4F.#"
              - name: PG_DB
                value: "spiders"
              - name: OTEL_EXPORTER_OTLP_METRICS_ENDPOINT
                value: "http://localhost:4319"
              - name: MY_IP
                valueFrom:
                  fieldRef:
                    fieldPath: status.podIP
          restartPolicy: Never
      backoffLimit: 2