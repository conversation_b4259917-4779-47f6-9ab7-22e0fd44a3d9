import logging
import json
import boto3
from botocore.exceptions import <PERSON><PERSON><PERSON><PERSON><PERSON>
from typing import Dict
from llm_cache import LL<PERSON>ache
import prompts


# Set up logging
logger = logging.getLogger(__name__)
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s %(levelname)s %(name)s: %(message)s"
)


class LLMJudge:

    def __init__(self, region: str, llm_id: str, llm_cache_instance: LLMCache):
        self.region = region
        self.llm_id = llm_id
        self._cache = llm_cache_instance

    def run_llm_judge(self, query, location, analysis, job) -> Dict[str, any]:

        cache_key = self._cache.generate_cache_key(
            self.llm_id, prompts.RANKING_PROMPT_VERSION, query, location, analysis, job)

        cached_response = self._cache.fetch_from_cache(cache_key)

        parsed = {}

        if cached_response is not None:
            parsed = json.loads(cached_response)
        else:
            # If not in cache, call the model.
            prompt = self.get_ranking_prompt(
                self.get_job_relevancy_question(query=query, location=location, analysis=analysis, job=job)
            )
            if "llama" in self.llm_id:
                try:
                    model_response = self.call_llama(prompt)
                    parsed = json.loads(model_response["generation"])
                    self._cache.save_to_cache(cache_key, model_response["generation"])
                except Exception as ex:
                    logging.warning(f"Running model an exception occurred: {ex}")

        return {
            "Job": job,
            "Reasoning": parsed['Reasoning'] if 'Reasoning' in parsed else '',
            "Relevancy": parsed['Relevancy'] if 'Relevancy' in parsed else ''
        }

    def get_analysis(self, query: str, location: str) -> Dict[str, any]:
        cache_key = self._cache.generate_cache_key(
            self.llm_id, prompts.SEARCH_ANALYSIS_PROMPT_VERSION, query, location, "", "")

        cached_response = self._cache.fetch_from_cache(cache_key)

        parsed = {}

        if cached_response is not None:
            parsed = json.loads(cached_response)
        else:
            prompt = self.get_search_analysis_prompt(
                self.get_search_analysis_question(query, location)
            )
            if "llama" in self.llm_id:
                model_response = self.call_llama(prompt)
                parsed = json.loads(model_response['generation'])
                self._cache.save_to_cache(cache_key, model_response["generation"])
        return {
            "Analysis": parsed["Analysis"] if "Analysis" in parsed else "",
            "Related": parsed["Related"] if "Related" in parsed else ""
        }

    def get_search_analysis_question(self, search, location):
        return f" Query:{search}\nLocation:{location}"

    def get_search_analysis_prompt(self, question: str):
        return prompts.SEARCH_ANALYSIS_PROMPT + question

    def get_ranking_prompt(self, question: str) -> str:
        return prompts.RANKING_PROMPT + question

    def get_job_relevancy_question(self, query, location, analysis, job):
        return f'''
            Query: {query}
            Location: {location}
            Analysis: {analysis}
            Job: """{job}"""
        '''

    def string_to_bool(self, s):
        s = s.lower().strip()
        if s == "match" or s == "similar":
            return True
        elif s == "irrelevant":
            return False
        else:
            raise ValueError("Invalid input: must be 'Match', 'Similar' or 'Irrelevant'")

    def call_llama(self, message: str):

        # Embed the prompt in Llama 3's instruction format.
        formatted_prompt = f"""
        <|begin_of_text|><|start_header_id|>user<|end_header_id|>
        {message}
        <|eot_id|>
        <|start_header_id|>assistant<|end_header_id|>
        """
        # Format the request payload using the model's native structure.
        native_request = {
            "prompt": formatted_prompt,
            "max_gen_len": 512,
            "temperature": 0.5,
        }

        model_response = self.invoke_llm_request(native_request)
        return model_response

    def invoke_llm_request(self, native_request):
        # Convert the native request to JSON.
        request = json.dumps(native_request)

        # Create a Bedrock Runtime client in the AWS Region of your choice.
        client = boto3.client("bedrock-runtime", region_name=self.region)

        try:
            # Invoke the model with the request.
            response = client.invoke_model(
                modelId=self.llm_id, body=request, accept='application/json', contentType='application/json')

        except (ClientError, Exception) as e:
            msg = f"ERROR: Can't invoke '{self.llm_id}'. Reason: {e}"
            logging.error(msg)
            return {"Error": msg}

        body_bytes = response["body"].read()
        model_response = json.loads(body_bytes)
        return model_response
