package main

import (
	"context"
	"fmt"
	"log/slog"
	"sync"
	"talent/jobs-ingestion/src/common/telemetry"
	"talent/jobs-ingestion/src/dto"
	"talent/jobs-ingestion/src/internal/application/jobs"
	"time"
)

const (
	PARTITIONS   = jobs.Partitions
	TABLE_PREFIX = jobs.JobsTable
	BATCH_SIZE   = 500
)

var (
	jobsRepository *jobs.Repository

	jobIDsChans []chan *dto.JobIdTranslation
	otl         telemetry.Telemetry
)

func InitializeTelemetry(meterName string) {
	ctx, cancel := context.WithCancel(context.Background())
	otl = *otl.InitMetrics(meterName, &ctx)
	defer cancel()
}

func initJobsRepositoryConnection() {
	jobsRepository = jobs.NewRepository(&otl)
}

func storeJobIDTranslation(wg *sync.WaitGroup, jobsChan <-chan *dto.JobIdTranslation) {
	defer wg.Done()

	var jobIDsBatch []*dto.JobIdTranslation
	for {
		job, ok := <-jobsChan
		if !ok {
			// Store the last batch
			if len(jobIDsBatch) > 0 {
				if err := jobsRepository.StoreJobIDsInCache(jobIDsBatch); err != nil {
					slog.Error("Error storing job batch", "error", err)
				}
			}
			return
		}
		if job == nil {
			slog.Warn("Received nil job")
			continue
		}
		if job.JobID == 0 {
			slog.Warn("Received job with 0 job_id", "job", job)
			continue
		}
		if job.SystemHashSourceUniqueJob == "" {
			slog.Warn("Received job with empty system_hash_source_unique_job", "job", job)
			continue
		}

		// Append the job to the batch
		jobIDsBatch = append(jobIDsBatch, job)
		// Store the batch if it's full
		if len(jobIDsBatch) >= BATCH_SIZE {
			if err := jobsRepository.StoreJobIDsInCache(jobIDsBatch); err != nil {
				slog.Error("Error storing job batch", "error", err)
			}
			// Reset the batch
			jobIDsBatch = jobIDsBatch[:0]
		}
	}
}

func main() {
	defer otl.Close()
	defer jobsRepository.CloseRepositoryConnection()

	// Initialize telemetry
	InitializeTelemetry("migrate_job_ids")
	// Initialize repository
	initJobsRepositoryConnection()

	startTime := time.Now()
	jobIDsChans = make([]chan *dto.JobIdTranslation, PARTITIONS)

	var wg, wWg sync.WaitGroup

	// Create workers to store job ids in postgres
	for i := 0; i < PARTITIONS; i++ {
		jobIDsChans[i] = make(chan *dto.JobIdTranslation, BATCH_SIZE)
		wWg.Add(1)
		go storeJobIDTranslation(&wWg, jobIDsChans[i])
	}

	// Create workers to read jobs from postgres
	for i := 0; i < PARTITIONS; i++ {
		wg.Add(1)

		go func(chanIndex int) {
			defer wg.Done()
			defer close(jobIDsChans[chanIndex])

			if err := jobsRepository.GetAllJobIDsByBatches(
				context.Background(),
				[]string{"system_hash_source_unique_job", "system_legacy_id", "job_id"},
				BATCH_SIZE,
				jobIDsChans[chanIndex],
			); err != nil {
				slog.Error("Error getting jobs from database", "error", err)
			}
		}(i)
	}

	slog.Info("Waiting for postgres readers channels to finish...")
	wg.Wait()

	slog.Info("Waiting for postgres writing channels to finish...")
	wWg.Wait()

	elapsedTime := time.Since(startTime)
	fmt.Printf("Script took %s\n", elapsedTime)
}
