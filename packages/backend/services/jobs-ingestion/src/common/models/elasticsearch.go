package models

import "time"

// SearchResponse represents the overall _search response
type ElasticsearchResponse struct {
	Took     int    `json:"took"`
	TimedOut bool   `json:"timed_out"`
	Hits     Hits   `json:"hits"`
	Error    any    `json:"error,omitempty"`
	ScrollID string `json:"_scroll_id"`
	Count    int    `json:"count,omitempty"`
	Shards   Shards `json:"_shards,omitempty"`
}

type ElasticsearchResponseStd struct {
	Took     int     `json:"took"`
	TimedOut bool    `json:"timed_out"`
	Hits     HitsStd `json:"hits"`
	Error    any     `json:"error,omitempty"`
	ScrollID string  `json:"_scroll_id"`
	Count    int     `json:"count,omitempty"`
	Shards   Shards  `json:"_shards,omitempty"`
}

// SearchResponse represents the overall _search response
type ElasticsearchParityResponse struct {
	Took         int          `json:"took"`
	TimedOut     bool         `json:"timed_out"`
	Hits         HitsParity   `json:"hits"`
	Aggregations Aggregations `json:"aggregations,omitempty"`
	Error        any          `json:"error,omitempty"`
	ScrollID     string       `json:"_scroll_id"`
}

type ElasticsearchGeocodeResponse struct {
	Took     int         `json:"took"`
	TimedOut bool        `json:"timed_out"`
	Hits     HitsGeocode `json:"hits"`
	Error    any         `json:"error,omitempty"`
	ScrollID string      `json:"_scroll_id"`
}

type Aggregations struct {
	ScanID ScanID `json:"scanid"`
}

type Bucket struct {
	// Define los campos que contiene cada bucket si es necesario
	// Por ejemplo:
	Key      interface{} `json:"key"`
	DocCount interface{} `json:"doc_count"`
}
type ScanID struct {
	DocCountErrorUpperBound interface{} `json:"doc_count_error_upper_bound"`
	SumOtherDocCount        interface{} `json:"sum_other_doc_count"`
	Buckets                 []Bucket    `json:"buckets"`
}

type ResAggregationsScanid struct {
	Scanid map[string]struct {
		Total int
	}
}

// Shards represents the _shards section of the response
type Shards struct {
	Total      int `json:"total"`
	Successful int `json:"successful"`
	Skipped    int `json:"skipped"`
	Failed     int `json:"failed"`
}

type Hits struct {
	Total struct {
		Relation string `json:"relation"`
		Value    int64  `json:"value"`
	} `json:"total"`
	Hits []ElasticJob `json:"hits"`
}

type HitsStd struct {
	Total struct {
		Relation string `json:"relation"`
		Value    int64  `json:"value"`
	} `json:"total"`
	Hits []ElasticEntryStd `json:"hits"`
}

type ElasticEntryStd struct {
	ID         string                 `json:"_id"`
	Index      string                 `json:"_index"`
	SourceData map[string]interface{} `json:"_source"`
	Sort       []interface{}          `json:"sort"`
}

type HitsParity struct {
	Total struct {
		Relation string `json:"relation"`
		Value    int64  `json:"value"`
	} `json:"total"`
	Hits []ElasticParityJob `json:"hits"`
}

type HitsGeocode struct {
	Total struct {
		Relation string `json:"relation"`
		Value    int64  `json:"value"`
	} `json:"total"`
	Hits []ElasticGeocode `json:"hits"`
}

type ScrollConfig struct {
	Size      int `json:"size"`
	TotalDocs int `json:"total_docs"`
}

type GeoData struct {
	Lat interface{} `json:"lat"`
	Lon interface{} `json:"lon"`
}

// ElasticJob structure (from the JSONL file)
// VERIFIED
type ElasticJob struct {
	ID         string               `json:"_id"`
	Index      string               `json:"_index"`
	SourceData ElasticJobSourceData `json:"_source"`
	Sort       []interface{}        `json:"sort"`
}

type ElasticParityJob struct {
	ID                int64
	SystemScanID      int
	SystemStatus      int
	SystemLegacyID    string
	SystemDateUpdated time.Time
	SystemDateExpired time.Time
	SystemDateFound   time.Time
	SystemDateReFound time.Time
	IsPpc             bool
}

type ElasticGeocode struct {
	ID         string                   `json:"_id"`
	Index      string                   `json:"_index"`
	SourceData ElasticGeocodeSourceData `json:"_source"`
	Sort       []interface{}            `json:"sort"`
}

type ElasticFalconGeocode struct {
	ID         string                         `json:"_id"`
	Index      string                         `json:"_index"`
	SourceData ElasticFalconGeocodeSourceData `json:"_source"`
}

type ElasticJobSourceData struct {
	ApplyApiEndpoint           interface{} `json:"apply_api_endpoint,omitempty"`
	ApplyClientJobId           interface{} `json:"apply_client_jobid,omitempty"`
	ApplyExtraInfo             interface{} `json:"apply_extra_info,omitempty"`
	ApplyEmail                 interface{} `json:"apply_email,omitempty"`
	ApplyType                  interface{} `json:"apply_type,omitempty"`
	ApplyDelivery              interface{} `json:"apply_delivery,omitempty"`
	ApplyStandardQuestions     interface{} `json:"apply_standard_questions,omitempty"`
	ApplyExtraQuestions        interface{} `json:"apply_extra_questions,omitempty"`
	CampaignObjective          interface{} `json:"campaign_objective,omitempty"`
	Cluster                    interface{} `json:"cluster,omitempty"`
	Company                    interface{} `json:"company,omitempty"`
	Industry                   interface{} `json:"industry,omitempty"`
	CompanyType                interface{} `json:"company_type,omitempty"`
	CompanyName                interface{} `json:"company_name,omitempty"`
	CompanyTypeNumeric         interface{} `json:"company_type_numeric,omitempty"`
	CrawlGroup                 interface{} `json:"crawlgroup,omitempty"`
	CrunchbaseUUID             interface{} `json:"crunchbase_uuid,omitempty"`
	ClientTag                  interface{} `json:"client_tag,omitempty"`
	Cpa                        interface{} `json:"cpa,omitempty"`
	CampaignId                 interface{} `json:"campaign_id,omitempty"`
	DataRandom                 interface{} `json:"data_random,omitempty"`
	FalconDateUpdatedV2        interface{} `json:"falcon_date_updated_v2,omitempty"`
	DateUpdated                interface{} `json:"date_updated,omitempty"`
	DateFound                  interface{} `json:"datefound,omitempty"`
	DateReopen                 interface{} `json:"datereopen,omitempty"`
	DateShow                   interface{} `json:"dateshow,omitempty"`
	DateClosedRaw              interface{} `json:"dateclosed_raw,omitempty"`
	DatePostedRaw              interface{} `json:"dateposted_raw,omitempty"`
	DateClosed                 interface{} `json:"dateclosed,omitempty"`
	DatePosted                 interface{} `json:"dateposted,omitempty"`
	DateOut                    interface{} `json:"dateout,omitempty"`
	EmpCode                    interface{} `json:"empcode,omitempty"`
	EmpName                    interface{} `json:"empname,omitempty"`
	EmpNameNormalized          interface{} `json:"empname_normalized,omitempty"`
	EmpEmail                   interface{} `json:"emp_email,omitempty"`
	ExperienceRequired         interface{} `json:"experience_required,omitempty"`
	ExperienceRequiredMax      interface{} `json:"experience_required_max,omitempty"`
	ExperienceRequiredMin      interface{} `json:"experience_required_min,omitempty"`
	ExperienceRequiredSnippet  interface{} `json:"experience_required_snippet,omitempty"`
	ExperienceRequiredValid    interface{} `json:"experience_required_valid,omitempty"`
	FeedType                   interface{} `json:"feed_type,omitempty"`
	FlagActive                 interface{} `json:"flag_active,omitempty"`
	FlagGeolocation            interface{} `json:"flag_geolocation,omitempty"`
	FlagGeolocationStatus      interface{} `json:"flag_geolocation_status,omitempty"`
	FlagHasHTML                interface{} `json:"flag_has_html,omitempty"`
	FlagHTML                   interface{} `json:"flag_html,omitempty"`
	FlagHTMLError              interface{} `json:"flag_html_error,omitempty"`
	FlagJobDesc                interface{} `json:"flag_jobdesc,omitempty"`
	FlagLanguageDetection      interface{} `json:"flag_language_detection,omitempty"`
	FlagPostProcess            interface{} `json:"flag_postprocess,omitempty"`
	GeoCity                    interface{} `json:"geo_city,omitempty"`
	GeoCountry                 interface{} `json:"geo_country,omitempty"`
	GeoLatLon                  interface{} `json:"geo_latlon,omitempty"`
	GeoLatLon0GeoCoordinate    interface{} `json:"geo_latlon_0_geo_coordinate,omitempty"`
	GeoLatLon1GeoCoordinate    interface{} `json:"geo_latlon_1_geo_coordinate,omitempty"`
	GeoRegion1                 interface{} `json:"geo_region1,omitempty"`
	GeoRegion2                 interface{} `json:"geo_region2,omitempty"`
	GeoRemote                  interface{} `json:"geo_remote,omitempty"`
	GeoDat                     GeoData     `json:"geodat,omitempty"`
	GLVersion                  interface{} `json:"gl_version,omitempty"`
	GoogleError                interface{} `json:"google_error,omitempty"`
	GoogleIndexed              interface{} `json:"google_indexed,omitempty"`
	MetSeoRules                interface{} `json:"met_seo_rules,omitempty"`
	HashJobDescCompany         interface{} `json:"hash_jobdesc_company,omitempty"`
	HashJobDescTitle           interface{} `json:"hash_jobdesc_title,omitempty"`
	HashTitleCompany           interface{} `json:"hash_title_company,omitempty"`
	HTML                       interface{} `json:"html,omitempty"`
	ID                         interface{} `json:"id"`
	IsATS                      interface{} `json:"is_ats,omitempty"`
	IsAutoApply                interface{} `json:"is_autoapply,omitempty"`
	IsGL                       interface{} `json:"is_gl,omitempty"`
	IsPPC                      interface{} `json:"is_ppc,omitempty"`
	IsSandbox                  interface{} `json:"is_sandbox,omitempty"`
	IsExclusive                interface{} `json:"is_exclusive,omitempty"`
	IsFastApply                interface{} `json:"fast_apply,omitempty"`
	JobSource                  interface{} `json:"job_source,omitempty"`
	JobTemp                    interface{} `json:"job_temp,omitempty"`
	JobDataPostProcess         interface{} `json:"jobdata_postprocess,omitempty"`
	JobDesc                    interface{} `json:"jobdesc,omitempty"`
	JobDescLen                 interface{} `json:"jobdesc_len,omitempty"`
	JobHash                    interface{} `json:"jobhash,omitempty"`
	JobID                      interface{} `json:"jobid,omitempty"`
	JobTypeVersion             interface{} `json:"jobtype_version,omitempty"`
	JobTypes                   interface{} `json:"jobtypes,omitempty"`
	JobBudget                  interface{} `json:"job_budget,omitempty"`
	JobPixel                   interface{} `json:"job_pixel,omitempty"`
	Language                   interface{} `json:"language,omitempty"`
	LeadType                   interface{} `json:"lead_type,omitempty"`
	Link                       interface{} `json:"link,omitempty"`
	LinkedInID                 interface{} `json:"linkedin_id,omitempty"`
	Location                   interface{} `json:"location,omitempty"`
	LocationMySQL              interface{} `json:"location_mysql,omitempty"`
	Logo                       interface{} `json:"logo,omitempty"`
	Platform                   interface{} `json:"platform,omitempty"`
	PPC                        interface{} `json:"ppc,omitempty"`
	PPCDiscount                interface{} `json:"ppc_discount,omitempty"`
	PPCU                       interface{} `json:"ppc_u,omitempty"`
	PPCUDiscount               interface{} `json:"ppc_u_discount,omitempty"`
	QualityCity                interface{} `json:"quality_city,omitempty"`
	QualityCountry             interface{} `json:"quality_country,omitempty"`
	QualityLocVersion          interface{} `json:"quality_loc_version,omitempty"`
	QualityLocation            interface{} `json:"quality_location,omitempty"`
	QualityState               interface{} `json:"quality_state,omitempty"`
	Rank                       interface{} `json:"rank,omitempty"`
	ReqID                      interface{} `json:"reqid,omitempty"`
	SalaryAvg                  interface{} `json:"salary_avg,omitempty"`
	SalaryCurrency             interface{} `json:"salary_currency,omitempty"`
	SalaryMatch                interface{} `json:"salary_match,omitempty"`
	SalaryMax                  interface{} `json:"salary_max,omitempty"`
	SalaryMin                  interface{} `json:"salary_min,omitempty"`
	SalaryMonthly              interface{} `json:"salary_monthly,omitempty"`
	SalarySnippet              interface{} `json:"salary_snippet,omitempty"`
	SalaryType                 interface{} `json:"salary_type,omitempty"`
	SalaryValid                interface{} `json:"salary_valid,omitempty"`
	SalaryVersion              interface{} `json:"salary_version,omitempty"`
	SalaryYearly               interface{} `json:"salary_yearly,omitempty"`
	ScanID                     interface{} `json:"scanid,omitempty"`
	ShowRawHTML                interface{} `json:"show_raw_html,omitempty"`
	ShowSalaryOnFront          interface{} `json:"show_salary_on_front,omitempty"`
	SiteCode                   interface{} `json:"sitecode,omitempty"`
	SOCCardID                  interface{} `json:"soc_card_id,omitempty"`
	SOCBroadGroup              interface{} `json:"soc_broad_group,omitempty"`
	SOCClassifier              interface{} `json:"soc_classifier,omitempty"`
	SOCLocalClassificationType interface{} `json:"soc_local_classification_type,omitempty"`
	SOCDetailedGroup           interface{} `json:"soc_detailed_group,omitempty"`
	SOCDetectedLanguage        interface{} `json:"soc_detected_language,omitempty"`
	SOCJobZone                 interface{} `json:"soc_job_zone,omitempty"`
	SOCMajorGroup              interface{} `json:"soc_major_group,omitempty"`
	SOCMatchedTitle            interface{} `json:"soc_matched_title,omitempty"`
	SOCMinorGroup              interface{} `json:"soc_minor_group,omitempty"`
	SOCOccupationTitle         interface{} `json:"soc_occupation_title,omitempty"`
	SOCOnetSOCCode             interface{} `json:"soc_onetsoc_code,omitempty"`
	SOCTopDescScore            interface{} `json:"soc_top_desc_score,omitempty"`
	SOCTopTitleScore           interface{} `json:"soc_top_title_score,omitempty"`
	Source                     interface{} `json:"source,omitempty"`
	SourceApplyEmail           interface{} `json:"source_apply_email,omitempty"`
	SourcePPC                  interface{} `json:"source_ppc,omitempty"`
	SourceEmpName              interface{} `json:"source_empname,omitempty"`
	SourceJobType              interface{} `json:"source_jobtype,omitempty"`
	SourceJobTypeNormalized    interface{} `json:"source_jobtype_normalized,omitempty"`
	SourceJobTypeSnippet       interface{} `json:"source_jobtype_snippet,omitempty"`
	SourceLocation             interface{} `json:"source_location,omitempty"`
	SourceName                 interface{} `json:"source_name,omitempty"`
	SourceCountry              interface{} `json:"source_country,omitempty"`
	SourceState                interface{} `json:"source_state,omitempty"`
	SourceCity                 interface{} `json:"source_city,omitempty"`
	SourceRemote               interface{} `json:"source_remote,omitempty"`
	SourceSalaryStr            interface{} `json:"source_salary_str,omitempty"`
	SourceBenefit              interface{} `json:"source_benefit,omitempty"`
	SpiderName                 interface{} `json:"spider_name,omitempty"`
	SpiderPostProcess          interface{} `json:"spider_postprocess,omitempty"`
	Strategy                   interface{} `json:"strategy,omitempty"`
	Tag                        interface{} `json:"tag,omitempty"`
	Title                      interface{} `json:"title,omitempty"`
	TitleModified              interface{} `json:"title_modified,omitempty"`
	TitleSimply                interface{} `json:"title_simply,omitempty"`
	TitleWordCount             interface{} `json:"title_word_count,omitempty"`
	TrackerEnabled             interface{} `json:"tracker_enabled,omitempty"`
	VendorCampaign             interface{} `json:"vendor_campaign,omitempty"`
	UpdatesToday               interface{} `json:"updates_today,omitempty"`
}

type ElasticGeocodeSourceData struct {
	Search            interface{} `json:"search,omitempty"`
	Data              interface{} `json:"data,omitempty"`
	Source            interface{} `json:"source,omitempty"`
	ClientIp          interface{} `json:"client_ip,omitempty"`
	DateInserted      interface{} `json:"date_inserted,omitempty"`
	DataInserted      interface{} `json:"data_inserted,omitempty"`
	ID                interface{} `json:"id,omitempty"`
	GeoDat            GeoData     `json:"geodat,omitempty"`
	Region1           interface{} `json:"region1,omitempty"`
	Region2           interface{} `json:"region2,omitempty"`
	Country           interface{} `json:"country,omitempty"`
	City              interface{} `json:"city,omitempty"`
	Region1Normalized interface{} `json:"region1_normalized,omitempty"`
}

type ElasticFalconGeocodeSourceData struct {
	Search interface{} `json:"search,omitempty"`
	Data   interface{} `json:"data,omitempty"`
	Source interface{} `json:"source,omitempty"`
}
