apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: geocode-virtualservice
  labels:
    app: geocode
spec:
  hosts:
    - geocode-service.apps.talent.com
  gateways:
    - apps-gateway
  http:
    - match:
        - uri:
            prefix: /
      route:
        - destination:
            host: geocode-service.apps.svc.cluster.local
            port:
              number: 7003
---
apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: geocode-grpc-virtualservice
  labels:
    app: geocode
spec:
  hosts:
    - geocode-grpc-service.apps.talent.com
  gateways:
    - apps-gateway
  http:
    - match:
        - uri:
            prefix: /
      route:
        - destination:
            host: geocode-service.apps.svc.cluster.local
            port:
              number: 7004
