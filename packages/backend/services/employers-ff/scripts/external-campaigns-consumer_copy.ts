/* eslint-disable @typescript-eslint/no-explicit-any */
import "dotenv/config";
import { KafkaManagerService } from "../../../libs/ts/queue/src/index";
import { brokerMSK } from "../../../libs/ts/queue/src/kafka/types";
import { EachMessagePayload } from "kafkajs";
import Piscin<PERSON> from "piscina";
import path from "path";
import os from "os";

const EXTERNAL_CAMPAIGNS_KAFKA_BROKER: brokerMSK[] = process.env.EXTERNAL_CAMPAIGNS_KAFKA_BROKER
  ? (process.env.EXTERNAL_CAMPAIGNS_KAFKA_BROKER.split(",") as brokerMSK[])
  : [
      "kafka-broker-auto-campaign-v1-0.talent.private:9092",
      "kafka-broker-auto-campaign-v1-1.talent.private:9092",
      "kafka-broker-auto-campaign-v1-2.talent.private:9092",
    ];

const KAFKA_CLIENT_ID = process.env.EXTERNAL_CAMPAIGNS_KAFKA_CLIENT_ID ?? "external_campaigns";

// Define a base group ID and add a suffix for each partition
const KAFKA_BASE_GROUP_ID = process.env.EXTERNAL_CAMPAIGNS_KAFKA_GRUOP_ID1 ?? "external-campaigns-consumer-appcast";

const kafkaConfig = {
  retry: {
    initialRetryTime: 250,
    retries: 5,
    maxRetryTime: 25000,
  },
  connectionTimeout: 2500,
  requestTimeout: 25000,
};

let app: any; // NestJS application context
let workerPool: Piscina; // Piscina worker pool

const TOPIC = "ec-campaigns";

// Create an array to store all Kafka manager instances
const kafkaManagers: KafkaManagerService[] = [];

/**
 * Setup multiple consumers, one per partition
 */
function run() {
  const kafkaManager = KafkaManagerService.getInstanceReplication(
    EXTERNAL_CAMPAIGNS_KAFKA_BROKER,
    KAFKA_CLIENT_ID,
    kafkaConfig,
  );
  
  // Store the manager instance for later cleanup
  kafkaManagers.push(kafkaManager);
  
  console.log(`Starting consumer with group ID ${KAFKA_BASE_GROUP_ID}`);
  
  kafkaManager.consumer.runConsumer(
    {
      topics: [TOPIC],
      fromBeginning: true,
    },
    KAFKA_BASE_GROUP_ID,
    (payload) => processTopic(payload),
    12,
    true
  );
}

async function processTopic({ message, topic, partition }: EachMessagePayload) {
  //  // Check if the queue is approaching its limit before adding more work
  //  if (workerPool.queueSize >= workerPool.options.maxQueue * 0.8) {
  //   // Queue is getting full, pause the consumer temporarily
  //   console.log(`Queue near capacity (${workerPool.queueSize}/${workerPool.options.maxQueue}), pausing consumer`);
  //   await kafkaManagers[0].consumer.pause([{ topic }]);
    
  //   // Wait for queue to drain before resuming
  //   const checkQueueAndResume = async () => {
  //     if (workerPool.queueSize < workerPool.options.maxQueue * 0.5) {
  //       console.log(`Queue reduced (${workerPool.queueSize}/${workerPool.options.maxQueue}), resuming consumer`);
  //       await kafkaManagers[0].consumer.resume([{ topic }]);
  //     } else {
  //       // Check again after a short delay
  //       setTimeout(checkQueueAndResume, 500);
  //     }
  //   };
    
  //   setTimeout(checkQueueAndResume, 500);
  // }

  await workerPool.run({
    message: message.value ? message.value.toString() : null,
    topic,
    serviceType: topic === "ec-campaigns" ? "campaignsConsumer" : "externalXmlConsumer"
  });
}

["SIGINT", "SIGTERM"].forEach((signal) => {
  process.on(signal, async () => {
    console.log("Closing connections...");
    
    // Disconnect all Kafka consumers
    for (const kafkaManager of kafkaManagers) {
      await kafkaManager.consumer.disconnect();
    }
    
    // Terminate the worker pool
    if (workerPool) {
      await workerPool.destroy();
    }
    
    if (app) {
      await app.close();
    }
    
    console.log("All connections closed");
    process.exit(0);
  });
});

// Bootstrap NestJS and start the process
async function bootstrap() {
  // Initialize Piscina worker pool
  const cpuCount = os.cpus().length;
  const numThreads = Math.max(2, Math.min(Math.floor(cpuCount * 0.75), 50));
  
  // Use environment variable if provided
  const maxThreads = numThreads;
    
  // console.log(`Setting up worker pool with ${maxThreads} threads (system has ${cpuCount} CPUs)`);
  
  workerPool = new Piscina({
    filename: path.resolve(__dirname, './worker.js'),
    minThreads: 10,
    maxThreads: 10,
    idleTimeout: 60000, // seconds
    maxQueue: 'auto',
  });

  console.log(`Started worker pool with ${maxThreads} threads`);
  
  await run();
}

bootstrap().catch(console.error);