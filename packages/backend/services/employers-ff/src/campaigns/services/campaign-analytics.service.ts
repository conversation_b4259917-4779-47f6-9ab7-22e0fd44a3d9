import { HttpException, HttpStatus, Inject, Injectable, Logger } from "@nestjs/common";
import { CampaignsService } from "./campaigns.service";
import { CampaignQuickFiltersDto } from "../dto/campaign-quick-filters.dto";
import { UsersService } from "../../users/services/user.service";
import { Campaigns } from "../entities/campaign.entity";
import { CampaignsAnalyticsRepository } from "../repositories/campaign-analytics.repository";
import { CurrencyService } from "../../common/resources/currencies";
import { EmployersAnalyticsDTO } from "../dto/employers-analytics-info.dto";
import { AccountService } from "../../accounts/services/accounts.service";
import { Accounts } from "../../accounts/entities/account.entity";
import {
  IEmployerAnalyticsResponse,
  IEmployersAnalyticsDataFormatted,
  IEmployersAnalyticsOverview,
  IEmployersAnalyticsRepositoryMetrics,
  IEmployersMetricsByCampaignId,
  IEmployersMetricsByJob,
  IEmployersSummaryData,
} from "../../common/interfaces/employers-analytics-responses.interface";
import { CampaignViewDTO } from "../dto/campaign-view.dto";
import { CampaignViewTrafficData } from "../../common/interfaces/campaign-view-data.interface";
import { AccountCurrency, SortType } from "../../common/resources/enums";
import path from "path";
import { CACHE_MANAGER } from "@nestjs/cache-manager";
import { Cache } from "cache-manager";
import Piscina from "piscina";
import os from "os";
import { config as dotenvConfig } from "dotenv";
import { TypeOrmModuleOptions } from "@nestjs/typeorm";
import talentRearchEmployersConfig from "../../configs/typeorm/talent-rearch-employers.config";
import postgreSQLConfig from "../../configs/typeorm/employers-postgres.config";
import {
  QuickFiltersResponse,
  QuickFiltersResult,
} from "../../common/interfaces/quick-filters.interface";
import { CustomAxiosAdapter } from "../../common/adapters/axios.adapter";

dotenvConfig({ path: path.resolve(__dirname, "../../../.env") });

/**
 * CampaignsAnalyticsService: manages quick filter's filtering, calculations and data formatting.
 */
@Injectable()
export class CampaignsAnalyticsService {
  private mySQLConn: any;
  private mySQLConnWithSubs: any;
  private postgreSQLConn: any;
  /**
   * Init the class' objects
   */
  constructor(
    @Inject(CACHE_MANAGER)
    private readonly cacheManager: Cache,
    private readonly campaignsService: CampaignsService,
    private readonly campaignsAnalyticsRepository: CampaignsAnalyticsRepository,
    private readonly accountService: AccountService,
    private piscinaAnalytics: Piscina,
    private piscinaQuickFilters: Piscina,
    private readonly http: CustomAxiosAdapter,
  ) {
    this.piscinaQuickFilters = new Piscina({
      filename: path.join(
        __dirname,
        "./../../../../packages/backend/services/employers-ff/employers_quick_filters_worker.js",
      ),
      minThreads: 4, // Ensure at least 2 workers are always running
      maxThreads: os.cpus().length, // Use the number of CPU cores as the maximum number of threads
    });

    this.piscinaAnalytics = new Piscina({
      filename: path.join(
        __dirname,
        "./../../../../packages/backend/services/employers-ff/employers_analytics_worker.js",
      ),
      minThreads: 4, // Ensure at least 2 workers are always running
      maxThreads: os.cpus().length, // Use the number of CPU cores as the maximum number of threads
    });

    this.mySQLConnWithSubs = this.createMySQLConfig();
    this.mySQLConn = this.cleanMySQLConfig(this.mySQLConnWithSubs);

    this.postgreSQLConn = this.createPostgreSQLConfig();
  }

  /**
   *
   */
  private cleanMySQLConfig(config: TypeOrmModuleOptions): TypeOrmModuleOptions {
    const { subscribers, ...cleanedConfig } = config;
    return cleanedConfig;
  }

  /**
   * Returns an instance of mySQL connection to be used in the worker files
   */
  private createMySQLConfig(): TypeOrmModuleOptions {
    return talentRearchEmployersConfig;
  }

  /**
   * Returns an instance of postgreSQL connection to be used in the worker files
   */
  private createPostgreSQLConfig(): TypeOrmModuleOptions {
    return postgreSQLConfig;
  }

  /**
   * Performs the calculations needed on each iteration to create the speed, budgetSpent and spendVs and adds it to the campaign.
   * Also formats the final data response.
   * @param queryResult
   * @param campaign
   * @param currency
   */
  private async formatQuickFiltersData(
    campaignStats: QuickFiltersResult[],
    currency: number,
    filters: CampaignQuickFiltersDto,
  ): Promise<QuickFiltersResponse> {
    const resultCards = {
      budgetMonth: 0,
      spentMonth: 0,
      spentLastMonth: 0,
      spentToday: 0,
      spentVs: "0",
    };
    let accountbudgetMonth = 0;
    const formattedData: any = [];
    let formattedDataFinal: any = [];
    const relations = ["campaigns", "accountsBudget"];
    const accountsArrays = []; //To save account ids
    try {
      resultCards.spentMonth = campaignStats[0].total_spent_period_cad;
      resultCards.spentLastMonth = campaignStats[0].total_spent_last_period_cad;
      resultCards.spentToday = campaignStats[0].total_spent_today_cad;
      resultCards.budgetMonth = 0; // deprecated until we find a good solution for this

      // Second pass - handle the data formatting, with or without grouping
      if (filters.accountPanel) {
        for (const account of campaignStats) {
          accountsArrays.push(account.account_id);
        }

        const uniqueAccountIds = [...new Set(accountsArrays)]; //unique ids
        const accountsInfoList = await this.accountService.findByIds(uniqueAccountIds, relations);
        const accountsInfoMap = new Map<number, Accounts>(); //set a map to store account info
        for (const acc of accountsInfoList) {
          if (acc?.id) {
            accountsInfoMap.set(acc.id, acc);
          }
        }
        for (const account of campaignStats) {
          const accountId = account.account_id;

          const spent =
            filters.underspending && account.flexible_spent_underspending > 0
              ? Number(account.flexible_spent_underspending)
              : Number(account.spent_period);
          const spentPeriodCad = Number(account.spent_period_cad ?? 0);
          const spentLastPeriodCad = Number(account.spent_last_period_cad ?? 0);
          const spentToday = account.spent_today ?? 0;

          const budgetSpentFinal = Number.isFinite(spent * currency)
            ? parseFloat((spent * currency).toFixed(2))
            : 0;
          const spentTodayFinal = Number.isFinite(spentToday * currency)
            ? spentToday * currency
            : 0;

          let cadc = 0;
          if (filters.yesterdayCadc) {
            cadc = account.missed_revenue || 0;
          }
          const accountInfo = accountsInfoMap.get(Number(accountId)); //let's see whether the account exists
          if (!accountInfo) {
            Logger.warn(`account data not found for ID: ${accountId}`);
            continue;
          }
          let accountBudget: any = "--";
          let campaignNumber: any = "--";
          let dateCreated: any = "--";
          let type: any = "--";
          if (accountInfo) {
            if (accountInfo.accountsBudget && accountInfo.accountsBudget.budget) {
              accountBudget = accountInfo.accountsBudget.budget;
            }

            if (accountInfo.campaigns) {
              campaignNumber = accountInfo.campaigns.length;
            }

            if (accountInfo.created) {
              dateCreated = accountInfo.created;
            }

            if (accountInfo.accountType) {
              type = accountInfo.accountType;
            }
          }
          if (accountBudget != "--") {
            accountbudgetMonth += Number(accountBudget);
          }
          // Calculate cost per conversion for the account
          const costConv =
            account.conversions_period &&
            Number.isFinite(spent / Number(account.conversions_period))
              ? Number((spent / Number(account.conversions_period)).toFixed(2))
              : "--";

          // Calculate spend vs
          let spendVs: number | string = "N/A";
          // If the current period is not in current month, we do not calculate the spendVs.
          if (spentPeriodCad === 0 && spentLastPeriodCad === 0) {
            spendVs = "0.00"; // No spend in either period
          } else if (spentLastPeriodCad === 0 && spentPeriodCad > 0) {
            spendVs = "100.00"; // All new spend this period
          } else if (spentLastPeriodCad > 0 && spentPeriodCad === 0) {
            spendVs = "-100.00"; // Stopped spending
          } else {
            spendVs = ((spentPeriodCad / spentLastPeriodCad - 1) * 100 * currency).toFixed(2);
          }

          formattedData.push({
            country: account.account_country ?? "",
            feedcode: account.feedcode,
            accountId: accountId,
            accountName: account.company_label,
            budget: accountBudget,
            campaignNumber: campaignNumber,
            dateCreated: dateCreated,
            type: type,
            vip: account.vip_account,
            currency: account.account_currency,
            budgetSpent: Number(budgetSpentFinal.toFixed(2)),
            spentToday: Number(spentTodayFinal.toFixed(2)),
            costConv: costConv,
            spendVs,
            ...(filters.yesterdayCadc && { cadc: cadc }),
          });
        }
        resultCards.budgetMonth = accountbudgetMonth;
        formattedDataFinal = formattedData;
      } else {
        campaignStats.forEach((stats: QuickFiltersResult) => {
          // Add up the budget
          resultCards.budgetMonth +=
            stats.budget_type == "monthly" ? stats.budget_month : stats.budget_day;
          // Calculating budgetDelivery
          let budgetDelivery = "";
          if (stats.has_pacing == "0") {
            budgetDelivery = "Accelerated";
          } else if (stats.has_pacing == "1") {
            budgetDelivery = "Paced";
          } else {
            budgetDelivery = "Paced (Flexible)";
          }

          // Calculating budget | Default 0 => "not_sponsored"
          let budget = 0;
          if (stats.budget_type == "monthly") {
            budget = stats.budget_month;
          } else if (stats.budget_type == "daily") {
            budget = stats.budget_day;
          } else if (filters.underspending) {
            budget = stats.flexible_spent_underspending
              ? (stats.flexible_spent_underspending || 0) * currency
              : (stats.flexible_spent || 0) * currency;
          }

          // Calculating conversionLetters
          let conversionLetters = "";
          if (stats.conversion_type == "application") {
            conversionLetters = "A";
          } else if (stats.conversion_type == "applystart") {
            conversionLetters = "AS";
          } else if (stats.conversion_type == "other") {
            conversionLetters = "O";
          } else if (stats.conversion_type == "qualified_application") {
            conversionLetters = "QA";
          } else if (stats.conversion_type == "hire") {
            conversionLetters = "H";
          }

          // If underspending Quick Filter, the conversions and spent are calculated using the campaign's date_start and date_end
          const spent =
            filters.underspending && stats.flexible_spent_underspending > 0
              ? Number(stats.flexible_spent_underspending)
              : Number(stats.spent_period);
          const spentPeriodCad = Number(stats.spent_period_cad ?? 0);
          const spentLastPeriodCad = Number(stats.spent_last_period_cad ?? 0);
          const spentToday = stats.spent_today ?? 0;
          const spentDaysPeriod = Number(stats.spent_days_period) ?? 0;
          const conversionsTotal =
            conversionLetters === "" ? 0 : (Number(stats.conversions_total) ?? 0);

          const conversionTargetCost = Number(stats.conversion_target_cost) ?? 0;

          // Calculating and Formatting
          const periodConversions =
            conversionLetters === "" ? 0 : (Number(stats.conversions_period) ?? 0);

          const conversions = filters.underspending ? conversionsTotal : periodConversions;

          const costConv =
            conversions && Number.isFinite(spent / conversions)
              ? Number(((spent / conversions) * currency).toFixed(2))
              : "--";
          const spikeDiff = Number.isFinite(spentToday / (spent / spentDaysPeriod))
            ? spentToday / (spent / spentDaysPeriod) - 1
            : 0;
          const totalConversions = Number.isFinite(conversions)
            ? `${conversions} ${conversionLetters}`
            : 0;

          let spendVs: number | string = "N/A";
          // If the current period is not in current month, we do not calculate the spendVs.
          if (spentPeriodCad === 0 && spentLastPeriodCad === 0) {
            spendVs = "0.00"; // No spend in either period
          } else if (spentLastPeriodCad === 0 && spentPeriodCad > 0) {
            spendVs = "100.00"; // All new spend this period
          } else if (spentLastPeriodCad > 0 && spentPeriodCad === 0) {
            spendVs = "-100.00"; // Stopped spending
          } else {
            spendVs = ((spentPeriodCad / spentLastPeriodCad - 1) * currency * 100).toFixed(2);
          }

          const budgetSpentFinal = Number.isFinite(spent * currency)
            ? parseFloat((spent * currency).toFixed(2))
            : 0;
          // Applying Currency change to fields
          const spentTodayFinal = Number.isFinite(spentToday * currency)
            ? spentToday * currency
            : 0;
          let targetFinal = 0;
          if (conversionTargetCost) {
            targetFinal = conversionTargetCost * currency;
          }

          formattedData.push({
            feedcode: stats.feedcode,
            campaignId: stats.campaign_id,
            accountId: stats.account_id,
            campaignName: stats.campaign_name,
            // The line below is what we're replacing with our grouping logic when filters.accountPanel is true
            ...(filters.accountPanel
              ? { accountName: stats.company_label }
              : { campaignId: stats.campaign_id, campaignName: stats.campaign_name }),
            objective: stats.campaign_objective,
            budgetType: stats.budget_type,
            budgetDelivery: budgetDelivery,
            budget: Number(budget),
            currency: stats.account_currency,
            budgetSpent: budgetSpentFinal,
            spendVs: spendVs,
            spentToday: spentTodayFinal,
            flexibleSpent: parseFloat(stats.flexible_spent.toFixed(2)),
            conversions: totalConversions,
            target: targetFinal,
            costConv: costConv,
            // jobs: stats.total_jobs_today || 0, // jobs wil come from jobs-processing endpoint
            country: stats.account_country,
            status: stats.campaign_status,
            ...(filters.yesterdayCadc && { cadc: stats.missed_revenue }), // Conditionally add cadc if yesterdayCadc is true
            spikeDiff: spikeDiff,
            speed: !isNaN(stats.speed) && stats.speed > 0 ? Number(stats.speed.toFixed(2)) : "--",
            vip: stats.vip_account,
          });
        });

        //Get the list of campaigns ids to query the jobs-processing endpoint to get the amount of jobs for each
        const campaignIds = formattedData
          .map((item: { campaignId: any }) => item.campaignId)
          .join(",");

        const totalJobsInfo = await this.getTotalJobsForCampaignsIds(campaignIds);
        const jobsCountMap = new Map(
          totalJobsInfo.map((item: { id: any; job_count: any }) => [item.id, item.job_count]),
        );

        //Assigning jobs count to formatted data
        formattedDataFinal = formattedData.map((item: { campaignId: any }) => ({
          ...item,
          jobs: jobsCountMap.get(item.campaignId) ?? 0,
        }));

        //Applying the noJObs filters at the end of the data since noJobs filter can not be performed on original data
        if (filters.noJobs) {
          formattedDataFinal = formattedDataFinal.filter(
            (item: { jobs: number }) => item.jobs === 0,
          );
        }
      }

      // Calculate the spentVs with the accumulator object for the cards at the top of the page.
      if (resultCards.spentLastMonth === 0 && resultCards.spentMonth === 0) {
        resultCards.spentVs = "0.00"; // No change, no spend in either
      } else if (resultCards.spentLastMonth === 0 && resultCards.spentMonth > 0) {
        resultCards.spentVs = "100.00"; // All new spend this month
      } else if (resultCards.spentLastMonth > 0 && resultCards.spentMonth == 0) {
        resultCards.spentVs = "-100.00";
      } else {
        resultCards.spentVs = (
          (resultCards.spentMonth / resultCards.spentLastMonth - 1) *
          100 *
          currency
        ).toFixed(2);
      }

      let total = 0;

      if (filters.accountPanel) {
        total = Number(campaignStats[0]?.total_accounts || 0);
      } else {
        total = Number(campaignStats[0]?.total_campaigns || 0);
      }

      // Return the final array with everything organised.
      return {
        total: total,
        limit: filters.limit ?? 10000,
        offset: filters.offset ?? 0,
        budgetMonth: resultCards.budgetMonth,
        spentMonth: Number(resultCards.spentMonth?.toFixed(2)),
        spentLastMonth: Number(resultCards.spentLastMonth?.toFixed(2)),
        spentToday: Number(resultCards.spentToday?.toFixed(2)),
        spentVsLastMonth: resultCards.spentVs == "N/A" ? "N/A" : `${resultCards.spentVs}%`,
        data: Array.isArray(formattedDataFinal) ? formattedDataFinal : [],
      };
    } catch (e) {
      Logger.error(new Error(`Error while calculating aggregations: ${e}`));
      throw e;
    }
  }

  /**
   *
   */
  async getTotalJobsForCampaignsIds(campaignIds: string) {
    try {
      const jobsInfo = await this.http.get(
        `${process.env.URL_JOB_PROCESING}/get-employer-jobs-count?campaign_ids=${campaignIds}&only_active=true`,
      );
      return jobsInfo.data ?? [];
    } catch (error: any) {
      Logger.log(`There was an error trying to get the total jobs of campaigns ${error.message}`);
      return [];
    }
  }
  // private async formatQuickFiltersData(
  //   campaignStats: QuickFiltersResult[],
  //   currency: number,
  //   filters: CampaignQuickFiltersDto,
  // ): Promise<QuickFiltersResponse> {
  //   const resultCards = {
  //     budgetMonth: 0,
  //     spentMonth: 0,
  //     spentLastMonth: 0,
  //     spentToday: 0,
  //     spentVs: "0",
  //   };
  //   let accountbudgetMonth = 0;
  //   const formattedData: any = [];
  //   const relations = ["campaigns", "accountsBudget"];

  //   try {
  //     resultCards.spentMonth = campaignStats[0].total_spent_period_cad;
  //     resultCards.spentLastMonth = campaignStats[0].total_spent_last_period_cad;
  //     resultCards.spentToday = campaignStats[0].total_spent_today_cad;
  //     resultCards.budgetMonth = 0; // deprecated until we find a good solution for this

  //     // Second pass - handle the data formatting, with or without grouping
  //     if (filters.accountPanel) {
  //       for (const account of campaignStats) {
  //         const accountId = account.account_id;

  //         const spent =
  //           filters.underspending && account.flexible_spent_underspending > 0
  //             ? Number(account.flexible_spent_underspending)
  //             : Number(account.spent_period);
  //         const spentPeriodCad = Number(account.spent_period_cad ?? 0);
  //         const spentLastPeriodCad = Number(account.spent_last_period_cad ?? 0);

  //         const spentToday = account.spent_today ?? 0;

  //         const budgetSpentFinal = Number.isFinite(spent * currency)
  //           ? parseFloat((spent * currency).toFixed(2))
  //           : 0;
  //         const spentTodayFinal = Number.isFinite(spentToday * currency)
  //           ? spentToday * currency
  //           : 0;

  //         let cadc = 0;
  //         if (filters.yesterdayCadc) {
  //           cadc = account.missed_revenue || 0;
  //         }
  //         let accountInfo: any;
  //         try {
  //           accountInfo = await this.accountService.findOne(Number(accountId), relations);
  //         } catch (e: any) {
  //           Logger.warn(`account data not found for ID: ${accountId}`);
  //           continue;
  //         }
  //         let accountBudget: any = "--";
  //         let campaignNumber: any = "--";
  //         let dateCreated: any = "--";
  //         let type: any = "--";
  //         if (accountInfo) {
  //           if (accountInfo.accountsBudget && accountInfo.accountsBudget.budget) {
  //             accountBudget = accountInfo.accountsBudget.budget;
  //           }

  //           if (accountInfo.campaigns) {
  //             campaignNumber = accountInfo.campaigns.length;
  //           }

  //           if (accountInfo.created) {
  //             dateCreated = accountInfo.created;
  //           }

  //           if (accountInfo.accountType) {
  //             type = accountInfo.accountType;
  //           }
  //         }
  //         if (accountBudget != "--") {
  //           accountbudgetMonth += Number(accountBudget);
  //         }
  //         // Calculate cost per conversion for the account
  //         const costConv =
  //           account.conversions_period && Number.isFinite(spent / Number(account.conversions_period))
  //             ? Number((spent / Number(account.conversions_period)).toFixed(2))
  //             : "--";

  //         // Calculate spend vs
  //         let spendVs: number | string = "N/A";
  //         // If the current period is not in current month, we do not calculate the spendVs.
  //         if (spentPeriodCad === 0 && spentLastPeriodCad === 0) {
  //           spendVs = "0.00"; // No spend in either period
  //         } else if (spentLastPeriodCad === 0 && spentPeriodCad > 0) {
  //           spendVs = "100.00"; // All new spend this period
  //         } else if (spentLastPeriodCad > 0 && spentPeriodCad === 0) {
  //           spendVs = "-100.00"; // Stopped spending
  //         } else {
  //           spendVs = ((spentPeriodCad / spentLastPeriodCad - 1) * 100 * currency).toFixed(2);
  //         }

  //         formattedData.push({
  //           country: account.account_country ?? "",
  //           feedcode: account.feedcode,
  //           accountId: accountId,
  //           accountName: account.company_label,
  //           budget: accountBudget,
  //           campaignNumber: campaignNumber,
  //           dateCreated: dateCreated,
  //           type: type,
  //           vip: account.vip_account,
  //           currency: account.account_currency,
  //           budgetSpent: Number(budgetSpentFinal.toFixed(2)),
  //           spentToday: Number(spentTodayFinal.toFixed(2)),
  //           costConv: costConv,
  //           spendVs,
  //           ...(filters.yesterdayCadc && { cadc: cadc }),
  //         });
  //       }
  //       resultCards.budgetMonth = accountbudgetMonth;
  //     } else {
  //       campaignStats.forEach((stats: QuickFiltersResult) => {
  //         // Calculating budgetDelivery
  //         let budgetDelivery = "";
  //         if (stats.has_pacing == '0') {
  //           budgetDelivery = "Accelerated";
  //         } else if (stats.has_pacing == '1') {
  //           budgetDelivery = "Paced";
  //         } else {
  //           budgetDelivery = "Paced (Flexible)";
  //         }

  //         // Calculating budget | Default 0 => "not_sponsored"
  //         let budget = 0;
  //         if (stats.budget_type == "monthly") {
  //           budget = stats.budget_month;
  //         } else if (stats.budget_type == "daily") {
  //           budget = stats.budget_day;
  //         } else if (filters.underspending) {
  //           budget = stats.flexible_spent_underspending ? ((stats.flexible_spent_underspending || 0) * currency) : ((stats.flexible_spent || 0) * currency);
  //         }

  //         // Calculating conversionLetters
  //         let conversionLetters = "";
  //         if (stats.conversion_type == "application") {
  //           conversionLetters = "A";
  //         } else if (stats.conversion_type == "applystart") {
  //           conversionLetters = "AS";
  //         } else if (stats.conversion_type == "other") {
  //           conversionLetters = "O";
  //         } else if (stats.conversion_type == "qualified_application") {
  //           conversionLetters = "QA";
  //         } else if (stats.conversion_type == "hire") {
  //           conversionLetters = "H";
  //         }

  //         // If underspending Quick Filter, the conversions and spent are calculated using the campaign's date_start and date_end
  //         const spent =
  //           filters.underspending && stats.flexible_spent_underspending > 0
  //             ? Number(stats.flexible_spent_underspending)
  //             : Number(stats.spent_period);
  //         const spentPeriodCad = Number(stats.spent_period_cad ?? 0);
  //         const spentLastPeriodCad = Number(stats.spent_last_period_cad ?? 0);
  //         const spentToday = stats.spent_today ?? 0;
  //         const spentDaysPeriod = Number(stats.spent_days_period) ?? 0;
  //         const conversionsTotal =
  //           conversionLetters === "" ? 0 : (Number(stats.conversions_total) ?? 0);

  //         const conversionTargetCost = Number(stats.conversion_target_cost) ?? 0;

  //         // Calculating and Formatting
  //         const periodConversions =
  //           conversionLetters === "" ? 0 : (Number(stats.conversions_period) ?? 0);

  //         const conversions = filters.underspending ? conversionsTotal : periodConversions;

  //         const costConv =
  //           conversions && Number.isFinite(spent / conversions)
  //             ? Number(((spent / conversions) * currency).toFixed(2))
  //             : "--";
  //         const spikeDiff = Number.isFinite(spentToday / (spent / spentDaysPeriod))
  //           ? spentToday / (spent / spentDaysPeriod) - 1
  //           : 0;
  //         const totalConversions = Number.isFinite(conversions)
  //           ? `${conversions} ${conversionLetters}`
  //           : 0;

  //         let spendVs: number | string = "N/A";
  //         // If the current period is not in current month, we do not calculate the spendVs.
  //         if (spentPeriodCad === 0 && spentLastPeriodCad === 0) {
  //           spendVs = "0.00"; // No spend in either period
  //         } else if (spentLastPeriodCad === 0 && spentPeriodCad > 0) {
  //           spendVs = "100.00"; // All new spend this period
  //         } else if (spentLastPeriodCad > 0 && spentPeriodCad === 0) {
  //           spendVs = "-100.00"; // Stopped spending
  //         } else {
  //           spendVs = ((spentPeriodCad / spentLastPeriodCad - 1) * currency * 100).toFixed(2);
  //         }

  //         const budgetSpentFinal = Number.isFinite(spent * currency)
  //           ? parseFloat((spent * currency).toFixed(2))
  //           : 0;
  //         // Applying Currency change to fields
  //         const spentTodayFinal = Number.isFinite(spentToday * currency)
  //           ? spentToday * currency
  //           : 0;
  //         let targetFinal = 0;
  //         if (conversionTargetCost) {
  //           targetFinal = conversionTargetCost * currency;
  //         }

  //         formattedData.push({
  //           feedcode: stats.feedcode,
  //           campaignId: stats.campaign_id,
  //           accountId: stats.account_id,
  //           campaignName: stats.campaign_name,
  //           // The line below is what we're replacing with our grouping logic when filters.accountPanel is true
  //           ...(filters.accountPanel
  //             ? { accountName: stats.company_label }
  //             : { campaignId: stats.campaign_id, campaignName: stats.campaign_name }),
  //           objective: stats.campaign_objective,
  //           budgetType: stats.budget_type,
  //           budgetDelivery: budgetDelivery,
  //           budget: Number(budget),
  //           currency: stats.account_currency,
  //           budgetSpent: budgetSpentFinal,
  //           spendVs: spendVs,
  //           spentToday: spentTodayFinal,
  //           flexibleSpent: parseFloat((stats.flexible_spent).toFixed(2)),
  //           conversions: totalConversions,
  //           target: targetFinal,
  //           costConv: costConv,
  //           jobs: (stats.total_jobs_today || 0), // fallback since if no jobs today it returns a null value.
  //           country: stats.account_country,
  //           status: stats.campaign_status,
  //           ...(filters.yesterdayCadc && { cadc: stats.missed_revenue }), // Conditionally add cadc if yesterdayCadc is true
  //           spikeDiff: spikeDiff,
  //           speed: !isNaN(stats.speed) && stats.speed > 0 ? Number(stats.speed.toFixed(2)) : "--",
  //           vip: stats.vip_account,
  //         });
  //       });
  //     }

  //     // Calculate the spentVs with the accumulator object for the cards at the top of the page.
  //     if (resultCards.spentLastMonth === 0 && resultCards.spentMonth === 0) {
  //       resultCards.spentVs = '0.00'; // No change, no spend in either
  //     } else if (resultCards.spentLastMonth === 0 && resultCards.spentMonth > 0) {
  //       resultCards.spentVs = '100.00'; // All new spend this month
  //     } else if (resultCards.spentLastMonth > 0 && resultCards.spentMonth == 0) {
  //       resultCards.spentVs = '-100.00';
  //     } else {
  //       resultCards.spentVs = (((resultCards.spentMonth / resultCards.spentLastMonth - 1) * 100) * currency).toFixed(2);
  //     }

  //     let total = 0;

  //     if (filters.accountPanel) {
  //       total = Number(campaignStats[0]?.total_accounts || 0);
  //     } else {
  //       total = Number(campaignStats[0]?.total_campaigns || 0);
  //     }

  //     // Return the final array with everything organised.
  //     return {
  //       total: total,
  //       limit: filters.limit ?? 10000,
  //       offset: filters.offset ?? 0,
  //       budgetMonth: resultCards.budgetMonth,
  //       spentMonth: Number(resultCards.spentMonth?.toFixed(2)),
  //       spentLastMonth: Number(resultCards.spentLastMonth?.toFixed(2)),
  //       spentToday: Number(resultCards.spentToday?.toFixed(2)),
  //       spentVsLastMonth: resultCards.spentVs == "N/A" ? "N/A" : `${resultCards.spentVs}%`,
  //       data: Array.isArray(formattedData) ? formattedData : [],
  //     };
  //   } catch (e) {
  //     Logger.error(new Error(`Error while calculating aggregations: ${e}`));
  //     throw e;
  //   }
  // }

  /**
   * Performs calculations and formatting to create a global summary overview of the data,
   * calculating metrics over each iteration of the data received from the findAnalyticsInfo repository method.
   * @param data - The raw analytics data containing employers and traffic data.
   * @returns Formatted analytics data with a summary.
   */
  private formatAnalyticsDataCampaignTable(data: any): any {
    // Iterate through employers's data as we must show all of the campaigns regardless of traffic data.
    const formattedData = data.employers
      .map((campaign: any) => {
        // Standard logic: Find a single matching campaign
        const stats = data.analytics.find(
          // (c: { campaignid: string | number }) => String(c.campaignid) === String(entry.id),
          (c: any) => String(c.campaignid) === String(campaign.campaign_id),
        );
        const budgetType = campaign.budget_type;
        const budget = budgetType === "monthly" ? campaign.budget_month : campaign.budget_day;

        const organicclicks = stats ? parseInt(stats.organicclicks) : 0;
        const paidclicks = stats ? parseInt(stats.paidclicks) : 0;
        const cost = stats ? parseFloat((Number(stats.cost) / 100).toFixed(2)) : 0;
        const talentapplyconversions = stats ? stats.talentapplyconversions : 0;
        const organicconversions = stats ? stats.organicconversions : 0;
        // Convert keys to camelCase and format the day
        const formattedEntry = {
          campaignId: campaign.campaign_id,
          campaignName: campaign.campaign_name,
          campaignObjective: campaign.campaign_objective,
          currency: campaign.account_currency,
          paidClicks: paidclicks,
          organicClicks: organicclicks,
          externalConversions: organicconversions,
          talentApplyConversions: talentapplyconversions,
          cost: cost,
          budget: budget,
          budgetType: budgetType,
          status: campaign.status,
        };

        const metrics = this.calculateMetricsEmployers(formattedEntry);

        return {
          ...formattedEntry,
          ...metrics,
        };
      })
      .filter(Boolean);

    // Summarize the data
    const summary = this.summarizeEmployersData(data);
    summary.costs = parseFloat(summary.costs.toFixed(2));
    return {
      summary: summary,
      data: formattedData as IEmployersMetricsByCampaignId[],
    };
  }

  /**
   * Performs calculations and formatting to create a global summary overview of the data,
   * calculating metrics over each iteration of the data received from the findAnalyticsInfo repository method.
   * @param data - The raw analytics data.
   * @returns Formatted analytics data with a summary.
   */
  private formatAnalyticsDataJobsTable(
    data: any,
    accountCurrency: AccountCurrency,
  ): IEmployersAnalyticsDataFormatted {
    const formattedData = data.analytics
      .map((entry: any) => {
        // Convert keys to camelCase and format the day
        const organicclicks = parseInt(entry.organicclicks) || 0;
        const paidclicks = parseInt(entry.paidclicks) || 0;
        const cost = parseFloat((Number(entry.cost) / 100).toFixed(2)) || 0;
        const talentapplyconversions = entry.talentapplyconversions || 0;
        const organicconversions = entry.organicconversions || 0;

        const formattedEntry = {
          jobId: entry.jobid,
          jobTitle: entry.jobtitle,
          jobLocation: entry.joblocation,
          jobCompanyName: entry.jobcompanyname,
          paidClicks: paidclicks,
          organicClicks: organicclicks,
          externalConversions: organicconversions,
          talentApplyConversions: talentapplyconversions,
          cost: cost, // Parses the number to 2 decimal places.
          reqid: entry.reqid ?? "--",
          campaignName: entry.campaignName ?? "-",
          currency: accountCurrency,
        };
        const metrics = this.calculateMetricsEmployers(formattedEntry);

        return {
          ...formattedEntry,
          ...metrics,
        };
      })
      .filter(Boolean);

    // Summarize the data
    const summary = this.summarizeEmployersData(data);
    summary.costs = parseFloat(summary.costs.toFixed(2));
    return {
      summary: summary,
      data: formattedData as IEmployersMetricsByJob[],
    };
  }

  /**
   * Performs calculations and formatting to create a global summary overview of the data,
   * calculating metrics over each iteration of the data received from the findAnalyticsInfo repository method.
   * @param data - The raw analytics data.
   * @returns Formatted analytics data with a summary.
   */
  private formatAnalyticsDataByDay(
    data: any,
    accountCurrencyOverview?: AccountCurrency,
  ): IEmployersAnalyticsDataFormatted {
    let accountCurrency: AccountCurrency | null = null;
    const employers = Array.isArray(data.employers) ? data.employers : [data.employers];
    if (accountCurrencyOverview) {
      accountCurrency = accountCurrencyOverview;
    } else if (employers.length > 0) {
      const employer = employers[0];
      accountCurrency = employer.account_currency;
    }

    const formattedData = data.analytics
      .map((entry: IEmployersAnalyticsRepositoryMetrics) => {
        // Convert keys to camelCase and format the day
        const organicclicks = parseInt(entry.organicclicks) || 0;
        const paidclicks = parseInt(entry.paidclicks) || 0;
        const cost = parseFloat((Number(entry.cost) / 100).toFixed(2)) || 0;
        const talentapplyconversions = entry.talentapplyconversions || 0;
        const organicconversions = entry.organicconversions || 0;

        const formattedEntry = {
          day: entry.day
            ? (entry.day as unknown as string)
            : new Date().toISOString().split("T")[0],
          paidClicks: paidclicks,
          organicClicks: organicclicks,
          externalConversions: organicconversions,
          talentApplyConversions: talentapplyconversions,
          cost: cost,
          currency: accountCurrency,
        };

        const metrics = this.calculateMetricsEmployers(formattedEntry);

        return {
          ...formattedEntry,
          ...metrics,
        };
      })
      .filter(Boolean);

    // Summarize the data
    const summary = this.summarizeEmployersData(data);
    summary.costs = parseFloat(summary.costs.toFixed(2));

    return {
      summary: summary,
      data: formattedData,
    };
  }

  /**
   * Takes already formatted data for creating the final sliced response according to the pagination parameters.
   * @param data
   * @param filters
   * @returns
   */
  private sliceAnalyticsData(
    data: IEmployersAnalyticsDataFormatted,
    filters: EmployersAnalyticsDTO,
  ): IEmployerAnalyticsResponse {
    const dataArray = data.data; // Assert that data.data is an array
    const offset = filters.offset ?? 0;
    let limit = filters.limit ?? dataArray.length;

    // For these views there should be no pagination.
    if (
      ["campaignDetailed", "account", "jobDetailed", "overview"].includes(filters.view) ||
      (!filters.limit && ["campaignTable", "jobTable"].includes(filters.view))
    ) {
      return {
        total: dataArray.length,
        offset: offset,
        limit: limit,
        ...(filters.view === "jobDetailed" ? { jobTitle: data.jobTitle } : {}),
        summary: data.summary,
        data: dataArray,
      };
    }
    const slicedData = dataArray.slice(offset, offset + limit);
    return {
      total: dataArray.length,
      offset: offset,
      limit: limit,
      summary: data.summary,
      data: slicedData,
    };
  }

  /**
   * Aggregates the data to return a summary of the traffic data received.
   * @param data
   * @returns
   */
  private summarizeEmployersData(data: any): IEmployersSummaryData {
    // Summarize the data
    const defaultResponse = {
      totalClicks: 0,
      organicClicks: 0,
      paidClicks: 0,
      costs: 0,
      conversions: 0,
    }; // Initial accumulator object
    if (!data.analytics || data.analytics.length == 0) return defaultResponse;
    return data.analytics.reduce(
      (
        acc: {
          totalClicks: number;
          organicClicks: number;
          paidClicks: number;
          costs: number;
          conversions: number;
          jobTitle: string;
        },
        analyticsInfo: {
          totalclicks: string | number;
          organicclicks: string | number;
          paidclicks: string | number;
          cost: number;
          organicconversions: string;
          talentapplyconversions: string;
          jobtitle: string;
        },
      ) => {
        const organicConversions = parseInt(analyticsInfo.organicconversions) || 0;
        const talentApplyConversions = parseInt(analyticsInfo.talentapplyconversions) || 0;

        const costs = Math.round((analyticsInfo.cost / 100) * 100) / 100;

        acc.totalClicks += Number(analyticsInfo.totalclicks) || 0;
        acc.organicClicks += Number(analyticsInfo.organicclicks) || 0;
        acc.paidClicks += Number(analyticsInfo.paidclicks) || 0;
        acc.costs += costs || 0;
        acc.conversions += organicConversions + talentApplyConversions;
        return acc;
      },
      { totalClicks: 0, organicClicks: 0, paidClicks: 0, costs: 0, conversions: 0 }, // Initial accumulator object
    );
  }
  /**
   * Takes traffic data for calculating different metrics.
   * @param data
   * @returns
   */
  private calculateMetricsEmployers(data: {
    paidClicks: any;
    externalConversions: any;
    talentApplyConversions: any;
    cost: any;
  }): {
    conversionRate: number | string;
    costPerConversion: number | string;
    averageCpc: number;
  } {
    parseInt(data.paidClicks);
    parseInt(data.externalConversions);
    parseInt(data.talentApplyConversions);
    parseFloat(data.cost);
    const totalConversions = data.externalConversions + data.talentApplyConversions;
    const conversionRate =
      data.paidClicks > 0 && totalConversions > 0
        ? parseFloat(((totalConversions / data.paidClicks) * 100).toFixed(2)) || "--"
        : "--";

    const costPerConversion =
      totalConversions > 0 ? parseFloat((data.cost / totalConversions).toFixed(2)) || "--" : "--";

    const averageCpc =
      data.paidClicks > 0 ? parseFloat((data.cost / data.paidClicks).toFixed(2)) : 0;

    return {
      conversionRate,
      costPerConversion,
      averageCpc,
    };
  }
  /**
   * Extracts the correct format that the FE is expecting for the account overview top table.
   * @param employersData
   * @returns
   */
  private formatOverviewAccounts(employersData: any[]): IEmployersAnalyticsOverview[] {
    const overviewAccounts: IEmployersAnalyticsOverview[] = [];
    // Remapping data so it matches types. THANKS NODEJS!! ...
    if (employersData && Array.isArray(employersData)) {
      const transformedData = employersData.map((item) => ({
        id: item.id || item.account_id,
        feedcode: item.feedcode,
        accountCurrency: item.account_currency, // Map from snake_case to camelCase
        status: item.status,
        companyLabel: item.company_label, // Map from snake_case to camelCase
      }));

      transformedData.forEach((item) => {
        overviewAccounts.push({
          accountId: item.id,
          feedcode: item.feedcode,
          currency: item.accountCurrency, // Now using camelCase
          status: item.status,
          companyName: item.companyLabel, // Now using camelCase
        });
      });
    }

    return overviewAccounts;
  }

  private dateDiff(date1: Date, date2: Date): number {
    const oneDay = 24 * 60 * 60 * 1000; // milliseconds in one day
    return Math.round((date1.getTime() - date2.getTime()) / oneDay);
  }
  private getFirstDayOfMonth(date: Date): Date {
    return new Date(date.getFullYear(), date.getMonth(), 1);
  }

  private isDateRangeInCurrentMonth(filters: CampaignQuickFiltersDto): Boolean {
    const now = new Date();

    // Get first and last day of the current month in local time
    const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const lastDayOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);

    const formatDate = (date: Date) => ({
      year: date.getFullYear(),
      month: date.getMonth() + 1, // Month is zero-based, so we add 1
      day: date.getDate(),
    });

    // Extract month and year from current month
    const { year: currentYear, month: currentMonth } = formatDate(firstDayOfMonth);

    // Get yesterday's date in local time
    const yesterdayDate = new Date();
    yesterdayDate.setDate(now.getDate() - 1);
    const {
      year: yesterdayYear,
      month: yesterdayMonth,
      day: yesterdayDay,
    } = formatDate(yesterdayDate);

    // Extract year and month from filters
    const extractYearMonth = (dateStr: string) => {
      const [year, month] = dateStr.split("-").map(Number);
      return { year, month };
    };

    const { year: startYear, month: startMonth } = extractYearMonth(filters.startPeriod);
    const { year: endYear, month: endMonth } = extractYearMonth(filters.endPeriod);

    // Check if the provided period matches the current month or yesterday
    const isCurrentMonth =
      startYear === currentYear &&
      startMonth === currentMonth &&
      endYear === currentYear &&
      endMonth === currentMonth;

    const isYesterday =
      startYear === yesterdayYear &&
      startMonth === yesterdayMonth &&
      endYear === yesterdayYear &&
      endMonth === yesterdayMonth &&
      Number(filters.startPeriod.split("-")[2]) === yesterdayDay &&
      Number(filters.endPeriod.split("-")[2]) === yesterdayDay;
    return isCurrentMonth || isYesterday;
  }

  /**
   * Get traffic and campaign information to feed the campaignView page.
   * @param id
   * @param accountId
   * @returns
   */
  public async getCampaignView(filters: CampaignViewDTO) {
    try {
      let formattedTrafficInfo;
      // Get campaign information.
      const campaignInfo = await this.campaignsService.findOne(filters.campaignId, [
        "campaignsBudget",
      ]);
      // Get the date range according to period selected
      const dates = this.getDatesFromPeriod(campaignInfo, filters);
      // Get jobs for this campaign
      const sponsoredJobsRequest = await this.getSponsoredJobsFromCampaign(filters.campaignId);
      let sponsoredJobsAmount = 0;
      if (sponsoredJobsRequest) {
        sponsoredJobsAmount = sponsoredJobsRequest.sponsoredJobs;
      }
      // Get traffic data
      const trafficInfo = await this.campaignsAnalyticsRepository.findCampaignTrafficData(
        filters,
        dates.startPeriod,
        dates.endPeriod,
      );
      if (trafficInfo && trafficInfo.length > 0) {
        formattedTrafficInfo = await this.formatAnalyticsDataCampaignView(trafficInfo);
      }
      // Ensure `formattedTrafficInfo` is always initialized
      if (!formattedTrafficInfo) {
        return {
          summary: {
            budgetType: campaignInfo.campaignsBudget.budgetType,
            spent: 0,
            spentToday: 0,
            costPerConversion: "--",
            conversions: 0,
            country: campaignInfo.country,
            conversionTargetCost: campaignInfo.conversionTargetCost,
            monthlyAllowance: campaignInfo.campaignsBudget.cumulativeAllowance,
            sponsoredJobs: 0,
            cpc: 0,
            ecpc: 0,
            conversionRate: "--",
            campaignBudget: !campaignInfo.campaignsBudget.budgetMonth
              ? 0
              : campaignInfo.campaignsBudget.budgetMonth,
            applyType: campaignInfo.applyType,
            campaignObjective: campaignInfo.campaignObjective,
            campaignId: campaignInfo.id,
            campaignName: campaignInfo.campaignName,
          },
          data: [],
        };
      }

      formattedTrafficInfo.summary.campaignBudget = campaignInfo.campaignsBudget.budgetMonth;
      formattedTrafficInfo.summary.applyType = campaignInfo.applyType;
      formattedTrafficInfo.summary.campaignObjective = campaignInfo.campaignObjective;
      formattedTrafficInfo.summary.campaignId = campaignInfo.id;
      formattedTrafficInfo.summary.campaignName = campaignInfo.campaignName;
      formattedTrafficInfo.summary.conversionTargetCost = campaignInfo.conversionTargetCost;
      formattedTrafficInfo.summary.country = campaignInfo.country;
      formattedTrafficInfo.summary.sponsoredJobs = sponsoredJobsAmount;
      formattedTrafficInfo.summary.monthlyAllowance =
        campaignInfo.campaignsBudget.cumulativeAllowance;

      return formattedTrafficInfo;
    } catch (e) {
      Logger.error(new Error(`Error message: ${e}`));
    }
  }

  /**
   * Determines wether we need to retrieve data month to date or to use the campaign's period (campaign.dateStart & campaign.dateEnd) and returns the date range.
   * @param campaign
   * @param filters
   * @returns object containning the date range to use with startPeriod and endPeriod.
   */
  private getDatesFromPeriod(
    campaign: Campaigns,
    filters: CampaignViewDTO,
  ): { startPeriod: string; endPeriod: string } {
    let startPeriod: string;
    let endPeriod: string;

    if (filters.trafficPeriod === "monthly") {
      const today = new Date();
      startPeriod = new Date(today.getFullYear(), today.getMonth(), 1).toISOString().split("T")[0]; // First day of the month
      endPeriod = today.toISOString().split("T")[0]; // Today's date
    } else {
      startPeriod = campaign.dateStart as unknown as string;
      endPeriod =
        (campaign.dateEnd as unknown as string) ??
        (new Date().toISOString().split("T")[0] as unknown as string);
    }
    return { startPeriod, endPeriod };
  }

  /**
   * Returns an aggregation by medium/source/publisher, also calculating the weighted values on each layer.
   * @param data CampaignViewTrafficData returned from the database
   * @returns the aggregated data object.
   */
  private formatAnalyticsDataCampaignView(data: CampaignViewTrafficData[]): any {
    const orderedData: { [key: string]: { [key: string]: any } } = {};
    const summary: any = {
      billedClicks: 0,
      spent: 0,
      spentToday: 0,
      costPerConversion: "--",
      conversions: 0,
      country: "ca",
      conversionTargetCost: 0,
      monthlyAllowance: 0,
      sponsoredJobs: 0,
      cpc: 0,
      ecpc: 0,
    };
    let summaryCount = 0;
    data.forEach((item) => {
      const medium = item.cache_utm_medium;
      let sourceKey: string = medium;
      let subSourceKey: string = item.cache_utm_product;
      summaryCount += 1;

      if (!orderedData[sourceKey]) {
        orderedData[sourceKey] = {};
      }

      if (!orderedData[sourceKey][subSourceKey]) {
        orderedData[sourceKey][subSourceKey] = {
          source: subSourceKey,
          cpc: 0,
          ecpc: 0,
          billedClicks: 0,
          spent: 0,
          spentToday: 0,
          conversions: 0,
          costPerConversion: "--",
          conversionRate: "--",
          exposure: 0,
        };
      }

      const entry = orderedData[sourceKey][subSourceKey];
      const formattedSpent = item.cost / 100;
      const formattedSpentToday = item.cost_today / 100;
      entry.billedClicks += item.billedclicks;
      entry.spent += formattedSpent;
      entry.conversions += item.talentapplyconversions + item.organicconversions;
      entry.cpc += Number(item.cpc.toFixed(2));
      entry.ecpc += item.ecpc > 0 ? Number((item.ecpc / 100).toFixed(2)) : 0;
      entry.exposure += item.exposure;
      entry.spentToday += formattedSpentToday;

      summary.billedClicks += item.billedclicks;
      summary.spent += formattedSpent || 0;
      summary.spentToday += formattedSpentToday || 0;
      summary.conversions += item.organicconversions + item.talentapplyconversions || 0;
      summary.cpc += Number(item.cpc.toFixed(2));
      summary.ecpc += item.ecpc > 0 ? Number((item.ecpc / 100).toFixed(2)) : 0;
    });

    const finalData: any[] = [];

    for (const sourceKey in orderedData) {
      const subSources = orderedData[sourceKey];
      let aggregatedSource: any = {
        source: sourceKey,
        cpc: 0,
        ecpc: 0,
        billedClicks: 0,
        spent: 0,
        conversions: 0,
        costPerConversion: "--",
        conversionRate: "--",
        exposure: 0,
        subRows: [],
      };

      let totalCount = 0;

      for (const subSourceKey in subSources) {
        const subSource = subSources[subSourceKey];
        aggregatedSource.billedClicks += subSource.billedClicks;
        aggregatedSource.spent += subSource.spent;
        aggregatedSource.conversions += subSource.conversions;
        aggregatedSource.cpc += subSource.cpc;
        aggregatedSource.ecpc += subSource.ecpc;
        aggregatedSource.exposure += subSource.exposure;
        totalCount += 1;
        aggregatedSource.subRows?.push(subSource);
      }
      if (totalCount > 0) {
        aggregatedSource.cpc = aggregatedSource.cpc / totalCount;
        aggregatedSource.ecpc = aggregatedSource.ecpc / totalCount;
        aggregatedSource.exposure = aggregatedSource.exposure / totalCount;
      }

      finalData.push(aggregatedSource);
    }
    // Add the metrics to the summary
    summary.costPerConversion =
      summary.conversions > 0 ? Number((summary.spent / summary.conversions).toFixed(2)) : "N/A";

    summary.conversionRate =
      summary.conversions > 0
        ? ((summary.conversions / summary.billedClicks) * 100).toFixed(2) + "%"
        : "--";

    // Format final values.
    summary.cpc = Number((summary.cpc / summaryCount).toFixed(2));
    summary.ecpc = Number((summary.ecpc / summaryCount).toFixed(2));
    summary.spent = summary.spent > 0 ? Number(summary.spent.toFixed(2)) : summary.spent;

    finalData.forEach((entry) => {
      entry.conversionRate =
        entry.billedClicks > 0 && entry.conversions > 0
          ? ((entry.conversions / entry.billedClicks) * 100).toFixed(2) + "%"
          : "--";
      entry.costPerConversion =
        entry.conversions > 0 ? Number((entry.spent / entry.conversions).toFixed(2)) : "--";
      entry.subRows?.forEach(
        (subEntry: {
          conversionRate: string;
          billedClicks: number;
          conversions: number;
          costPerConversion: string | number;
          spent: number;
        }) => {
          subEntry.conversionRate =
            subEntry.billedClicks > 0 && subEntry.conversions > 0
              ? ((subEntry.conversions / subEntry.billedClicks) * 100).toFixed(2) + "%"
              : "--";
          subEntry.costPerConversion =
            subEntry.conversions > 0
              ? Number((subEntry.spent / subEntry.conversions).toFixed(2))
              : "--";
        },
      );
    });

    return { summary, data: finalData };
  }

  /**
   * Returns the amount of jobs realted to the campaign provided.
   * @param campaignId
   * @returns
   */
  private async getSponsoredJobsFromCampaign(campaignId: number) {
    return await this.campaignsAnalyticsRepository.getSponsoredJobsFromCampaign(campaignId);
  }

  /**
   * Returns traffic information for the account id requested.
   * @param accountId
   */
  public async findAnalyticsHomeInfo(accountId: number) {
    const today = new Date();
    const todayFormatted = today.toISOString().split("T")[0];
    // Second range: From one month ago to today
    const oneMonthAgo = new Date(new Date());
    oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);

    const startMonthAgo = new Date();
    startMonthAgo.setMonth(startMonthAgo.getMonth() - 1);
    startMonthAgo.setDate(1);

    const endMonthAgo = new Date();
    endMonthAgo.setMonth(endMonthAgo.getMonth(), 0); // Current month, day 0 = last day of previous month

    // Get the starting day of the month for current month's date.
    const startOfMonth = new Date();
    startOfMonth.setDate(1); // Set to first day of the month

    try {
      const thisMonth = await this.campaignsAnalyticsRepository.getAnalyticsHomeData(
        accountId,
        startOfMonth.toISOString().split("T")[0],
        todayFormatted,
      );
      const data30DaysAgo = await this.campaignsAnalyticsRepository.getAnalyticsHomeData(
        accountId,
        oneMonthAgo.toISOString().split("T")[0],
        todayFormatted,
        true,
      );
      const lastMonth = await this.campaignsAnalyticsRepository.getAnalyticsHomeData(
        accountId,
        startMonthAgo.toISOString().split("T")[0],
        endMonthAgo.toISOString().split("T")[0],
      );
      let spentAndClicksThisMonth;
      let spentLastMonth;
      // Check for empty data before reducing.
      if (Array.isArray(thisMonth) && thisMonth.length > 0) {
        spentAndClicksThisMonth = thisMonth.reduce(
          (data: any, current: any) => {
            return {
              spent: data.spent + current.spent,
              clicks: data.clicks + current.clicks,
            };
          },
          { spent: 0, clicks: 0 }, // Initial value to start accumulation
        );
      }
      if (Array.isArray(lastMonth) && lastMonth.length > 0) {
        spentLastMonth = lastMonth.reduce(
          (total: number, current: any) => total + current.spent,
          0, // Initial value
        );
      }
      return {
        summary: { ...spentAndClicksThisMonth, spentLastMonth: spentLastMonth },
        costOverview: data30DaysAgo,
        visitsOverview: thisMonth,
      };
    } catch (e) {
      Logger.error(new Error(`Error message: ${e}`));
    }
  }

  /**
   * Sorts the data by costPerConversion either asc or desc according to filters
   * @param data
   */
  private sortByCostPerConversion(data: any[], sortType: SortType, quickFilters: boolean = false) {
    data.sort((a, b) => {
      let aVal = quickFilters ? a.costConv : a.costPerConversion;
      let bVal = quickFilters ? b.costConv : b.costPerConversion;

      // Treat "--" as 0 for sorting
      let aNum = aVal === "--" ? 0 : typeof aVal === "number" ? aVal : parseFloat(aVal as string);
      let bNum = bVal === "--" ? 0 : typeof bVal === "number" ? bVal : parseFloat(bVal as string);

      const sortDirection = sortType === "ASC" ? 1 : -1;

      return (aNum - bNum) * sortDirection;
    });
  }

  /**
   * Takes raw traffic data from employers_campaigns_stats table and aggregates it by campaignId.
   * @param data
   * @param lastPeriod
   * @returns
   * @param data
   * @param lastPeriod
   * @returns
   */
  private async aggregateAnalytics(data: any[], aggregatedColumn: string) {
    return Object.values(
      data.reduce(
        (acc, item) => {
          const key = item[aggregatedColumn]; // Use dynamic key for aggregation

          if (!acc[key]) {
            acc[key] = {
              campaignid: item.campaign_id,
              accountid: item.account_id,
              day: item.day,
              account_currency: "",
              cost: 0,
              organicconversions: 0,
              talentapplyconversions: 0,
              conversions: 0,
              totalclicks: 0,
              organicclicks: 0,
              paidclicks: 0,
              reqid: item.source_reqid,
              joblocation: item.source_location,
              jobid: item.job_id,
              jobtitle: item.source_title,
              jobcompanyname: item.source_company_name,
              campaignName: item.campaign_name,
            };
          }

          // Sum up numeric values
          acc[key].cost += item.billed_ppc || 0;
          acc[key].organicconversions += item.conversions_pp || 0;
          acc[key].talentapplyconversions += item.conversions_ta || 0;
          acc[key].conversions += item.conversions_ta + item.conversions_pp || 0;
          acc[key].totalclicks += item.total_clicks || 0;
          acc[key].organicclicks += item.organic_clicks || 0;
          acc[key].paidclicks += item.billed_clicks || 0;

          return acc;
        },
        {} as Record<string, any>,
      ),
    );
  }

  private sortByNumericAnalytics(data: any, sortType: SortType, sortColumn: string) {
    const column = sortColumn;
    if (column) {
      if (column === "conversions") {
        data.sort(
          (
            a: { externalConversions: any; talentApplyConversions: any },
            b: { externalConversions: any; talentApplyConversions: any },
          ) =>
            sortType === "ASC"
              ? a.externalConversions +
                a.talentApplyConversions -
                (b.externalConversions + b.talentApplyConversions)
              : b.externalConversions +
                b.talentApplyConversions -
                (a.externalConversions + a.talentApplyConversions),
        );
      } else if (column === "clicks") {
        data.sort(
          (
            a: { organicClicks: any; paidClicks: any },
            b: { organicClicks: any; paidClicks: any },
          ) =>
            sortType === "ASC"
              ? a.organicClicks + a.paidClicks - (b.organicClicks + b.paidClicks)
              : b.organicClicks + b.paidClicks - (a.organicClicks + a.paidClicks),
        );
      } else {
        data.sort((a: { [x: string]: number }, b: { [x: string]: number }) =>
          sortType === "ASC"
            ? (a[column as keyof typeof a] as number) - (b[column as keyof typeof b] as number)
            : (b[column as keyof typeof b] as number) - (a[column as keyof typeof a] as number),
        );
      }
    }
  }
  /**
   * Brings quick filters data from the worker file employers-quick-filters.worker.mjs and processes it to be return to the FE.
   * @param filters
   * @returns
   */
  async getQuickFiltersData(filters: CampaignQuickFiltersDto): Promise<QuickFiltersResponse> {
    const emptyResponse: QuickFiltersResponse = {
      total: 0,
      limit: 0,
      offset: 0,
      budgetMonth: 0,
      spentMonth: 0,
      spentLastMonth: 0,
      spentToday: 0,
      spentVsLastMonth: "N/A",
      data: [],
    };
    // Map the column to sort from FE to BE database naming.
    if (filters.sortColumn) {
      filters.sortColumn = this.mapQuickFiltersFields(filters.sortColumn);
    }
    // const excludeKeys = new Set(["limit", "offset", "sortType", "sortColumn", "displayCurrency"]);
    // const selectedFilters = Object.fromEntries(
    //   Object.entries(filters).filter(([key]) => !excludeKeys.has(key)),
    // );
    // const cacheKey = `fetchData:${JSON.stringify(selectedFilters)}`;
    // // Check if data is in cache
    // const cachedData: { currencyRate: number; data: any[] } | undefined =
    //   await this.cacheManager.get(cacheKey);

    // if (cachedData && cachedData.data.length > 0) {
    // const processedData = this.formatQuickFiltersData(
    //   cachedData.data,
    //   cachedData.currencyRate,
    //   filters,
    // );
    // return processedData;
    // }

    return new Promise(async (resolve, reject) => {
      try {
        const data = await this.piscinaQuickFilters.run({
          filters,
          mySQLConn: this.mySQLConn,
          postgreSQLConn: this.postgreSQLConn,
        });
        // Return the empty response placeholder ONLY when no employers's data was found.
        if (data.data.length === 0) {
          return resolve(emptyResponse);
        }
        // Store data in cache before filtering and resolving
        // await this.cacheManager.set(
        //   cacheKey,
        //   { currencyRate: data.currencyRate, data: data.data },
        //   5 * 60 * 1000,
        // ); // Cache for 5 minutes

        const processedData = await this.formatQuickFiltersData(
          data.data,
          data.currencyRate,
          filters,
        );
        // Spend VS and Cost per Conversion are the one columns that need to be sorted after data formatting.
        if (
          (filters.sortColumn === "spendVs" || filters.sortColumn === "costConv") &&
          filters.sortType
        ) {
          const sortedData = this.sortArray(
            processedData.data,
            filters.sortColumn,
            filters.sortType,
          );
          processedData.data = sortedData;
        }
        resolve(processedData);
      } catch (error: any) {
        Logger.error("Error in worker execution:", error.message);
        reject(new HttpException(error.message, HttpStatus.BAD_REQUEST));
      }
    });
  }

  /**
   * Brings analytics data from the worker file employers-analytics.worker.mjs
   * @param filters
   * @returns
   */
  async getAnalyticsData(filters: EmployersAnalyticsDTO): Promise<any> {
    const emptyResponse = {
      total: 0,
      offset: 0,
      limit: 25,
      summary: { totalClicks: 0, paidClicks: 0, organicClicks: 0, costs: 0, conversions: 0 },
      data: [],
      overview: [],
    };
    // const excludeKeys = new Set(["limit", "offset", "sortType", "sortColumn", "groupBy"]);
    // const selectedFilters = Object.fromEntries(
    //   Object.entries(filters).filter(([key]) => !excludeKeys.has(key)),
    // );
    // const cacheKey = `fetchData:${JSON.stringify(selectedFilters)}`;
    // // Check if data is in cache
    // const cachedData: any | undefined = await this.cacheManager.get(cacheKey);

    // if (
    //   cachedData &&
    //   cachedData.employersData.length != 0 &&
    //   cachedData.analyticsData.length != 0
    // ) {
    //   const processedData = await this.processAnalyticsData(
    //     filters,
    //     cachedData.analyticsData,
    //     cachedData.employersData,
    //     cachedData.accountCurrency,
    //   );
    //   return processedData;
    // }

    return new Promise(async (resolve, reject) => {
      try {
        const workerData = filters; // just to keep consistency
        const { data } = await this.piscinaAnalytics.run({
          workerData,
          mySQLConn: this.mySQLConn,
          postgreSQLConn: this.postgreSQLConn,
        });
        // Check if we got data.
        if (!data.employers && !data.analytics) {
          return emptyResponse;
        }

        const analyticsData = data.analytics;
        const employersData = data.employers;

        // This returns an array, not a single object
        const matchingAccounts = data.employers.filter(
          (account: any) => Number(account.account_id) === Number(filters.accountId),
        );

        // Access the first matching account's currency
        const accountCurrency =
          matchingAccounts.length > 0 ? matchingAccounts[0].account_currency : null;

        // Store data in cache before filtering and resolving
        // await this.cacheManager.set(
        //   cacheKey,
        //   {
        //     employersData: employersData,
        //     accountCurrency: accountCurrency,
        //     analyticsData: analyticsData,
        //   },
        //   5 * 60 * 1000,
        // ); // Cache for 5 minutes
        const processedData = await this.processAnalyticsData(
          filters,
          analyticsData,
          employersData,
          accountCurrency,
        );
        resolve(processedData);
      } catch (error: any) {
        Logger.error("Error in worker execution:", error);
        reject(new HttpException(error.message, HttpStatus.BAD_REQUEST));
      }
    });
  }
  /**
   * Takes analytics data to be processed and calculate metrics to be returned in the FE.
   * @param filters
   * @param analyticsData
   * @param employersData
   * @param accountCurrency
   * @returns
   */
  private async processAnalyticsData(
    filters: EmployersAnalyticsDTO,
    analyticsData: any,
    employersData: any,
    accountCurrency: AccountCurrency,
  ) {
    const emptyResponse = {
      total: 0,
      offset: 0,
      limit: 25,
      summary: { totalClicks: 0, paidClicks: 0, organicClicks: 0, costs: 0, conversions: 0 },
      data: [],
      overview: [],
    };
    let overviewAccounts;
    let aggregatedData;
    // Data comes raw from the DB, so we need to aggregate according to the view
    if (filters.groupBy) {
      aggregatedData = await this.aggregateAnalytics(analyticsData, filters.groupBy[0]);
    }
    analyticsData = aggregatedData as IEmployersAnalyticsRepositoryMetrics[];
    // Extract the account overview top table if needed.
    if (filters.view === "overview") {
      overviewAccounts = this.formatOverviewAccounts(employersData as Accounts[]);
    }
    // Perform calculations and formatting if we got data.
    // When we are looking for the jobTable, we don't query for employers's data as it is not needed.
    if (
      analyticsData &&
      analyticsData.length > 0 &&
      (["jobTable", "jobDetailed", "account", "overview"].includes(filters.view) ||
        (employersData && employersData.length > 0))
    ) {
      let formattedData;
      if (filters.view === "campaignTable") {
        formattedData = this.formatAnalyticsDataCampaignTable({
          employers: employersData,
          analytics: analyticsData,
        });
      } else if (filters.view === "jobTable") {
        formattedData = this.formatAnalyticsDataJobsTable(
          { analytics: analyticsData },
          accountCurrency,
        );
      } else {
        formattedData = this.formatAnalyticsDataByDay(
          { analytics: analyticsData, employers: employersData },
          accountCurrency,
        );
        if (filters.view === "jobDetailed") {
          formattedData.jobTitle = analyticsData[0].jobtitle;
        }
      }
      // Sort if requested by parameters
      if (filters.sortColumn && filters.sortType && filters.sortColumn === "costPerConversion") {
        this.sortByCostPerConversion(formattedData.data, filters.sortType);
      } else if (filters.sortColumn && filters.sortType) {
        this.sortByNumericAnalytics(formattedData.data, filters.sortType, filters.sortColumn);
      }
      const slicedData = this.sliceAnalyticsData(formattedData, filters);

      return { ...slicedData, overview: overviewAccounts };
    } else return emptyResponse;
  }

  /**
   * Helper function to sort data either asc or desc.
   * @param array
   * @param keySelector
   * @param direction
   * @returns
   */
  private sortArray<T>(array: T[], key: keyof T, direction: SortType): T[] {
    const toComparableValue = (value: any): number | string => {
      if (value === null || value === undefined) return 0;

      if (typeof value === "string") {
        const normalized = value.trim().toLowerCase();

        // Treat special strings as 0
        if (["n/a", "--", "na", "", "-"].includes(normalized)) return 0;

        // Remove % if present, then parse
        const cleaned = normalized.replace("%", "");
        const parsed = parseFloat(cleaned);

        // If it's still not a number, keep it as string
        return isNaN(parsed) ? normalized : parsed;
      }

      return typeof value === "number" ? value : value.toString().toLowerCase();
    };

    return [...array].sort((a, b) => {
      const valA = toComparableValue(a[key]);
      const valB = toComparableValue(b[key]);

      const comparison = valA < valB ? -1 : valA > valB ? 1 : 0;
      return direction === SortType.ASC ? comparison : -comparison;
    });
  }

  /**
   * Maps out the fields to match front end and back end.
   * @param key
   * @returns
   */
  private mapQuickFiltersFields(key: string) {
    switch (key) {
      case "budgetSpent":
      case "spent":
      case "cost":
        return "spent_period";
      case "budget":
        return "budget_month";
      case "budgetDelivery":
        return "has_pacing";
      case "campaignName":
        return "campaign_name";
      case "budgetType":
        return "budget_type";
      case "campaignId":
        return "campaign_id";
      case "spentToday":
        return "spent_today";
      case "jobs":
        return "total_jobs_today";
      case "budgetSpeed":
      case "speed":
        return "speed";
      case "objective":
        return "campaign_objective";
      case "status":
        return "campaign_status";
      case "country":
        return "account_country";
      case "target":
        return "conversion_target_cost";
      case "type":
      case "accountType":
      case "account_type":
        return "account_type";
      case "feedcode":
        return key;
      default:
        return key;
    }
  }
}
