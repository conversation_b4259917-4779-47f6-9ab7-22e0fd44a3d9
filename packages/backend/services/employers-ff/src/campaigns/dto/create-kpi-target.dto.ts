import { IsOptional, IsString, IsNotEmpty, IsDateString, IsEnum, IsNumber } from "class-validator";
import { Column } from "typeorm";
import { AccountCurrency, kpisTypes } from "../../common/resources/enums";

/**
 * Dto for campaigns_kpi_target table
 */
export class CreateKpiTargetDto {
  // campaignId should be optional because it is autogenerated in the campaign transaction during creation.
  @IsOptional()
  @IsNumber()
  campaignId?: number;

  @IsNumber()
  @IsOptional()
  userId?: number;

  @IsNumber()
  @IsNotEmpty()
  @Column({ type: "float", precision: 10, scale: 2 })
  kpiTarget: number;

  @IsNumber()
  @IsOptional()
  @Column({ type: "float", precision: 10, scale: 2 })
  kpiTargetCAD?: number;

  @IsEnum(kpisTypes, { message: "Invalid kpi value" })
  @IsNotEmpty()
  kpiValue: kpisTypes;

  @IsEnum(AccountCurrency, { message: "Invalid kpi currency" })
  @IsOptional()
  currency?: AccountCurrency;

  @IsString()
  @IsOptional()
  country?: string;

  @IsString()
  @IsOptional()
  campaignName?: string;

  @IsString()
  @IsOptional()
  feedcode?: string;

  @IsDateString()
  @IsOptional()
  created?: Date;
}
