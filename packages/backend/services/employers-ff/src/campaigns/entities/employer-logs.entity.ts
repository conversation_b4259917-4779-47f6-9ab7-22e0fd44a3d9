import { Column, <PERSON><PERSON>ty, Index, Join<PERSON><PERSON>umn, ManyToOne, PrimaryGeneratedColumn } from "typeorm";
import { Users } from "../../users/entities/user.entity";

/**
 *
 */
@Entity("employer_logs")
@Index("logID", ["id"])
@Index("date", ["date"])
@Index("group1", ["date", "action", "method", "entity_id", "entity"])
@Index("fk_employer_logs_users1_idx", ["user_id"])
@Index("idx_entity_id", ["entity_id"])
export class EmployerLogs {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({
    type: "varchar",
    length: 50,
    comment:
      "Project for which the record is being generated. [employers, talentpost, jobseeker, etc]",
  })
  source: string;

  @Column({ type: "datetime", comment: "Day the record was generated" })
  date: Date;

  @Column({ type: "int", nullable: true })
  user_id: number | null;

  @Column({
    type: "varchar",
    length: 50,
    nullable: true,
    comment: "[select, update, create, insert]",
  })
  action: string | null;

  @Column({
    type: "varchar",
    length: 50,
    nullable: true,
    comment: "Name of the method which is creating this record",
  })
  method: string | null;

  @Column({ type: "int", comment: "Object ID for the Object Logged" })
  entity_id: number;

  @Column({
    type: "varchar",
    length: 20,
    nullable: true,
    comment: "Type of element that you are interacting with. [user, job, account, campaign]",
  })
  entity: string | null;

  @Column({
    type: "json",
    nullable: true,
    comment: "Event description. It can be an error from mysql, from a class, from elastic, etc.",
  })
  before_data: Record<string, any> | null;

  @Column({ type: "json", nullable: true })
  after_data: Record<string, any> | null;

  @ManyToOne(() => Users)
  @JoinColumn({ name: "user_id" })
  user?: Users;
}