import { IsOptional, IsString, IsBoolean, IsNotEmpty } from "class-validator";
import { Type } from "class-transformer";

/**
 *
 */
export class FindOneWithFiltersDto {
  @Type(() => Number)
  @IsOptional()
  id?: number;

  @IsString()
  @IsOptional()
  dealId?: string;

  @IsString()
  @IsOptional()
  @IsNotEmpty()
  dealName?: string;

  @IsString()
  @IsOptional()
  dealOwnerId?: string;

  @IsString()
  @IsOptional()
  dealOwner?: string;

  @IsString()
  @IsOptional()
  dealOwnerEmail?: string;

  @IsString()
  @IsOptional()
  billedClientName?: string;

  @Type(() => Boolean)
  @IsOptional()
  @IsBoolean()
  validDeal?: boolean;

  @IsString()
  @IsOptional()
  billedClientType?: string;

  @IsString()
  @IsOptional()
  apiClientName?: string;

  @IsString()
  @IsOptional()
  accountName?: string;

  @IsString()
  @IsOptional()
  pandaDocStatus?: string;
}
