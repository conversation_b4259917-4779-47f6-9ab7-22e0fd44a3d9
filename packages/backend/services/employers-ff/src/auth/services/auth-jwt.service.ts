import { HttpException, HttpStatus, Injectable } from "@nestjs/common";
import { JwtService } from "@nestjs/jwt";
import { CreateRefreshTokenDto } from "../dto/create-refresh-token.dto";
import { Role } from "../resources/role.enum";

/**
 *
 */
@Injectable()
export class AuthJwtService {
  /**
   *
   */
  constructor(private readonly jwtService: JwtService) {}

  /**
   * Build and Generate Access Token
   * @param info
   * @returns
   */
  async generateAccessToken(info: number): Promise<{
    access_token: string;
    access_expiration_date: string;
    refresh_token: string;
    refresh_expiration_date: string;
  }> {
    const payload = {
      sub: info,
      user_id: info,
    };

    const accessToken = await this.jwtService.signAsync(payload, {
      expiresIn: "120m",
      secret: process.env.JWT_SECRET_ACCESS_KEY,
    });

    const refreshToken = await this.jwtService.signAsync(payload, {
      expiresIn: "14d",
      secret: process.env.JWT_SECRET_ACCESS_KEY,
    });

    return {
      access_token: accessToken,
      access_expiration_date: new Date(Date.now() + 60 * 60 * 1000).toISOString(), // 60 min
      refresh_token: refreshToken,
      refresh_expiration_date: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString(), // 14 days
    };
  }

  /**
   * Build and Generate Refresh Token
   * @param info
   * @returns
   */
  async generateRefreshToken(info: CreateRefreshTokenDto): Promise<string> {
    try {
      const payload: any = {
        sub: info.user_id,
        user_id: info.user_id
      };

      // Should only happen when creating a Token for a Dev
      if (info.account_role == Role.Dev) payload.account_role = info.account_role;

      return await this.jwtService.signAsync(payload, {
        expiresIn: "120m",
        secret: process.env.JWT_SECRET_ACCESS_KEY,
      });
    } catch (error: any) {
      throw new HttpException("Invalid refresh token", HttpStatus.UNAUTHORIZED);
    }
  }
}
