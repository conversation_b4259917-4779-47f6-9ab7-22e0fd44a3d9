import { InjectRepository } from "@nestjs/typeorm";
import { FindOptionsSelect, FindOptionsWhere, Repository } from "typeorm";
import { LeadsLegacy } from "../entities/leads-legacy.entity";

/**
 *
 */
export class LeadsLegacyRepository {
  /**
   *
   */
  constructor(
    @InjectRepository(LeadsLegacy)
    private readonly repository: Repository<LeadsLegacy>,
  ) {}

  /**
   *
   */
  async find(
    criteria: FindOptionsWhere<LeadsLegacy>,
    columns: FindOptionsSelect<LeadsLegacy>,
  ): Promise<LeadsLegacy | null> {
    return await this.repository.findOne({
      select: columns,
      where: criteria,
    });
  }
}
