import {
  Body,
  Controller,
  Get,
  Post,
  Query,
  UseInterceptors,
  ValidationPipe,
} from "@nestjs/common";
import { CreditNotesService } from "../services/credit-notes.service";
import { SearchCreditNoteDto } from "../dto/search-credit-note.dto";
import { PaginatedCreditNotes } from "../../common/interfaces/billings.interface";
import { User } from "../../auth/decorators/user.decorator";
import { UserAuthDto } from "../../common/dtos/user-auth.dto";
import { BillingPermissionService } from "../services/billing-permission.service";
import { BillingActionInterceptor } from "../../common/interceptors/billing-actions.interceptor";
import { CreateCreditNoteDto } from "../dto/create-credit-note.dto";
import { RequirePermissions } from "../../auth/decorators/permissions.decorator";
import { Permission } from "../../auth/resources/permission.enum";
import { AccountIdDto } from "../dto/accountId.dto";
/**
 * Controller responsible for managing credit notes.
 * Handles HTTP requests related to credit notes.
 */
@Controller({
  path: "credit-notes",
  version: "1",
})
@UseInterceptors(BillingActionInterceptor)
export class CreditNotesController {
  /**
   * Creates an instance of `CreditNotesController`.
   * @param creditNotesService The credit notes service to handle business logic.
   * @param billingPermissionService The billing permission service to handle billing permissions.
   */
  constructor(
    private readonly creditNotesService: CreditNotesService,
    private readonly billingPermissionService: BillingPermissionService,
  ) {}

  /**
   * Handles GET requests to search for credit notes based on specified filters.
   *
   * @param {SearchCreditNoteDto} filters - The filters to apply to the search.
   * @returns {Promise<PaginatedCreditNotes>} A promise that resolves to a paginated list of credit notes.
   */
  @Get("search")
  async search(
    @User() user: UserAuthDto,
    @Query(new ValidationPipe()) filters: SearchCreditNoteDto,
  ): Promise<PaginatedCreditNotes> {
    await this.billingPermissionService.checkPrivilagesByUserId();
    return await this.creditNotesService.search(filters);
  }

  /**
   * Endpoint to create a credit note.
   * @param {CreateCreditNoteDto} createCreditNoteDto - The data to be created.
   * @returns The record created.
   */
  @Post()
  @RequirePermissions(Permission.CreateCreditNotes)
  async createCreditNote(
    @Body() createCreditNoteDto: CreateCreditNoteDto,
    @User() user: UserAuthDto,
    @Query(new ValidationPipe()) filters: AccountIdDto,
  ): Promise<any> {
    return this.creditNotesService.create(createCreditNoteDto, user.user_id);
  }
}
