import { IsString, <PERSON>E<PERSON>, <PERSON>NotEmpty, IsOptional } from "class-validator";

/**
 * * Data Transfer Object (DTO) for updating a client in QuickBooks Online (QBO).
 */
export class UpdateQuickBooksCustomerDto {
  @IsString()
  @IsNotEmpty()
  id: string;

  @IsString()
  @IsNotEmpty()
  syncToken: string | null;

  @IsString()
  @IsOptional()
  givenName?: string;

  @IsString()
  @IsOptional()
  familyName?: string;

  @IsString()
  @IsNotEmpty()
  companyName: string;

  @IsString()
  @IsNotEmpty()
  displayName: string | null;

  @IsEmail()
  @IsNotEmpty()
  email: string;

  @IsString()
  @IsNotEmpty()
  address: string;

  @IsString()
  @IsOptional()
  @IsNotEmpty()
  city?: string | null;

  @IsString()
  @IsOptional()
  @IsNotEmpty()
  state?: string | null;

  @IsString()
  @IsNotEmpty()
  country: string | null;

  @IsString()
  @IsNotEmpty()
  @IsOptional()
  postalCode?: string | null;

  @IsString()
  @IsNotEmpty()
  salesTermRef: string;

  @IsString()
  @IsOptional()
  @IsNotEmpty()
  notes?: string;
}
