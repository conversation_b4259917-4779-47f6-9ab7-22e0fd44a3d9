import { IsNotEmpty, Validate<PERSON>f, IsString, IsNumber, IsIn } from "class-validator";
import { CreditNoteReason, TalentCompany } from "../../common/resources/enums";
import { IsValidCurrency } from "../decorators/is-valid-currency.decorators";

/**
 * DTO for creating a credit note.
 */
export class CreateCreditNoteDto {
  @IsNumber()
  @IsNotEmpty()
  docNumberQbo: number;

  @IsNotEmpty()
  @IsNumber()
  amount: number;

  @IsIn(Object.values(CreditNoteReason).filter((value) => typeof value === "string"))
  reason: string;

  @IsString()
  @IsNotEmpty()
  @IsValidCurrency({ message: "Must be a valid currency" })
  currency!: string;

  @ValidateIf((o) => o.reason === "Other")
  @IsNotEmpty({ message: "comments is required when reason is Other" })
  @IsString({ message: "comments must be a string" })
  comments!: string;

  @IsString()
  @IsNotEmpty()
  @IsIn(Object.values(TalentCompany).filter((value) => typeof value === "string"))
  company: string;
}
