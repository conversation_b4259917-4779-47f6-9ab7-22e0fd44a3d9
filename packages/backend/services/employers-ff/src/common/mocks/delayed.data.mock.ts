import { DeleteResult } from "typeorm";
import { CreateDelayedDto } from "../../billings/dto/create-delayed.dto";
import { UpdateDelayedDto } from "../../billings/dto/update-delayed.dto";
import { Delayed } from "../../billings/entities/delayed.entity";
import { Accounts } from "../../accounts/entities/account.entity";

const date_start = new Date();
date_start.setDate(1);

const date_end = new Date(date_start);
const nextMonth = date_end.getMonth() + 1;
date_end.setMonth(nextMonth);

export const createDelayedDto: CreateDelayedDto = {
  accountId: 100237,
  amount: 10,
  dateStart: date_start,
  dateEnd: date_end,
  status: "Not Billed",
  reason: "Other",
  description: "test",
};
export const updateDelayedDto: UpdateDelayedDto = {
  ...createDelayedDto,
  quickbookInvoiceDocNumber: 6284,
};

export const createdDelayed: Delayed = {
  ...createDelayedDto,
  date: new Date(),
  id: 1,
  amountCad: 10,
  billDate: new Date(),
  quickbookInvoiceDocNumber: 0,
  account: new Accounts(),
  accountId: 100237,
};

export const deleteResult: DeleteResult = {
  raw: 1,
};

export const filters = { ...createDelayedDto, accountId: 1 };
