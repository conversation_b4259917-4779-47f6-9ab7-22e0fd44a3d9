import { HttpException, HttpStatus, Injectable } from "@nestjs/common";
import { isEmail } from "class-validator";
import { createHash } from "crypto";
import * as privacy from "libs/privacy/src";
import { HttpService } from "@nestjs/axios";
import { firstValueFrom } from "rxjs";
import * as crypto from "crypto";

/**
 *
 */
@Injectable()
export class PrivacyService {
  private IV: Buffer;
  private CIPHER: string;
  private ENCRYPTION_KEY: Buffer;
  private ENCRYPTION_VERSION: string;
  private SALT: string;
  private PEPPER: string;
  private bcrypt = require('bcryptjs');

  /**
   *
   */
  constructor() {
    const key = process.env.ENCRYPTION_KEY ?? "";
    const method = process.env.CIPHER_METHOD ?? "";
    const version = process.env.ENCRYPTION_VERSION ?? "";
    const salt = process.env.SALT ?? "";
    const pepper = process.env.PEPPER ?? "";
    // Initialize your encryption parameters here
    this.IV = Buffer.alloc(16); // Initialize with appropriate IV length
    this.CIPHER = method;
    this.ENCRYPTION_KEY = Buffer.from(key, "base64");
    this.ENCRYPTION_VERSION = version;
    this.SALT = salt;
    this.PEPPER = pepper;
  }

  /**
   * Returns an encrypted version of the provided email.
   * @param emailString
   * @param returnRaw
   */
  async secureEmail(emailString: string, returnRaw = false): Promise<Record<string, any>> {
    const email = emailString.toLowerCase().trim(); // Normalizing the email

    const out: Record<string, any> = {
      error: true,
      email_valid: "yes",
      email_normalized: email,
    };

    // Validates the format of the email
    if (!isEmail(email)) {
      const reason = "Invalid Email Format. ";
      out.email_valid = "no";
      out.reason = reason;
      return out;
    }

    // Validates the email is not a 'default sample' like <EMAIL>
    if (this.isGenericEmail(email)) {
      const reason = "Generic Email Format. ";
      out.email_valid = "no";
      out.reason = reason;
      return out;
    }

    out.error = false;
    out.encrypted = privacy.cipher.encrypt(email);
    if (returnRaw) {
      out.raw = email;
    }

    return out;
  }

  /**
   * Generates a sha256 hash of the input string
   * @param string
   */
  generateHash(string: string): string {
    // Create a SHA-256 hash object
    const hash = createHash('sha256');
  
    // Update the hash with the input data
    hash.update(string);
  
    // Finalize the hash and return the result as a hexadecimal string
    return hash.digest('hex');
  }

  /**
   * Checks if the provided email address is a generic email format.
   * A generic email format typically contains the domain "@email."
   * @param email
   */
  private isGenericEmail(email: string): boolean {
    return /@email\./i.test(email);
  }

  /**
   * Compare a password to a hashed password using the old hashing method
   * @param password 
   * @param hashedPassword 
   * @returns 
   */
  comparePassword(password: string, hashedPassword: string): boolean {
    const inputHashedPassword = privacy.legacy.pSHA1(password);
    return inputHashedPassword === hashedPassword;
  }

  async checkPassword(userPassword: string, inputPassword: string) {
    try {
      // Check input password against hashed password
      const validPwd = this.bcrypt.compareSync(inputPassword, userPassword);
      if (!validPwd) {
        throw new HttpException("Invalid Credentials.", HttpStatus.UNAUTHORIZED);
      }
      return;
    } catch (error: any) {
      // Handle the error
      console.error("Error sign in user:", error.message);
      throw new HttpException(error.message, error.HttpStatus || HttpStatus.BAD_REQUEST);
    }
  }

  /**
   * Check if password is Pwned or not
   * @param rawPassword 
   * @returns 
   */
  async isPasswordPwned(rawPassword: string) {
    try {

      // If password is empty then return false to avoid breaking the process
      if (!rawPassword) return false;

      // Encrypt with SHA1
      const sha1Pwd = crypto.createHash('sha1').update(rawPassword).digest('hex');
      const firstFiveChars = sha1Pwd.slice(0, 5);
      const remainingSha1Pwd = sha1Pwd.slice(5);

      // Instantiate HttpService
      const httpService = new HttpService();
  
      // Pwned URL
      const url = 'https://api.pwnedpasswords.com/range/' + firstFiveChars;
      const response = await firstValueFrom(httpService.get(url));
      
      // If no information received then return false to avoid breaking the process
      if (!response.data) return false;

      // Convert the result into an array
      const pwnedArrayPwds = response.data.split('\r\n');

      // Check if remainingSha1Pwd matches any entry in pwnedArrayPwds
      const isPwned = pwnedArrayPwds.some((pwnedPwd: string) => pwnedPwd.startsWith(remainingSha1Pwd.toUpperCase()));
      console.log("isPwned", isPwned);

      // If a match is found, return true (password is pwned)
      if (isPwned) return true;
  
      return false;
    } catch (error: any) {
      // If error return false to avoid breaking the process
      return false;
    }
  }
}
