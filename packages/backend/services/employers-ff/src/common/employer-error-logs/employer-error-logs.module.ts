import { Module } from "@nestjs/common";
import { EmployerErrorLogsService } from "./services/employer-error-logs.service";
import { TypeOrmModule } from "@nestjs/typeorm";
import { EmployerErrorLog } from "./entities/employer-error-log.entity";
import { ToolsTrackingLog } from "./entities/tools-tracking.entity";
import { ToolsTrackingLogService } from "./services/tools-tracking.service";
import { LogsCleanupService } from "../services/logs-cleanup.service";
import { EmployerLogs } from "../../campaigns/entities/employer-logs.entity";

/**
 *
 */
@Module({
  providers: [EmployerErrorLogsService, ToolsTrackingLogService, LogsCleanupService],
  exports: [EmployerErrorLogsService, ToolsTrackingLogService],
  imports: [TypeOrmModule.forFeature([EmployerErrorLog, ToolsTrackingLog, EmployerLogs])],
})
export class EmployerErrorLogsModule {}
