import { In, Repository } from "typeorm";
import { InjectRepository } from "@nestjs/typeorm";
import { PpcLegacy } from "../entities/account-ppc-legacy.entity";

/**
 * Account setting class
 */
export class PpcLegacyRepository {
  /**
   *
   */
  constructor(
    @InjectRepository(PpcLegacy)
    private readonly ppcLegacy: Repository<PpcLegacy>,
  ) {}

  /**
   *
   */
  async findOneByEmpcode(empcodes: (string | null)[]) {
    // Execute the query with the specified options
    return await this.ppcLegacy.find({
      select: { id: true, billing_discount: true, empcode: true },
      where: { empcode: In(empcodes) },
    });
  }
}
