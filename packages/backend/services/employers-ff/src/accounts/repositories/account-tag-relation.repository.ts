import { Repository, Entity<PERSON>anager } from "typeorm";
import { AccountTag } from "../entities/account-tag-relation.entity";
import { AccountTagRelationDto } from "../dto/account-tag-relation.dto";
import { InjectRepository } from "@nestjs/typeorm";
import { GetAccountTagsDto } from "../dto/get-account-tag-relation.dto";

/**
 *
 */
export class AccountTagRelationRepository {
  /**
   *
   */
  constructor(
    @InjectRepository(AccountTag)
    private readonly accountTagRelation: Repository<AccountTag>,
  ) {}

  /**
   * Creates a new account tag relation object.
   */
  async create(
    accountTagRelationDto: AccountTagRelationDto,
    entityManager?: EntityManager,
  ): Promise<AccountTag> {
    const repository = entityManager
      ? entityManager.getRepository(AccountTag)
      : this.accountTagRelation;
    const accountRelation = repository.create(accountTagRelationDto);
    return await repository.save(accountRelation);
  }

  /**
   * Retrieves campaign settings based on provided filters.
   *
   * @param filters - The filters to apply.
   * @returns A promise that resolves to the retrieved campaign settings.
   */
  async getAccountTags(filters: GetAccountTagsDto, includeEntities: boolean = false): Promise<any> {
    // Create a JSON object to store the GET query parameters.
    const options: any = {};

    // Iterate through the provided filters and populate the options object.
    for (const [key, value] of Object.entries(filters)) {
      // Only include parameters with defined values.
      if (value !== undefined) {
        options[key] = value;
      }
    }

    // If includeEntities is true, include related entities in the query
    let relations: any = [];
    if (includeEntities) {
      relations = ["account", "tag", "tag.tagType"]; // Include related entities
    }

    // Find account tags based on the constructed options.
    return await this.accountTagRelation.find({ where: options, relations });
  }

  /**
   * Deletes a row from the database using the primary key: id.
   * @param id 
   * @returns 
   */
  async delete(id: number) {
    return await this.accountTagRelation.delete({ id: id });
  }

  /**
   * Updates a row from the database according to the Data Transfer Object's parameters received.
   * @param accountTagDto 
   * @returns 
   */
  async update(accountTagDto: AccountTagRelationDto) {
    const accountTag = await this.accountTagRelation.preload(accountTagDto);
    if (accountTag) {
      return await this.accountTagRelation.save(accountTag);
    }
  }
}
