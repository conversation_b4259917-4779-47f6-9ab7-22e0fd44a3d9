import { Observer } from "../../common/interfaces/observer.interface";
import { Injectable, Logger } from "@nestjs/common";
import { CustomAxiosAdapter } from "../../common/adapters/axios.adapter";
import * as privacy from "libs/privacy/src";
import { UsersService } from "../../users/services/user.service";
import { AccountService } from "../services/accounts.service";
import { AccountBudgetDto } from "../dto/account-budget.dto";
import { formatAccountOwners } from "../../common/resources/utils";

/**
 * Observer responsible for sending notifications when the account changed.
 */
@Injectable()
export class AccountNotificationsObserver implements Observer {
  /**
   * Constructor to initialize the observer with necessary services.
   */
  constructor(
    private readonly accountService: AccountService,
    private readonly http: CustomAxiosAdapter,
    private readonly user: UsersService,
  ) {
    // Subscribe to account changes and process them.
    this.accountService.subscribeToAccountChanges().subscribe(async (data) => {
      try {
        this.process(data);
      } catch (error: any) {
        Logger.error(new Error(`[AccountsNotificationObserver] [subscribe] ${error.message}`));
      }
    });
  }

  /**
   * Process method to handle account changes and send notifications.
   * @param data
   */
  async process(data: any) {
    // Control the sending of emails to the CS or SO when a account is updated.
    // This method will only be used when one of the CSowner or SalesOwner fields contains an email.
    try {
      await this.sendEmailAccountUpdated(data);
    } catch (error: any) {
      const errorMessage = error && error.message ? error.message : "Unknown error occurred";
      Logger.error(new Error(`[AccountsNotificationObserver] [process] ${errorMessage}`));
      throw error;
    }
  }

  /**
   * Control the sending of emails to the CS or SO when a account is updated.
   * This method will only be used when one of the CSowner or SalesOwner fields contains an email
   * @param data
   */
  async sendEmailAccountUpdated(data: any) {
    // Check if these fields have been changed
    const hasChanged =
      data.evenType == "update" && this.hasBudgetChanged(data.accountInfo, data.updatedBudget);

    // If any changes occur, we must send a notification
    if (!hasChanged) {
      return false;
    }

    try {
      // Get recipents of that notification (sales, secondary_sales and cs)
      const recipients = formatAccountOwners(data?.accountInfo?.accountsOwners);
      if (!recipients?.email_address) {
        throw new Error("No account owners found");
      }
      // user who made the changes
      const user = await this.user.findOneBy(data.accountInfo.updatedBy);
      const modifiedBy = user?.email ? privacy.cipher.decrypt(user?.email) : "";

      const emailData: any = {
        email_address: recipients.email_address,
        template_id: 30,
        data: {
          employer_budgetAccount_info: {
            email_subject: `Change to [${data.accountInfo?.companyLabel} / ${data.accountInfo?.id}]`,
            company_label: data.accountInfo?.companyLabel,
            account_id: data.accountInfo?.id,
            modified_by: modifiedBy,
            feedcode: data.accountInfo?.feedcode,
            before_account_budget: data?.accountInfo?.accountsBudget?.budget,
            after_account_budget: data.updatedBudget?.budget,
          },
        },
      };

      if (recipients.cc_addresses && recipients.cc_addresses.length > 0) {
        emailData.cc_addresses = recipients.cc_addresses;
      }

      // Send email with the defined template
      await this.sendEmailNotification(emailData);
      return true;
    } catch (error: any) {
      const errorMessage = error && error.message ? error.message : "Unknown error occurred";
      Logger.error(
        new Error(`[AccountsNotificationObserver] [sendEmailAccountUpdated] ${errorMessage}`),
      );
      return false;
    }
  }

  /**
   * Analyze whether there were changes in the budget that needed to be notified
   * @param accountInfo
   * @param updatedBudget
   * @returns
   */
  hasBudgetChanged(accountInfo: any, updatedBudget?: AccountBudgetDto): boolean {
    // If budgetParams is not defined, no changes occurred
    if (!updatedBudget) {
      return false;
    }

    const { accountsBudget } = accountInfo;

    const changes = [
      accountsBudget.budget !== undefined &&
        String(accountsBudget.budget) !== String(updatedBudget.budget),
    ];

    return changes.some((change) => change);
  }

  /**
   * Send notificaitons when certain event was triggered
   * @param templateId
   * @param data
   */
  async sendEmailNotification(emailData: any) {
    try {
      const response = await this.http.post(`${process.env.URL_SEND_EMAILS}`, emailData);
      return response;
    } catch (error: any) {
      // Log the raw error for debugging

      // Don't try to access properties on potentially undefined error
      if (error === undefined) {
        Logger.error(
          new Error(
            "[AccountsNotificationObserver] [sendEmailNotification] Undefined error occurred",
          ),
        );
        // Create and throw a new Error instead of rethrowing undefined
        throw new Error("Email notification failed with undefined error");
      } else {
        // For defined errors, extract the message if available
        const errorMessage = error && error.message ? error.message : "Unknown error occurred";
        Logger.error(
          new Error(`[AccountsNotificationObserver] [sendEmailNotification] ${errorMessage}`),
        );
        // Rethrow the original error since it's defined
        throw error;
      }
    }
  }
}
