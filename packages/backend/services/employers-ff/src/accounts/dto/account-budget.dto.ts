import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "class-validator";

/**
 *
 */
export class AccountBudgetDto {

  @IsOptional()
  @IsNumber()
  id!: number | null; // This field refers to the account_id in the budgeting service

  @IsOptional()
  @IsNumber()
  @Min(0, { message: "Value must be greater than 0" })
  budget!: number | null;
  
  @IsOptional()
  @IsNumber()
  spent_budget!: number | null;
}