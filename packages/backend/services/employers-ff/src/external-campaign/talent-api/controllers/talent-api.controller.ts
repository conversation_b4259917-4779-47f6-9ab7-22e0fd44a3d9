import {
  Body,
  Controller,
  Get,
  HttpException,
  HttpStatus,
  Param,
  Patch,
  Post,
  BadRequestException,
  Headers,
  Query,
} from "@nestjs/common";
import { TalentApiService } from "../services/talent-api.service";
import {
  ApiBadRequestResponse,
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiTags,
  ApiTooManyRequestsResponse,
  ApiUnauthorizedResponse,
  ApiBody,
  ApiExcludeController,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiConsumes,
  ApiBearerAuth,
} from "@nestjs/swagger";
import { CreateTalentApiDto } from "../dto/create-campaign-talent-api.dto";
import { UpdateCampaignTalentApiDto } from "../dto/update-campaign-talent-api.dto";
import {
  createOkResponse,
  apiBadRequestResponse,
  apiUnauthorizedResponse,
  apiNotFoundResponse,
  apiTooManyRequestsResponse,
  apiInternalServerErrorResponse,
  updateOkResponse,
  createOkResponseGetCampaign,
  createOkResponseGetAccount,
} from "../../../common/mocks/talent-api.mock";
import { User } from "../../../auth/decorators/user.decorator";
import { UserAuthDto } from "../../../common/dtos/user-auth.dto";
import { GetCampaignDto } from "../dto/get-campaign-talent-api.dto";
import { validate } from "class-validator";
import { Throttle } from "@nestjs/throttler";
import { RequirePermissions } from "../../../auth/decorators/permissions.decorator";
import { Permission } from "../../../auth/resources/permission.enum";

/**
 *
 */
@Controller({
  path: "talent-api",
  version: "1",
})
@Throttle({ default: { limit: 50, ttl: 1000 } }) // Allow 50 requests per second for these endpoints
@ApiTags("Employers")
export class TalentApiController {
  /**
   *
   */
  constructor(private readonly talentApiService: TalentApiService) {}

  /**
   * Endpoint to retrieve campaign information based on client ID and API campaign ID.
   *
   * @param clientId - The unique identifier of the client.
   * @param apiCampaignId - The unique identifier of the campaign in the API.
   * @returns The campaign details fetched using the provided apiCampaignId.
   * @throws HttpException if the client validation fails or the campaign is not found.
   */
  @Get("/campaigns/:id")
  @ApiBearerAuth("bearerAuth")
  @ApiOperation({ summary: "Employer get Campaign method", description: "get campaign method" })
  @ApiQuery({
    name: "client_id",
    required: true,
    description:
      "A token provided by Talent.com (if you don’t have it, ask your Account Management Team. A specific token will be provided to you). This token is a unique client identifier.",
  })
  @ApiParam({
    name: "id",
    description:
      "An unique campaign API ID created in our database. This value will be returned in the POST method as api_campaign_id. example: name_123_123456.",
  })
  @ApiOkResponse(createOkResponseGetCampaign)
  @ApiBadRequestResponse(apiBadRequestResponse)
  @ApiUnauthorizedResponse(apiUnauthorizedResponse)
  @ApiNotFoundResponse(apiNotFoundResponse)
  @ApiTooManyRequestsResponse(apiTooManyRequestsResponse)
  @ApiInternalServerErrorResponse(apiInternalServerErrorResponse)
  @RequirePermissions(Permission.GetAllExternalCampaigns)
  async getCampaign(
    @Query("client_id") clientId: string,
    @Param("id") apiCampaignId: string,
    @User() user: UserAuthDto,
  ) {
    const getCampaignDto = new GetCampaignDto();
    getCampaignDto.apiCampaignId = apiCampaignId;

    // Validate the DTO manually
    const errors = await validate(getCampaignDto);
    if (errors.length > 0) {
      throw new BadRequestException(errors);
    }

    // Validate the client using the provided client ID (associated with the API key)
    await this.talentApiService.getValidatedClientTagName(clientId);

    // Fetch and return the campaign information using the apiCampaignId
    return this.talentApiService.getCampaignV1(
      getCampaignDto.apiCampaignId,
      user.user_id,
      clientId,
    );
  }

  /**
   * Handles a GET request to retrieve account information based on account name and currency.
   * Validates the client using an API key header and then fetches the account details.
   *
   * @param accountName - The name of the account (provided as a route parameter).
   * @param accountCurrency - The currency of the account (provided as a route parameter).
   * @param xApiKey - The API key sent in the request headers for client validation.
   * @returns The account information retrieved by the talentApiService.
   */
  @Get("accounts/:name")
  @ApiBearerAuth("bearerAuth")
  @ApiOperation({ summary: "Employer get Account method", description: "get account method" })
  @ApiQuery({
    name: "client_id",
    required: true,
    description:
      "A token provided by Talent.com (if you don’t have it, ask your Account Management Team. A specific token will be provided to you). This token is a unique client identifier.",
  })
  @ApiQuery({
    name: "account_currency_name",
    required: true,
    description: "Currency of the Account.",
  })
  @ApiParam({
    name: "name",
    description: "Name of the account.",
  })
  @ApiOkResponse(createOkResponseGetAccount)
  @ApiBadRequestResponse(apiBadRequestResponse)
  @ApiUnauthorizedResponse(apiUnauthorizedResponse)
  @ApiNotFoundResponse(apiNotFoundResponse)
  @ApiTooManyRequestsResponse(apiTooManyRequestsResponse)
  @ApiInternalServerErrorResponse(apiInternalServerErrorResponse)
  @RequirePermissions(Permission.GetAllExternalCampaigns)
  async getAccount(
    @Query("client_id") clientId: string,
    @Query("account_currency_name") accountCurrency: string,
    @Param("name") accountName: string,
    @User() user: UserAuthDto,
  ) {
    // Validate the client using the provided client ID
    await this.talentApiService.getValidatedClientTagName(clientId);

    // Fetch and return the account information using the account name and currency
    return await this.talentApiService.getAccount(
      accountName,
      accountCurrency,
      user.user_id,
      clientId,
    );
  }

  // Creates a new campaign by setting fields and preprocessing the data
  /**
   *
   */
  @Post("external-campaigns")
  @ApiBearerAuth("bearerAuth")
  @ApiOperation({
    summary: "Employer create Campaign method",
    description: "create campaign method",
  })
  @ApiBody({ type: CreateTalentApiDto })
  @ApiConsumes("multipart/form-data")
  @ApiOkResponse(createOkResponse)
  @ApiBadRequestResponse(apiBadRequestResponse)
  @ApiUnauthorizedResponse(apiUnauthorizedResponse)
  @ApiNotFoundResponse(apiNotFoundResponse)
  @ApiTooManyRequestsResponse(apiTooManyRequestsResponse)
  @ApiInternalServerErrorResponse(apiInternalServerErrorResponse)
  @RequirePermissions(Permission.CreateExternalCampaign)
  async createCampaign(@Body() createTalentApiDto: CreateTalentApiDto, @User() user: UserAuthDto) {
    const dataMap = new Map(Object.entries(createTalentApiDto));
    dataMap.set("user_action", "create");
    dataMap.set("user_id", user.user_id);
    const key = createTalentApiDto.client_id;
    const sourceFields = this.talentApiService.setFields(dataMap);

    const result = await this.talentApiService.processIngestion(sourceFields, key);
    createTalentApiDto.api_campaign_id = result
    return createTalentApiDto;
  }

  // Updates an existing campaign by setting fields and preprocessing the data
  /**
   *
   */
  @Patch("external-campaigns")
  @ApiBearerAuth("bearerAuth")
  @ApiOperation({
    summary: "Employer update Campaign method",
    description: "update campaign method",
  })
  @ApiBody({ type: CreateTalentApiDto })
  @ApiOkResponse(updateOkResponse)
  @ApiBadRequestResponse(apiBadRequestResponse)
  @ApiUnauthorizedResponse(apiUnauthorizedResponse)
  @ApiNotFoundResponse(apiNotFoundResponse)
  @ApiTooManyRequestsResponse(apiTooManyRequestsResponse)
  @ApiInternalServerErrorResponse(apiInternalServerErrorResponse)
  @RequirePermissions(Permission.UpdateExternalCampaignById)
  async updateCampaign(@Body() updateCampaignTalentApiDto: UpdateCampaignTalentApiDto) {
    const dataMap = new Map(Object.entries(updateCampaignTalentApiDto));
    const key = updateCampaignTalentApiDto.client_id;
    const sourceFields = this.talentApiService.setFields(dataMap);
    const result = await this.talentApiService.processIngestion(sourceFields, key as string);
    updateCampaignTalentApiDto.api_campaign_id = result
    return updateCampaignTalentApiDto;
  }
}

/**
 *
 */
@ApiExcludeController()
@Controller({
  path: "talent-api",
  version: "2",
})
export class TalentApiControllerV2 {
  /**
   *
   */
  constructor(private readonly talentApiService: TalentApiService) {}

  /**
   * Get info from campaign with apiCampaignId, even if the campaign only exist in External Campaigns
   * and don't exist in Talent.
   */
  @Get("campaign/:apiCampaignId")
  @RequirePermissions(Permission.GetAllExternalCampaigns)
  async getCampaign(
    @Param("apiCampaignId") apiCampaignId: string,
    @User() user: UserAuthDto,
    @Headers("x-api-key") apiKey: string,
  ) {
    return this.talentApiService.getCampaign(apiCampaignId, user.user_id, apiKey);
  }

  /**
   * Handles a GET request to retrieve account information based on account name and currency.
   * In version 2, if the account is not found in the Talent database, it fetches the record that exists in the client database.
   *
   * @param accountName - The name of the account (provided as a route parameter).
   * @param accountCurrency - The currency of the account (provided as a route parameter).
   * @returns The account information retrieved by the talentApiService.
   */
  @Get("account/:accountName/:accountCurrency")
  @RequirePermissions(Permission.GetAllExternalCampaigns)
  async getAccount(
    @Param("accountName") accountName: string, // Retrieve the account name from the route parameters
    @Param("accountCurrency") accountCurrency: string, // Retrieve the account currency from the route parameters
    @User() user: UserAuthDto,
    @Headers("x-api-key") apiKey: string,
  ) {
    // Set the version of the service to 2 to enable fetching from the client database if not found in Talent
    this.talentApiService.version = 2;

    // Fetch and return the account information using the account name and currency
    return await this.talentApiService.getAccount(
      accountName,
      accountCurrency,
      user.user_id,
      apiKey,
    );
  }

  /**
   * Updates an existing campaign by setting fields and preprocessing the data.
   *
   * @param campaignId - The ID of the campaign to update.
   * @param accountId - The ID of the account associated with the campaign.
   * @param updateCampaignTalentApiDto - The data transfer object containing the fields to update.
   * @returns A map containing the updated fields.
   * @throws HttpException if no fields are provided for the update.
   */
  @Patch("external-campaigns/:campaign_id/:account_id")
  @RequirePermissions(Permission.UpdateExternalCampaignById)
  async updateCampaign(
    @Param("campaign_id") campaignId: string,
    @Param("account_id") accountId: string,
    @Body() updateCampaignTalentApiDto: UpdateCampaignTalentApiDto,
    @Headers("x-api-key") apiKey: string,
  ) {
    // Validate that at least one field is provided in the request body
    if (Object.keys(updateCampaignTalentApiDto).length < 1) {
      throw new HttpException(
        `You need at least one field to update`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }

    // Create a map to store the provided fields and additional metadata
    const dataMap = new Map(Object.entries(updateCampaignTalentApiDto));
    dataMap.set("user_action", "update");
    dataMap.set("campaign_id", campaignId);
    dataMap.set("account_id", accountId);

    // Set the fields and version in the talentApiService and process the external campaign
    const sourceFields = this.talentApiService.setFields(dataMap);
    await this.talentApiService.processIngestion(sourceFields, apiKey as string, 2);
    // Return the map containing the updated fields and metadata
    return updateCampaignTalentApiDto;
  }
}
