/* eslint-disable @typescript-eslint/no-explicit-any */
import { HttpException, HttpStatus, Inject, Injectable } from "@nestjs/common";
import { ExternalCampaign } from "../entities/external-campaign.entity";
import { Repository, SelectQueryBuilder } from "typeorm";
import { InjectRepository } from "@nestjs/typeorm";
import { CreateExternalCampaignDto } from "../dto/create-external-campaign.dto";
import { FeedcodeExternalCampaignDto } from "../dto/feedcode-external-campaign.dto";
import { SearchExternalCampaignDto } from "../dto/search-external-campaign.dto";
import { ClientExternalCampaignDto } from "../dto/client-external-campaign.dto";
import { CACHE_MANAGER } from "@nestjs/cache-manager";
import { Cache } from "cache-manager";

// PENDING: Implement proper error handling
/**
 * Class in charge of all operations with External campaign's  entity (CRUD)
 */
@Injectable()
export class ExternalCampaignRepository {
  /**
   * Getting an instance in the constructor of the actual ExternalCampaign repository
   */
  constructor(
    @InjectRepository(ExternalCampaign)
    private readonly repository: Repository<ExternalCampaign>,
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
  ) {}

  /**
   * Method for finding a single record by ID
   */
  async findOne(field: string, value: string) {
    try {
      return await this.repository.findOne({
        where: { [field]: value },
        order: { date: "DESC" },
      });
    } catch (error) {
      // Catch any error that occurs during the process
      console.log(error);
    }
  }

  /**
   * Method for managing DB insertions in Autocamapign's database
   */
  async create(
    createExternalCampaignDto: CreateExternalCampaignDto, // Takes a DTO (Data Transfer Object) for creating External campaigns
  ): Promise<ExternalCampaign> {
    // Returns a promise that resolves to an ExternalCampaign object
    // Set the 'date' property of the DTO to the current date and time in ISO string format
    createExternalCampaignDto.date = new Date();
    // Create a new External campaign entity using the provided DTO
    const data = this.repository.create(createExternalCampaignDto);
    try {
      // Save the newly created External campaign entity to the database
      await this.repository.save(data);
      // Return the saved data
      return data;
    } catch (error) {
      // If an error occurs during saving, log the error
      console.log(error);
      // Throw a new error indicating failure to create the External campaign
      throw new HttpException("Error creating External campaign", HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Provides an instance of typeORM's repository
   * @returns list of active automatic Campaigns in talent
   */
  createRepoInstance(): ExternalCampaign {
    return this.repository.create();
  }

  /**
   * Method for handling deletes by ID in Autocampaign's entity
   */
  async delete(id: number): Promise<void> {
    try {
      // Attempt to delete the record
      const result = await this.repository.delete(id);

      // If the record is not found, throw a NotFoundException
      if (result.affected === 0) {
        throw new HttpException(`Record with ID "${id}" not found.`, HttpStatus.NOT_FOUND);
      }
    } catch (error) {
      // Catch any error that occurs during the process
      console.log(error);
    }
  }

  /**
   * Get a feedcodeList from external_campaigns
   * @returns
   */
  async getFeedcodeList(clientDto: ClientExternalCampaignDto): Promise<any> {
    const clientMap: Record<string, number> = {
      appcast: 1,
      goldenbees: 2,
      recruitics: 3,
      recruitics_xml: 3,
    };

    const clientHash = clientMap[clientDto.client] ?? 0;

    const queryBuilder = this.repository
      .createQueryBuilder("externalCampaign")
      .select("externalCampaign.talentFeedcode", "feedcode")
      .addSelect(
        `CASE WHEN COUNT(a.feedcode) > 0 AND a.status = 'active' THEN TRUE ELSE FALSE END`,
        "existsInTalent",
      )
      .leftJoin("accounts", "a", "externalCampaign.talentFeedcode = a.feedcode")
      .groupBy("externalCampaign.talentFeedcode")
      .orderBy("externalCampaign.talentFeedcode", "ASC");

    if (clientHash === 0) {
      queryBuilder.where("externalCampaign.apiClientName = :apiClientName", {
        apiClientName: clientDto.client,
      });
      queryBuilder.andWhere("externalCampaign.clientHash = 0");
    } else {
      queryBuilder.where("externalCampaign.clientHash = :clientHash", {
        clientHash,
      });
    }

    const feedcodesWithStatus = await queryBuilder.getRawMany();
    //return rawFeedcodes.map((r) => r.feedcode);
    return feedcodesWithStatus.map((feedcode) => ({
      feedcode: feedcode.feedcode,
      existsInTalent: feedcode.existsInTalent === "1", // boolean
    }));
  }

  /**
   * Get a companyNameList by feedcode from external_campaigns
   * @param feedcodeDto
   * @returns
   */
  async getCompanyNameListByFeedcode(feedcodeDto: FeedcodeExternalCampaignDto): Promise<string[]> {
    // Extract feedcode from DTO
    const { feedcode } = feedcodeDto;

    // Create a QueryBuilder instance
    const queryBuilder: SelectQueryBuilder<ExternalCampaign> =
      this.repository.createQueryBuilder("externalCampaign");

    // Select the unique company names based on the feedcode
    const companies = await queryBuilder
      .select("externalCampaign.companyName")
      .where("externalCampaign.talentFeedcode = :feedcode", { feedcode })
      .groupBy("externalCampaign.companyName")
      .orderBy("externalCampaign.companyName")
      .getRawMany();

    // Extract and return the company names
    const result = companies.map((cn) => cn.externalCampaign_company_name);

    // Return result
    return result;
  }

  /**
   * Method to list all the campaigns from external campaigns by the filter provided in the DTO.
   * We have only the last register and we can have pagination or not.
   * @param searchDto
   * @param pagination
   * @returns
   */
  async listAutoCampaignEntry(
    searchDto: SearchExternalCampaignDto,
    pagination: boolean,
  ): Promise<any> {
    // Retrieve variables from searchDto, with default values
    const {
      apiClientName,
      talentFeedcode,
      companyName,
      talentCampaign,
      campaignCountry,
      apiCampaignId,
      from,
      limit,
      table,
      sort,
      order,
    } = this.extractSearchOptions(searchDto);

    // Create a QueryBuilder instance
    const queryBuilder: SelectQueryBuilder<ExternalCampaign> =
      this.repository.createQueryBuilder("externalCampaign");

    // Build the search filter based on provided filterFields
    const filterFields = {
      //api_client_name: apiClientName, //This field is not used for filtering in PHP.
      talent_feedcode: talentFeedcode,
      company_name: companyName,
      talent_campaign: talentCampaign,
      campaign_country: campaignCountry,
      api_campaign_id: apiCampaignId,
    };

    for (const [key, value] of Object.entries(filterFields)) {
      if (value) {
        queryBuilder.andWhere(`externalCampaign.${key} = :${key}`, { [key]: value });
      }
    }

    // Apply sorting
    if (sort.trim().toLowerCase().startsWith("case")) {
      // Raw SQL expression — don't prefix with table alias
      queryBuilder.addOrderBy(sort, order);
    } else {
      // Normal column — prefix with table alias
      queryBuilder.addOrderBy(`externalCampaign.${sort}`, order);
    }

    // Apply additional filters based on the 'table' field
    const today = new Date().toISOString().split("T")[0]; // Today's date in 'YYYY-MM-DD' format
    switch (table) {
      case "entries":
        queryBuilder
          .andWhere("externalCampaign.action != '-'")
          .andWhere("externalCampaign.action != 'not allowed'")
          .andWhere("externalCampaign.action != 'toggleOff'")
          .andWhere("externalCampaign.action != 'toggleOn'")
          .andWhere(
            "(externalCampaign.campaign_status IS NULL OR externalCampaign.campaign_status = '' OR externalCampaign.campaign_status = 'active')",
          )
          .andWhere("externalCampaign.campaign_sponsorship_ended != '1'")
          .andWhere(
            "(externalCampaign.campaign_end_date IS NULL OR externalCampaign.campaign_end_date = '' OR externalCampaign.campaign_end_date >= :today)",
            { today: today },
          );
        break;

      case "running":
        queryBuilder
          .andWhere(
            "(externalCampaign.action = '-' OR externalCampaign.action = 'toggleOff' OR externalCampaign.action = 'toggleOn')",
          )
          .andWhere(
            "(externalCampaign.campaign_status IS NULL OR externalCampaign.campaign_status = '' OR externalCampaign.campaign_status = 'active')",
          )
          .andWhere(
            "(externalCampaign.campaign_sponsorship_ended IS NULL OR externalCampaign.campaign_sponsorship_ended != '1')",
          )
          .andWhere(
            "(externalCampaign.campaign_end_date IS NULL OR externalCampaign.campaign_end_date = '' OR externalCampaign.campaign_end_date >= :today)",
            { today: today },
          );
        break;

      case "paused":
        queryBuilder
          .andWhere(
            "(externalCampaign.action = '-' OR externalCampaign.action = 'toggleOff' OR externalCampaign.action = 'toggleOn' OR externalCampaign.action = 'not allowed')",
          )
          .andWhere(
            "(externalCampaign.campaign_status = 'paused' OR externalCampaign.campaign_sponsorship_ended = '1' OR (externalCampaign.campaign_sponsorship_ended IS NULL AND externalCampaign.campaign_end_date < :today))",
            { today: today },
          )
          .andWhere(
            "(externalCampaign.campaign_end_date IS NULL OR externalCampaign.campaign_end_date = '' OR externalCampaign.campaign_end_date < :today)",
            { today: today },
          );
        break;

      default:
        break;
    }

    // Subquery to get latest date per api_campaign_id for collapsing
    const subQueryBuilder = this.repository
      .createQueryBuilder("ec")
      .select("ec.api_campaign_id, MAX(ec.date) AS latest_date");

    // Add dynamic where conditions to the subquery
    for (const [key, value] of Object.entries(filterFields)) {
      if (value) {
        subQueryBuilder.andWhere(`ec.${key} = :${key}`, { [key]: value });
      }
    }

    subQueryBuilder.groupBy("ec.api_campaign_id");

    const subQuery = subQueryBuilder.getQuery();

    queryBuilder
      .innerJoin(
        `(${subQuery})`,
        "latest_campaigns",
        "externalCampaign.api_campaign_id = latest_campaigns.api_campaign_id AND externalCampaign.date = latest_campaigns.latest_date",
      )
      .setParameters({
        apiClientName,
        talentFeedcode,
        companyName,
        talentCampaign,
        campaignCountry,
        apiCampaignId,
      });

    if (pagination) {
      queryBuilder.offset(from);
      queryBuilder.limit(limit);
    }

    // Execute the query to get all results and count
    const [campaigns, total] = await queryBuilder.getManyAndCount();

    // If no results, throw error, otherwise return the results of the query
    if (campaigns.length === 0) {
      throw new HttpException("Campaign not found.", HttpStatus.NOT_FOUND);
    }

    // Return result
    return { total: total, campaigns };
  }

  /**
   * Extracts and normalizes search and pagination options from the given DTO.
   *
   * This method pulls values from the provided `SearchExternalCampaignDto`, applying default values
   * where necessary, and maps the user-provided sort key to the appropriate database column.
   * It also determines the correct sort order depending on the sort field.
   *
   * @param searchDto
   * @returns
   */
  private extractSearchOptions(searchDto: SearchExternalCampaignDto): {
    apiClientName: string | undefined;
    talentFeedcode: string | undefined;
    companyName: string | undefined;
    talentCampaign: string | undefined;
    campaignCountry: string | undefined;
    apiCampaignId: string | undefined;
    from: number;
    limit: number;
    table: string;
    sort: string;
    order: "ASC" | "DESC";
  } {
    const {
      apiClientName,
      talentFeedcode,
      companyName,
      talentCampaign,
      campaignCountry,
      apiCampaignId,
      from = 0,
      limit = 50,
      table = "",
      sort: sortKey = "date",
      order: userOrder = "ASC",
    } = searchDto;

    const sort = this.mapSortField(sortKey);
    const order = sort === "date" ? "DESC" : (userOrder ?? "ASC");

    return {
      apiClientName,
      talentFeedcode,
      companyName,
      talentCampaign,
      campaignCountry,
      apiCampaignId,
      from,
      limit,
      table,
      sort,
      order,
    };
  }

  /**
   * Maps a user-provided sort key to the corresponding database column name.
   *
   * This is used to translate frontend-friendly sort field names to the actual
   * column names used in the database, ensuring the query builder sorts correctly.
   *
   * @param {string} sort - The sort field requested by the user (e.g., "company").
   * @returns {string} The actual column name to be used in the SQL `ORDER BY` clause.
   */
  private mapSortField(sort: string): string {
    /**
     * Returns a SQL CASE expression that safely casts a string column to FLOAT
     * for numeric sorting. If the value does not match a numeric pattern,
     * it returns NULL to avoid casting errors.
     *
     * @param {string} column - The name of the database column to cast.
     * @returns {string} A SQL CASE expression that casts the column to FLOAT.
     */
    const simpleNumericCast = (column: string): string =>
      `CASE WHEN externalCampaign.${column} REGEXP '^[0-9]+(\\.[0-9]+)?$' THEN CAST(externalCampaign.${column} AS FLOAT) ELSE NULL END`;

    /**
     * Special numeric cast for budget: "no limit" is treated as either MAX or MIN.
     */
    const budgetCast = (): string => {
      return `CASE
      WHEN externalCampaign.campaignBudgetValue REGEXP '^[0-9]+(\\.[0-9]+)?$' THEN CAST(externalCampaign.campaignBudgetValue AS FLOAT)
      ELSE 2000000
    END`;
    };

    const sortFieldMap: Record<string, string> = {
      apiCampaignId: "apiCampaignId",
      company: "companyName",
      campaign: "talentCampaign",
      delivery: "campaignDelivery",
      status: "campaignStatus",
      sponsorshipEnded: "campaignSponsorshipEnded",
      campaignDates: "campaignStartDate",
      country: "campaignCountry",
      budgetType: "campaignBudgetType",
      costPerConversionTarget: "campaignTargetCost",
      conversionType: "campaignConversionType",

      // Budget is treated specially to push "no limit" values appropriately
      budget: budgetCast(),

      // Regular numeric cast for other fields
      cpc: simpleNumericCast("campaignCpc"),
      jobs: simpleNumericCast("xmlNumJobs"),
      daysRemaining: simpleNumericCast("estimatedBudgetDaysRemaining"),
    };

    return sortFieldMap[sort] ?? sort;
  }

  /**
   * Method to list all campaigns from external campaigns filtered by an array of apiCampaignIds.
   * @param apiCampaignIds Array of apiCampaignIds to filter campaigns
   * @returns
   */
  async listAutoCampaignEntryByApiCampaignIds(apiCampaignIds: string[]): Promise<any> {
    if (!apiCampaignIds || apiCampaignIds.length === 0) {
      throw new HttpException("The apiCampaignIds array cannot be empty.", HttpStatus.BAD_REQUEST);
    }

    const cacheKey = `list_auto_campaign_entry_by_api_campaign_ids_${JSON.stringify(apiCampaignIds)}`;
    const cachedResults = await this.cacheManager.get<any>(cacheKey);

    if (cachedResults) {
      return cachedResults;
    }

    // Create a QueryBuilder instance
    const queryBuilder: SelectQueryBuilder<ExternalCampaign> =
      this.repository.createQueryBuilder("externalCampaign");

    // Add filter for apiCampaignIds
    queryBuilder.where("externalCampaign.api_campaign_id IN (:...apiCampaignIds)", {
      apiCampaignIds,
    });

    // Apply collapsing and inner hits logic
    const subQueryBuilder = this.repository
      .createQueryBuilder("ec")
      .select("ec.api_campaign_id, MAX(ec.date) AS latest_date")
      .where("ec.api_campaign_id IN (:...apiCampaignIds)", { apiCampaignIds })
      .groupBy("ec.api_campaign_id");

    const subQuery = subQueryBuilder.getQuery();

    queryBuilder.innerJoin(
      `(${subQuery})`,
      "latest_campaigns",
      "externalCampaign.api_campaign_id = latest_campaigns.api_campaign_id AND externalCampaign.date = latest_campaigns.latest_date",
    );

    // Execute the query to get all results
    const campaigns = await queryBuilder.getMany();

    // If no results, throw an error
    if (campaigns.length === 0) {
      throw new HttpException(
        "No external campaigns found for the provided apiCampaignIds.",
        HttpStatus.NOT_FOUND,
      );
    }

    await this.cacheManager.set(cacheKey, campaigns, 300000);

    // Return the campaigns
    return campaigns;
  }

  /**
   * Read the entries in the auto_campaign_api table and return the data. If there is no entry, return an error.
   * @param column column that we are going to search. Ex: "api_campaign_id"
   * @param value value of the column we are going to search. Ex: "appcast_1684_254116"
   * @param limit limit of registries to bring. Ex: 1 or 2.
   * @returns
   */
  async readAutoCampaignEntry(column: string, value: string, limit: number = 0): Promise<any> {
    if (column.trim() === "") {
      throw new HttpException("'column' is required", HttpStatus.BAD_REQUEST);
    }
    if (value.trim() === "") {
      throw new HttpException("'value' is required", HttpStatus.BAD_REQUEST);
    }

    let query = this.repository
      .createQueryBuilder("ec")
      .where(`ec.${column} = :value`, { value })
      .orderBy("ec.date", "DESC");

    if (limit > 0) {
      query = query.limit(limit);
    }

    const result = await query.getMany();

    if (result.length === 0) {
      throw new HttpException("No entry found", HttpStatus.NOT_FOUND);
    }

    // Return result
    return result;
  }

  /**
   * This method will check if there’s another campaign from the auto_campaign_api bucket with the same campaign_id as this one
   * and the last campaign_sponsorship_ended = '0'
   * @param filteredCampaignId
   * @returns
   */
  async checkDuplicatedCampaigns(filteredCampaignId: any) {
    if (!filteredCampaignId || filteredCampaignId.length === 0) {
      throw new HttpException("'filteredCampaignId' is required", HttpStatus.BAD_REQUEST);
    }

    const apiClientName = "appcast";
    const pattern = "appcast_12492_%";

    const campaigns = await this.repository
      .createQueryBuilder("externalCampaign")
      .select("externalCampaign.apiCampaignId")
      .where("externalCampaign.apiClientName = :apiClientName", { apiClientName })
      .andWhere("externalCampaign.apiCampaignId LIKE :pattern", { pattern })
      .andWhere("externalCampaign.campaignId IN (:...filteredCampaignId)", { filteredCampaignId })
      .groupBy("externalCampaign.apiCampaignId")
      .orderBy("externalCampaign.apiCampaignId", "ASC")
      .getMany();

    // Return result
    return campaigns.map((campaign) => campaign.apiCampaignId);
  }

  /**
   * Checks for a duplicated campaign based on specific conditions.
   *
   * @param campaignId - The ID of the campaign to check for duplication.
   * @param apiCampaignId - The API campaign ID to exclude from the search.
   * @returns A promise that resolves to the duplicated campaign if found, otherwise null.
   */
  async duplicatedCampaign(campaignId: string, apiCampaignId: string) {
    // Query the repository to find a campaign that matches the given campaignId.
    const campaign = await this.repository
      .createQueryBuilder("externalCampaign")
      .select("id, campaign_sponsorship_ended")
      // Check if the campaignId matches the one provided.
      .where("externalCampaign.campaignId = :campaignId", { campaignId })
      // Ensure the apiCampaignId starts with "appcast_%_" and includes the campaignId.
      .andWhere("externalCampaign.apiCampaignId LIKE :pattern", {
        pattern: `appcast_%`,
      })
      // Exclude the current apiCampaignId from the search.
      .andWhere("externalCampaign.apiCampaignId != :apiCampaignId", { apiCampaignId })
      // Ensure the client name is "appcast".
      .andWhere("externalCampaign.client_hash = :clientHash", { clientHash: 1 })
      // Order the results by date in descending order to get the most recent campaign first.
      .orderBy("externalCampaign.date", "DESC")
      // Limit the results to only one campaign.
      .limit(1)
      // Execute the query and get the first result.
      .getOne();

    // Return the duplicated campaign if found, otherwise return null.
    return campaign;
  }

  /**
   * Function to check if the company has at least one campaign created in Talent.
   * @param companyName string Company name to check. Example: 'Dahmen Personalservice GmbH'
   * @param feedcodeDto string Feedcode linked to the company list. Example: 'appcast-appcast-exchanges-usd-cpc-amazon'
   * @returns
   */
  async hasCompanyCampaign(
    companyName: string,
    feedcodeDto: FeedcodeExternalCampaignDto,
  ): Promise<boolean> {
    // Init variables
    let result: boolean = false;

    // Validate companyName and feedcode
    const { feedcode } = feedcodeDto;
    if (feedcode.trim() === "") {
      throw new HttpException("Feedcode is required", HttpStatus.BAD_REQUEST);
    }
    if (companyName.trim() === "") {
      throw new HttpException("Company Name is required", HttpStatus.BAD_REQUEST);
    }

    // Verify if the company has campaigns in Talent
    const resultHasCompany = await this.repository
      .createQueryBuilder("external_campaigns")
      .where("external_campaigns.talent_feedcode = :feedcode", { feedcode })
      .andWhere("external_campaigns.talent_campaign LIKE :campaign", {
        campaign: `${companyName}%`,
      })
      .limit(1)
      .getOne();

    // Return result
    result = !!resultHasCompany;
    return result;
  }

  /**
   * Retrieves a list of campaigns that need to be notified based on their notification status.
   *
   * This method queries the campaign repository, selecting campaigns with specific notification statuses
   * ('toSend' or 'toSendLive'), and groups the results by the `api_campaign_id`. The results are ordered
   * by the campaign date in descending order and limited to a maximum of 10,000 campaigns.
   *
   * @returns  A promise that resolves to an array of campaign records.
   */
  async listCampaigsToNotified(emailType: string) {
    const query = this.repository
      .createQueryBuilder("campaign")
      .select("campaign.*") // Adjust to select specific fields as needed
      .where("campaign.notified IN (:...statuses)", { statuses: ["toSend", "toSendLive"] }) // Filter campaigns by notification status
      .groupBy("campaign.api_campaign_id") // Group by API campaign ID to avoid duplicates
      .orderBy("campaign.date", "DESC") // Order by the campaign date in descending order
      .limit(5000); // Limit the result set to 10,000 records

    if (emailType === "validations") {
      query.andWhere('campaign.notified = "toSend"');
      query.andWhere('campaign.action = "not allowed"');
      query.andWhere('campaign.campaign_sponsorship_ended <> "1"');
    } else {
      query.andWhere('campaign.action <> "not allowed"');
      query.andWhere('campaign.action <> "error"');
      query.andWhere('campaign.action <> "campaign"');
    }

    return await query.getRawMany(); // Execute the query and return the results
  }

  /**
   * Updates the 'notified' field for a specific campaign by its ID.
   *
   * This method updates the `notified` field of a campaign in the repository,
   * identified by its ID, to a new value.
   *
   * @param  id - The ID of the campaign to update.
   * @param  notified - The new value for the 'notified' field.
   * @returns  A promise that resolves to the result of the update operation.
   */
  async updateNotifiedField(id: number, notified: string) {
    return await this.repository.update(id, { notified } as Partial<ExternalCampaign>); // Update the notified field
  }

  /**
   * Searches for feedcode validation based on the given parameters.
   *
   * @param id - The campaign ID to search for.
   * @param currency - The currency associated with the account.
   * @param tag - A tag to include in the search query.
   * @returns An array of records containing the feedcode of external campaigns.
   */
  async searchFeedcodeValidation(id: string, currency: string, tag: string) {
    let clientHash = 0;
    switch (tag) {
      case "appcast":
        clientHash = 1;
        break;
      case "recruitics":
        clientHash = 2;
        break;
      case "goldenbees":
        clientHash = 3;
        break;
      default:
        clientHash = 0;
        break;
    }

    // Create the query builder to search for feedcode validation
    const result = this.repository
      .createQueryBuilder("external_campaigns")
      .select("a.feedcode", "feedcode")
      .innerJoin("accounts", "a", "external_campaigns.talent_feedcode = a.feedcode")
      .where("external_campaigns.publisher_id = :publisherId", {
        publisherId: `${id}`,
      })
      .andWhere("external_campaigns.account_currency_name = :currency", { currency })
      .andWhere("external_campaigns.client_hash = :clientHash", { clientHash })
      .andWhere("a.status = 'active'")
      .limit(1);

    // Execute the query and return the results as an array of raw data
    return await result.getRawOne();
  }
}
