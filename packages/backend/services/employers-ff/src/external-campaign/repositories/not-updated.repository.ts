/* eslint-disable @typescript-eslint/no-explicit-any */
import { HttpException, HttpStatus, Inject, Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { NotUpdated } from "../entities/not-updated.entity";
import { Repository } from "typeorm";
import { CreateNotUpdatedDto } from "../dto/create-not-updated.dto";
import { UpdateNotUpdatedDto } from "../dto/update-not-updated.dto";
import { CACHE_MANAGER } from "@nestjs/cache-manager";
import { Cache } from "cache-manager";

/**
 * Class in charge of all operations with External Campaign's Not Updated (CRUD)
 */
@Injectable()
export class NotUpdatedRepository {
  /**
   * Getting an instance in the constructor of the actual ExternalCampaignNotUpdated repository
   */
  constructor(
    @InjectRepository(NotUpdated)
    private readonly notUpdatedRepository: Repository<NotUpdated>,
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
  ) {}

  /**
   * Function to find a single record by ID
   * @param id
   * @returns
   */
  async findOne(id: number) {
    try {
      // Try to get the record
      const record = await this.notUpdatedRepository.findOneBy({ id });

      // If not found, throw an error
      if (!record) {
        throw new HttpException("No record found", HttpStatus.NOT_FOUND);
      }

      // Otherwise, return the record
      return record;
    } catch (error) {
      // Catch any error that occurs during the process
      console.log(error);
    }
  }

  /**
   * Function to create a new record
   * @param createNotUpdatedDto
   * @returns
   */
  async create(createNotUpdatedDto: CreateNotUpdatedDto, userId: number): Promise<any> {
    const { apiCampaignId } = createNotUpdatedDto;

    const auNotUp = this.notUpdatedRepository.create({
      apiCampaignId,
      created: new Date(),
      createdBy: userId, // must be the user logged in; in this case, we are sending it through the JSON
    });

    try {
      await this.notUpdatedRepository.save(auNotUp);
      return auNotUp;
    } catch (error) {
      // Check if the error is an instance of ValidationError
      console.log(error);
    }
  }

  /**
   * Function to update an existing record
   * @param id
   * @param updateNotUpdatedDto
   * @returns
   */
  async update(id: number, updateNotUpdatedDto: UpdateNotUpdatedDto): Promise<any> {
    try {
      const { apiCampaignId, createdBy } = updateNotUpdatedDto;

      // Retrieve the record to update
      const externalCampaignNotUpdated: any = await this.findOne(id);

      if (typeof externalCampaignNotUpdated == "undefined") {
        throw new HttpException(
          "Couldn't find externalcampaign for the specified ID",
          HttpStatus.NOT_FOUND,
        );
      }

      // Update the record with the new values
      if (typeof apiCampaignId !== "undefined") {
        externalCampaignNotUpdated.apiCampaignId = apiCampaignId;
      }
      if (typeof createdBy !== "undefined") {
        externalCampaignNotUpdated.createdBy = createdBy;
      }

      // Save the updated record
      return await this.notUpdatedRepository.save(externalCampaignNotUpdated);
    } catch (error) {
      // Catch any error that occurs during the process
      console.log(error);
    }
  }

  /**
   * Function to remove a record by ID
   * @param apiCampaignId
   */
  async remove(apiCampaignId: string): Promise<void> {
    try {
      // Attempt to delete the record
      const result = await this.notUpdatedRepository.delete({ apiCampaignId });

      // If the record is not found, throw a NotFoundException
      if (result.affected === 0) {
        throw new HttpException(
          `Record with apiCampaignId "${apiCampaignId}" not found`,
          HttpStatus.NOT_FOUND,
        );
      }
    } catch (error) {
      // Catch any error that occurs during the process
      console.log(error);
    }
  }

  /**
   * find a single record for any field
   * @param field
   * @param value
   * @returns
   */
  async find(field: string, value: string) {
    const records = await this.notUpdatedRepository.find({
      where: { [field]: value },
    });
    return records.length > 0;
  }

  /**
   * List all the non-automatic campaigns from the bucket external_campaigns_not_updated to manage the toggle and tag the campaign automatic or not.
   * @returns
   */
  async listNonAutoCampaigns(): Promise<string[]> {
    const nonAutomaticCampaigns = await this.notUpdatedRepository
      .createQueryBuilder("campaign")
      .select("campaign.apiCampaignId")
      .getMany();

    const apiCampaignIdList = nonAutomaticCampaigns
      .map((campaign) => campaign.apiCampaignId)
      .filter((id) => !!id);

    return apiCampaignIdList;
  }
}
