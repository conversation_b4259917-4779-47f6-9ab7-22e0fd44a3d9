import { forwardRef, Module } from "@nestjs/common";
import { XmlService } from "./services/xml.service";
import { ExternalCampaignModule } from "../external-campaign.module";
import { CustomAxiosAdapter } from "../../common/adapters/axios.adapter";
import { HttpModule } from "@nestjs/axios";
import { AccountsModule } from "../../accounts/accounts.module";
import { TagsModule } from "../../tags/tags.module";
import { CacheModule } from "@nestjs/cache-manager";
import { EmployerErrorLogsModule } from "../../common/employer-error-logs/employer-error-logs.module";
import { ExternalCampaignIngestionService } from "../services/abstract/external-campaign-ingestion.service";

/**
 * Module for managing all ExternalCampaign XML ingestion Logic
 */
@Module({
  imports: [
    HttpModule,
    forwardRef(() => ExternalCampaignModule),
    AccountsModule,
    TagsModule,
    EmployerErrorLogsModule,
  ],
  providers: [XmlService, CustomAxiosAdapter, ExternalCampaignIngestionService],
  controllers: [],
  exports: [XmlService],
})
export class XmlModule {}
