import { HttpException, HttpStatus, Inject, Logger } from "@nestjs/common";
import { NotUpdatedService } from "../not-updated.service";
import { ExternalCampaignService } from "../external-campaign.service";
import { ExternalCampaign } from "../../entities/external-campaign.entity";
import { CampaignStatus } from "../../../common/resources/enums";
import { CACHE_MANAGER } from "@nestjs/cache-manager";
import { Cache } from "cache-manager";

/**
 * Abstract class serving as interface for all external campaign ingestion services (Client-api, XML, Talent-api)
 */
export class ExternalCampaignIngestionService {
  /**
   *
   */
  constructor(
    @Inject(NotUpdatedService) protected readonly notUpdatedService: NotUpdatedService,
    @Inject(ExternalCampaignService)
    private readonly externalCampaignService: ExternalCampaignService,
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
  ) {}

  /**
   * Check if the campaign is selected as non-external campaigns
   * @return bool Whether the campaign is selected as non-external campaigns
   */
  async isNonExternalCampaigns(apiCampaignId: string): Promise<boolean> {
    return await this.notUpdatedService.find("apiCampaignId", apiCampaignId);
  }

  /**
   * Remove special characters from a string. Example:
   * "RN - <60 day old posts" it's going to be returned as "RN - 60 day old posts" removing the < character.
   * @param string Name to be cleaned
   * @return string
   */
  protected cleanParam(line: string): string {
    const trimmedline = line.trim();
    if (trimmedline == "") {
      // PENDING: Replace with proper error handling
    }
    // Remove all angle brackets, square brackets, curly brackets and .js extensions, iframes and
    // Directory traversal attacks in strings, iframe, onmouseover events
    const customRegex =
      /[{}[]]|iframe|onmouseover|onclick|onchange|undefined|alert\([\S\s]*\)|\/\.\.\/|\.\.\/|src=|[+=']|&#39;/gi;

    return trimmedline.replace(customRegex, "");
  }

  /**
   * Main method for storing logs for each process executed in the system.
   * This method is responsible for persisting log information, including details about the action,
   * additional content, and a traceable JSON payload, for debugging or auditing purposes.
   *
   * @param data - A Map containing key-value pairs of properties to be stored with the log.
   *               These properties can provide additional context for the log entry. If undefined, an empty object is used.
   * @param action - A string representing the action being logged (e.g., "create", "update", "delete").
   * @param content - A string describing the content or the specific process related to the log.
   * @param userId - The ID of the user associated with the log entry, stored as a number for traceability.
   *
   * @returns A Promise<void> that resolves when the log is successfully saved.
   */
  async saveLog(
    data: Map<string, string> | undefined,
    action: string,
    content: string,
    userId: string,
  ): Promise<void> {
    // Convert the Map to a plain JSON object if `data` is defined; otherwise, use an empty object.
    const jsonData = data ? Object.fromEntries(data) : {};

    Logger.log(
      `[external-campaign][${action}][${content}][${userId}] Data: ${JSON.stringify(jsonData)}`,
    );

    // Validate and convert the `userId` to a number.
    /*const userIdNumber = Number(userId);
    if (isNaN(userIdNumber)) {
      throw new HttpException(`Invalid userId: ${userId}`, HttpStatus.BAD_REQUEST);
    }

    // // Create the DTO (Data Transfer Object) expected by the `create` method.
    // const createLogDto = {
    //   entity: "external-campaign", // Category or identifier for the system.
    //   action, // The action being logged (e.g., "create", "update", "delete").
    //   content, // Description or content of the log.
    //   userId: userIdNumber, // User ID converted to a number for traceability.
    //   data: jsonData, // Additional data as a JSON object.
    //   type: jsonData.type, // Default value for 'type' (can be adjusted based on logic).
    //   entityId: jsonData.entityId, // Default value for 'entityId' (adjust based on logic).
    //   status: typeof jsonData.messageStatus === 'object' 
    //     ? JSON.stringify(jsonData.messageStatus) 
    //     : jsonData.messageStatus, // Initial status of the log.
    //   feedcode: jsonData.feedcode, // Additional field for feedcode.
    //   date: new Date(), // Timestamp for when the log was created.
    // };

    // Call the `create` method of the `toolsTrackingLogService` with the DTO.
    //await this.toolsTrackingLogService?.create(createLogDto);*/
  }

  /**
   * Method in charge of determinating whether a budget for an incoming campaign is valid or not based on the predefined business rules
   * @param campaignBudgetType
   * @param campaignBudgetValue
   * @param action
   * @param campaignStatus
   * @returns Object indicating the new action, status, budget type, value and result message out of this process
   */
  validateBudget(
    campaignBudgetType: string,
    campaignBudgetValue: string,
    action: string,
    campaignStatus: string,
    talentCampaignID: string,
    isFullyAutoCampaign: any,
  ): Map<string, string> {
    const budgetValidation = new Map();
    let message: string = "";
    if (campaignBudgetValue === "no limit") {
      message = "Campaign has no budget limitations";
    } else {
      let minimumBudget = 0;
      const minimumBudgetValues = this.externalCampaignService.getMinimumBudgetValues();
      // Determine the budget type and assign the corresponding value
      switch (campaignBudgetType) {
        case "weekly":
          minimumBudget = minimumBudgetValues.get("weekly") ?? 0;
          break;
        case "daily":
          minimumBudget = minimumBudgetValues.get("daily") ?? 0;
          break;
        default:
          minimumBudget = minimumBudgetValues.get("monthly") ?? 0;
          break;
      }
      if (parseFloat(campaignBudgetValue) < minimumBudget || parseFloat(campaignBudgetValue) == 0) {
        message = `The campaign_budget_value is too low for a $campaignBudgetType budget. Please add a budget value higher or equal than ${minimumBudget}`;
        action = "not allowed";
        if (!talentCampaignID) {
          isFullyAutoCampaign = false;
        } else {
          isFullyAutoCampaign = true;
          // If the campaign exists in talent, it will be paused
          campaignStatus = "paused";
        }
      }
    }
    budgetValidation.set("action", action);
    budgetValidation.set("campaignStatus", campaignStatus);
    budgetValidation.set("message", message);
    budgetValidation.set("isFullyAutoCampaign", isFullyAutoCampaign);
    return budgetValidation;
  }

  /**
   * Method in charge of checking whether or not we should insert an external campaign if it has differences from the last entry we've stored for that specific campaign
   * @param newEntry Instance of external campaign indicating this is the new data we've received from the client
   * @param lastEntry Instance of external campaign indicating this is the last data we've stored from the client
   * @returns A boolean indicating whether the entries differ or not
   */
  hasChangedExternalCampaignEntry(
    newEntry: ExternalCampaign,
    lastEntry: ExternalCampaign,
    compareKeys: string[],
  ): boolean {
    if (!compareKeys || compareKeys.length === 0) {
      throw new HttpException("Compare keys are not defined", HttpStatus.BAD_REQUEST);
    }

    // Convert data to insert, delete the id from the lastEntry, and equal the date.
    const lastEntryConverted: any = this.externalCampaignService.convertKeysToSnakeCase(lastEntry);

    lastEntryConverted["date"] = newEntry["date"];
    delete lastEntryConverted.id;
    for (const key of compareKeys) {
      if (newEntry[key] == undefined && !lastEntryConverted[key]) continue;
      if (String(newEntry[key]) !== String(lastEntryConverted[key])) {
        return true;
      }
    }
    return false;
  }

  /**
   * Calculates the notification status and updates the new entry based on certain conditions.
   *
   * @param notified - The current notification status.
   * @param lastEntry - The last entry to compare against.
   * @param newEntry - The new entry that is being evaluated and updated.
   * @param isAccountCreated - A boolean indicating if the account is already created.
   * @returns The updated newEntry with the calculated notification status.
   */
  calculateNotifyAndStatus(
    notified: string,
    lastEntry: any,
    newEntry: any,
    isAccountCreated: boolean,
    talentCampaignID: string,
  ) {
    // If there is no previous entry (i.e., lastEntry is empty):
    if (lastEntry === null || Object.keys(lastEntry).length == 0) {
      // Check if the new entry action is "not allowed" and it is defined.
      if (newEntry?.action == "not allowed" && newEntry?.action != undefined) {
        /**
         * Set the notification status to "toSend" if:
         * - The campaign's budget is below the minimum (action is "not allowed").
         * - The account already exists (isAccountCreated is true).
         * Otherwise, set the notification status to "no".
         */
        newEntry.notified = isAccountCreated ? "toSend" : "no";
      } else {
        // If the action is not "not allowed", retain the current notification status.
        newEntry.notified = notified;
      }
    } else if (newEntry.action == "not allowed") {
      // If the new entry action is "not allowed", handle it with a custom method.
      newEntry = this.handleNotAllowedAction(
        newEntry,
        isAccountCreated,
        lastEntry,
        talentCampaignID,
      );
    } else if (
      // If the action is "account", or it's a "campaign" action and the last entry was notified with "yesAccount",
      // or the last entry was notified with "no", retain the current notification status.
      newEntry.action == "account" ||
      (newEntry.action == "campaign" && lastEntry.notified == "yesAccount") ||
      lastEntry.notified == "no"
    ) {
      newEntry.notified = notified;
    } else {
      // In all other cases, retain the last entry's notification status.
      newEntry.notified = lastEntry.notified ?? "";
    }

    // Return the updated new entry.
    return newEntry;
  }

  /**
   * Handles the "not allowed" action for a campaign entry and determines its status and notification needs.
   *
   * @param newEntry - The new entry being evaluated.
   * @param isAccountCreated - A boolean indicating if the account is already created.
   * @param lastEntry - The previous entry for comparison.
   * @returns The updated newEntry with the appropriate status and notification settings.
   */
  handleNotAllowedAction(
    newEntry: any,
    isAccountCreated: boolean,
    lastEntry: any,
    talentCampaignID: string,
  ) {
    // If the campaign exists in Talent (indicated by a non-empty talentCampaignID), pause the campaign.
    if (talentCampaignID != "") {
      newEntry.status = CampaignStatus.paused;
    }

    /**
     * Rules for sending notifications when a campaign is not allowed:
     * - If the account already exists, determine if a notification should be sent.
     * - If a notification was already sent ("yesValidation") and the budget hasn't changed,
     *   maintain the existing notification status. Otherwise, set it to "toSend".
     */
    if (isAccountCreated) {
      if (
        lastEntry.notified == "yesValidation" &&
        lastEntry.campaign_budget_value == newEntry.campaign_budget_value &&
        lastEntry.campaign_budget_type == newEntry.campaign_budget_type
      ) {
        // Maintain the notification status if the budget hasn't changed and the notification was already sent.
        newEntry.notified = "yesValidation";
      } else {
        // Set the notification status to "toSend" if the budget has changed or no notification was sent.
        newEntry.notified = "toSend";
      }
    } else {
      // If the account is not created, no notification is needed.
      newEntry.notified = "no";
    }

    // Return the updated new entry.
    return newEntry;
  }

  /**
   *
   * @param action
   * @param campaignStatus
   * @param campaignBugetType
   * @param campaignBugetValue
   */
  handleValidateBudget(
    action: string,
    campaignStatus: string,
    campaignBugetType: string,
    campaignBugetValue: any,
    sourceFields: Map<string, string>,
    talentCampaignID: string,
    isFullyAutoCampaign: any,
  ) {
    let isInDifferentMonths: boolean = false;
    if (
      sourceFields.has("campaign_start_date") &&
      sourceFields.get("campaign_start_date") != "" &&
      sourceFields.has("campaign_end_date") &&
      sourceFields.get("campaign_end_date") != ""
    ) {
      const startDateValue = sourceFields.get("campaign_start_date") ?? "";
      const endDateValue = sourceFields.get("campaign_end_date") ?? "";
      const startDate = startDateValue.split("-")[1];
      const endDate = endDateValue.split("-")[1];
      isInDifferentMonths = startDate != endDate;
    }

    let validateBudget: Map<string, string>;

    if (isInDifferentMonths) {
      validateBudget = new Map<string, string>([
        ["action", action],
        ["campaignStatus", campaignStatus],
      ]);
    } else {
      validateBudget = this.validateBudget(
        campaignBugetType,
        campaignBugetValue,
        action,
        campaignStatus,
        talentCampaignID,
        isFullyAutoCampaign,
      );
    }

    return validateBudget;
  }

  /**
   * Process the campaign data, then create or update the campaign if necessary
   * @throws Exception
   */
  async processExternalCampaign(
    sourceFields: Map<string, string>,
    apiCampaignId: string,
    source: string,
    compareKeys: string[],
    newEntry: any,
    lastEntry: any,
    talentCampaignID: any,
    isFullyAutoCampaign: any,
  ) {
    try {
      // Indicating the logs DB we've ingested a campaign and started to process it
      sourceFields.set("type", "receive-entry");
      sourceFields.set(
        "entityId",
        apiCampaignId ?? `campaign-${sourceFields.get("campaign_name")}`,
      );
      sourceFields.set("messageStatus", `receive entry from ${source}`);
      this.saveLog(
        sourceFields,
        `received-${source}`,
        "request received",
        sourceFields.get("user_id") ?? "80010",
      );

      // === Common code ===
      let insert: boolean = false;
      if (!lastEntry) {
        insert = true;
      } else if (this.hasChangedExternalCampaignEntry(newEntry, lastEntry, compareKeys)) {
        insert = !(
          newEntry["campaign_sponsorship_ended"] == "1" &&
          lastEntry["campaignSponsorshipEnded"] == "1"
        );
      }

      if (insert) {
        // Insert the auto campaign
        const result = await this.insertAutoCampaign(
          newEntry,
          sourceFields,
          talentCampaignID,
          apiCampaignId,
          source,
          isFullyAutoCampaign,
        );
        if (!result) {
          // Handle insertion failure (e.g., log, retry)
          return result; // Assuming result indicates failure
        }
      }
      // return success
      const dataToLog = new Map<string, any>(Object.entries(newEntry));
      dataToLog.set("type", "success-entry");
      dataToLog.set("entityId", apiCampaignId ?? `campaign-${sourceFields.get("campaign_name")}`);
      dataToLog.set("messageStatus", `success entry from ${source}`);
      this.saveLog(
        dataToLog,
        `processed-${source}`,
        "success",
        sourceFields.get("user_id") ?? "80010",
      );
    } catch (err: any) {
      sourceFields.set("type", "error-external-campaigns");
      sourceFields.set("error", err as string);
      sourceFields.set(
        "entityId",
        apiCampaignId ?? `campaign-${sourceFields.get("campaign_name")}`,
      );
      sourceFields.set("messageStatus", `receive entry from ${source}`);
      this.saveLog(
        sourceFields,
        `error-${source}`,
        apiCampaignId,
        sourceFields.get("user_id") ?? "80010",
      );
      throw new HttpException(err.message, HttpStatus.BAD_REQUEST);
    }
  }

  /**
   * Creates or updates an auto campaign in Talent and our internal database.
   *
   * @param newEntry An object containing the campaign data.
   * @returns A Promise that resolves to an object with the message "Success" if the campaign was inserted or updated successfully. An error is thrown if an error occurs during the process.
   */
  async insertAutoCampaign(
    newEntry: any,
    sourceFields: Map<string, string>,
    talentCampaignID: string,
    apiCampaignId: string,
    source: string,
    isFullyAutoCampaign: any,
  ): Promise<any> {
    try {
      // If a campaign already exists in Talent, update it
      const jobValue =
        sourceFields.has("job") && sourceFields.get("job") !== "" ? sourceFields.get("job") : "";
      sourceFields.set("job", "");

      if (talentCampaignID && talentCampaignID != "") {
        // updateCampaignAutomatically
        await this.externalCampaignService.updateCampaignAutomatically(
          parseInt(talentCampaignID, 10),
          newEntry,
          jobValue,
        );
        Logger.log(
          `[processExternalCampaign] [updateCampaignAutomatically] [${source}] Campaign ('${talentCampaignID}', '${apiCampaignId}') was successfully updated in Talent.`,
        );
      } else if (isFullyAutoCampaign && newEntry.action == "campaign") {
        // A campaign doesn't exist in Talent, if fully automatic, create it
        await this.externalCampaignService.createCampaignAutomatically(
          newEntry.talent_feedcode,
          newEntry,
          jobValue,
        );
        newEntry["notified"] = "toSendLive";
        newEntry["action"] = "-";
        Logger.log(
          `[processExternalCampaign] [createCampaignAutomatically] [${source}] Campaign '${apiCampaignId}' was successfully created in Talent.`,
        );
      }
      return { message: "Success" };
    } catch (error: any) {
      newEntry["errorMessage"] = "Error in creating campaign or updating automatically";
      newEntry["error"] = {
        message: (error as any)?.message || "An unexpected error occurred",
        status: (error as any)?.status || 500,
      };
      newEntry["type"] = "error-external-campaigns";
      newEntry["entityId"] = apiCampaignId;
      newEntry["messageStatus"] = "Error in creating campaign or updating automatically";
      this.saveLog(
        new Map<string, any>(Object.entries(newEntry)),
        `insert-auto-campaign-error-${source}`,
        apiCampaignId,
        sourceFields.get("user_id") ?? "80010",
      );
      newEntry["action"] = "error";
      if (source == "talent-api") {
        Logger.warn(
          `[processExternalCampaign] [createCampaignAutomatically] [talent-api] ERROR: ${error.options?.cause ?? error.message}`,
        );
        throw new HttpException(
          "Unexpected error creating your campaign. Please contact your account manager.",
          HttpStatus.BAD_REQUEST,
        );
      }

      //Let's get if the error is Lock wait timeout exceeded and delete the apiCampaignId from the cache so that it can be recreated/updated next run
      await this.cacheManager.del(apiCampaignId);
    } finally {
      // This block will execute regardless of success or failure in the try block
      await this.externalCampaignService.createAutoCampaignEntry(newEntry);
      Logger.log(
        `[processExternalCampaign] [createAutoCampaignEntry] [${source}] Campaign '${apiCampaignId}' was successfully created in external campaign bucket.`,
      );
    }
  }
}
