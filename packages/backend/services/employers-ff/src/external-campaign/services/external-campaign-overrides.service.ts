/* eslint-disable @typescript-eslint/no-explicit-any */
import { HttpException, HttpStatus, Inject, Injectable } from "@nestjs/common";
import { CreateOverrideDto } from "../dto/create-override.dto";
import { ExternalCampaignOverridesRepository } from "../repositories/external-campaign-overrides.repository";
import { Override } from "../entities/override.entity";
import { SearchOverrideDto } from "../dto/search-override.dto";
import { SearchExternalCampaignDto } from "../dto/search-external-campaign.dto";
import { CheckOverrideDto } from "../dto/check-override.dto";
import { DeleteOverrideDto } from "../dto/delete-override.dto";
import { ExternalCampaignHelperService } from "./external-campaign-helper.service";
import { ExternalCampaignService } from "./external-campaign.service";
import { ExternalCampaignIngestionService } from "./abstract/external-campaign-ingestion.service";

/**
 * Override functionality and business logic manager:
 * ------------------------------------ Campaign Overrides' Logic -------------------------------------------
 * A campaign override means, a specific set of values to give priority in case of auto campaign updates      *
 * Even if we receive different data from the client, we must only use the data that comes into the override. *
 */
@Injectable()
export class ExternalCampaignOverridesService {
  private readonly campaignDeliveries: { [key: number]: string } = {
    0: "accelerated",
    1: "paced",
    2: "paced (flexible)",
  };

  private readonly budgetTypes: { [key: number]: string } = {
    0: "monthly",
    1: "daily",
    2: "weekly",
  };

  private readonly conversionTypes: { [key: number]: string } = {
    0: "applystart",
    1: "application",
    2: "qualified_application",
    3: "other",
  };

  /**
   * Injecting the access to the database through override entity
   */
  constructor(
    // Inject the OverrideRepository instance
    private readonly overridesRepository: ExternalCampaignOverridesRepository,
    private readonly helperService: ExternalCampaignHelperService,
    private readonly externalCampaignService: ExternalCampaignService,
    @Inject(ExternalCampaignIngestionService)
    private readonly externalIngestion: ExternalCampaignIngestionService,
  ) {}

  /**
   * Function to find a single ExternalCampaign override record by field and value
   * Returns the last record inserted by the field filetered
   * @param field
   * @param value
   * @returns
   */
  async findOne(field: string, value: string) {
    return await this.overridesRepository.findOne(field, value);
  }

  /**
   * Function to insert a new ExternalCampaign override entry
   * @param createOverrideDto
   * @param userId
   * @returns
   */
  async insertEntry(
    createOverrideDto: CreateOverrideDto,
    userId: number,
  ): Promise<{
    data: Override;
    duplicated: boolean;
  }> {
    return await this.overridesRepository.create(createOverrideDto, userId);
  }

  /**
   * Get information from an override
   * @param apiCampaignId
   * @param maxDate
   * @returns
   */
  async getOverrideInfo(apiCampaignId: string, maxDate: string = ""): Promise<Override> {
    // Init variables
    let data: any = {};
    // Validations
    if (!apiCampaignId.trim()) {
      throw new HttpException("Please enter a valid apiCampaignId", HttpStatus.BAD_REQUEST);
    }

    // We only need maxDate for the History Panel, we are going to implement this part after falcon
    if (maxDate === "") {
      data = await this.findOne("apiCampaignId", apiCampaignId);
    } // else will be for history panel

    // Check if data return result
    if (!data) {
      throw new HttpException(
        `No overrides found for ApiCampaignId ${apiCampaignId}`,
        HttpStatus.NOT_FOUND,
      );
    }

    // Return results
    return data;
  }

  /**
   * This method is used by overrides' table in AutoCampaign dashboard for showing the overrides based on the conditions we receive
   * @param searchDto a key-value object (DTO) indicating which are going to be the conditions of the SQL query
   * For instance:
   * searchDto = {
   *   apiCampaignId: "appcast_123_456",
   *   feedcode: "appcast_test",
   *   companyName: "talent.com",
   *   country: "ca"
   * }
   * @returns
   */
  async getAutoCampaignOverrides(searchDto: SearchOverrideDto): Promise<any> {
    try {
      // Retrieve distinct apiCampaignId values filtering by the values in the dto for status != 0
      const apiCampaignIds = await this.overridesRepository.getAutoCampaignOverridesApiCampaignIds(
        searchDto,
        true,
      );

      // Return an empty array when there are no overrides
      if (!apiCampaignIds.apiCampaignIds || apiCampaignIds.apiCampaignIds.length === 0) {
        return [];
      }

      // Fetch detailed override information for each apiCampaignId
      const overridesArray: Override[] = [];
      for (const apiCampaignId of apiCampaignIds.apiCampaignIds) {
        const overrideInfo = await this.getOverrideInfo(apiCampaignId);
        // Check if the result is "ok" and overrideStatus is valid
        if (overrideInfo.overrideStatus) {
          overridesArray.push(overrideInfo);
        }
      }

      // Return the results
      return { total: apiCampaignIds.total, overrides: overridesArray };
    } catch (error: any) {
      throw new HttpException(
        `Error retrieving AutoCampaign Overrides: ${error.message}`,
        HttpStatus.NOT_FOUND,
      );
    }
  }

  /**
   * Method to update in the repository an existing override. Mainly used for deleteOverride to change status to 0.
   * @param id
   * @param updateData
   * @returns
   */
  async updateOverride(id: number, updateData: Partial<Override>) {
    await this.overridesRepository.updateOverride(id, updateData);
  }

  /**
   * This is the wrapper for inserting new autoCampaign overrides based on the info we got from the front (check file action-insert-override.php)
   * @param createOverrideDto
   * @param userId
   * @returns
   */
  async insertOverride(createOverrideDto: CreateOverrideDto, userId: number): Promise<any> {
    try {
      // Insert the data into the database
      const result = await this.insertEntry(createOverrideDto, userId);

      if (!result) {
        throw new HttpException("Failed to insert the override", HttpStatus.INTERNAL_SERVER_ERROR);
      }

      return { result: "Success", data: result.data, duplicated: result.duplicated };
    } catch (error: any) {
      throw new HttpException(
        `Error inserting override: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Method in charge of validating each of the parameters required to either update or insert overrides
   * @param override Example:
   * const override: any = {
   *   apiCampaignId: "test_33855_362075",
   *   clientName: "appcast",
   *   feedcode: "test_feedcode",
   *   overrideData: {
   *     budgetType: "daily",
   *     delivery: "paced (flexible)",
   *     conversionType: "application",
   *     budget: "1000",
   *     conversionTargetCost: "77",
   *     dateStart: "2024-09-04 10:04:05",
   *     dateEnd: "2024-09-24 10:04:05"
   *   }
   * };
   * @returns
   */
  async validateOverrideParams(override: any): Promise<any> {
    // Init variables
    const overrideValues = override.overrideData;
    const errors: { [key: string]: string } = {};
    const searchDto: SearchExternalCampaignDto = { apiCampaignId: override.apiCampaignId };
    if (!overrideValues || Object.keys(overrideValues).length < 1) {
      throw new HttpException(
        "You haven't entered any data for creating an override. In case you want to edit an override without any information, please use override deletion.",
        HttpStatus.BAD_REQUEST,
      );
    }

    // Performing field validation
    // Getting the data from the entry in external_campaigns for validating extra info.
    const entry = await this.getCampaignEntry(searchDto);

    // Checking previous values based on existing talent campaign (if it exists)
    const currentTalentCampaign = await this.getCurrentTalentCampaignWithBudget(
      override.apiCampaignId,
    );

    // Checking conversion type and target cost (it must be NON EMPTY AND HIGHER THAN 0 when there's a conversion type)
    this.validateConversionTypeAndTargetCost(overrideValues, errors, entry, currentTalentCampaign);

    // Validating budget delivery in case we receive it.
    this.validateBudgetDelivery(overrideValues, entry, errors);

    // Numeric inputs Validation
    this.validateNumericInputs(overrideValues, entry, errors, this.isNumeric);

    // Dates validation
    this.validateDates(overrideValues, errors, currentTalentCampaign);

    // Checking budget change: It mustn't be lower than the current spent (flexibleSpent)
    this.validateBudgetChange(overrideValues, errors, currentTalentCampaign);

    // Return errors if any
    return errors;
  }

  /**
   * Utility function to check if a string is a valid number
   * @param value
   * @returns
   */
  isNumeric(value: string): boolean {
    return /^-?\d+(\.\d+)?$/.test(value);
  }

  /**
   * Helper function to get data from the entry in external_campaigns for validating extra info.
   * @param searchDto
   * @returns
   * @throws HttpException if there's an error getting campaign data
   */
  async getCampaignEntry(searchDto: SearchExternalCampaignDto): Promise<any> {
    let callEntry: any = {};

    try {
      callEntry = await this.helperService.listAutoCampaignEntry(searchDto, false);
    } catch (error: any) {
      console.log("There's an error getting campaign data: ", error.message);
    }

    if (!callEntry || !Array.isArray(callEntry.campaigns) || callEntry.campaigns.length === 0) {
      throw new HttpException(
        `There was an error while trying to get the last entry from automatic campaigns.`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }

    return callEntry.campaigns[0];
  }

  /**
   * Helper function to validate conversion type and target cost
   * @param overrideValues
   * @param errors
   * @param entry
   */
  validateConversionTypeAndTargetCost(
    overrideValues: any,
    errors: { [key: string]: string },
    entry: any,
    currentTalentCampaign: any,
  ) {
    // Extract values in priority order: override > currentTalentCampaign > entry
    const conversionType =
      overrideValues?.conversionType ??
      currentTalentCampaign?.conversionType ??
      entry.conversionType ??
      null;

    const conversionTargetCost =
      overrideValues?.conversionTargetCost ??
      currentTalentCampaign?.conversionTargetCost ??
      entry.conversionTargetCost ??
      null;

    // Validate the relationship between conversionType and conversionTargetCost
    if (conversionType !== null && conversionTargetCost === null) {
      errors.conversionTargetCost =
        "A conversion target cost is needed since we have a conversion type";
    }

    if (conversionTargetCost !== null && conversionType === null) {
      errors.conversionType = "A conversion type is needed since we have a conversion target cost";
    }
  }

  /**
   * Helper function to validate budget delivery.
   * @param overrideValues
   * @param entry
   * @param errors
   */
  validateBudgetDelivery(overrideValues: any, entry: any, errors: { [key: string]: string }): void {
    if (
      overrideValues.delivery === "paced" &&
      (overrideValues.budgetType === "monthly" ||
        (!overrideValues.budgetType &&
          (entry.campaignBudgetType === "monthly" || !entry.campaignBudgetType)))
    ) {
      const budget = parseFloat(overrideValues.budget) || parseFloat(entry.campaignBudgetValue);
      if (isNaN(budget) || budget < 300) {
        errors.budget =
          "This campaign cannot be paced as it has a monthly budget lower than 300. Please change the budget delivery to 'accelerated'";
      }
    }
  }

  /**
   * Helper function to validate numeric inputs.
   * @param overrideValues
   * @param entry
   * @param errors
   * @param isNumeric
   */
  validateNumericInputs(
    overrideValues: any,
    entry: any,
    errors: { [key: string]: string },
    isNumeric: (value: any) => boolean,
  ): void {
    const numericsToValidate = ["budget", "cpc", "conversionTargetCost"];

    for (const item of numericsToValidate) {
      /* Budget change validations according to the following information:
    1. If a CS changes the budget to below 50 And it's a budget type Monthly then
    show this error message when submitting the change: “The current monthly budget value is not allowed".
    Please change to a value higher than 50.
    2. If a CS changes the budget to below 10 And it's a budget type Daily then
    show this error message when submitting the change: “The current monthly budget value is not allowed".
    Please change to a value higher than 10.
    3. If a CS changes the budget to below 70 And it's a budget type Weekly then
    show this error message when submitting the change:
    “The current monthly budget value is not allowed. Please change to a value higher than 70."*/

      if (overrideValues[item]) {
        const rawValue = overrideValues[item];

        // Check if the value is numeric
        if (!isNumeric(rawValue)) {
          errors[item] = "This value should be a number";
          continue; // Skip further checks if the value is not a valid number
        }

        const value = parseFloat(rawValue);
        const budgetType = overrideValues.budgetType || entry.campaignBudgetType;

        if (item === "budget") {
          this.validateBudgetByType(value, budgetType, errors);
        }
      }
    }
  }

  /**
   * Helper function to validate budget based on its type and value.
   * @param value - The budget value to validate.
   * @param budgetType - The type of budget (monthly, daily, weekly).
   * @param errors - The errors object to populate with error messages.
   */
  validateBudgetByType(value: number, budgetType: string, errors: { [key: string]: string }) {
    if (budgetType === "monthly" && value < 50) {
      errors.budget =
        "The current monthly budget value is not allowed. Please change to a value higher than 50";
    } else if (budgetType === "daily" && value < 10) {
      errors.budget =
        "The current daily budget value is not allowed. Please change to a value higher than 10";
    } else if (budgetType === "weekly" && value < 70) {
      errors.budget =
        "The current weekly budget value is not allowed. Please change to a value higher than 70";
    }
  }

  /**
   * Helper function to get the current talent campaign with budget.
   * @param apiCampaignId
   * @returns
   */
  async getCurrentTalentCampaignWithBudget(apiCampaignId: string): Promise<any> {
    try {
      return await this.helperService.findOneByApiCampaignId(apiCampaignId, ["campaignsBudget"]);
    } catch (error) {
      // Handle the error (e.g., logging or rethrowing as a different error)
      console.error("Error getting current Talent campaign with budget:", error);
      throw new HttpException(
        "Error getting current Talent campaign with budget",
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Helper function to validate the start and end dates.
   * @param overrideValues
   * @param errors
   * @param currentTalentCampaign
   */
  validateDates(
    overrideValues: any,
    errors: { [key: string]: string },
    currentTalentCampaign: any,
  ): void {
    const now = new Date();
    const today = new Date(
      Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate(), 0, 0, 0),
    );
    if (overrideValues.dateStart || overrideValues.dateEnd) {
      const startDate = new Date(overrideValues.dateStart);
      const endDate = new Date(overrideValues.dateEnd);
      const currentStartDate = currentTalentCampaign?.dateStart
        ? new Date(currentTalentCampaign.dateStart)
        : null;
      const currentEndDate = currentTalentCampaign?.dateEnd
        ? new Date(currentTalentCampaign.dateEnd)
        : null;

      /* Performing validations
    1. Start date and end date shouldn't be prior than today unless they already have those dates (no changes from user)
    2. End date and start date cannot be equals to each other.
    3. End date cannot be prior to start date.*/
      if (startDate < today && (!currentStartDate || startDate !== currentStartDate)) {
        errors.startDate = "The start date cannot be prior to today";
      }

      if (endDate <= startDate) {
        errors.endDate = "The end date cannot be equal to or before the start date";
      } else if (endDate < today && (!currentEndDate || endDate !== currentEndDate)) {
        errors.endDate = "The end date cannot be before today";
      }
    }
  }

  /**
   * Helper function to validate the budget change.
   * @param overrideValues
   * @param errors
   * @param currentTalentCampaign
   */
  validateBudgetChange(
    overrideValues: any,
    errors: { [key: string]: string },
    currentTalentCampaign: any,
  ): void {
    // Check if the budget is 0
    if (Number(overrideValues.budget) === 0) {
      errors.budget = "This value cannot be 0";
    }

    // Check if the budget is lower than the current spent (flexibleSpent)
    const currentFlexibleSpent =
      currentTalentCampaign?.campaignsBudget?.flexibleSpent !== undefined
        ? currentTalentCampaign.campaignsBudget.flexibleSpent
        : null;
    const currentBudgetMonth =
      currentTalentCampaign?.campaignsBudget?.budgetMonth !== undefined
        ? currentTalentCampaign.campaignsBudget.budgetMonth
        : null;

    if (overrideValues.budget) {
      if (
        currentFlexibleSpent !== null &&
        overrideValues.budget !== currentBudgetMonth &&
        overrideValues.budgetType === "monthly"
      ) {
        // In case the override budget is empty, or it's less than the current flexible spent of the existing campaign, we won't be able to insert this override
        if (
          (this.isNumeric(overrideValues.budget) &&
            parseFloat(overrideValues.budget) < parseFloat(currentFlexibleSpent)) ||
          String(overrideValues.budget).trim() === ""
        ) {
          errors.budget = "This value cannot be lower than current flexible spent";
        }
      }
    }
  }

  /**
   * List all the historical entries
   * @param apiCampaignId
   */
  async listHistoricalEntriesFromOverrides(apiCampaignId: string) {
    if (apiCampaignId.trim() === "") {
      throw new HttpException("ApiCampaignID shouldn't be empty", HttpStatus.BAD_REQUEST);
    }

    const records = await this.overridesRepository.listHistoricalEntries(apiCampaignId);

    const entries = records.map((record) => {
      const overrideData = JSON.parse(record.override_override_data);
      const entry = {
        ...record,
        override: 1,
      };
      if (overrideData.delivery) {
        entry["campaign_delivery"] = overrideData.delivery;
      }
      if (overrideData.budgetType) {
        entry["campaign_budget_type"] = overrideData.budgetType;
      }
      if (overrideData.budget) {
        entry["campaign_budget_value"] = overrideData.budget;
      }
      if (overrideData.conversionTargetCost) {
        entry["campaign_target_cost"] = overrideData.conversionTargetCost;
      }
      if (overrideData.conversionType) {
        entry["campaign_conversion_type"] = overrideData.conversionType;
      }
      if (overrideData.dateStart) {
        entry["campaign_start_date"] = overrideData.dateStart;
      }
      if (overrideData.dateEnd) {
        entry["campaign_end_date"] = overrideData.dateEnd;
      }
      // Delete overrideData from result
      delete entry.overrideData;

      return entry;
    });

    return entries;
  }

  /**
   * Search all the campaigns from external campaigns's bucket by the filter provided in the DTO.
   * We have the last register for each apiCampaignId.
   * This method is created to feed the dashboard for Ingested Campaigns.
   * @param searchDto
   * @returns
   */
  async getOverridesByFilters(searchDto: SearchOverrideDto): Promise<any> {
    // Getting the data from DDBB
    const overrides = await this.getAutoCampaignOverrides(searchDto);

    // If there are no results, we return total 0 and an empty array
    if (!overrides.overrides || overrides.overrides.length === 0) {
      return {
        total: 0,
        overrides: [],
      };
    }

    // Logic for override's table
    try {
      const overridesTable = await this.renderOverridesTable(overrides.overrides);

      // Return the result
      return {
        total: overrides.total,
        overrides: overridesTable,
      };
    } catch (error: any) {
      throw new HttpException(error.message, HttpStatus.NOT_FOUND);
    }
  }

  /**
   * Function to iterate over overrides and build the override table
   * @param overrides
   * @returns
   */
  async renderOverridesTable(overrides: any) {
    // Init variales
    const lines: any[] = [];
    let overrideData: any = {};

    // Get apiCampaignIds from overrides
    const apiCampaignIds = overrides.map(
      (overrides: { apiCampaignId: string }) => overrides.apiCampaignId,
    );

    // Get externalCampaignEntries for all the apiCampaignIds
    try {
      const entries =
        await this.helperService.listAutoCampaignEntryByApiCampaignIds(apiCampaignIds);

      // Iterate through each campaign and perform any desired operations
      for (const override of overrides) {
        let line: any = {};

        // Get external campaign info in case override info is missing for some fields
        const autoCampaignEntry = entries.find(
          (entry: { apiCampaignId: string }) => entry.apiCampaignId === override.apiCampaignId,
        );

        // Convert overrideData to json
        overrideData = JSON.parse(override.overrideData);

        // Build the override's table
        line = await this.linesToRenderOverridesTable(
          line,
          override,
          overrideData,
          autoCampaignEntry,
        );

        // Final array with all the lines
        lines.push(line);
      }

      // Return the result
      return lines;
    } catch (error: any) {
      throw new HttpException(error.message, HttpStatus.NOT_FOUND);
    }
  }

  /**
   * Helper function to build the override's table
   * @param line
   * @param override
   * @param overrideData
   * @param entryCampaigns
   * @returns
   */
  async linesToRenderOverridesTable(
    line: any,
    override: any,
    overrideData: any,
    entryCampaigns: any,
  ) {
    // Build the override's table
    line["ApiClientName"] = entryCampaigns.apiClientName;
    line["DateCreated"] = this.formatDateCreated(override.created);
    line["ApiCampaignID"] = override.apiCampaignId;
    line["Feedcode"] = override.feedcode;
    line["Company"] = override.companyName;
    line["Campaign"] = override.campaignName;
    line["Country"] = entryCampaigns.campaignCountry;
    line["Delivery"] = {
      clientData:
        entryCampaigns.campaignDelivery?.toLowerCase() === "not paced"
          ? "Accelerated"
          : this.ucfirst(entryCampaigns.campaignDelivery) || "",
      overwrittenData: this.ucfirst(overrideData.delivery) || null,
    };
    line["SponsorshipEnded"] = override.campaignSponsorshipEnded;
    line["StartDate"] = this.getEntryOverrideValue(
      entryCampaigns?.campaignStartDate,
      overrideData?.dateStart,
    );
    line["EndDate"] = this.getEntryOverrideValue(
      entryCampaigns?.campaignEndDate,
      overrideData?.dateEnd,
    );
    line["BudgetType"] = this.getEntryOverrideValue(
      this.ucfirst(entryCampaigns?.campaignBudgetType) || "Monthly",
      this.ucfirst(overrideData?.budgetType),
    );
    line["Budget"] = this.getEntryOverrideValue(
      entryCampaigns?.campaignBudgetValue,
      overrideData?.budget,
    );
    line["CPC"] = entryCampaigns?.campaignCpc || 0;
    line["ConversionType"] = this.getEntryOverrideValue(
      this.ucfirst(entryCampaigns?.campaignConversionType),
      this.ucfirst(overrideData?.conversionType),
    );
    line["ConversionTargetCost"] = this.getEntryOverrideValue(
      entryCampaigns?.campaignTargetCost === "N/A"
        ? entryCampaigns?.campaignTargetCost
        : parseInt(entryCampaigns?.campaignTargetCost, 10),
      overrideData?.conversionTargetCost,
    );
    return line;
  }

  /**
   * Helper function to format date for overrides panel
   */
  private formatDateCreated(date: Date): string {
    return date
      .toLocaleString("en-US", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit",
        hour12: false,
      })
      .replace(",", "")
      .replace(" AM", "")
      .replace(" PM", "");
  }

  /**
   * Helper function to create some entries for overrides panel
   */
  private getEntryOverrideValue(entryValue: any, overrideValue: any) {
    return {
      clientData: entryValue || "",
      overwrittenData: overrideValue || null,
    };
  }

  /**
   * Helper function to emulate ucfirst from PHP
   * @param str
   * @returns
   */
  ucfirst(str: string): string {
    if (!str) return str; // Handle empty strings
    return str.charAt(0).toUpperCase() + str.slice(1);
  }

  /**
   * Function to check if an override exists to give the possibility of creating it based on a key:value pair. e.g
   * {apiCampaignId: "appcast_27821_239368"}
   * {campaignName: "switzerland - talent_gbp - 239368 - ag_staffing"}
   * @param checkDto
   * @returns
   */
  async renderNewOverride(checkDto: CheckOverrideDto) {
    // Init variables
    const { field, value } = checkDto;

    // Check for override existence and define searchDto
    try {
      let overrideExists: any = {};
      let searchDto: SearchExternalCampaignDto = {};
      const banner = {
        banner:
          "This campaign has an override already. Please close this pop-up and search for the campaign you want to edit in the 'Campaigns Overrides'",
      };
      switch (field) {
        case "apiCampaignId":
          overrideExists = await this.helperService.checkOverrideExistence(value, "");
          searchDto = { apiCampaignId: value };
          break;

        case "campaignName":
          overrideExists = await this.helperService.checkOverrideExistence("", value);
          searchDto = { talentCampaign: value };
          break;

        default:
          break;
      }

      // If override exists, return the banner
      if (overrideExists.exists) {
        return banner;
      }

      // Retrieve info from external campaigns
      const call = await this.helperService.listAutoCampaignEntry(searchDto, false);
      if (call.total > 0) {
        return await this.getNewOverrideInfoToRender(call);
      }
    } catch (error: any) {
      throw new HttpException(
        "Error retrieving override existence. Please, check that the apiCampaignId or campaignName is correct.",
        HttpStatus.NOT_FOUND,
      );
    }
  }

  /**
   * Helper function to return the information to render the table to create new overrides
   * @param call
   * @returns
   */
  async getNewOverrideInfoToRender(call: any) {
    // Checking values from Talent campaign (if it's already created)
    const currentTalentCampaign = await this.getCurrentTalentCampaignWithBudget(
      call.campaigns[0].apiCampaignId,
    );
    return {
      ApiClientName: call.campaigns[0].apiClientName,
      ApiCampaignID: call.campaigns[0].apiCampaignId,
      Feedcode: call.campaigns[0].talentFeedcode,
      Company: call.campaigns[0].companyName,
      Campaign: call.campaigns[0].talentCampaign,
      Country: call.campaigns[0].campaignCountry,
      Delivery: call.campaigns[0].campaignDelivery ? call.campaigns[0].campaignDelivery : "Empty",
      SponsorshipEnded: call.campaigns[0].campaignSponsorshipEnded ? 1 : 0,
      StartDate: call.campaigns[0].campaignStartDate
        ? call.campaigns[0].campaignStartDate
        : "Empty",
      EndDate: call.campaigns[0].campaignEndDate ? call.campaigns[0].campaignEndDate : "Empty",
      BudgetType: call.campaigns[0].campaignBudgetType,
      Budget:
        call.campaigns[0].campaignBudgetValue.trim().toLowerCase() === "no limit"
          ? 2000000
          : call.campaigns[0].campaignBudgetValue,
      CPC: call.campaigns[0].campaignCpc,
      ConversionType: call.campaigns[0].campaignConversionType
        ? call.campaigns[0].campaignConversionType
        : "Empty",
      ConversionTargetCost: call.campaigns[0].campaignTargetCost,
      Jobs: parseInt(call.campaigns[0].xmlNumJobs, 10),
      FlexibleSpent: parseFloat(currentTalentCampaign?.campaignsBudget?.flexibleSpent) || null,
    };
  }

  /**
   * Function to insert a new ExternalCampaign override entry
   * @param createOverrideDto
   * @param userId
   * @returns
   */
  async actionInsertOverride(createOverrideDto: CreateOverrideDto, userId: number) {
    // Build the override to validateOverrideParams
    const overrideData = this.buildOverrideData(createOverrideDto);
    const override = {
      apiCampaignId: createOverrideDto.apiCampaignId,
      apiClientName: createOverrideDto.apiClientName,
      feedcode: createOverrideDto.feedcode,
      companyName: createOverrideDto.companyName,
      campaignName: createOverrideDto.campaignName,
      campaignSponsorshipEnded: createOverrideDto.campaignSponsorshipEnded,
      country: createOverrideDto.country,
      overrideData: overrideData,
    };

    // Performing validations for the params we've received until now ONLY if we've not received any error up to this point
    try {
      const validationProcess = await this.validateOverrideParams(override);
      // Check if the validation process is successful
      if (Object.keys(validationProcess).length === 0) {
        // Transform overrideData to string
        override.overrideData = JSON.stringify(overrideData);

        // Create the new dto to insert the override
        const insertOverrideDto: CreateOverrideDto = {
          ...override,
          created: new Date(),
          createdBy: userId,
          overrideStatus: true,
        };

        // Check if the override exists to define a create or update for the saveLog
        const logType = await this.checkLogType(override.apiCampaignId);

        // Insert the override and check the result
        const result = await this.insertOverride(insertOverrideDto, userId);

        if (result.result === "Success") {
          // Check if is a new override to avoid store duplications in the database
          if (!result.duplicated) {
            // We wait 500ms before reading the database record to avoid the latency caused by Master and Slaves
            await new Promise((resolve) => setTimeout(resolve, 800));
            // Update campaign in Talent, if exists, and save a log with the action
            await this.updateInTalentAndLog(override, logType, userId);
          }

          // Return override's result with only needed data for frontend
          delete result.duplicated;
          return result;
        } else {
          throw new HttpException(
            "Error: there was an error while trying to insert override, please try again.",
            HttpStatus.BAD_REQUEST,
          );
        }
      } else {
        // Validation failed; send BadRequest exception
        throw new HttpException(validationProcess, HttpStatus.BAD_REQUEST);
      }
    } catch (error: any) {
      console.error("Error:", error.response);
      throw error;
    }
  }

  /**
   * Helper method to build overrideData from createOverrideDto
   * @param createOverrideDto
   * @returns
   */
  private buildOverrideData(createOverrideDto: CreateOverrideDto): any {
    const overrideData: any = {};
    const mappings: Record<string, Record<string, any>> = {
      delivery: this.campaignDeliveries,
      budgetType: this.budgetTypes,
      conversionType: this.conversionTypes,
    };

    for (const [key, map] of Object.entries(mappings)) {
      if (createOverrideDto[key as keyof CreateOverrideDto] !== undefined) {
        overrideData[key] = map[createOverrideDto[key as keyof CreateOverrideDto] as string];
      }
    }

    const directFields: (keyof CreateOverrideDto)[] = [
      "budget",
      "conversionTargetCost",
      "dateStart",
      "dateEnd",
    ];

    for (const field of directFields) {
      if (createOverrideDto[field] !== undefined) {
        overrideData[field] = createOverrideDto[field];
      }
    }

    return overrideData;
  }

  /**
   * Check if an override exists to decide if logType is "override-updated" or "override-created" for saveLog
   * @param apiCampaignId
   * @returns
   */
  async checkLogType(apiCampaignId: string) {
    const overrideExists = await this.helperService.checkOverrideExistence(apiCampaignId, "");
    return overrideExists.exists ? "override-updated" : "override-created";
  }

  /**
   * Method to check if there's a campaign in Talent to update it with the override, and saveLog the created, updated or deleted override.
   * @param override
   * @param logType
   * @param userId
   */
  async updateInTalentAndLog(override: any, logType: string, userId: number) {
    // Check if the campaign exists in Talent to update the campaign with the new override information
    const updateOverrideInTalent = await this.updateOverrideInTalentCampaign(override, logType);

    // Save a log to register the new override (create/update/delete)
    const dataError = new Map<string, any>();
    dataError.set("entityId", override.apiCampaignId);
    dataError.set("feedcode", override.feedcode);
    dataError.set(
      "talentCampaignId",
      updateOverrideInTalent.result === "Updated"
        ? updateOverrideInTalent.talentCampaign?.id
        : null,
    );
    dataError.set("type", logType);
    dataError.set("overrideData", override.overrideData);
    dataError.set("processed", logType === "override-deleted" ? "0" : "1");

    await this.externalIngestion.saveLog(dataError, "", `${override.apiCampaignId}`, `${userId}`);
  }

  /**
   * Method to trigger the external campaign updateCampaignAutomatically when
   * the campaign exists in Talent and we create/edit/delete an override.
   * @param override
   * @param logType
   * @returns
   */
  // async updateOverrideInTalentCampaign(apiCampaignId: string, logType: string) {
  async updateOverrideInTalentCampaign(override: any, logType: string) {
    const { apiCampaignId, overrideData } = override;
    try {
      // Check if the campaign exists in Talent
      const talentCampaign = await this.helperService.findOneByApiCampaignId(apiCampaignId);
      const talentCampaignId = talentCampaign?.id;

      if (talentCampaignId) {
        // Get external campaign data
        const externalCampaign = await this.externalCampaignService.findOne(
          "apiCampaignId",
          apiCampaignId,
        );
        // Validate start date
        const campaignStartDate =
          !overrideData.dateStart || overrideData.dateStart === "" || logType === "override-deleted"
            ? externalCampaign?.date
            : externalCampaign?.campaignStartDate;
        // Create the campaignData json for the updateCampaignAutomatically
        const campaignData = {
          estimated_budget_days_remaining: externalCampaign?.estimatedBudgetDaysRemaining,
          campaign_cpc: externalCampaign?.campaignCpc,
          xml_num_jobs: externalCampaign?.xmlNumJobs,
          campaign_target_cost: externalCampaign?.campaignTargetCost,
          campaign_sponsorship_ended: externalCampaign?.campaignSponsorshipEnded,
          api_campaign_id: externalCampaign?.apiCampaignId,
          api_client_key: externalCampaign?.apiClientKey,
          api_client_name: externalCampaign?.apiClientName,
          account_id: externalCampaign?.accountId,
          account_name: externalCampaign?.accountName,
          account_currency_name: externalCampaign?.accountCurrencyName,
          company_id: externalCampaign?.companyId,
          company_name: externalCampaign?.companyName,
          company_currency_name: externalCampaign?.companyCurrencyName,
          company_country: externalCampaign?.companyCountry,
          company_monthly_budget: externalCampaign?.companyMonthlyBudget,
          xml_feed_link: externalCampaign?.xmlFeedLink,
          campaign_id: externalCampaign?.campaignId,
          campaign_name: externalCampaign?.campaignName,
          campaign_status: externalCampaign?.campaignStatus,
          campaign_country: externalCampaign?.campaignCountry,
          talent_campaign: externalCampaign?.talentCampaign,
          talent_feedcode: externalCampaign?.talentFeedcode,
          action: externalCampaign?.action,
          source: externalCampaign?.source,
          campaign_delivery: externalCampaign?.campaignDelivery,
          campaign_conversion_type: externalCampaign?.campaignConversionType,
          campaign_budget_type: externalCampaign?.campaignBudgetType,
          campaign_budget_value: externalCampaign?.campaignBudgetValue,
          notified: externalCampaign?.notified,
          campaign_start_date: campaignStartDate,
          campaign_end_date: externalCampaign?.campaignEndDate,
        };

        // Trigger the updateCampaignAutomatically
        await this.externalCampaignService.updateCampaignAutomatically(
          talentCampaignId,
          campaignData,
        );
        return { result: "Updated", talentCampaign };
      } else {
        return { result: "Not updated", talentCampaign: undefined };
      }
    } catch (error) {
      // If there's no campaign created in Talent, we don't need to trigger the updateCampaignAutomatically.
      // The override will be applied when we create the campaign.
      return { result: "Not updated", talentCampaign: undefined };
    }
  }

  /**
   * Function to insert a new ExternalCampaign override entry with statusOverride=0
   * @param deleteOverrideDto
   * @param userId
   * @returns
   */
  async actionDeleteOverride(deleteOverrideDto: DeleteOverrideDto, userId: number) {
    // Get apiCampaignIds from DTO
    const { apiCampaignIds } = deleteOverrideDto;

    // Try to delete the overrides
    try {
      const deleteResult = await this.deleteOverridesChangeStatus(apiCampaignIds, userId);
      return deleteResult;
    } catch (error) {
      console.error("Failed to delete the override:", error);
    }
  }

  /**
   * In campaign overrides we give the user an option to check several overrides to delete at once and this functionality
   * is done via this function, nothing else. It is an insert because we must store the deleted overrides in case of
   * needing them in the future, therefore, for knowing an override is deleted, we use a property called overrideStatus.
   * @param apiCampaignIds Array containing all the apiCampaignIds of the overrides to delete
   * @return array
   */
  async deleteOverridesChangeStatus(apiCampaignIds: string[], userId: number): Promise<any> {
    try {
      if (!apiCampaignIds || apiCampaignIds.length === 0) {
        throw new HttpException(
          "Please send a valid list of api campaign IDs to delete",
          HttpStatus.BAD_REQUEST,
        );
      }

      const updateResults = await Promise.allSettled(
        apiCampaignIds.map(async (apiCampaignId) => {
          try {
            // Fetch the latest override record
            const overrideData = await this.getOverrideInfo(apiCampaignId);

            if (!overrideData) {
              throw new Error("Override not found.");
            }

            // If the last overrideStatus is false, this is a duplicated override
            const duplicated = !overrideData.overrideStatus;

            // Save the updated override data
            await this.updateOverride(overrideData.overrideId, {
              overrideStatus: false,
              created: new Date(),
              createdBy: userId,
            });

            if (!duplicated) {
              // Build the override to update the campaign in Talent and save a log with the action
              const override = {
                apiCampaignId: apiCampaignId,
                feedcode: overrideData.feedcode,
                overrideData: {},
              };

              // Check if the campaign exists in Talent to delete the override information, and save the log
              await this.updateInTalentAndLog(override, "override-deleted", userId);
            }
            return { apiCampaignId, result: "Success" };
          } catch (error: any) {
            return { apiCampaignId, result: "Error", errorMessage: error.message };
          }
        }),
      );

      // Convert results into a structured response
      return updateResults.reduce(
        (acc, res) => {
          if (res.status === "fulfilled") {
            acc[res.value.apiCampaignId] = { result: res.value.result };
          } else {
            acc[res.reason.apiCampaignId] = {
              result: "Error",
              errorMessage: res.reason.errorMessage,
            };
          }
          return acc;
        },
        {} as Record<string, { result: string; errorMessage?: string }>,
      );
    } catch (error: any) {
      throw new HttpException(
        `Error deleting AutoCampaign Overrides: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
