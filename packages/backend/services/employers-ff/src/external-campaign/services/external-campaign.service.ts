/* eslint-disable @typescript-eslint/no-explicit-any */
import { HttpException, HttpStatus, Inject, Injectable, Logger } from "@nestjs/common";
import { ExternalCampaign } from "../entities/external-campaign.entity";
import { ExternalCampaignRepository } from "../repositories/external-campaign.repository";
import { CreateExternalCampaignDto } from "../dto/create-external-campaign.dto";
import { AccountService } from "../../accounts/services/accounts.service";
import { CampaignsService } from "../../campaigns/services/campaigns.service";
import {
  CampaignApplyType,
  CampaignStatus,
  CampaignType,
  CountryList,
  SortType,
} from "../../common/resources/enums";
import { CustomAxiosAdapter } from "../../common/adapters/axios.adapter";
import { GetHubspotDealIdDto } from "../../campaigns/dto/get-hubspot-deal-id.dto";
import { CampaignsTransactionalService } from "../../campaigns/services/campaigns-transactional.service";
import { BudgetType } from "../../campaigns/dto/create-budget.dto";
import { FeedcodeExternalCampaignDto } from "../dto/feedcode-external-campaign.dto";
import { SearchExternalCampaignDto } from "../dto/search-external-campaign.dto";
import { NotUpdatedRepository } from "../repositories/not-updated.repository";
import { TagsService } from "../../tags/services/tags.service";
import { ClientExternalCampaignDto } from "../dto/client-external-campaign.dto";
import { NotUpdatedService } from "./not-updated.service";
import { CreateNotUpdatedDto } from "../dto/create-not-updated.dto";
import { Override } from "../entities/override.entity";
import { CampaignProcessService } from "../../campaigns/services/campaign-process.service";
import { UsersService } from "../../users/services/user.service";
import * as privacy from "libs/privacy/src";
import {
  AccountWithOwners,
  DataBody,
  EmailsByType,
  EmailStructure,
  UpdateNotified,
} from "../../common/interfaces/externalCampaignsEmails.interface";
import { emailStyleMock } from "../../common/mocks/external-campaign.mock";
import { CreateTalentCampaignDto } from "../dto/create-talent-campaign.dto";
import { Campaigns } from "../../campaigns/entities/campaign.entity";
import { AccountBudgetDto } from "../../accounts/dto/account-budget.dto";
import { ExternalCampaignHelperService } from "./external-campaign-helper.service";
import { UpdateCampaignApplyDto } from "../../campaigns/dto/update-campaign-apply.dto";
import { CACHE_MANAGER } from "@nestjs/cache-manager";
import { Cache } from "cache-manager";

/**
 * Main Automatic Campaign's functionality and business logic manager
 */
@Injectable()
export class ExternalCampaignService {
  private readonly minimumBudgetValues: Map<string, number> = new Map([
    ["monthly", 50],
    ["weekly", 70],
    ["daily", 10],
  ]);

  private readonly allowedFieldList: string[] = [
    "date",

    "api_campaign_id",
    "api_client_key",
    "api_client_name",

    "account_id",
    "account_name",
    "account_currency_name",
    "account_budget",
    "account_country",

    "company_id",
    "company_name",
    "company_currency_name",
    "company_country",
    "company_monthly_budget",
    "xml_feed_link",

    "campaign_id",
    "campaign_name",
    "campaign_status",
    "campaign_country",
    "campaign_delivery",
    "campaign_sponsorship_ended",
    "campaign_conversion_type",
    "campaign_target_cost",
    "estimated_budget_days_remaining",
    "campaign_cpc",
    "campaign_budget_type",
    "campaign_budget_value",
    "campaign_start_date",
    "campaign_end_date",
    "campaign_apply_type",
    "campaign_application_delivery_type",
    "apply_email",

    "xml_num_jobs",
    "talent_campaign",
    "talent_feedcode",

    "action",
    "source",
    "notified",
  ];

  /**
   * Injecting the access to the database through externalCampaign entity
   */
  constructor(
    // Inject the ExternalCampaignRepository instance
    private readonly externalCampaignRepository: ExternalCampaignRepository,
    private readonly accountService: AccountService,
    private readonly campaignsService: CampaignsService,
    @Inject(CustomAxiosAdapter)
    private readonly axios: CustomAxiosAdapter,
    private readonly campaignsTransactionalService: CampaignsTransactionalService,
    private readonly notUpdatedRepository: NotUpdatedRepository,
    private readonly tagsService: TagsService,
    private readonly notUpdatedService: NotUpdatedService,
    private readonly campaignProcessService: CampaignProcessService,
    @Inject(UsersService)
    private readonly usersService: UsersService,
    private readonly helperService: ExternalCampaignHelperService,
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
  ) {}

  /**
   * Function to find a single ExternalCampaign record by field and value
   */
  async findOne(field: string, value: string) {
    const result = await this.externalCampaignRepository.findOne(field, value);
    return result;
  }

  /**
   * Function to create a new ExternalCampaign record
   */
  async create(createExternalCampaignDto: CreateExternalCampaignDto): Promise<ExternalCampaign> {
    return await this.externalCampaignRepository.create(createExternalCampaignDto);
  }

  /**
   * Provides an instance of typeORM's repository
   * @returns list of active automatic Campaigns in talent
   */
  createRepoInstance(): ExternalCampaign {
    return this.externalCampaignRepository.createRepoInstance();
  }

  /**
   * Function to remove an ExternalCampaign record by ID
   */
  async delete(id: number): Promise<void> {
    const result = await this.externalCampaignRepository.delete(id);

    return result;
  }

  /**
   * Function to generate link actions on the dashboard depending on the existence of the feedcode/account/campaign
   * in Talent.
   *
   * @param feedcode  feedcode to be checked or created in Talent. Ex: "appcast-bayard-advertising"
   * @param campaign  campaign name to be checked or created in Talent. Ex: "ULINE - 232417 - US- PA - SLS - NYC"
   * @param apiCampaignId  apiCampaignId for the campaign. Ex: "appcast_12492_254613"
   * @param accountCurrencyName  currency from the account. Ex: "USD"
   * @param  source  the source form the campaign to generate actions for is ingested
   * @param  accountBudget  the total budget the account is willing to spend through a billing month
   * @param  accountName  The name of the account that the campaign as such belongs to
   * @return array
   */
  async generateLinks(
    feedcode: string,
    campaign: string,
    apiCampaignId: string,
    accountCurrencyName: string,
    source: string = "",
    accountBudget?: string,
    accountName?: string,
  ): Promise<{
    action: string;
    talentCampaignID: string;
    isFullyAutoCampaign: boolean;
    params: string;
  }> {
    // Validate parameters
    if (feedcode.trim() === "") {
      throw new HttpException("Feedcode is required", HttpStatus.BAD_REQUEST);
    }

    // Check if the campaign is empty and throw an error if it is
    if (campaign.trim() === "") {
      throw new HttpException("Campaign is required", HttpStatus.BAD_REQUEST);
    }

    // Check if the API campaign ID is empty and throw an error if it is
    if (apiCampaignId.trim() === "") {
      throw new HttpException("Api campaign id is required", HttpStatus.BAD_REQUEST);
    }

    // Check if the account currency name is empty and the source is not "xml"
    // If so, throw an error
    if (accountCurrencyName.trim() === "" && source !== "xml") {
      throw new HttpException("Account currency name is required", HttpStatus.BAD_REQUEST);
    }

    const generateLinks: Map<string, string> = new Map();
    let actionName: string = "";

    // Validate if feedcode, account or campaign exists to show the real Talent data on the dashboard
    let campaignExists: Map<string, string>;

    // Validate account Existence
    const accountExists: Map<string, string> = await this.verifyAccountExists(feedcode);
    if (accountExists.get("message") == "AccountDoesntExist") {
      actionName = "Create Account";
    } else {
      // Validate campaign Existence
      campaignExists = await this.verifyCampaignExists(apiCampaignId, feedcode);
      actionName = campaignExists.get("message") == "CampaignDoesntExist" ? "Create Campaign" : "-";
      generateLinks.set("campaignID", campaignExists.get("campaignID") ?? "");
    }
    generateLinks.set("isFullyAutoCampaign", accountExists.get("isFullyAutoCampaign") ?? "");
    //}

    switch (actionName) {
      case "Create Campaign":
        generateLinks.set("action", "campaign");
        generateLinks.set("params", `?action=campaign&api_campaign_id=${apiCampaignId}`);
        break;
      case "Create Account":
        generateLinks.set("action", "account");
        generateLinks.set(
          "params",
          `?action=account&api_campaign_id=${apiCampaignId}&feedcode=${feedcode}&currency=${accountCurrencyName}&source=${source}&accountBudget=${accountBudget}&accountName=${accountName}`,
        );
        break;
      default:
        generateLinks.set("action", "-");
    }
    return {
      action: generateLinks.get("action") as string,
      talentCampaignID: generateLinks.get("campaignID") as string,
      isFullyAutoCampaign: generateLinks.get("isFullyAutoCampaign") == "1",
      params: generateLinks.get("params") as string,
    };
  }

  // PENDING: Complete these methods with the apporpiate services imports

  /**
   * Takes an existing talent feedcode in order to validate if an employer account associated with it already exists.
   *
   * @param feedcode - The feedcode string to be checked.
   * @returns A Map containing information about whether the account exists or not, and if it exists, its account ID and isFullyAutoCampaign setting.
   */
  async verifyAccountExists(feedcode: string): Promise<Map<string, string>> {
    // Check if the feedcode is empty or consists only of whitespace characters
    if (feedcode.trim() === "") {
      throw new HttpException("Feedcode is required", HttpStatus.BAD_REQUEST);
    }

    // Call the account service to find the account associated with the feedcode
    const accountExists = new Map<string, string>();
    try {
      const out = await this.accountService.findAccountByWithSettings("feedcode", feedcode);

      // Check if the account exists
      if (out) {
        // If the account exists, set the message, account ID, and isFullyAutoCampaign setting in the Map
        accountExists.set("message", "AccountExists");
        accountExists.set("accountID", `${out.id}`);
        let isFullY: any = 0;
        if (out.accountSettings) {
          isFullY = out.accountSettings.isFullyAutoCampaign;
        }
        accountExists.set("isFullyAutoCampaign", `${isFullY}`);
      } else {
        // If the account doesn't exist, set the message in the Map
        accountExists.set("message", "AccountDoesntExist");
      }
    } catch (error: any) {
      if (error.message === "'account' not found") {
        accountExists.set("message", "AccountDoesntExist");
      }
    }

    // Return result
    return accountExists;
  }

  /**
   * Verifies whether an external campaign with the provided `apiCampaignId` already exists in Talent for the `feedcode`.
   *
   * This method fetches campaign data using the `campaignsService` and checks if a matching campaign
   * is found based on the API campaign ID. It returns a `Map<string, string>` containing:
   *
   * - "message": Indicates whether the campaign exists ("CampaignExists" or "CampaignDoesntExist")
   * - "campaignID": The ID of the found campaign (if applicable, otherwise an empty string)
   *
   * @param {string} apiCampaignId The API campaign ID to be used for verification.
   * @param {string} feedcode The feedcode to be used for account's verification.
   * @returns {Promise<Map<string, string>>} A Promise resolving to a Map containing the verification results.
   * @throws {Error} If the provided `apiCampaignId` is empty or trimmed to an empty string.
   */
  async verifyCampaignExists(
    apiCampaignId: string,
    feedcode: string,
  ): Promise<Map<string, string>> {
    // Validate mandatory API campaign ID and feedcode
    if (apiCampaignId.trim() === "") {
      throw new HttpException("Api campaign id is required", HttpStatus.BAD_REQUEST);
    }
    if (feedcode.trim() === "") {
      throw new HttpException("Feedcode is required", HttpStatus.BAD_REQUEST);
    }

    // Initialize the response Map with appropriate default values
    const campaignExists = new Map([
      ["message", "CampaignDoesntExist"],
      ["campaignID", ""],
    ]);

    // Fetch campaign data using the injected campaignsService
    const campaign = await this.campaignsService.getCampaignInfoByApiCampaignIdNFeedCode(
      apiCampaignId,
      feedcode,
    );

    // Update the response Map if a campaign is found
    if (campaign) {
      campaignExists.set("message", "CampaignExists");
      campaignExists.set("campaignID", `${campaign.id}`);
    }

    return campaignExists;
  }

  /**
   * In Automatic Campaigns we got some business rules for only allowing a minimum budget depeding on the budget type, this method returns
   * the constants defined for those classes
   * @returns
   */
  getMinimumBudgetValues(): Map<string, number> {
    return this.minimumBudgetValues;
  }

  /**
   * Validates an active client ID.
   *
   * @param clientId The client ID to validate.
   * @returns A Promise that resolves with the modified client name.
   */
  validateActiveCliendID(clientId: string) {
    // Check if clientId is empty or doesn't match the expected format
    if (!clientId.trim() || !/^auto-[a-zA-Z0-9-]+$/.test(clientId)) {
      throw new HttpException("Error: Invalid client ID", HttpStatus.BAD_REQUEST);
    }

    // Split the clientId by "-" to get the apiClientName
    const apiClientName = clientId.split("-").slice(1).join("-");

    // PENDING: validate the tag with the tags table

    // Return the modified apiClientName
    return apiClientName;
  }

  /**
   * Updates an account's budget based on provided data, considering various conditions.
   *
   * This function validates and potentially updates the budget for an account identified by the
   * `feedcode`. It performs the following checks and actions:
   *
   * 1. **Budget Validation:** Returns `true` if no budget is provided.
   * 2. **Account Lookup:** Retrieves account information using the `accountService` based on the
   *    provided `feedcode`. Returns `true` if no account is found.
   * 3. **Budget Comparison:** Returns `true` if the provided budget is the same as the existing one.
   * 4. **"No Limit" Handling:** If the budget is a string "no limit" (case-insensitive), converts it
   *    to a numerical value (e.g., 2,000,000).
   * 5. **Budget vs. Spend Validation:** Returns `false` if the provided budget is less than the
   *    account's current spend.
   * 6. **Update (Incomplete):** The current implementation doesn't include the actual update logic.
   *    You'll need to replace the commented line (`//await this.`) with the code to update the account
   *    budget using the `accountService`.
   *
   * @param {string} feedcode The unique identifier for the account.
   * @param {number|string} budget The new budget value to be applied. Can be a number or the string "no limit".
   * @returns {Promise<boolean>} A Promise resolving to `true` if the budget was updated or no update
   *                             was necessary, `false` if the budget is invalid or less than the spend.
   */
  async updateAccountBudgetFromApi(feedcode: string, budget: any): Promise<boolean> {
    // 1. Validate if we have a budget
    if (!budget) {
      return true;
    }

    // 2. Validate account information
    const account = await this.accountService.findBy("feedcode", feedcode, true);
    if (!account?.[0]?.id) {
      return true;
    }

    // 3. The budget is the same as the existing one, no update needed
    if (account[0].accountsBudget.budget === budget) {
      return true;
    }

    // 4. Set values and handle "no limit" budget
    const currentAccountSpend = account[0].accountsBudget.spentBudget ?? "";
    const accountId = account[0]?.id;
    if (typeof budget === "string" && budget.toLowerCase() === "no limit") {
      budget = 2000000;
    }
    if (
      typeof budget === "string" &&
      budget.toLowerCase() !== "no limit" &&
      budget.toLowerCase() !== ""
    ) {
      budget = parseFloat(budget);
    }

    // 5. Validate if the budget is less than the spend
    if (budget < currentAccountSpend) {
      return false;
    }

    // 6. Update the account budget (implementation required)
    await this.updateAccountBudget(accountId, {
      id: accountId,
      budget: budget,
      spent_budget: null,
    });

    return true;
  }

  /**
   *
   */
  async updateAccountBudget(accountId: number, updateBudgetDto: AccountBudgetDto): Promise<any> {
    try {
      const budgetInfo = await this.axios.get(
        `${process.env.URL_BUDGETING}/api/v1/account_budgets/${accountId}`,
      );

      if (!budgetInfo) {
        throw new HttpException(
          `Account id not found in budgeting service ${accountId}`,
          HttpStatus.NOT_FOUND,
        );
      }
      // Check the response from account-budget
      const updatedBudget: any = await this.axios.patch(
        `${process.env.URL_BUDGETING}/api/v1/account_budgets/${budgetInfo.account_id}`,
        updateBudgetDto,
      );

      return updatedBudget;
    } catch (error: any) {
      if (error?.response?.data?.error == "No account budget rows affected") {
        return "No account budget changes identified";
      }
      throw error;
    }
  }

  /**
   * Perform some validations and prepare data to create/update a talent campaign.
   * @param isCreate boolean If the campaign is being created or updated.
   * @param account any The account information.
   * @param newEntry any The new entry, object with the information we want to create/update.
   * @param insert any The final object that will be used to create/update the campaign. When creating a campaign
   * this object will be empty. When updating a campaign this object will have the current campaign information (last entry).
   * @return Promise<any>
   */
  async campaignValidations(
    isCreate: boolean,
    account: any,
    newEntry: any,
    insert: any = {},
    campaignData?: any,
  ): Promise<any> {
    await this.validateTag(account);
    await this.validateCampaignName(newEntry, insert, isCreate);
    await this.validateCountry(newEntry, account, insert, isCreate);
    await this.validateBudget(newEntry, insert, isCreate);
    await this.validateEstimatedBudgetDays(newEntry, insert);
    await this.newDeliveryLogic(newEntry, insert, isCreate, campaignData);
    await this.validateHubspotDealId(newEntry, account, insert);
    return insert;
  }

  /**
   * Account tag validation
   * @param account
   */
  async validateTag(account: any): Promise<void> {
    if (!account || Object.entries(account).length < 1) {
      throw new HttpException(
        `The account should have an autoCampaignApiTagName`,
        HttpStatus.BAD_REQUEST,
      );
    }
    try {
      return this.getTagNameByid(account);
    } catch (error: any) {
      Logger.error(new Error(error.message));
    }
  }

  /**
   * Campaign name validation
   * @param newEntry
   * @param insert
   * @param isCreate
   */
  async validateCampaignName(newEntry: any, insert: any, isCreate: boolean): Promise<void> {
    if (newEntry.talentCampaign) {
      insert.campaignName = newEntry.talentCampaign.trim();
    } else if (isCreate) {
      throw new HttpException("Error: 'campaign_name' null, empty or 0", HttpStatus.BAD_REQUEST);
    }
  }

  /**
   * Country validation. Considers if we have multiple countries or default value scenario.
   * In case of multi-countries, campaign's main country will be account's country, and the
   * country filter will be 'All Countries'.
   * We are going to bring accepted countries in Talent, or use the Geocode.
   * @param newEntry
   * @param account
   * @param insert
   * @param isCreate
   */
  async validateCountry(
    newEntry: any,
    account: any,
    insert: any,
    isCreate: boolean,
  ): Promise<void> {
    const campaignCountry = newEntry.campaignCountry ?? "";
    const insertCountries = (campaignCountry || "").split(",").map((c: string) => c.trim());
    const isMultiCountry = this.checkIfMultiCountry(campaignCountry, insertCountries, isCreate);

    const { country, countryFilter } = isMultiCountry
      ? { country: account.country, countryFilter: "All Countries" }
      : await this.getCountryFilterToValidate(campaignCountry);

    if (!countryFilter && !country && isCreate)
      throw new HttpException(
        "Error: An error occurred while assigning campaign's country, please review",
        HttpStatus.INTERNAL_SERVER_ERROR,
      );

    this.updateInsertFilters(
      insert,
      country,
      countryFilter,
      newEntry.apiCampaignId ?? insert.autoCampaignApi,
    );

    if (insertCountries[0].toLowerCase() !== "all countries" && insertCountries[0] !== "") {
      insert.country = await this.getCountryFilter(insertCountries[0]);
    } else {
      insert.country = account.country;
    }
  }

  /**
   * Validate if campaignCountry is "All countries"
   * @param campaignCountry
   * @param insertCountries
   * @param isCreate
   * @returns
   */
  private checkIfMultiCountry(
    campaignCountry: string,
    insertCountries: string[],
    isCreate: boolean,
  ): boolean {
    return (
      (!campaignCountry && isCreate) ||
      insertCountries.length > 1 ||
      campaignCountry.toLowerCase() === "all countries"
    );
  }

  /**
   * Get countryFilter to validateCountry
   * @param campaignCountry
   * @returns
   */
  private async getCountryFilterToValidate(
    campaignCountry: string,
  ): Promise<{ country: string; countryFilter: string }> {
    let countryFilter = await this.getCountryFilter(campaignCountry);
    if (!countryFilter) {
      const geo = await this.getGeocode();
      countryFilter = geo?.data?.status === "OK" ? geo.data.countrycode.toLowerCase() : "";
    }
    return { country: countryFilter, countryFilter };
  }

  /**
   * Get countries from Talent's accepted countries list
   * @param campaignCountry
   * @returns
   */
  async getCountryFilter(campaignCountry: string): Promise<string> {
    // Check if campaignCountry is a valid key in the CountryList enum
    if (Object.keys(CountryList).includes(campaignCountry)) {
      // Return the value directly if it's a valid key
      return campaignCountry;
    }

    // Otherwise, attempt to find the key based on the country name
    return (
      (Object.keys(CountryList) as Array<keyof typeof CountryList>).find(
        (key) => CountryList[key] === campaignCountry,
      ) ?? ""
    );
  }

  /**
   * Execute the post to the geocode method
   */
  async getGeocode() {
    try {
      // At this point we are sending mocked data
      const locationData = {
        location: "Ontario",
        country: "Canada",
        debug: true,
      };
      const geoResult = await this.axios.post(
        `${process.env.GEOCODE}/geo-from-string`,
        locationData,
      );
      return geoResult;
    } catch (error) {
      console.log("error", error);
      // If there's an error we don't want to stop the process, and we send an empty value.
      return "";
    }
  }

  /**
   * Update insert.filters with tag, country and countryFilter
   * @param insert
   * @param country
   * @param countryFilter
   * @param apiCampaignIdTag
   */
  private updateInsertFilters(
    insert: any,
    country: string,
    countryFilter: string,
    apiCampaignIdTag: string,
  ): void {
    if (countryFilter && country) {
      const tmpFilter = insert.filters ?? { tag: [apiCampaignIdTag] };
      tmpFilter.country = country;
      tmpFilter.country_filter = countryFilter;
      insert.filters = JSON.stringify(tmpFilter);
    }
  }

  /**
   * Budget validation (only will update when both campaignBudgetType and campaignBudgetValue are sent).
   * Types are daily, weekly, monthly.
   * For weekly type we will calculate the daily budget based on the weekly budget.
   * Default budget will be type monthly and value 2000000.
   * @param newEntry
   * @param insert
   * @param isCreate
   */
  async validateBudget(newEntry: any, insert: any, isCreate: boolean): Promise<void> {
    const budgetValue = parseInt(newEntry.campaignBudgetValue, 10);

    if (
      newEntry.campaignBudgetType &&
      (budgetValue || newEntry.campaignBudgetValue === "no limit")
    ) {
      const budgetType = newEntry.campaignBudgetType.trim();
      insert.budgetType = budgetType;

      if (budgetType === "daily") {
        insert.budgetDay = budgetValue;
        insert.budgetMonth = 0;
      } else if (budgetType === "monthly") {
        insert.budgetDay = 0;
        insert.budgetMonth = newEntry.campaignBudgetValue === "no limit" ? 2000000 : budgetValue;
      } else if (budgetType === "weekly" && budgetValue >= 1 && budgetValue <= 2000000) {
        insert.budgetType = "daily";
        insert.budgetDay = parseInt((Number(budgetValue) / 7).toString(), 10);

        insert.budgetMonth = 0;
      }
    }

    if (!insert.budgetType && isCreate) {
      insert.budgetType = "monthly";
      insert.budgetMonth = budgetValue || 2000000;
    }
  }

  /**
   * Campaign estimated budget days remaining
   * @param newEntry
   * @param insert
   */
  async validateEstimatedBudgetDays(newEntry: any, insert: any): Promise<void> {
    if (
      newEntry.estimatedBudgetDaysRemaining !== "" &&
      newEntry.estimatedBudgetDaysRemaining >= 0
    ) {
      insert.estimatedBudgetDaysRemaining = parseInt(newEntry.estimatedBudgetDaysRemaining, 10);
    }
  }

  /**
   * Applies delivery logic based on the campaign's start and end dates, and budget values.
   *
   * @param newEntry - The new entry data containing campaign details.
   * @param insert - The object where pacing information will be inserted.
   * @returns void
   */
  async newDeliveryLogic(newEntry: any, insert: any, isCreate: boolean, campaignData?: any) {
    // Check if campaign dates are valid; if not, set pacing to 2
    let campaignStartDate = newEntry.campaignStartDate;
    let campaignEndDate = newEntry.campaignEndDate;
    if (!isCreate) {
      if (!campaignStartDate || campaignStartDate == "") {
        campaignStartDate = campaignData.dateStart;
      }
      if (!campaignEndDate || campaignEndDate == "") {
        campaignEndDate = campaignData.dateEnd;
      }
    }
    if (
      campaignStartDate &&
      campaignEndDate &&
      !this.validateDeliveryDates(campaignStartDate, campaignEndDate)
    ) {
      insert.hasPacing = 2;
    } else if (newEntry.campaignBudgetValue < 300) {
      insert.hasPacing = 0;
    } else {
      // Validate delivery with client-specific rules
      this.deliveryClientValidation(newEntry, insert);
    }
  }

  /**
   * Validates if the start and end dates fall within the same month and year.
   *
   * @param dateStart - The start date of the campaign.
   * @param dateEnd - The end date of the campaign.
   * @returns A boolean indicating if both dates are within the same month and year.
   */
  validateDeliveryDates(dateStart: string, dateEnd: string): boolean {
    // Check if the start and end dates are in the same month and year
    return (
      new Date(dateStart).toISOString().slice(0, 7) === new Date(dateEnd).toISOString().slice(0, 7)
    );
  }

  /**
   * Sets pacing based on the campaign's budget value to protect limited budgets.
   *
   * @param newEntry - The new entry data containing campaign budget information.
   * @param insert - The object where pacing information will be inserted.
   * @returns void
   */
  protectionLimitedBudget(newEntry: any, insert: any): void {
    // If the budget value is between 0 and 2,000,000, set pacing to 1
    if (newEntry.campaignBudgetValue > 0 && newEntry.campaignBudgetValue < 2000000) {
      insert.hasPacing = 1;
      // If the budget value is set to "no limit", set pacing to 0
    } else if (newEntry.campaignBudgetValue === "no limit") {
      insert.hasPacing = 0;
    }
  }

  /**
   * Sets pacing based on the integration type, delegating to budget protection if needed.
   *
   * @param newEntry - The new entry data containing integration and budget information.
   * @param insert - The object where pacing information will be inserted.
   * @returns void
   */
  integrationType(newEntry: any, insert: any): void {
    // If the source is "client-api", set pacing to 0
    if (newEntry.source === "talent-api") {
      insert.hasPacing = 0;
    } else {
      // Otherwise, apply limited budget protection
      this.protectionLimitedBudget(newEntry, insert);
    }
  }

  /**
   * Sets pacing based on the campaign's delivery type, or delegates to integration type handling.
   *
   * @param newEntry - The new entry data containing delivery and integration information.
   * @param insert - The object where pacing information will be inserted.
   * @returns void
   */
  deliveryClientValidation(newEntry: any, insert: any): void {
    // If campaign delivery type is specified, set pacing to that value
    if (newEntry.campaignDelivery) {
      // Map campaignDelivery values to corresponding numeric values
      const deliveryMapping: { [key: string]: number } = {
        accelerated: 0,
        "not paced": 0,
        paced: 1,
        "paced (flexible)": 2,
      };

      // Convert campaignDelivery to numeric value if it matches the mapping
      if (newEntry.campaignDelivery in deliveryMapping) {
        newEntry.campaignDelivery = deliveryMapping[newEntry.campaignDelivery];
      } else {
        newEntry.campaignDelivery = deliveryMapping.paced;
      }

      // Set pacing to the (possibly transformed) campaignDelivery value
      insert.hasPacing = newEntry.campaignDelivery;
    } else {
      // Otherwise, apply integration type handling
      this.integrationType(newEntry, insert);
    }
  }

  /**
   * HubSpot Deal ID from talent_employers.deals database --> we need to test with real data
   * @param newEntry
   * @param account
   * @param insert
   */
  async validateHubspotDealId(newEntry: any, account: any, insert: any): Promise<void> {
    const tagName = this.getTagNameByid(account);
    if (newEntry.accountName && tagName && newEntry.talentFeedcode && newEntry.companyName) {
      const hubspotDealIdDto: GetHubspotDealIdDto = {
        accountId: account.id,
        companyName: newEntry.companyName,
      };
      try {
        insert.dealId = await this.campaignsService.getHubspotDealIdByCampaign(
          hubspotDealIdDto,
          account.hubspotDealId,
        );
        if (insert.dealId == undefined || insert.dealId == null) {
          insert.dealId = "";
        }
      } catch (error) {
        console.log("error", error);
        insert.dealId = "";
      }
    }
  }

  /**
   * Asynchronously creates an external campaign entry.
   *
   * @param newEntry - The new entry to be created.
   * @returns A promise resolving to the created entry.
   */
  async createAutoCampaignEntry(newEntry: Partial<CreateExternalCampaignDto>): Promise<any> {
    // Validate the new Entry
    if (typeof newEntry !== "object" || Array.isArray(newEntry)) {
      throw new HttpException(
        "Parameter must be an object with the data to create an entry in the database",
        HttpStatus.BAD_REQUEST,
      );
    }

    // Convert data to insert format
    const newInsert = this.convertDataToInsert(newEntry);

    // Attempt to create the campaign entry
    const result = await this.create(newInsert);

    // Validate result here
    if (!result || Object.keys(result).length < 1) {
      throw new HttpException(
        "Error creating the record on the bucket",
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }

    return result;
  }

  /**
   * Converts raw data to a CreateExternalCampaignDto object.
   *
   * @param data - Raw data to be converted.
   * @returns A CreateExternalCampaignDto object populated with converted data.
   */
  convertDataToInsert(data: any): CreateExternalCampaignDto {
    // Destructure data object
    const {
      account_budget,
      account_country,
      account_currency_name,
      account_id,
      account_name,
      action,
      api_campaign_id,
      api_client_key,
      api_client_name,
      campaign_application_delivery_type,
      campaign_apply_type,
      campaign_budget_type,
      campaign_budget_value,
      campaign_country,
      campaign_cpc,
      campaign_daily_budget,
      campaign_delivery,
      campaign_end_date,
      campaign_id,
      campaign_monthly_budget,
      campaign_name,
      campaign_sponsorship_ended,
      campaign_start_date,
      campaign_status,
      company_country,
      company_currency_name,
      company_id,
      company_monthly_budget,
      company_name,
      estimated_budget_days_remaining,
      notified,
      source,
      talent_campaign,
      talent_feedcode,
      xml_feed_link,
      xml_num_jobs,
      campaign_weekly_budget,
      campaign_conversion_type,
      campaign_target_cost,
      apply_email,
      publisher_id,
    } = data;

    /**
     * Helper function to convert a value to a string or use a default value.
     *
     * @param value - The value to be converted.
     * @param defaultValue - The default value to be used if the original value is null or undefined.
     * @returns A string representation of the value or the default value.
     */
    const getStringOrDefault = (value: any, defaultValue: string) =>
      value ? String(value) : defaultValue;
    // Return an object with converted data

    return {
      accountBudget: getStringOrDefault(account_budget, ""),
      accountCountry: getStringOrDefault(account_country, ""),
      accountCurrencyName: getStringOrDefault(account_currency_name, ""),
      accountId: getStringOrDefault(account_id, ""),
      accountName: getStringOrDefault(account_name, ""),
      action: getStringOrDefault(action, ""),
      apiCampaignId: getStringOrDefault(api_campaign_id, ""),
      apiClientKey: getStringOrDefault(api_client_key, ""),
      apiClientName: getStringOrDefault(api_client_name, ""),
      campaignApplicationDeliveryType: getStringOrDefault(campaign_application_delivery_type, ""),
      campaignApplyType: getStringOrDefault(campaign_apply_type, ""),
      campaignBudgetType: getStringOrDefault(campaign_budget_type, ""),
      campaignBudgetValue: getStringOrDefault(campaign_budget_value, ""),
      campaignCountry: getStringOrDefault(campaign_country, ""),
      campaignCpc: getStringOrDefault(campaign_cpc, ""),
      campaignDailyBudget: getStringOrDefault(campaign_daily_budget, ""),
      campaignDelivery: getStringOrDefault(campaign_delivery, ""),
      campaignEndDate: getStringOrDefault(campaign_end_date, ""),
      campaignId: getStringOrDefault(campaign_id, ""),
      campaignMonthlyBudget: getStringOrDefault(campaign_monthly_budget, ""),
      campaignName: getStringOrDefault(campaign_name, ""),
      campaignSponsorshipEnded: getStringOrDefault(
        campaign_sponsorship_ended,
        campaign_sponsorship_ended !== undefined ? String(campaign_sponsorship_ended) : "",
      ),
      campaignStartDate: getStringOrDefault(campaign_start_date, ""),
      campaignStatus: getStringOrDefault(campaign_status, ""),
      companyCountry: getStringOrDefault(company_country, ""),
      companyCurrencyName: getStringOrDefault(company_currency_name, ""),
      companyId: getStringOrDefault(company_id, ""),
      companyMonthlyBudget: getStringOrDefault(company_monthly_budget, ""),
      companyName: getStringOrDefault(company_name, ""),
      date: new Date(),
      estimatedBudgetDaysRemaining: estimated_budget_days_remaining,
      notified: getStringOrDefault(notified, ""),
      source: getStringOrDefault(source, ""),
      talentCampaign: getStringOrDefault(talent_campaign, ""),
      talentFeedcode: getStringOrDefault(talent_feedcode, ""),
      xmlFeedLink: getStringOrDefault(xml_feed_link, ""),
      xmlNumJobs: xml_num_jobs,
      campaignWeeklyBudget: getStringOrDefault(campaign_weekly_budget, ""),
      campaignConversionType: getStringOrDefault(campaign_conversion_type, ""),
      campaignTargetCost: getStringOrDefault(campaign_target_cost, ""),
      accountOwner: getStringOrDefault(apply_email, ""), // change this to the correct value
      applyEmail: getStringOrDefault(apply_email, ""),
      publisherId: getStringOrDefault(publisher_id, ""),
    };
  }

  /**
   * Automatically creates a campaign with the provided parameters.
   * @param talentFeedcodeName - The name of the talent feed code.
   * @param newEntry - The new campaign entry object containing various campaign properties.
   * @param job
   */
  async createCampaignAutomatically(talentFeedcodeName: string, newEntry: any, job?: any) {
    // parameters validation/
    if (!talentFeedcodeName) {
      throw new HttpException("talentFeedcodeName is required", HttpStatus.BAD_REQUEST);
    }

    // Convert data to insert format
    const data = this.convertKeysToSnakeCase(newEntry);
    let newInsert = this.convertDataToInsert(data);

    // Here will be the override code
    newInsert = await this.applyOverrideToAutoCampaign(newInsert);

    // Instantiate a new CreateExternalCampaignDto and assign values
    const campaignEntry = new CreateExternalCampaignDto();
    Object.assign(campaignEntry, newInsert);

    if (!campaignEntry.apiCampaignId) {
      throw new HttpException("'campaign_id' null, empty or 0", HttpStatus.BAD_REQUEST);
    }

    //set the filters for the country
    let insert: any = {};
    insert["autoCampaignApi"] = campaignEntry.apiCampaignId;

    //bring the account or cached result
    const account = await this.findAccountBy("feedcode", talentFeedcodeName);

    // Set the Date
    this.validateAndSetDates(campaignEntry, insert);

    // Common campaign validations
    const validations = await this.campaignValidations(true, account[0], campaignEntry, insert);
    // unify the result of the validations
    insert = { ...insert, ...validations };

    // decide status based in cpc, campaign_status and campaign_sponsorship_ended
    this.validateAndSetStatus(campaignEntry, insert);

    // Validate talentApply settings for the campaign
    const applySet = this.validateTalentApplyConfig(
      campaignEntry.campaignApplicationDeliveryType,
      campaignEntry.applyEmail,
      campaignEntry.campaignApplyType,
    );

    // unify the result of the apply
    insert = { ...insert, ...applySet };

    // Add the userId to insert
    if (newEntry.userId) {
      insert["userId"] = newEntry.userId;
    } else {
      insert["userId"] = 80010;
    }

    // Validate the conversion cost and type (old cpa goal validation)
    // If we don't have targetCost, conversionTargetCost and conversionType will be null
    // If we have targetCost, we take conversionType by the client, or "application"
    if (
      !campaignEntry.campaignTargetCost ||
      campaignEntry.campaignTargetCost === "" ||
      campaignEntry.campaignTargetCost == "0" ||
      campaignEntry.campaignTargetCost === "N/A"
    ) {
      insert["conversionType"] = null;
      insert["conversionTargetCost"] = null;
    } else {
      insert["conversionType"] =
        campaignEntry.campaignConversionType !== ""
          ? campaignEntry.campaignConversionType
          : "application";
      insert["conversionTargetCost"] = campaignEntry.campaignTargetCost;
    }

    // Create the final JSON to be sent to the campaign transaction
    const transactionalParams = this.createTransactionalParams(account, insert, campaignEntry);

    try {
      await this.campaignsTransactionalService.create(
        account[0]?.id as number,
        insert["userId"] as number,
        transactionalParams.campaignParams,
        transactionalParams.filterTextParams,
        transactionalParams.budgetParams,
        transactionalParams.applyParams.applyType == CampaignApplyType.jobredirect
          ? undefined
          : transactionalParams.applyParams,
        undefined,
        undefined,
        undefined,
        campaignEntry.source,
      );

      if (campaignEntry.source == "xml" && job && Object.keys(job).length > 0) {
        // Send jobs to procesing again
        delete job.source_vendor_campaign;
        //await this.campaignProcessService.sendMessagesToJobsProcessQueue(job);
      }
    } catch (error: any) {
      Logger.error(new Error(`error creating the campaign: ${error.message}`));
      throw error;
    }
  }

  /**
   * Create the final JSON to be sent to the campaign transaction to create the campaign in Talent.
   * @param account
   * @param insert
   * @param campaignEntry
   * @returns
   */
  createTransactionalParams(account: any, insert: any, campaignEntry: any) {
    const dateStart = insert.dateStart != "" ? insert.dateStart : "";
    const dateEnd = insert.dateEnd != "" ? insert.dateEnd : null;
    const campaignParams: any = {
      accountId: Number(account[0].id),
      campaignName: insert.campaignName,
      autoCampaignApi: insert.autoCampaignApi,
      dealId: Number(insert.dealId),
      status: insert.status,
      applyType:
        insert.applyType || insert.applyType != ""
          ? insert.applyType
          : CampaignApplyType.jobredirect,
      conversionType: insert.conversionType,
      conversionTargetCost: insert.conversionTargetCost,
      campaignObjective: null,
      dateStart: this.formatDateToYYYYMMDD(dateStart) as any as Date,
      lastDateStart: null,
      dateEnd: this.formatDateToYYYYMMDD(dateEnd) as any as Date,
      sponsored: 1,
      lastClick: 0,
      type: CampaignType.by_tags,
      priority: 20,
      active: insert.active,
      paused: insert.paused,
      validated: insert.validated,
      removed: insert.remoded,
      paymentRequired: 0,
      created: new Date(),
      updated: new Date(),
      createdBy: insert.userId || 80010,
      updatedBy: insert.userId || 80010,
      cpcModifier: 1,
      sourcePpc: 0,
      country: insert.country,
      csLastChecked: null,
    };
    if (insert.ppc) {
      campaignParams.ppc = Number(insert.ppc);
    }
    const applyParams = {
      applyType:
        insert.applyType || insert.applyType != ""
          ? insert.applyType
          : CampaignApplyType.jobredirect,
      campaignId: campaignEntry.campaignId, // Used only for transaction access, not part of Talent Campaign.
      activeDelivery: insert.activeDelivery,
      deliveryEmail: insert.deliveryEmail,
      createdBy: insert.userId || 80010,
      ...(insert.deliveryDataSource && { deliveryDataSource: insert.deliveryDataSource }),
    };
    const filterTextParams = insert.filters;
    const budgetParams = {
      has_pacing: insert.hasPacing,
      budget_type: insert.budgetType == "monthly" ? BudgetType.MONTHLY : BudgetType.DAILY,
      budget: insert.budgetType == "monthly" ? insert.budgetMonth : insert.budgetDay,
    };

    return {
      campaignParams,
      applyParams,
      filterTextParams,
      budgetParams,
    };
  }

  /**
   * Validates the talent apply configuration and processes the delivery type and email.
   *
   * @param campaignApplicationDeliveryType - The delivery type of the campaign application.
   * @param applyEmail - The email address to which applications will be sent.
   * @param campaignApplyType - The type of application for the campaign (e.g., 'talentapply', 'jobredirect').
   * @returns An object containing the processed apply type, active delivery type, delivery email, and delivery data source.
   */
  validateTalentApplyConfig(
    campaignApplicationDeliveryType: string,
    applyEmail: string,
    campaignApplyType: string,
  ) {
    // Initialize an object to store the validation results and processed data.
    const out = {
      applyType: "",
      activeDelivery: "",
      deliveryEmail: "",
      deliveryDataSource: "",
    };

    // Check if the apply type is 'talentapply'
    if (campaignApplyType && campaignApplyType.toLowerCase() == "talentapply") {
      // Validate the delivery type
      if (campaignApplicationDeliveryType.trim() == "") {
        throw new HttpException("The Delivery type is required", HttpStatus.BAD_REQUEST);
      }

      // We return the applyType in camelCase due to the method that creates campaign demands it
      out.applyType = "talentApply";
      out.activeDelivery = campaignApplicationDeliveryType?.toLowerCase();

      const deliveryTypes = ["api", "email", "feed email", "none"];
      // Ensure the delivery type is valid
      if (!deliveryTypes.includes(campaignApplicationDeliveryType)) {
        throw new HttpException(
          "invalid 'campaign delivery type' to create campaign",
          HttpStatus.BAD_REQUEST,
        );
      }

      // Process email delivery type
      const emailDelivery = this.processEmailDelivery(campaignApplicationDeliveryType, applyEmail);
      out.activeDelivery = emailDelivery.activeDelivery;
      out.deliveryDataSource = emailDelivery.deliveryDataSource;
      out.deliveryEmail = emailDelivery.newApplyEmail;
    } else if (campaignApplyType && campaignApplyType.toLowerCase() == "jobredirect") {
      out.applyType = "jobredirect";
    }

    // Return the validation result and processed data
    return out;
  }

  /**
   * Processes the email delivery type for a campaign and validates the apply email.
   *
   * @param campaignApplicationDeliveryType - The delivery type of the campaign application.
   * @param applyEmail - The email address to which applications will be sent.
   * @returns An object containing the processed apply email, delivery data source, and active delivery type.
   */
  processEmailDelivery(campaignApplicationDeliveryType: string, applyEmail: string) {
    let newApplyEmail: string = applyEmail;
    let deliveryDataSource: string = "";
    let activeDelivery: string = campaignApplicationDeliveryType;

    if (
      campaignApplicationDeliveryType &&
      campaignApplicationDeliveryType.toLowerCase() == "email"
    ) {
      // If apply email is set to "notEmail", clear it
      if (newApplyEmail == "notEmail") {
        newApplyEmail = "";
      }

      // Validate the email address
      if (!newApplyEmail) {
        throw new HttpException("invalid 'apply email' to create campaign", HttpStatus.BAD_REQUEST);
      }

      deliveryDataSource = "manual";

      // Adjust values for feed email delivery type
      if (campaignApplicationDeliveryType.toLowerCase() == "feed email") {
        deliveryDataSource = "feed";
        activeDelivery = "email";
      }
    }

    return { newApplyEmail, deliveryDataSource, activeDelivery };
  }

  /**
   * Validates the campaign start and end dates and sets them in the insert object.
   *
   * @param newEntry - The new campaign entry object containing various campaign properties.
   * @param insert - The object that will be updated with the validated start and end dates.
   */
  validateAndSetDates(newEntry: any, insert: any) {
    const currentDate = new Date();
    const startDate = newEntry.campaignStartDate ? new Date(newEntry.campaignStartDate) : null;
    const endDate = newEntry.campaignEndDate ? new Date(newEntry.campaignEndDate) : null;

    // Validate invalid date ranges
    if (
      startDate &&
      endDate &&
      (startDate > endDate ||
        (startDate > endDate && endDate < currentDate) ||
        (startDate <= currentDate && endDate <= currentDate))
    ) {
      throw new HttpException(
        "startDate and/or endDate are not valid to create a campaign",
        HttpStatus.BAD_REQUEST,
      );
    }
    // Validate and set start date
    if (startDate) {
      insert["dateStart"] = startDate;
      if (startDate <= currentDate) {
        insert["dateStart"] = currentDate;
      }
    } else {
      insert["dateStart"] = currentDate;
    }

    // Validate and set end date
    if (endDate) {
      insert["dateEnd"] = endDate;
    }
  }

  /**
   * Validates the campaign status and sets the status and pay-per-click (PPC) value for the insert object.
   *
   * @param newEntry - The new campaign entry object containing various campaign properties.
   * @param insert - The object that will be updated with the validated status and PPC value.
   */
  validateAndSetStatus(newEntry: any, insert: any) {
    // Check if the campaign status is 'active' and the sponsorship status is 'false'
    if (
      newEntry.campaignStatus &&
      newEntry.campaignStatus == "active" &&
      (newEntry.campaignSponsorshipEnded == "false" || newEntry.campaignSponsorshipEnded == "")
    ) {
      // Set the status to 'active' and update the PPC value
      insert["status"] = CampaignStatus.active;
      insert["ppc"] = newEntry.campaignCpc;
      insert["active"] = 1;
      insert["validated"] = 1;
      insert["paused"] = 0;
      insert["removed"] = 0;

      // If the PPC value is not a valid number, set the status to 'Validating' and clear the PPC value
      if (!parseFloat(newEntry.campaignCpc)) {
        insert["status"] = CampaignStatus.validating;
        insert["active"] = 1;
        insert["validated"] = 0;
        insert["paused"] = 0;
        insert["removed"] = 0;
        insert["ppc"] = "";
      }

      // Validate future dates and set status to "pending"
      if (insert.dateStart > new Date() && insert.dateEnd > insert.dateStart) {
        insert.status = CampaignStatus.pending;
        insert["active"] = 1;
        insert["validated"] = 1;
        insert["paused"] = 0;
        insert["removed"] = 0;
      }
    } else {
      // Throw an exception if the campaign status is invalid for creating a campaign
      throw new HttpException(
        "invalid 'campaign_status' to create campaign",
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  /**
   * Update a campaign in the Talent bucket campaigns
   * @param campaignId
   * @param updateData
   */
  async updateCampaignAutomatically(campaignId: number, updateData: any, job?: any) {
    // Parameters validation
    if (!campaignId) {
      throw new HttpException("campaignId is required", HttpStatus.BAD_REQUEST);
    }
    if (!updateData) {
      throw new HttpException("updateData is required", HttpStatus.BAD_REQUEST);
    }

    // Convert the data
    updateData = this.convertDataToInsert(updateData);

    // Here will go the override code
    updateData = await this.applyOverrideToAutoCampaign(updateData);

    // Bring the campaign data
    const campaignData = await this.campaignsService.findOne(campaignId, [
      "campaignsBudget",
      "campaignFilters",
      "campaignApply",
      "account.accountSettings",
      "account.accountTag.tag",
    ]);

    // Campaign validation
    if (!campaignData) {
      throw new HttpException("'campaign' not found", HttpStatus.NOT_FOUND);
    }

    // Create variable to update
    const newData = structuredClone(campaignData);

    // Dates validation
    if (updateData.campaignStartDate! || updateData.campaignEndDate!) {
      await this.validateDatesForUpdate(updateData, newData);
    }

    // Common campaign validations
    const validations = await this.campaignValidations(
      false,
      campaignData.account,
      updateData,
      newData,
      campaignData,
    );
    Object.assign(newData, validations);

    // Validate and set campaign status
    try {
      this.validateStatusForUpdate(updateData, newData);
    } catch (error: any) {
      Logger.error(new Error(`There's an error with validateStatusForUpdate: ${error.message}`));
      throw error;
    }

    // Validate campaignTargetCost (cpa goal doesn't exist any more)
    this.validateCampaignTargetCostForUpdate(updateData, newData);

    // Validate campaign apply type
    this.validateApplyTypeForUpdate(updateData, newData);

    // Build the update JSON to be sent to the campaign transaction
    const transactionalParams = this.createUpdateTransactionalParams(
      campaignData.account,
      newData,
      updateData,
    );

    const { campaignParamsUpdate, applyParamsUpdate, budgetParamsUpdate } = this.defineUpdateParams(
      transactionalParams,
      updateData,
      campaignData,
      campaignData.campaignName,
    );

    if (updateData.source == "xml") {
      delete campaignParamsUpdate.ppc;
      delete campaignParamsUpdate.sponsored;
    }

    try {
      // Update the campaign in Talent
      await this.campaignsTransactionalService.update(
        campaignData.id,
        campaignData.account?.id as number,
        80010,
        campaignParamsUpdate,
        applyParamsUpdate as unknown as UpdateCampaignApplyDto,
        undefined,
        transactionalParams.filterTextParams,
        Object.entries(budgetParamsUpdate).length > 0
          ? this.convertKeysToSnakeCase(budgetParamsUpdate)
          : undefined,
        undefined,
        "",
        updateData.source,
      );

      if (updateData.source == "xml" && job && Object.keys(job).length > 0) {
        // Send jobs to procesing again
        delete job.source_vendor_campaign;
        //await this.campaignProcessService.sendMessagesToJobsProcessQueue(job);
      }
    } catch (error: any) {
      Logger.error(
        new Error(`Error updating the campaign: ${error.options?.cause ?? error.message}`),
      );
      throw error;
    }
  }

  /**
   * Create the final JSON to be sent to the campaign transaction to update the campaign in Talent.
   * @param account
   * @param newData
   * @param updateData
   * @param campaignNameTalent
   * @returns
   */
  createUpdateTransactionalParams(account: any, newData: any, updateData: any) {
    const newCampaignParams: any = {
      accountId: account.id,
      campaignName: updateData.talentCampaign,
      autoCampaignApi: updateData.apiCampaignId,
      dealId: newData.dealId,
      status: newData.status,
      applyType: newData.applyType,
      conversionType: newData.conversionType,
      conversionTargetCost: newData.conversionTargetCost,
      campaignObjective: null,
      dateStart: this.formatDateToYYYYMMDD(newData.dateStart),
      dateEnd: this.formatDateToYYYYMMDD(newData.dateEnd),
      sponsored: 1,
      lastClick: newData.lastClick,
      type: newData.type,
      priority: newData.priority,
      paymentRequired: newData.paymentRequired!,
      created: newData.created,
      updated: new Date(),
      updatedBy: 80010,
      cpcModifier: newData.cpcModifier,
      sourcePpc: newData.sourcePpc,
      country:
        updateData.companyCountry == "All Countries"
          ? updateData.accountCountry
          : updateData.companyCountry,
    };
    if (updateData.campaignCpc) {
      newCampaignParams.ppc = Number(updateData.campaignCpc);
    }
    const applyParams = {
      campaignId: newData.id,
      activeDelivery: newData.activeDelivery,
      deliveryEmail: newData.deliveryEmail,
      deliveryDataSource: newData.deliveryDataSource,
    };
    const filterTextParams = newData.filters;
    const budgetParams = {
      campaignId: newData.id,
      budgetType: newData.budgetType == "monthly" ? BudgetType.MONTHLY : BudgetType.DAILY,
      budget: newData.budgetType == "monthly" ? newData.budgetMonth : newData.budgetDay,
      estimatedBudgetRemainingDays: newData.estimatedBudgetDaysRemaining,
      hasPacing: newData.hasPacing,
      status: newData.status,
      country: newData.country,
    };

    return {
      newCampaignParams,
      applyParams,
      filterTextParams,
      budgetParams,
    };
  }

  /**
   * Dates validation for updateCampaignAutomatically.
   * @param updateData
   * @param newData
   */
  async validateDatesForUpdate(updateData: any, newData: any) {
    let newDateStart: Date | null = null;
    let newDateEnd: Date | null = null;
    const now = new Date();
    const today = new Date(
      Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate(), 0, 0, 0),
    );

    // Convert incoming date strings to Date objects
    const { updateStartDate, updateEndDate, currentStartDate, currentEndDate } =
      this.convertDateData(updateData, newData);

    // Validate and calculate new start date
    if (updateStartDate) {
      newDateStart = this.validateNewStartDate(
        updateStartDate,
        updateEndDate,
        currentStartDate,
        today,
      );
    }
    // Validate and calculate new end date
    if (updateEndDate) {
      newDateEnd = this.validateNewEndDate(
        updateEndDate,
        currentStartDate,
        currentEndDate,
        newDateStart,
        today,
      );
    }
    // Final adjustments: Ensure the start and end dates are logically consistent
    newDateStart = this.adjustStartDate(newDateStart, newDateEnd, currentEndDate);
    newDateEnd = this.adjustEndDate(newDateEnd, newDateStart, currentStartDate);
    // Ensure that if endDate is set, we have a corresponding startDate for the update transaction
    if (newDateEnd && !newDateStart && currentStartDate) {
      newDateStart = currentStartDate;
    }
    // Assign final validated values
    newData.dateStart = newDateStart;
    newData.dateEnd = newDateEnd;
  }

  /**
   * Helper method for validateDatesForUpdate to convert incoming date strings to Date objects
   * @param updateData
   * @param newData
   * @returns
   */
  convertDateData(
    updateData: any,
    newData: any,
  ): {
    updateStartDate: Date | null;
    updateEndDate: Date | null;
    currentStartDate: Date | null;
    currentEndDate: Date | null;
  } {
    return {
      updateStartDate: updateData.campaignStartDate ? new Date(updateData.campaignStartDate) : null,
      updateEndDate: updateData.campaignEndDate ? new Date(updateData.campaignEndDate) : null,
      currentStartDate: newData.dateStart ? new Date(newData.dateStart) : null,
      currentEndDate: newData.dateEnd ? new Date(newData.dateEnd) : null,
    };
  }

  /**
   * Validate the new start date based on logic:
   * 1. It must be before the end date (if provided).
   * 2. It cannot be earlier than today unless it improves the current start date.
   * 3. It must differ from the existing start date.
   * @param updateStartDate
   * @param updateEndDate
   * @param currentStartDate
   * @param today
   * @returns Date | null
   */
  validateNewStartDate(
    updateStartDate: Date,
    updateEndDate: Date | null,
    currentStartDate: Date | null,
    today: Date,
  ): Date | null {
    if (updateEndDate && updateEndDate.getTime() <= updateStartDate.getTime()) {
      return null;
    }
    if (!currentStartDate || updateStartDate.getTime() !== currentStartDate.getTime()) {
      let newStart: Date | null = updateStartDate;

      if (newStart.getTime() < today.getTime()) {
        newStart =
          currentStartDate && newStart.getTime() < currentStartDate.getTime() ? today : null;
      }
      return newStart;
    }
    if (updateStartDate.getTime() === currentStartDate.getTime()) {
      return currentStartDate;
    }
    return null;
  }

  /**
   * Validate the new end date based on logic:
   * 1. It must be today or later.
   * 2. It cannot be earlier than or equal to the start date.
   * 3. It must differ from the existing end date.
   * @param updateEndDate
   * @param currentStartDate
   * @param currentEndDate
   * @param newStartDate
   * @param today
   * @returns Date | null
   */
  validateNewEndDate(
    updateEndDate: Date,
    currentStartDate: Date | null,
    currentEndDate: Date | null,
    newStartDate: Date | null,
    today: Date,
  ): Date | null {
    if (updateEndDate < today) {
      return null;
    }
    if (
      (newStartDate && updateEndDate > newStartDate) ||
      (currentStartDate && updateEndDate > currentStartDate)
    ) {
      if (!currentEndDate || updateEndDate.getTime() !== currentEndDate.getTime()) {
        return updateEndDate;
      }
    }
    if (currentEndDate && updateEndDate.getTime() === currentEndDate.getTime()) {
      return currentEndDate;
    }
    return null;
  }

  /**
   * Adjust the start date if the end date is not provided.
   * @param newDateStart The proposed new start date
   * @param newDateEnd The proposed new end date
   * @param currentEndDate The current end date
   * @returns Adjusted start date
   */
  adjustStartDate(
    newDateStart: Date | null,
    newDateEnd: Date | null,
    currentEndDate: Date | null,
  ): Date | null {
    if (newDateStart && !newDateEnd) {
      return !currentEndDate || newDateStart < currentEndDate ? newDateStart : null;
    }
    return newDateStart;
  }

  /**
   * Adjust the end date if the start date is not provided.
   * @param newDateEnd The proposed new end date
   * @param newDateStart The proposed new start date
   * @param currentStartDate The current start date
   * @returns Adjusted end date
   */
  adjustEndDate(
    newDateEnd: Date | null,
    newDateStart: Date | null,
    currentStartDate: Date | null,
  ): Date | null {
    if (newDateEnd && !newDateStart) {
      return !currentStartDate || currentStartDate < newDateEnd ? newDateEnd : null;
    }
    return newDateEnd;
  }

  /**
   * Validate status and set status for updateCampaignAutomatically
   * @param updateData
   * @param newData
   */
  validateStatusForUpdate(updateData: any, newData: any) {
    // if (newData.status === "validating" && !parseFloat(updateData.campaignCpc)) {
    //   throw new HttpException(
    //     "Campaign status 'Validating', please set a cpc value greater than 0",
    //     HttpStatus.BAD_REQUEST,
    //   );
    // }

    if (updateData.campaignStatus) {
      const isSponsorshipInactive =
        updateData.campaignSponsorshipEnded === "0" ||
        updateData.campaignSponsorshipEnded === "" ||
        updateData.campaignSponsorshipEnded == "false" ||
        !updateData.campaignSponsorshipEnded;
      if (
        updateData.campaignStatus === "active" &&
        isSponsorshipInactive &&
        (updateData.campaignCpc || newData.ppc)
      ) {
        Object.assign(newData, {
          status: CampaignStatus.active,
          active: 1,
          paused: 0,
          validated: 1,
          removed: 0,
        });
      } else {
        Object.assign(newData, {
          status: CampaignStatus.paused,
          paused: 1,
          active: 1,
          validated: 1,
        });
      }
      if (updateData.campaignStatus === "removed") {
        Object.assign(newData, {
          status: CampaignStatus.removed,
          paused: 0,
          active: 1,
          validated: 1,
          removed: 1,
        });
      }
    }

    const startDate = isNaN(new Date(updateData.campaignStartDate).getTime())
      ? new Date(newData.dateStar)
      : new Date(updateData.campaignStartDate);

    const endDate = updateData.campaignEndDate
      ? new Date(updateData.campaignEndDate)
      : new Date(newData.dateEnd);

    if (startDate > new Date() && endDate > startDate) {
      Object.assign(newData, {
        status: CampaignStatus.pending,
        active: 0,
        validated: 1,
        paused: 0,
      });
    }

    const isInvalidDateRange =
      updateData.campaignStartDate &&
      updateData.campaignEndDate &&
      (updateData.campaignStartDate === updateData.campaignEndDate ||
        (updateData.campaignStartDate > updateData.campaignEndDate &&
          new Date(updateData.campaignEndDate) < new Date()) ||
        (new Date(updateData.campaignStartDate) <= new Date() &&
          new Date(updateData.campaignEndDate) <= new Date()));

    if (isInvalidDateRange) {
      Object.assign(newData, {
        status: CampaignStatus.paused,
        paused: 1,
        active: 0,
        validated: 0,
      });
    }
  }

  /**
   * Validate campaignTargetCost for updateCampaignAutomatically
   * @param updateData
   * @param newData
   */
  validateCampaignTargetCostForUpdate(updateData: any, newData: any) {
    // Validate the conversion cost and type (old cpa goal validation)
    // If we don't have targetCost, conversionTargetCost and conversionType will be null
    // If we have targetCost, we take conversionType by the client, or "application"
    // Check if updateData.campaignTargetCost is invalid
    if (
      !updateData.campaignTargetCost ||
      updateData.campaignTargetCost === "" ||
      updateData.campaignTargetCost === "N/A" ||
      parseFloat(updateData.campaignTargetCost) <= 0
    ) {
      newData.conversionTargetCost = null;
      newData.conversionType = null;
    } else {
      newData.conversionType =
        updateData.campaignConversionType !== ""
          ? updateData.campaignConversionType
          : "application";
      newData.conversionTargetCost = parseFloat(updateData.campaignTargetCost);
    }
  }

  /**
   * Validate campaign apply type for updateCampaignAutomatically
   * @param updateData
   * @param newData
   */
  validateApplyTypeForUpdate(updateData: any, newData: any) {
    // Checks if the 'campaign_apply_type' field in $updateData is not empty and assign the value to 'applyType' in $campaignData
    updateData.campaignApplyType && (newData.applyType = updateData.campaignApplyType);

    // Checks if the 'campaign_application_delivery_type' field in $updateData is not empty and assign the value to 'activeDelivery' in $campaignData.
    updateData.campaignApplicationDeliveryType &&
      (newData.activeDelivery = updateData.campaignApplicationDeliveryType);

    // Initializes 'delivery Email' in $campaign Data to null.
    newData.deliveryEmail = null;

    // Checks if the 'apply_email' field in $updateData is not empty and assigns the value to 'deliveryEmail' in $campaignData.
    updateData.applyEmail && (newData.deliveryEmail = updateData.applyEmail);

    // Checks if at least one of the fields is not empty to validate and process additional data.
    if (
      updateData.campaignApplyType ||
      updateData.campaignApplicationDeliveryType ||
      updateData.applyEmail
    ) {
      const applySet = this.validateTalentApplyConfig(
        updateData.campaignApplicationDeliveryType,
        updateData.applyEmail,
        updateData.campaignApplyType,
      );
      // Check the validation result
      if (!applySet) {
        throw new HttpException("error validating apply settings", HttpStatus.BAD_REQUEST);
      }

      // Combines the original data with additional validated and processed data.
      Object.assign(newData, applySet);
    }
  }

  /**
   * Converts the keys of an object from camelCase to snake_case.
   *
   * @param obj - The object whose keys need to be converted.
   * @returns A new object with the keys in snake_case format.
   */
  convertKeysToSnakeCase(obj: Record<string, any>): Record<string, any> {
    // Initialize an empty object to hold the snake_case keys and values
    const snakeCaseObj: Record<string, any> = {};

    // Iterate over each key in the original object
    Object.keys(obj).forEach((key) => {
      // Convert the key from camelCase to snake_case
      const snakeCaseKey = key.replace(/([A-Z])/g, (letter) => `_${letter.toLowerCase()}`);
      // Assign the value from the original object to the new key in the new object
      snakeCaseObj[snakeCaseKey] = obj[key];
    });

    // Return the new object with snake_case keys
    return snakeCaseObj;
  }

  /**
   * Get a clientList from external_campaigns
   * @returns
   */
  async getClientsList() {
    const tagList = await this.tagsService.getTagByType(7);

    // Return only the tag names as a client list
    const result = tagList.map((campaign) => ({
      id: campaign.id,
      name: campaign.name,
    }));

    return result;
  }

  /**
   * Get a feedcodeList from external_campaigns with existsInTalent true or false.
   * @returns An array of feedcode's
   */
  async getFeedcodeListWithTalentCheck(clientDto: ClientExternalCampaignDto): Promise<any> {
    return await this.externalCampaignRepository.getFeedcodeList(clientDto);
  }

  /**
   * Get a companyNameList by feedcode from external_campaigns
   * @param feedcodeDto
   * @returns
   */
  async getCompanyNameListByFeedcode(feedcodeDto: FeedcodeExternalCampaignDto): Promise<any> {
    // Get the list of companyName for the feedcode selected
    const companyNameList =
      await this.externalCampaignRepository.getCompanyNameListByFeedcode(feedcodeDto);

    // Check if the company has at least one existing campaign in Talent using hasCompanyCampaign()
    const companiesWithCampaign = await Promise.all(
      companyNameList.map(async (companyName: string) => {
        try {
          const hasCampaign = await this.externalCampaignRepository.hasCompanyCampaign(
            companyName,
            feedcodeDto,
          );
          return { companyName, hasCampaign };
        } catch (error) {
          // Handle the error by assuming the company has no campaigns in Talent yet
          return { companyName, hasCampaign: false };
        }
      }),
    );

    // Return result
    return companiesWithCampaign;
  }

  /**
   * Search all the campaigns from external campaigns's bucket by the filter provided in the DTO.
   * We have the last register for each apiCampaignId.
   * This method is created to feed the dashboard for Ingested Campaigns.
   * @param searchDto
   * @returns
   */
  async getCampaignsByFilters(searchDto: SearchExternalCampaignDto): Promise<any> {
    // Init variables
    let campaigns: any;
    let paginatedCampaigns: any;
    let totalCampaigns: number;
    const { table = "" } = searchDto;

    // Define sort and order
    if (searchDto.sortColumn || searchDto.sortType) {
      searchDto.sort = searchDto.sortColumn ?? searchDto.sort;
      searchDto.order = searchDto.sortType ?? searchDto.order;
    }

    // Get all the campaigns with filters and no pagination because we'll do the pagination after process
    const campaignsList = await this.externalCampaignRepository.listAutoCampaignEntry(
      searchDto,
      true,
    );

    // Logic for each table: entries, running, paused
    switch (table) {
      case "entries":
        campaigns = await this.renderEntriesTable(campaignsList, searchDto, table);
        break;

      case "running":
      case "paused":
        campaigns = await this.renderRunningOrPausedTable(campaignsList, searchDto, table);
        break;

      default:
        break;
    }

    // Paginate the results
    if (table) {
      paginatedCampaigns = campaigns.lines;
      totalCampaigns = campaigns.total;
    } else {
      paginatedCampaigns = campaignsList.campaigns;
      totalCampaigns = campaignsList.total;
    }

    // Return the result
    return {
      total: totalCampaigns,
      campaigns: paginatedCampaigns,
    };
  }

  /**
   * Provide all the campaigns to feed the entries table in the Ingested Campaigns' dashboard.
   * @param campaignsList
   * @param searchDto
   * @returns
   */
  async renderEntriesTable(
    campaignsList: any,
    searchDto: SearchExternalCampaignDto,
    table: string,
  ) {
    // Init variables
    let total = campaignsList.total;
    let lines: any[] = [];
    let hasDelivery: boolean = false;

    // Check for duplicated apiCampaignIds
    const duplicatedApiCampaignIds = await this.getDuplicatedCampaignIds(campaignsList, searchDto);

    // Iterate through each campaign and perform any desired operations
    for (const campaign of campaignsList.campaigns) {
      const line: any = {};

      // Validate feedcode and generateLinks()
      try {
        await this.validateFeedcodeAndGenerateLinks(campaign, line, duplicatedApiCampaignIds);
      } catch (error) {
        total = total - 1;
        continue;
      }

      // First columns: Feed, Date, ApiCampaignId, Feedcode, Company, Campaign
      await this.setFirstColumnsLines(line, campaign, table, duplicatedApiCampaignIds);

      if (campaign.campaignDelivery) {
        hasDelivery = true;
        line["delivery"] = campaign.campaignDelivery;
      } else {
        line["delivery"] = null;
      }

      // Columns: Status and Sponsorship Ended
      await this.setStatusAndSponsorshipLines(line, campaign, table);

      // Column: Country
      await this.setCountryLine(line, campaign, table);

      // Budget and budget type
      try {
        this.getBudgetTypeAndBudgetToShow(
          line,
          campaign.apiClientName,
          campaign.campaignBudgetType,
          campaign.campaignBudgetValue,
          campaign.campaignMonthlyBudget,
          campaign.campaignWeeklyBudget,
        );
      } catch (error) {
        // Handle any unexpected errors
        console.error("An unexpected error occurred:", error);
        line["budgetType"] = null;
        line["budget"] = null;
      }

      // More columns
      line["cpc"] = campaign.campaignCpc === "" ? null : campaign.campaignCpc;
      line["jobs"] = parseInt(campaign.xmlNumJobs, 10);

      // Conditional columns
      if (parseFloat(campaign.campaignTargetCost) > 0) {
        line["costPerConversionTarget"] = parseFloat(campaign.campaignTargetCost);
      } else {
        line["costPerConversionTarget"] = null;
      }
      if (campaign.campaignConversionType) {
        line["conversionType"] = this.ucwords(campaign.campaignConversionType);
      } else {
        line["conversionType"] = null;
      }

      // Final array with all the lines
      lines.push(line);
    }

    // Validate if there is any line with Delivery
    if (!hasDelivery) {
      lines = lines.map((line) => {
        const { ...rest } = line;
        return rest;
      });
    }

    // Return result
    return { total, lines };
  }

  /**
   * Provide all the campaigns to feed the running or paused table in the Ingested Campaigns' dashboard.
   * We need to call the method with the table "running" or "paused" values.
   * @param campaignsList
   * @param searchDto
   * @param table
   * @returns
   */
  async renderRunningOrPausedTable(
    campaignsList: any,
    searchDto: SearchExternalCampaignDto,
    table: string,
  ) {
    // Init variables
    const total = campaignsList.total;
    let lines: any[] = [];
    let hasDelivery: boolean = false;

    // List all the non-automatic campaigns to manage the toggle and tag the campaign automatic or not
    const nonAutomaticCampaigns = await this.listNonAutoCampaigns();

    // Check overrides' apiCampaignIds list
    const overridesInfo = await this.helperService.getOverrideApiCampaignIdsByFilters(searchDto);

    // Iterate through each campaign
    for (const campaign of campaignsList.campaigns) {
      const line: any = {};
      let arrayChanges = [];

      // we bring the old entry to compare
      const oldCampaignEntries = await this.externalCampaignRepository.readAutoCampaignEntry(
        "apiCampaignId",
        campaign.apiCampaignId,
        2,
      );
      const oldEntry = oldCampaignEntries ? oldCampaignEntries[1] : {};

      // We compare the new value with the old value and take only the variables with differences
      if (oldEntry) {
        arrayChanges = this.compareEntries(campaign, oldEntry);
      }

      // First columns: Feed, Date, ApiCampaignId, Feedcode, Company, Campaign
      await this.setFirstColumnsLines(line, campaign, table);

      // Format the data to display
      // Old values
      if (oldEntry) {
        await this.formatDataDisplay(oldEntry, "oldEntry");

        // Format old entry values if they are empty related to campaign dates validation
        await this.formatValuesCampaignDatesValidation(oldEntry);
      }
      // New values
      await this.formatDataDisplay(campaign, "campaign");

      // Check if there's an override
      const override = overridesInfo.overrides?.find(
        (ovr: { apiCampaignId: any }) => ovr.apiCampaignId === campaign.apiCampaignId,
      );
      const overrideData = JSON.parse(override?.overrideData ?? "{}");

      // Add override flag to the line
      line.overrideBanner = !!override?.overrideData;

      // Merge with prefixed override fields
      const mergedCampaign = this.mergeCampaignWithOverrides(campaign, overrideData);

      // Column: Delivery
      hasDelivery = await this.setDeliveryLine(
        line,
        mergedCampaign,
        hasDelivery,
        oldEntry,
        arrayChanges,
      );

      // Columns: Status and Sponsorship Ended
      await this.setStatusAndSponsorshipLines(line, mergedCampaign, table, oldEntry, arrayChanges);

      // Format the data for Campaign Dates, when we have value
      await this.setCampaignDatesLine(line, mergedCampaign, oldEntry, arrayChanges);

      // Column: Country
      await this.setCountryLine(line, mergedCampaign, table, oldEntry, arrayChanges);

      // Budget and budget type
      await this.setBudgetLines(line, mergedCampaign, oldEntry, arrayChanges);

      // More columns
      await this.setCostRelatedLines(line, mergedCampaign, oldEntry, arrayChanges);
      await this.setJobsAndDaysRemainingLines(line, mergedCampaign, oldEntry, arrayChanges);

      // This line is going to be done after Falcon line["History"] = "-";

      // Check if the api_campaign_id is automatic or not to switch the toggle (automatic by default)
      line["autoApi"] = !(await this.isNonAutoCampaign(
        nonAutomaticCampaigns,
        campaign.apiCampaignId,
      ));

      // Final array with all the lines
      lines.push(line);
    }

    // Validate if there is any line with Delivery
    if (!hasDelivery) {
      lines = lines.map((line) => {
        const { ...rest } = line;
        return rest;
      });
    }

    // Return result
    return { total, lines };
  }

  /**
   * Check the duplicated campaigns
   * @param campaignsList
   * @param searchDto
   * @returns
   */
  async getDuplicatedCampaignIds(campaignsList: any, searchDto: SearchExternalCampaignDto) {
    const { talentFeedcode, apiCampaignId } = searchDto;
    if (
      talentFeedcode === "appcast-appcast-exchanges-usd-us-cpa" ||
      (apiCampaignId && /appcast_12492_/i.test(apiCampaignId))
    ) {
      return await this.checkDuplicatedCampaigns(campaignsList.campaigns);
    }
    return [];
  }

  /**
   * First columns: Feed, Date, ApiCampaignId, Feedcode, Company, Campaign
   * @param line
   * @param campaign
   * @param duplicatedApiCampaignIds
   * @returns
   */
  async setFirstColumnsLines(
    line: any,
    campaign: any,
    table: string,
    duplicatedApiCampaignIds: any = [],
  ) {
    line["feed"] = campaign.xmlFeedLink;
    if (table === "entries") {
      line["date"] = campaign.date;
    }
    line["apiCampaignId"] = campaign.apiCampaignId;
    line["feedcode"] = campaign.talentFeedcode;
    line["company"] = campaign.companyName;

    // Check duplicatedApiCampaignIds only in entries table
    if (duplicatedApiCampaignIds.length > 0) {
      line["campaign"] = duplicatedApiCampaignIds.includes(campaign.apiCampaignId)
        ? { campaign: campaign.talentCampaign, addInRed: "DUPLICATED" }
        : campaign.talentCampaign;
    } else {
      line["campaign"] = campaign.talentCampaign;
    }
  }

  /**
   * Format data to display
   * @param data
   * @param typeData
   */
  async formatDataDisplay(data: any, typeData: string) {
    if (typeData === "oldEntry") {
      data.campaignSponsorshipEnded = data.campaignSponsorshipEnded ? "false" : "true";
    }
    if (typeData === "campaign") {
      data.campaignSponsorshipEnded = data.campaignSponsorshipEnded ? "1" : "0";
    }
    data.campaignMonthlyBudget =
      parseFloat(data.campaignMonthlyBudget) ||
      data.campaignMonthlyBudget === "0" ||
      data.campaignMonthlyBudget != "no limit"
        ? parseFloat(data.campaignMonthlyBudget)
        : 2000000;
    data.campaignTargetCost = parseFloat(data.campaignTargetCost);
    data.xmlNumJobs = parseInt(data.xmlNumJobs, 10);
    data.estimatedBudgetDaysRemaining = parseInt(data.estimatedBudgetDaysRemaining, 10);
    data.xmlNumJobs = parseInt(data.xmlNumJobs, 10);
    data.campaignWeeklyBudget = Math.round(parseFloat(data.campaignWeeklyBudget) * 100) / 100;
  }

  /**
   * Merge campaign information with overrides information, adding a prefix to overrideData
   * (example: "overrideDelivery") so, there's no doubt which values are overwritten when
   * we are returning line information for the dashboard.
   * @param campaign
   * @param overrides
   * @returns
   */
  private mergeCampaignWithOverrides(campaign: any, overrides?: any): any {
    const result = { ...campaign };

    if (overrides) {
      for (const [key, value] of Object.entries(overrides)) {
        const overrideKey = `override${key.charAt(0).toUpperCase()}${key.slice(1)}`;
        result[overrideKey] = value;
      }
    }

    return result;
  }

  /**
   * Resolves the value of a campaign field by:
   * 1. Returning the override if present (assumes override key follows "overrideX" naming).
   * 2. Returning { oldEntry, newEntry } if the value has changed.
   * 3. Returning the current campaign value otherwise.
   * This is always the order clientData/overwrittenData > oldEntry/newEntry > campaignValue.
   *
   * @param campaign - The current campaign object.
   * @param oldEntry - The previous campaign object.
   * @param arrayChanges - Object indicating which keys have changed.
   * @param key - The campaign field key (e.g. "campaignDelivery").
   * @param overrideKey - The override field key if exists (e.g. "overrideDelivery").
   */
  private checkOverrideOldNewValue(
    campaign: any,
    oldEntry: any,
    arrayChanges: any,
    key: string,
    overrideKey: string = "",
  ) {
    // Check if there's an override
    let overrideValue = campaign[overrideKey];
    if (overrideKey === "overrideBudget" && overrideValue !== undefined) {
      overrideValue = String(overrideValue);
    }
    if (overrideValue !== undefined) {
      return {
        clientData: campaign[key],
        overwrittenData: overrideValue,
      };
    }

    // Check if there's oldEntry vs newEntry
    if (arrayChanges[key]) {
      return {
        oldEntry: oldEntry[key],
        newEntry: campaign[key],
      };
    }

    // If no override and no arrayChanges, send the campaign value
    return campaign[key];
  }

  /**
   * Column Delivery
   * @param line
   * @param campaign
   * @param oldEntry
   * @param arrayChanges
   * @param hasDelivery
   */
  async setDeliveryLine(
    line: any,
    campaign: any,
    hasDelivery: boolean,
    oldEntry: any = {},
    arrayChanges: any = [],
  ) {
    if (
      (campaign.campaignDelivery && campaign.campaignDelivery != null) ||
      campaign.overrideDelivery != undefined
    ) {
      hasDelivery = true;
      line["delivery"] = this.checkOverrideOldNewValue(
        campaign,
        oldEntry,
        arrayChanges,
        "campaignDelivery",
        "overrideDelivery",
      );
    } else {
      line["delivery"] = null;
    }
    return hasDelivery;
  }

  /**
   * Format old entry values if they are empty related to campaign dates validation
   * @param oldEntry
   */
  async formatValuesCampaignDatesValidation(oldEntry: any) {
    if (!oldEntry.campaignStatus || oldEntry.campaignStatus === null) {
      oldEntry.campaignStatus = "-";
    }
    if (!oldEntry.campaignStartDate || oldEntry.campaignStartDate === null) {
      oldEntry.campaignStartDate = "-";
    }
    if (!oldEntry.campaignEndDate || oldEntry.campaignEndDate === null) {
      oldEntry.campaignEndDate = "-";
    }
  }

  /**
   * Columns: Status and Sponsorship Ended
   * @param line
   * @param campaign
   * @param table
   * @param oldEntry
   * @param arrayChanges
   * @returns
   */
  async setStatusAndSponsorshipLines(
    line: any,
    campaign: any,
    table: string,
    oldEntry: any = {},
    arrayChanges: any = [],
  ) {
    switch (table) {
      case "entries":
        line["status"] = campaign.campaignStatus;
        line["sponsorshipEnded"] = campaign.campaignSponsorshipEnded ? "1" : "0";
        break;

      case "running":
        line["status"] = this.checkOverrideOldNewValue(
          campaign,
          oldEntry,
          arrayChanges,
          "campaignStatus",
          "",
        );
        break;

      case "paused":
        line["status"] = this.checkOverrideOldNewValue(
          campaign,
          oldEntry,
          arrayChanges,
          "campaignStatus",
          "",
        );
        line["sponsorshipEnded"] = this.checkOverrideOldNewValue(
          campaign,
          oldEntry,
          arrayChanges,
          "campaignSponsorshipEnded",
          "",
        );
        break;

      default:
        break;
    }
  }

  /**
   * Column: Campaign Dates
   * @param line
   * @param campaign
   * @param oldEntry
   * @param arrayChanges
   * @returns
   */
  async setCampaignDatesLine(line: any, campaign: any, oldEntry: any = {}, arrayChanges: any = []) {
    if (campaign.campaignStartDate && campaign.campaignEndDate) {
      const datesArrayChanges =
        arrayChanges.campaignStartDate && arrayChanges.campaignEndDate
          ? `${this.formatDate(arrayChanges.campaignStartDate)} - ${this.formatDate(arrayChanges.campaignEndDate)}`
          : "";
      const datesOldEntry =
        oldEntry.campaignStartDate && oldEntry.campaignEndDate
          ? `${this.formatDate(oldEntry.campaignStartDate)} - ${this.formatDate(oldEntry.campaignEndDate)}`
          : "-";
      const datesCampaign = `${this.formatDate(campaign.campaignStartDate)} - ${this.formatDate(campaign.campaignEndDate)}`;
      line["campaignDates"] =
        datesArrayChanges && oldEntry
          ? { oldEntry: datesOldEntry, newEntry: datesCampaign }
          : datesCampaign;
    } else {
      line["campaignDates"] = null;
    }

    // Overrides always take precedence over oldEntry/newEntry diffs for the dashboard
    if (campaign.overrideDateStart || campaign.overrideDateEnd) {
      line["campaignDates"] = this.buildCampaignDatesOverride(campaign);
    }
  }

  /**
   * Build the campaignDates line when there's an override.
   * @param campaign
   * @returns
   */
  private buildCampaignDatesOverride(campaign: any) {
    const campaignStartDate = campaign.campaignStartDate
      ? this.formatDate(campaign.campaignStartDate)
      : "";

    const campaignEndDate = campaign.campaignEndDate
      ? this.formatDate(campaign.campaignEndDate)
      : "";

    let overrideStartDate = "";
    if (campaign.overrideDateStart) {
      overrideStartDate = this.formatDate(campaign.overrideDateStart);
    } else if (campaign.campaignStartDate) {
      overrideStartDate = this.formatDate(campaign.campaignStartDate);
    }

    let overrideEndDate = "";
    if (campaign.overrideDateEnd) {
      overrideEndDate = this.formatDate(campaign.overrideDateEnd);
    } else if (campaign.campaignEndDate) {
      overrideEndDate = this.formatDate(campaign.campaignEndDate);
    }
    return {
      clientData: `${campaignStartDate} - ${campaignEndDate}`,
      overwrittenData: `${overrideStartDate} - ${overrideEndDate}`,
    };
  }

  /**
   * Column: Country
   * @param line
   * @param campaign
   * @param table
   * @param oldEntry
   * @param arrayChanges
   * @returns
   */
  async setCountryLine(
    line: any,
    campaign: any,
    table: string,
    oldEntry: any = {},
    arrayChanges: any = [],
  ) {
    // Init variables
    // Country: we want only one country per campaign, if we have more than one we are going to take "All Countries"
    switch (table) {
      case "entries": {
        line["country"] = this.countryLineForDashboard(campaign);
        break;
      }

      case "running":
      case "paused": {
        if (Object.entries(oldEntry).length > 0 && arrayChanges.campaignCountry) {
          const oldCountry = this.countryLineForDashboard(oldEntry);
          const newCountry = this.countryLineForDashboard(campaign);
          // Check if both oldCountry and newCountry are equals because we can have "All Countries" from a multicountry or "All Countries" campaign
          // If not, we set oldEntry and newEntry values
          line["country"] =
            oldCountry === newCountry
              ? newCountry
              : {
                  oldEntry: oldCountry,
                  newEntry: newCountry,
                };
        } else {
          line["country"] = this.countryLineForDashboard(campaign);
        }
        break;
      }

      default:
        break;
    }
  }

  /**
   * Columns: Budget Type and Budget
   * @param line
   * @param campaign
   * @param oldEntry
   * @param arrayChanges
   * @returns
   */
  async setBudgetLines(line: any, campaign: any, oldEntry: any = {}, arrayChanges: any = []) {
    if (
      campaign.campaignBudgetType &&
      campaign.campaignBudgetType != "" &&
      campaign.campaignBudgetType != null
    ) {
      line["budgetType"] = this.checkOverrideOldNewValue(
        campaign,
        oldEntry,
        arrayChanges,
        "campaignBudgetType",
        "overrideBudgetType",
      );
      line["budget"] = this.checkOverrideOldNewValue(
        campaign,
        oldEntry,
        arrayChanges,
        "campaignBudgetValue",
        "overrideBudget",
      );
    } else if (
      campaign.apiClientName === "appcast" &&
      campaign.campaignWeeklyBudget &&
      campaign.campaignWeeklyBudget >= 1 &&
      campaign.campaignWeeklyBudget <= 2000000
    ) {
      // If the client_tag is Appcast, and we are taking a weekly budget (campaign_weekly_budget is a number from 1 to 2000000),
      // Budget Type on the dashboard is “weekly”, if not monthly
      line["budgetType"] = "weekly";
      line["budget"] = this.checkOverrideOldNewValue(
        campaign,
        oldEntry,
        arrayChanges,
        "campaignBudgetValue",
        "overrideBudget",
      );
    } else {
      line["budgetType"] = "monthly";
      line["budget"] = this.checkOverrideOldNewValue(
        campaign,
        oldEntry,
        arrayChanges,
        "campaignBudgetValue",
        "overrideBudget",
      );
    }
  }

  /**
   * Columns: CPC, Cost per Conversion Target and Conversion Type
   * @param line
   * @param campaign
   * @param oldEntry
   * @param arrayChanges
   * @returns
   */
  async setCostRelatedLines(line: any, campaign: any, oldEntry: any = {}, arrayChanges: any = []) {
    const cpcValue = this.checkOverrideOldNewValue(
      campaign,
      oldEntry,
      arrayChanges,
      "campaignCpc",
      "",
    );
    line["cpc"] = cpcValue === "" ? null : cpcValue;

    if (
      (campaign.campaignTargetCost && parseFloat(campaign.campaignTargetCost) > 0) ||
      campaign.overrideConversionTargetCost != undefined
    ) {
      line["costPerConversionTarget"] = this.checkOverrideOldNewValue(
        campaign,
        oldEntry,
        arrayChanges,
        "campaignTargetCost",
        "overrideConversionTargetCost",
      );
    } else {
      line["costPerConversionTarget"] = null;
    }

    if (
      (campaign.campaignConversionType &&
        campaign.campaignConversionType != null &&
        campaign.campaignConversionType != "") ||
      campaign.overrideConversionType != undefined
    ) {
      line["conversionType"] = this.checkOverrideOldNewValue(
        {
          ...campaign,
          campaignConversionType: this.ucwords(campaign.campaignConversionType),
        },
        {
          ...oldEntry,
          campaignConversionType: this.ucwords(oldEntry.campaignConversionType),
        },
        arrayChanges,
        "campaignConversionType",
        "overrideConversionType",
      );
    } else {
      line["conversionType"] = null;
    }
  }

  /**
   * Column: Jobs and Days Remaining
   * @param line
   * @param campaign
   * @param oldEntry
   * @param arrayChanges
   * @returns
   */
  async setJobsAndDaysRemainingLines(
    line: any,
    campaign: any,
    oldEntry: any = {},
    arrayChanges: any = [],
  ) {
    line["jobs"] = this.checkOverrideOldNewValue(
      campaign,
      oldEntry,
      arrayChanges,
      "xmlNumJobs",
      "",
    );
    line["daysRemaining"] = this.checkOverrideOldNewValue(
      campaign,
      oldEntry,
      arrayChanges,
      "estimatedBudgetDaysRemaining",
      "",
    );
  }

  /**
   * Check the duplicated campaigns
   * @param campaignsList
   * @returns
   */
  async checkDuplicatedCampaigns(campaignsList: any[]) {
    if (!Array.isArray(campaignsList)) {
      throw new HttpException("Expected an array for campaignsList", HttpStatus.BAD_REQUEST);
    }

    // Get list of campaignId with duplicated campaigns for CPA and CPC
    const filteredCampaignId = await this.findFilteredCampaigns(campaignsList);

    // check if there’s another campaign from the auto_campaign_api bucket with the same campaign_id as this one
    // and the last campaign_sponsorship_ended = '0'
    const duplicatedApiCampaignIdsResult =
      filteredCampaignId.length > 0
        ? await this.externalCampaignRepository.checkDuplicatedCampaigns(filteredCampaignId)
        : [];

    if (duplicatedApiCampaignIdsResult.length > 0) {
      return duplicatedApiCampaignIdsResult;
    } else {
      return [];
    }
  }

  /**
   * List of campaignId with duplicated campaigns for CPA and CPC
   * @param campaignsList
   * @returns
   */
  async findFilteredCampaigns(campaignsList: any[]): Promise<any> {
    if (!Array.isArray(campaignsList)) {
      throw new HttpException("Expected an array for campaignsList", HttpStatus.BAD_REQUEST);
    }

    // Define the filter criteria
    const actionFilter = ["-", "not allowed"];
    const campaignStatus = "active";
    const sponsorshipEnded = "";

    // Filter the provided campaignsList based on the criteria
    const filteredCampaigns = campaignsList.filter((campaign) => {
      return (
        !actionFilter.includes(campaign.action) &&
        campaign.campaignStatus === campaignStatus &&
        campaign.campaignSponsorshipEnded === sponsorshipEnded
      );
    });

    // Extract and sort the campaign IDs
    const campaignIdList = filteredCampaigns
      .map((campaign) => campaign.campaignId)
      .sort((a, b) => a - b);

    return campaignIdList;
  }

  /**
   * Validate if feedcode exist and generate links
   * @param campaign
   * @param line
   * @param duplicatedApiCampaignIds
   */
  async validateFeedcodeAndGenerateLinks(
    campaign: any,
    line: any,
    duplicatedApiCampaignIds: any[],
  ) {
    const generateAction = await this.generateLinks(
      campaign.talentFeedcode,
      campaign.talentCampaign,
      campaign.apiCampaignId,
      campaign.accountCurrencyName,
      campaign.source,
      campaign.accountBudget,
      campaign.accountName,
    );

    const action = generateAction.action;
    const params = generateAction.params;

    switch (action) {
      case "feedcode":
        line["action"] = !duplicatedApiCampaignIds.includes(campaign.apiCampaignId)
          ? { actionName: "Create Feedcode", link: action, params }
          : "";
        break;

      case "account":
        line["action"] = !duplicatedApiCampaignIds.includes(campaign.apiCampaignId)
          ? { actionName: "Create Account", link: action, params }
          : "";
        break;

      case "campaign":
        line["action"] = !duplicatedApiCampaignIds.includes(campaign.apiCampaignId)
          ? { actionName: "Create Campaign", link: action, params }
          : "";
        break;

      default:
        line["action"] = !duplicatedApiCampaignIds.includes(campaign.apiCampaignId)
          ? { actionName: action }
          : "";
        break;
    }
  }

  /**
   * Return the value for the line["Country"] on entries, running or paused table in the Ingested Campaigns' dashboard.
   * @param campaign
   * @returns
   */
  countryLineForDashboard(campaign: any) {
    const country = campaign.campaignCountry.split(",");
    let lineCountry: any = {};
    // If there's more than one country or "All Countries" -> lineCountry = "All Countries" (e.g., country = "All Countries" or "United States,Canada")
    if (country.length > 1 || this.ucwords(country[0]) === "All Countries") {
      lineCountry = "All Countries";
    } else if (Object.keys(CountryList).includes(country[0])) {
      // If campaignCountry is a valid key in the CountryList enum -> lineCountry = key (e.g., country = "us")
      lineCountry = country[0];
    } else {
      // If campaignCountry is a valid country in the CountryList enum -> lineCountry = value (e.g., country = "Canada")
      // if not, we get the companyCountry -> lineCountry = campaign.companyCountry
      const reverseCountryList: { [key: string]: string } = Object.fromEntries(
        Object.entries(CountryList).map(([key, value]) => [value, key]),
      );
      const isValidCountry = country in reverseCountryList;
      lineCountry =
        campaign.campaignCountry && isValidCountry
          ? campaign.campaignCountry
          : campaign.companyCountry;
    }
    return lineCountry;
  }

  /**
   * Function to get the Budget Type and the Budget to show the values on the dashboard (entries table and history panel).
   * @param line Line array to store the new values line["Budget Type"] and line["Budget"].
   * @param apiClientName Api client name(Ex: "appcast").
   * @param campaignBudgetType Campaign Budget Type (Ex: "monthly", "daily").
   * @param campaignBudgetValue Campaign Budget Value (Ex: "no limit", "77", "2000000").
   * @param campaignMonthlyBudget Campaign Monthly Budget (Ex: "no limit", "77", "2000000").
   * @param campaignWeeklyBudget Campaign Weekly Budget (Ex: "no limit", "77", "2000000").
   */
  getBudgetTypeAndBudgetToShow(
    line: any,
    apiClientName: string,
    campaignBudgetType: string = "",
    campaignBudgetValue: string = "",
    campaignMonthlyBudget: string = "",
    campaignWeeklyBudget: string = "",
  ): void {
    // Parameter validation
    if (!apiClientName.trim()) {
      throw new HttpException("'apiClientName' is required", HttpStatus.BAD_REQUEST);
    }

    let budgetType: string;
    let budget: string;

    // Check for budget type and value
    if (campaignBudgetType) {
      budgetType = campaignBudgetType;
      budget = campaignBudgetValue;
    } else if (
      apiClientName === "appcast" &&
      campaignWeeklyBudget &&
      +campaignWeeklyBudget >= 1 &&
      +campaignWeeklyBudget <= 2000000
    ) {
      // Appcast client with valid weekly budget
      budgetType = "weekly";
      budget = campaignWeeklyBudget;
    } else {
      // Default to monthly budget
      budgetType = "monthly";
      budget = campaignMonthlyBudget;
      if (budget === "no limit") {
        budget = "2000000";
      }
    }

    // Update the `line` object directly
    line["budgetType"] = budgetType === "" ? null : budgetType;
    line["budget"] = budget === "" ? null : budget;
  }

  /**
   * Converts the first character of each word in a string to uppercase (ucwords("ingested campaigns") = "Ingested Campaigns").
   * @param str
   * @returns
   */
  ucwords(str: string): string {
    return str
      .toLowerCase() // Ensure the string is in lowercase for consistency
      .split(" ") // Split the string into an array of words
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1)) // Convert the first letter of each word to uppercase
      .join(" "); // Join the words back into a single string
  }

  /**
   * Compare newEntry with the oldEntry to take only the variables with differences.
   * @param newEntry
   * @param oldEntry
   * @returns
   */
  compareEntries(newEntry: any, oldEntry: any): any {
    const changes: any = {};
    for (const key in newEntry) {
      // eslint-disable-next-line no-prototype-builtins
      if (newEntry.hasOwnProperty(key) && oldEntry.hasOwnProperty(key)) {
        if (newEntry[key] !== oldEntry[key]) {
          changes[key] = newEntry[key];
        }
      }
    }
    return changes;
  }

  /**
   * List all the non-automatic campaigns from the bucket external_campaigns_not_updated to manage the toggle and tag the campaign automatic or not.
   * @returns
   */
  async listNonAutoCampaigns() {
    return await this.notUpdatedRepository.listNonAutoCampaigns();
  }

  /**
   * Check if an api_campaign_id is included in the non automatic campaigns from the listNonAutoCampaigns.
   * @param nonAutomaticCampaigns
   * @param apiCampaignId
   * @returns
   */
  async isNonAutoCampaign(nonAutomaticCampaigns: string[], apiCampaignId: string) {
    return nonAutomaticCampaigns.includes(apiCampaignId);
  }

  /**
   * Format date to YYYY-MM-DD
   * @param dateString
   * @returns
   */
  formatDate(dateString: string): string {
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = `0${date.getMonth() + 1}`.slice(-2);
    const day = `0${date.getDate()}`.slice(-2);
    return `${year}-${month}-${day}`;
  }

  /**
   * Checks if there is a duplicated campaign based on the provided campaign ID and API campaign ID.
   *
   * @param campaignId - The ID of the campaign to check for duplication.
   * @param apiCampaignId - The API campaign ID to check against.
   * @throws HttpException - If either campaignId or apiCampaignId is missing or empty.
   * @returns A boolean indicating whether a duplicated campaign exists and is not sponsored.
   */
  async duplicatedCampaign(campaignId: string, apiCampaignId: string) {
    // Validate that both campaignId and apiCampaignId are provided and not empty.
    if (campaignId.trim() === "" || apiCampaignId.trim() === "") {
      throw new HttpException("campaignId or apiCampaignId are required", HttpStatus.BAD_REQUEST);
    }

    // Query the repository to check for a duplicated campaign.
    const result = await this.externalCampaignRepository.duplicatedCampaign(
      campaignId,
      apiCampaignId,
    );

    // Determine if the result is a duplicated campaign and is not sponsored.
    const duplicatedCampaign: boolean = result != null && !result.campaignSponsorshipEnded;

    // Return whether a duplicated campaign exists.
    return duplicatedCampaign;
  }

  /**
   * Pauses a campaign in the Talent system based on the provided API campaign ID and status.
   *
   * @param apiCampaignId - The API campaign ID to identify the campaign in the Talent system.
   * @param status - The status to be applied to the campaign (e.g., "paused").
   * @throws HttpException - If either apiCampaignId or status is missing or empty.
   * @returns A boolean indicating whether the campaign was successfully paused in the Talent system.
   */
  async pausedCampaignTalent(apiCampaignId: string, status: string) {
    // Validate that both apiCampaignId and status are provided and not empty.
    if (apiCampaignId.trim() === "" || status.trim() === "") {
      throw new HttpException("campaignId or status are required", HttpStatus.BAD_REQUEST);
    }

    // Retrieve the campaign information in Talent using the apiCampaignId.
    const campaignInfo = await this.campaignsService.findOneByApiCampaignId(apiCampaignId);
    const talentCampaignId = campaignInfo?.id;

    let result;
    // If a valid campaign ID is found in Talent, update its status to "paused" and set "paused" flag.
    if (typeof talentCampaignId === "number") {
      result = await this.campaignsService.update(
        talentCampaignId,
        {
          status: CampaignStatus.paused,
          paused: 1,
          updatedBy: 80010, //This is the hardcoded external user made for this.
        },
        "dev",
      );
    }

    // Return true if the campaign was successfully paused, otherwise false.
    return typeof result?.currentCampaign?.id === "number";
  }

  /**
   * Method to retrieve the actual spent budget of an existing campaign.
   *
   * @param apiCampaignId - The API campaign ID to identify the campaign.
   * @throws HttpException - If apiCampaignId is missing or empty, or if no campaign is found.
   * @returns The actual budget spent for the campaign, based on its budget type.
   */
  async getCampaignBudgetSpent(apiCampaignId: string) {
    // Validate that the apiCampaignId is provided and not empty.
    if (apiCampaignId.trim() === "") {
      throw new HttpException("campaignId or status are required", HttpStatus.BAD_REQUEST);
    }

    // Retrieve the campaign information, including its budget details, using the apiCampaignId.
    const campaignInfo = await this.campaignsService.findOneByApiCampaignId(apiCampaignId, [
      "campaignsBudget",
    ]);

    // Validate that the campaign information was found.
    if (!campaignInfo) {
      throw new HttpException("No results found", HttpStatus.NOT_FOUND);
    }

    // Assign the spent budget based on the campaign's budget type.
    let budgetSpent;
    if (campaignInfo.campaignsBudget.budgetType === "daily") {
      budgetSpent = campaignInfo.campaignsBudget.spentToday;
    } else {
      budgetSpent = campaignInfo.campaignsBudget.monthlySpent;
    }

    // Return the actual budget spent for the campaign.
    return budgetSpent;
  }

  /**
   * Toggles the automatic status of a campaign.
   *
   * Depending on the current status of the campaign identified by `apiCampaignId`,
   * this method either stops or starts the automation process for that campaign.
   *
   * @param apiCampaignId - The ID of the campaign in the API.
   * @returns A string message indicating whether the automation was stopped or started.
   * @throws HttpException - If any of the required parameters are missing or if an error occurs during the status switch.
   */
  async switchAutomaticStatus(apiCampaignId: string) {
    // Validate Params: Ensure all parameters are provided and are not empty strings
    if (apiCampaignId.trim() === "") {
      throw new HttpException("apiCampaignId is required", HttpStatus.BAD_REQUEST);
    }

    // Check if the apiCampaignId is already in the "not updated" bucket
    const actualRegistry = await this.notUpdatedService.find("apiCampaignId", apiCampaignId.trim());

    // If the apiCampaignId is not found in the bucket, stop the automation and add it to the bucket
    if (!actualRegistry) {
      // Prepare the data to insert into the "not updated" bucket
      const insert: CreateNotUpdatedDto = {
        apiCampaignId: apiCampaignId,
        created: new Date(), // This would typically be a timestamp
        createdBy: 80010, // Example user ID, likely representing the system or admin
      };

      // Attempt to stop the automation for the given campaign
      const switchStop = await this.automaticUpdateStop(apiCampaignId, insert);
      if (switchStop) {
        return "Automation is going to stop";
      } else {
        throw new HttpException("Something went wrong with the switch", HttpStatus.BAD_REQUEST);
      }
    } else {
      // If the apiCampaignId is found in the bucket, remove it and start the automation again
      const switchStart = await this.automaticUpdateStart(apiCampaignId);
      if (switchStart) {
        return "Automation is going to start";
      } else {
        throw new HttpException("Something went wrong with the switch", HttpStatus.BAD_REQUEST);
      }
    }
  }

  /**
   * Stops the automatic updates for a specific campaign.
   *
   * This method adds the campaign to a "not updated" bucket, effectively stopping
   * any further automatic updates. It also logs this action in the `auto_campaign_api`
   * table to reflect the change in the history panel.
   *
   * @param apiCampaignId - The ID of the campaign in the API.
   * @param insert - The data to be inserted into the "not updated" bucket to stop the automation.
   * @returns The new entry in the `auto_campaign_api` table if successful, or `false` if the operation fails.
   * @throws HttpException - If the `apiCampaignId` is not provided.
   */
  async automaticUpdateStop(apiCampaignId: string, insert: CreateNotUpdatedDto) {
    // Validate Params: Ensure the apiCampaignId is provided and is not an empty string
    if (apiCampaignId.trim() === "") {
      throw new HttpException("apiCampaignId is required", HttpStatus.BAD_REQUEST);
    }

    // Insert the campaign into the "not updated" bucket to stop the automation
    await this.notUpdatedService.create(insert, insert.createdBy);

    // Retrieve the current campaign information from the 'auto_campaign_api' table
    const campaignInfo = await this.findOne("apiCampaignId", apiCampaignId.trim());

    // Validate that the current action is not '-'. If it is, proceed with stopping the automation
    if (campaignInfo?.action !== "-" || !campaignInfo) {
      return false;
    }

    // Modify the campaign information to reflect the 'toggleOff' action
    const campaignInfoModified = this.convertKeysToSnakeCase({
      ...campaignInfo,
      action: "toggleOff",
    });

    // Create a new entry in the 'auto_campaign_api' table with the updated action
    const newEntry = await this.createAutoCampaignEntry(campaignInfoModified);

    // Return the new entry if the operation was successful
    if (newEntry) {
      return newEntry;
    }
  }

  /**
   * Starts the automatic updates for a specific campaign.
   *
   * This method reactivates the automation for a campaign by removing it from the "not updated" bucket
   * and logs the changes in the `auto_campaign_api` table to reflect this in the history panel.
   *
   * @param apiCampaignId - The ID of the campaign in the API.
   * @returns The final entry in the `auto_campaign_api` table with the action set to `"-"` if successful, or `false` if the operation fails.
   * @throws HttpException - If the `apiCampaignId` is not provided.
   */
  async automaticUpdateStart(apiCampaignId: string) {
    // Validate Params: Ensure the apiCampaignId is provided and is not an empty string
    if (apiCampaignId.trim() === "") {
      throw new HttpException("apiCampaignId is required", HttpStatus.BAD_REQUEST);
    }

    // Remove the campaign from the "not updated" bucket to reactivate automation
    await this.notUpdatedService.remove(apiCampaignId);

    // Retrieve the current campaign information from the 'auto_campaign_api' table
    const campaignInfo = await this.findOne("apiCampaignId", apiCampaignId);

    // Validate that the current action is 'toggleOff' or '-'. If it is, proceed with reactivating the automation
    if ((campaignInfo?.action !== "toggleOff" && campaignInfo?.action !== "-") || !campaignInfo) {
      return false;
    }

    // Modify the campaign information to reflect the 'toggleOn' action
    const campaignInfoModified = this.convertKeysToSnakeCase({
      ...campaignInfo,
      action: "toggleOn",
    });
    const newEntry = await this.createAutoCampaignEntry(campaignInfoModified);

    // If the new entry is successfully created, proceed to reset the action back to '-'
    if (newEntry) {
      const campaignInfoModifiedNewEntry = this.convertKeysToSnakeCase({
        ...campaignInfo,
        action: "-",
      });
      const newEntryDash = await this.createAutoCampaignEntry(campaignInfoModifiedNewEntry);

      // Return the final entry with the action set to '-'
      if (newEntryDash) {
        return newEntryDash;
      }
    } else {
      return false;
    }
  }

  /**
   * Function to check if an override exists in the database by apyCampaignId or by talentCampaign.
   * An override is valid when overrideStatus is true.
   * @param apiCampaignId
   * @param talentCampaign
   * @returns
   */
  async checkOverrideExistence(apiCampaignId: string = "", talentCampaign: string = "") {
    try {
      if (!apiCampaignId.trim() && !talentCampaign.trim()) {
        throw new HttpException(
          "Error: You must send at least an apiCampaignID or a talent_campaign.",
          HttpStatus.BAD_REQUEST,
        );
      }
      let field: string = "apiCampaignId";
      let value: string = apiCampaignId;
      if (!apiCampaignId.trim()) {
        field = "campaignName";
        value = talentCampaign;
      }
      let response;
      const result = await this.helperService.findOneOverride(field, value);

      if (!result || result === undefined || !result.overrideStatus) {
        response = { exists: false, overrideData: {} };
      } else if (result.overrideStatus) {
        response = { exists: true, overrideData: result };
      }

      return response;
    } catch (error: any) {
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  /**
   * This method receives the array by reference with all auto campaign's data either for updating or creating it
   * and checks if there's an override in the DB, for replacing the variables involved in it.
   * @param campaignData Array with campaign data (by reference)
   */
  async applyOverrideToAutoCampaign(campaignData: any): Promise<any> {
    if (Object.keys(campaignData).length < 1 || !campaignData.apiCampaignId) {
      return campaignData;
    }

    // Checking Override existence for applying its changes
    const overrideData = await this.checkOverrideExistence(campaignData.apiCampaignId);
    // This will not happen when the override doesn't exists, only when there was an error while executing the function
    if (!overrideData) {
      return campaignData;
    }

    // In case the override exists, we must
    if (overrideData.exists) {
      return this.modifyOverrideData(overrideData.overrideData, campaignData);
    }

    return campaignData;
  }

  /**
   *
   * @param overrideData
   * @param campaignData
   */
  modifyOverrideData(overrideData: any, campaignData: any) {
    const newOverrideData = overrideData as Override;
    const data = JSON.parse(newOverrideData.overrideData.replace(/\\/g, ""));

    // Now we must determine which individual value the override needs to modify
    if (data.delivery) {
      campaignData.campaignDelivery = data.delivery;
    }
    if (data.dateStart) {
      campaignData.campaignStartDate = data.dateStart;
    }
    if (data.dateEnd) {
      campaignData.campaignEndDate = data.dateEnd;
    }
    if (data.budgetType) {
      campaignData.campaignBudgetType = data.budgetType;
    }
    if (data.budget) {
      campaignData.campaignBudgetValue = data.budget;
    }
    if (data.conversionType) {
      campaignData.campaignConversionType = data.conversionType;
    }
    if (data.conversionTargetCost) {
      campaignData.campaignTargetCost = data.conversionTargetCost;
    }

    return campaignData;
  }

  /**
   * Sends email notifications based on the type of email and campaign status.
   *
   * This method retrieves a list of campaigns that need to be notified and sends email notifications
   * based on certain conditions. It filters campaigns according to the `notified` status and other criteria
   * depending on the type of email. If the `emailType` is "validations," it filters campaigns with specific
   * statuses and conditions. Otherwise, it sends notifications for all campaigns that are allowed for
   * notification. The campaigns are sorted by the number of jobs in descending order before preparing
   * and sending emails.
   *
   * @param emailType - The type of email to send, default is "validations".
   * @throws If no campaigns are found that need notification.
   * @returns A promise that resolves with the updateNotified information.
   */
  async sendEmailNotifications(emailType: string = "validations") {
    // Get the list of campaigns to be notified and sort them by the number of jobs
    const campaigns = await this.externalCampaignRepository.listCampaigsToNotified(emailType);

    if (!campaigns || campaigns.length < 1) {
      // Throw error if no campaigns are found
      throw new HttpException("No entries to be notified were found", HttpStatus.NOT_FOUND);
    }

    // Sort campaigns by the number of jobs in descending order
    campaigns.sort((a, b) => Number(b.xml_num_jobs) - Number(a.xml_num_jobs));

    // Prepare emails and notifications update
    const { emails, updateNotified } = await this.prepareDaraForEmailsNotifications(campaigns);

    // Send email notifications and update the notified field
    await this.sendEmailNotification(emails, updateNotified);

    return { updateNotified }; // Return the updated notification information
  }

  /**
   * Sends email notifications to users based on the provided email data and updates the notified status.
   *
   * This method processes the email data by iterating through different types of emails (e.g., sendAccounts,
   * sendValidation, sendLiveCampaigns) and prepares each email with appropriate content. It then sends the
   * emails using an external endpoint and handles errors if the sending fails. The email content includes a
   * header, pre-body message, campaign data, and a footer with a call to action. After sending, it updates
   * the notification status of the campaigns.
   *
   * @param emails - A structured object containing email data for different types of notifications.
   * @param  updateNotified - An object containing the updated notification status for each campaign.
   * @returns A promise that resolves when the emails are sent.
   */
  async sendEmailNotification(emails: any, updateNotified: any) {
    // Loop through the email types (sendAccounts, sendValidation, sendLiveCampaigns, etc.)
    for (const [type, emailsByType] of Object.entries(emails) as [
      keyof EmailStructure,
      EmailsByType,
    ][]) {
      // Loop through each user and their associated email data
      for (const [userMe, clientData] of Object.entries(emailsByType)) {
        const cta = process.env.URL_EXTERNAL_DASHBOARD; // Call to action URL
        const subject = "Automatic Campaigns Update"; // Default email subject
        let preBody = ""; // Pre-body content
        const header = this.createHeaderEmailsNotifications(); // Header content
        const bodyToSent = this.createFeedcodesRowsInEmail(clientData); // Main email body content
        const footerBody = `<table style="max-width: 85%" align="center" role="presentation" aria-hidden="true" cellspacing="0" cellpadding="0" border="0">
                            <tr><td align="center" style='padding: 24px 20px 32px; border-top: 1px solid #F0F0F3; font-size: 14px; line-height: 1.6; color: #676767; font-weight: normal; font-family: Tahoma,sans-serif;'>
                            <a href="${cta}" class="hyper-btn">Access the Sales Dashboard</a></td</tr></table>`; // Footer with call to action

        // Determine the pre-body content based on the email type
        if (type === "sendAccounts") {
          preBody = this.createPrebodyEmailsNotifications("You have new accounts. Create them!");
        } else if (type === "sendValidation") {
          preBody = this.createPrebodyEmailsNotifications(
            "The following campaigns were NOT created on Talent as they do not respect the current campaigns minimum budgets.",
          );
        } else if (type === "sendLiveCampaigns") {
          preBody = this.createPrebodyEmailsNotifications("The following campaigns are now LIVE!");
        } else {
          preBody = this.createPrebodyEmailsNotifications(
            "You have new campaigns coming. Start spending!",
          );
        }

        // Construct the email data to be sent
        const emaiData: any = {
          email_address: userMe,
          template_id: 24,
          lang_loc: {
            language: "en",
            country: "ca",
          },
          data: {
            gnr_email_info: {
              from_name: "Talent.com",
              email_subject: subject,
              email_body: `${header}${preBody}${bodyToSent}${footerBody}`, // Full email content
            },
          },
        };

        // Send the email and handle any errors that occur during the process
        try {
          await this.sendEmailsEndpoint(emaiData, clientData, updateNotified, type);
        } catch (error: any) {
          console.log("Error updating notified status", updateNotified); // Log errors
        }
      }
    }
  }

  /**
   * Creates owner information for accounts based on provided IDs and owner types.
   *
   * This method determines the account owner, sales owner, and customer service (CS) owner based on the
   * input `idsAndTypes`, which contains user IDs and their corresponding owner types ("sales" or "cs").
   * If no IDs are provided, default values are returned. For each user, the method retrieves their email,
   * decrypts it, and assigns it as either the sales or CS owner based on the type. If no specific user
   * is found, the default account owner is returned.
   * @param  idsAndTypes - An array of objects with user IDs and owner types.
   * @returns A promise that resolves to an object containing the account owner, sales owner, and CS owner.
   */
  async createOwners(idsAndTypes: { id: number; accountOwnerType: string }[]) {
    const accountOwner = process.env.EXTERNAL_DEFAULT_ACCOUNT_OWNER ?? "<EMAIL>"; // Default account owner
    // If no users are provided, return default owner information
    if (Object.keys(idsAndTypes).length === 0 || idsAndTypes.length == 0) {
      return {
        accountOwner,
        accountSalesOwner: accountOwner,
        accountCSOwner: accountOwner,
      };
    }

    let accountSalesOwner = ""; // Placeholder for sales owner
    let accountCSOwner = ""; // Placeholder for CS owner

    // Loop through the provided user IDs and account owner types
    for (const user of idsAndTypes) {
      const userInfo = await this.usersService.findOneBy(user.id); // Retrieve user information
      const modifiedBy = userInfo?.email ? privacy.cipher.decrypt(userInfo?.email) : accountOwner; // Decrypt email or use default

      // Assign the decrypted email based on the account owner type
      if (user.accountOwnerType == "sales") {
        accountSalesOwner = modifiedBy;
      } else if (user.accountOwnerType == "cs") {
        accountCSOwner = modifiedBy;
      }
    }

    // Return the account owner, sales owner, and CS owner
    return {
      accountOwner,
      accountSalesOwner,
      accountCSOwner,
    };
  }

  /**
   * Validates and retrieves the account owners (sales, customer service, and general) for a given account.
   *
   * This method checks if the account has associated owners. If not, it returns empty owner data. If the
   * account has owners, it extracts their IDs and owner types (sales or CS) and passes them to the
   * `createOwners` method, which returns the corresponding owners. The result includes the account's sales
   * owner, customer service owner, and general owner.
   *
   * @param  account - The account object that may contain owner information.
   * @returns A promise that resolves to an object containing the sales, CS, and general account owners.
   */
  async validateAccountOwners(account: any) {
    let idsAndtypes: any;

    // Check if the account has owners; if not, set idsAndtypes to an empty array
    if (!account?.accountsOwners) {
      idsAndtypes = [];
    } else {
      // Extract the IDs and owner types from the account's owners
      idsAndtypes =
        account.accountsOwners.map((item: any) => ({
          id: item.userId,
          accountOwnerType: item.accountOwnerType,
        })) ?? {};
    }

    // Call createOwners to get the accountSalesOwner, accountCSOwner, and accountOwner
    const { accountSalesOwner, accountCSOwner, accountOwner } =
      await this.createOwners(idsAndtypes);

    // Return the owners' information
    return { accountSalesOwner, accountCSOwner, accountOwner };
  }

  /**
   * Generates the HTML structure for the header section of an email notification.
   *
   * This method returns a string that represents the HTML table structure for the
   * header of email notifications related to automatic campaign updates. The header
   * is designed to be responsive and visually appealing, using specific colors, fonts,
   * and padding to enhance the presentation of the email.
   *
   * @returns HTML string representing the email header for automatic campaigns update notifications.
   */
  createHeaderEmailsNotifications(): string {
    return `<!-- Header -->
  <tr>
    <td>
      <table width="100%" bgcolor="#F0F0F3" align="center"
             style="color: #691F74; border-collapse: collapse;" role="presentation" 
             aria-hidden="true" cellspacing="0" cellpadding="0" border="0">
        <tr bgcolor="#691F74">
          <td align="center" style='padding: 30px 10px 30px 10px; 
                                   border-radius: 8px 8px 0px 0px; 
                                   color: white; font-size: 24px; 
                                   line-height: 36px; font-family: Tahoma, sans-serif;'>
            Automatic Campaigns Update
          </td>
        </tr>
      </table>
    </td>
  </tr>
  <!-- Header -->`;
  }

  /**
   * Creates the pre-body section of an email notification.
   *
   * This method generates an HTML string for the pre-body content of an email notification.
   * The pre-body is styled as a centered heading with specific font properties and color,
   * and can display any custom text passed as a parameter.
   *
   * @param  text - The custom text to be displayed in the pre-body section of the email.
   * @returns  HTML string representing the styled pre-body section with the provided text.
   */
  createPrebodyEmailsNotifications(text: string): string {
    return `<h1 style='text-align: center; color: #E34B31; font-size: 13.5pt; line-height: 48px; font-family: Tahoma, sans-serif;'>${text}</h1>`;
  }

  /**
   * Prepares data for email notifications based on the provided campaigns.
   *
   * This asynchronous method processes a list of campaigns to determine which email notifications
   * need to be sent. It initializes the necessary email structures and notification tracking,
   * processes each campaign based on its `notified` status, and populates email and notification
   * data accordingly.
   *
   * The method categorizes campaigns into two types: those ready to be sent ("toSend") and
   * those related to live campaigns ("toSendLive"). It then processes the campaigns, assigns
   * ownership details (sales, customer service, and account owners), and updates the notification
   * tracking structure.
   *
   * @param  campaigns - An array of campaign objects to be processed.
   * @returns A promise that resolves to an object containing the generated email structures and
   * updated notification tracking for the processed campaigns.
   */
  async prepareDaraForEmailsNotifications(
    campaigns: any,
  ): Promise<{ emails: EmailStructure; updateNotified: UpdateNotified }> {
    const emails: EmailStructure = this.initializeEmailStructure();
    const updateNotified: UpdateNotified = this.initializeUpdateNotified();

    const feedCodes: string[] = Array.from(
      new Set(campaigns.map((c: any) => c.talent_feedcode).filter(Boolean)),
    );

    const accounts = await this.accountswithOwners(feedCodes);

    for (const campaign of campaigns) {
      const client = campaign.api_client_name;
      const feedcode = campaign.talent_feedcode;
      const apiCampaignId = campaign.api_campaign_id;
      const toSend = campaign.notified;
      const action = campaign.action;

      // Retrieve account details based on feedcode
      let account = accounts.find((a) => a.account.feedcode === feedcode);
      if (!account) {
        const accountOwner = process.env.EXTERNAL_DEFAULT_ACCOUNT_OWNER ?? "<EMAIL>";
        account = {
          account: undefined,
          accountSalesOwner: accountOwner,
          accountCSOwner: accountOwner,
          accountOwner,
        };
      }

      const campaignInfo = { campaign, client, apiCampaignId, feedcode };

      // Process campaigns marked as "toSend"
      if (toSend === "toSend") {
        await this.processCampaignAction(
          action,
          account,
          campaignInfo,
          emails,
          feedCodes,
          updateNotified,
        );
      } else if (toSend === "toSendLive") {
        // Process live campaigns marked as "toSendLive"
        this.toSendLiveProcess(
          campaign,
          emails,
          updateNotified,
          account.accountSalesOwner,
          account.accountCSOwner,
        );
        updateNotified.yesLiveCampaign[campaign.id] = apiCampaignId;
      }
    }

    return { emails, updateNotified };
  }

  /**
   * Processes campaign actions and updates the corresponding email notifications.
   *
   * This asynchronous method handles various campaign actions, including "Create Feedcode"
   * and "Create Account," and updates email notification structures based on the action type.
   * It uses the provided account and campaign information to determine whether new account
   * or validation notifications need to be sent, and updates the `emails`, `feedCodes`, and
   * `updateNotified` data structures accordingly.
   *
   * For campaigns marked as "not allowed," validation notifications are prepared and sent
   * to the account sales owner and, if applicable, the customer service owner.
   *
   * @param  action - The action type for the campaign (e.g., "Create Feedcode", "Create Account", "not allowed").
   * @param  accountInfo - Object containing account information, including the account itself, and its owners.
   * @param  accountInfo.account - The account data related to the campaign.
   * @param accountInfo.accountOwner - The owner of the account.
   * @param  accountInfo.accountSalesOwner - The sales owner of the account.
   * @param  accountInfo.accountCSOwner - The customer service owner of the account.
   * @param  campaignInfo - Object containing campaign-related information.
   * @param  campaignInfo.campaign - The campaign being processed.
   * @param  campaignInfo.client - The client associated with the campaign.
   * @param  campaignInfo.apiCampaignId - The API campaign ID.
   * @param  campaignInfo.feedcode - The feedcode related to the campaign.
   * @param  emails - The structure where email notifications are prepared.
   * @param  feedCodes - Array to track unique feedcodes for email notifications.
   * @param  updateNotified - Object tracking which campaigns have been notified.
   * @returns A promise that resolves when the campaign action is processed.
   */
  async processCampaignAction(
    action: string,
    accountInfo: AccountWithOwners,
    campaignInfo: {
      campaign: any;
      client: string;
      apiCampaignId: string;
      feedcode: string;
    },
    emails: EmailStructure,
    feedCodes: string[],
    updateNotified: UpdateNotified,
  ) {
    const { account, accountOwner, accountSalesOwner, accountCSOwner } = accountInfo;
    const { campaign, client, apiCampaignId, feedcode } = campaignInfo;

    switch (action) {
      case "account":
        // Skip if the account already exists
        if (account && Object.entries(account).length > 0) return;

        // Update send accounts with new account or campaign info
        this.updateSendAccounts(emails, accountOwner, client, campaign, apiCampaignId, feedcode);

        // Ensure the feedcode is added to send accounts only once
        if (!feedCodes.includes(feedcode)) {
          this.addFeedcodeToSendAccounts(emails, accountOwner, client, feedcode);
          feedCodes.push(feedcode);
        }

        // Mark the campaign as notified for account creation
        updateNotified.yesAccount[campaign.id] = apiCampaignId;
        break;

      case "not allowed": {
        // Prepare validation data for "not allowed" campaigns
        const data = this.prepareValidationData(campaign);

        // Send validation emails to the sales owner
        this.updateSendValidation(
          emails,
          accountSalesOwner,
          client,
          campaign,
          apiCampaignId,
          feedcode,
          data,
        );

        // If CS owner is different, send validation email to CS owner as well
        if (accountCSOwner !== accountSalesOwner) {
          this.updateSendValidation(
            emails,
            accountCSOwner,
            client,
            campaign,
            apiCampaignId,
            feedcode,
            data,
          );
        }

        // Mark the campaign as notified for validation
        updateNotified.yesValidation[campaign.id] = apiCampaignId;
        break;
      }
    }
  }

  /**
   * Processes the sending of live campaign notifications via email.
   *
   * This method prepares and sends notifications for live campaigns to the respective
   * account owners. It extracts necessary campaign information and prepares the data
   * for live campaigns, then updates the email structure for both the account sales owner
   * and the customer service owner if they are different.
   *
   * @param  campaign - The campaign object containing relevant details for processing.
   * @param  emails - The structure where email notifications are prepared.
   * @param  updateNotified - Object tracking which campaigns have been notified.
   * @param  accountSalesOwner - The sales owner responsible for the account.
   * @param  accountCSOwner - The customer service owner for the account.
   * @returns  This method does not return any value.
   */
  toSendLiveProcess(
    campaign: any,
    emails: EmailStructure,
    updateNotified: UpdateNotified,
    accountSalesOwner: string,
    accountCSOwner: string,
  ) {
    const {
      api_client_name: client,
      api_campaign_id: apiCampaignId,
      talent_feedcode: feedcode,
    } = campaign;

    // Prepare the data for the live campaign notification
    const data = this.prepareLiveCampaignData(campaign);

    // Update email structure for the sales owner
    this.updateSendLiveCampaigns(
      emails,
      accountSalesOwner,
      client,
      campaign,
      apiCampaignId,
      feedcode,
      data,
    );

    // If the customer service owner is different, also send a notification to them
    if (accountCSOwner !== accountSalesOwner) {
      this.updateSendLiveCampaigns(
        emails,
        accountCSOwner,
        client,
        campaign,
        apiCampaignId,
        feedcode,
        data,
      );
    }
  }

  /**
   * Initializes and returns the email structure for notifications.
   *
   * This private method creates and returns a new `EmailStructure` object,
   * which serves as a template for organizing various types of email notifications.
   * The structure includes fields for sending account-related notifications,
   * validation notifications, and live campaign notifications, each initialized as empty objects.
   *
   * @returns  A newly created email structure object with empty fields
   * for `sendAccounts`, `sendValidation`, and `sendLiveCampaigns`.
   */
  private initializeEmailStructure(): EmailStructure {
    return {
      sendAccounts: {},
      sendValidation: {},
      sendLiveCampaigns: {},
    };
  }

  /**
   * Initializes and returns the update notification tracking structure.
   *
   * This private method creates and returns a new `UpdateNotified` object,
   * which is used to keep track of the notification statuses for various campaign
   * actions. The structure includes fields for tracking which accounts have
   * received notifications, which validations have been acknowledged, and
   * which live campaigns have been notified, each initialized as empty objects.
   *
   * @returns A newly created update notification structure object
   * with empty fields for `yesAccount`, `yesValidation`, and `yesLiveCampaign`.
   */
  private initializeUpdateNotified(): UpdateNotified {
    return {
      yesAccount: {},
      yesValidation: {},
      yesLiveCampaign: {},
    };
  }

  /**
   * Updates the email structure for sending account notifications.
   *
   * This private method modifies the `emails` structure to ensure that
   * the account owner's email notification data is initialized and updated
   * with the relevant campaign information. It creates nested objects
   * for each account owner and client if they do not already exist,
   * and appends the campaign ID and API campaign ID to the list of IDs
   * associated with that client.
   *
   * @param  emails - The structure where email notifications are stored.
   * @param  accountOwner - The owner of the account related to the campaign.
   * @param  client - The client associated with the campaign.
   * @param  campaign - The campaign object containing relevant details.
   * @param  apiCampaignId - The API campaign ID associated with the campaign.
   * @returns  This method does not return any value.
   */
  private updateSendAccounts(
    emails: EmailStructure,
    accountOwner: string,
    client: string,
    campaign: any,
    apiCampaignId: string,
    feedcode: string,
  ): void {
    // Initialize the structure for the account owner if it does not exist
    if (!emails.sendAccounts[accountOwner]) {
      emails.sendAccounts[accountOwner] = {};
    }

    // Initialize the structure for the client under the account owner if it does not exist
    if (!emails.sendAccounts[accountOwner][client]) {
      emails.sendAccounts[accountOwner][client] = {
        client,
        ids: [],
        feedcodes: [{ name: "", rows: [] }],
      };
    }

    // avoid insert the same feedcode if is alredy in the rows
    if (
      !emails.sendAccounts[accountOwner][client].feedcodes[0].rows.some(
        (item) => item.Feedcode == feedcode,
      )
    ) {
      emails.sendAccounts[accountOwner][client].feedcodes[0].rows.push({ Feedcode: feedcode });
    }

    // Append the campaign ID and API campaign ID to the list of IDs for the client
    emails.sendAccounts[accountOwner][client].ids.push({ [campaign.id]: apiCampaignId });
  }

  /**
   * Adds a feedcode to the email structure for sending account notifications.
   *
   * This private method appends a specified feedcode to the list of feedcodes
   * associated with a particular client under a specific account owner.
   * It assumes that the account owner and client structures have already been initialized
   * in the `emails` structure.
   *
   * @param  emails - The structure where email notifications are stored.
   * @param  accountOwner - The owner of the account related to the feedcode.
   * @param  client - The client associated with the feedcode.
   * @param  feedcode - The feedcode to be added to the client's notification data.
   * @returns  This method does not return any value.
   */
  private addFeedcodeToSendAccounts(
    emails: EmailStructure,
    accountOwner: string,
    client: string,
    feedcode: string,
  ): void {
    // Add the feedcode to the feedcodes array for the specified client under the account owner
    emails.sendAccounts[accountOwner][client].feedcodes[0].rows.push({ Feedcode: feedcode });
  }

  /**
   * Updates the email structure for sending validation notifications.
   *
   * This private method modifies the `emails` structure to ensure that
   * the validation notification data for a specific account owner and client
   * is initialized and updated with the relevant campaign information.
   * It creates nested objects for each account owner and client if they do not already exist,
   * and appends the campaign ID, API campaign ID, and additional data associated
   * with a specific feedcode to the corresponding lists.
   *
   * @param  emails - The structure where email notifications are stored.
   * @param accountOwner - The owner of the account related to the validation.
   * @param  client - The client associated with the validation.
   * @param  campaign - The campaign object containing relevant details.
   * @param  apiCampaignId - The API campaign ID associated with the campaign.
   * @param  feedcode - The feedcode related to the validation.
   * @param  data - Additional data to be included in the validation notification.
   * @returns  This method does not return any value.
   */
  private updateSendValidation(
    emails: EmailStructure,
    accountOwner: string,
    client: string,
    campaign: any,
    apiCampaignId: string,
    feedcode: string,
    data: any,
  ): void {
    // Initialize the structure for the account owner if it does not exist
    if (!emails.sendValidation[accountOwner]) {
      emails.sendValidation[accountOwner] = {};
    }

    // Initialize the structure for the client under the account owner if it does not exist
    if (!emails.sendValidation[accountOwner][client]) {
      emails.sendValidation[accountOwner][client] = { client, ids: [], feedcodes: {} };
    }

    // Initialize the structure for the feedcode if it does not exist
    if (!emails.sendValidation[accountOwner][client].feedcodes[feedcode]) {
      emails.sendValidation[accountOwner][client].feedcodes[feedcode] = {
        name: feedcode,
        rows: [],
      };
    }

    // Append the campaign ID and API campaign ID to the list of IDs for the client
    emails.sendValidation[accountOwner][client].ids.push({ [campaign.id]: apiCampaignId });

    // Append additional data related to the feedcode to the rows array
    emails.sendValidation[accountOwner][client].feedcodes[feedcode].rows.push(data);
  }

  /**
   * Updates the email structure for sending live campaign notifications.
   *
   * This private method modifies the `emails` structure to ensure that
   * the live campaign notification data for a specific account owner and client
   * is initialized and updated with the relevant campaign information.
   * It creates nested objects for each account owner and client if they do not already exist,
   * and appends the campaign ID, API campaign ID, and additional data associated
   * with a specific feedcode to the corresponding lists.
   *
   * @param  emails - The structure where email notifications are stored.
   * @param  accountOwner - The owner of the account related to the live campaign.
   * @param  client - The client associated with the live campaign.
   * @param  campaign - The campaign object containing relevant details.
   * @param apiCampaignId - The API campaign ID associated with the campaign.
   * @param  feedcode - The feedcode related to the live campaign.
   * @param data - Additional data to be included in the live campaign notification.
   * @returns  This method does not return any value.
   */
  private updateSendLiveCampaigns(
    emails: EmailStructure,
    accountOwner: string,
    client: string,
    campaign: any,
    apiCampaignId: string,
    feedcode: string,
    data: any,
  ): void {
    // Initialize the structure for the account owner if it does not exist
    if (!emails.sendLiveCampaigns[accountOwner]) {
      emails.sendLiveCampaigns[accountOwner] = {};
    }

    // Initialize the structure for the client under the account owner if it does not exist
    if (!emails.sendLiveCampaigns[accountOwner][client]) {
      emails.sendLiveCampaigns[accountOwner][client] = { client, ids: [], feedcodes: {} };
    }

    // Initialize the structure for the feedcode if it does not exist
    if (!emails.sendLiveCampaigns[accountOwner][client].feedcodes[feedcode]) {
      emails.sendLiveCampaigns[accountOwner][client].feedcodes[feedcode] = {
        name: feedcode,
        rows: [],
      };
    }

    // Append the campaign ID and API campaign ID to the list of IDs for the client
    emails.sendLiveCampaigns[accountOwner][client].ids.push({ [campaign.id]: apiCampaignId });

    // Append additional data related to the feedcode to the rows array
    emails.sendLiveCampaigns[accountOwner][client].feedcodes[feedcode].rows.push(data);
  }

  /**
   * Prepares validation data for a given campaign.
   *
   * This private method constructs an object containing essential
   * details related to a campaign that are needed for validation purposes.
   * It extracts relevant properties from the campaign object and formats them
   * in a structured way, providing default values for any missing fields.
   *
   * @param  campaign - The campaign object containing relevant details.
   * @returns  An object containing the validation data for the campaign.
   */
  private prepareValidationData(campaign: any): any {
    return {
      "Company Name": campaign.company_name, // Name of the company associated with the campaign
      "Client Campaign Name": campaign.campaign_name, // Name of the campaign as identified by the client
      "Client Campaign ID": campaign.campaign_id, // Unique identifier for the campaign
      "Budget Type": campaign.campaign_budget_type || "-", // Type of budget for the campaign; defaults to "-" if undefined
      Budget: campaign.campaign_budget_value || "-", // Budget value for the campaign; defaults to "-" if undefined
    };
  }

  /**
   * Prepares data for a given live campaign.
   *
   * This private method constructs an object containing key details
   * related to a live campaign. It extracts relevant properties from
   * the provided campaign object, organizing them in a structured format
   * for easier access and processing in live campaign notifications.
   *
   * @param  campaign - The campaign object containing relevant details.
   * @returns  An object containing the data for the live campaign.
   */
  private prepareLiveCampaignData(campaign: any): any {
    return {
      Campaign: campaign.talent_campaign, // Name of the talent campaign
      Delivery: campaign.campaign_delivery, // Delivery method or details for the campaign
      Country: campaign.campaign_country, // Country associated with the campaign
      BudgetType: campaign.campaign_budget_type, // Type of budget for the campaign
      BudgetValue: campaign.campaign_budget_value, // Value of the budget for the campaign
      CPA: campaign.campaign_cpa_goal, // Cost Per Acquisition (CPA) goal for the campaign
      Jobs: campaign.xml_num_jobs, // Number of jobs associated with the campaign
    };
  }

  /**
   * Constructs an HTML table representing feedcodes for email notifications.
   *
   * This method generates a structured HTML string that includes
   * information about clients and their respective feedcodes. It iterates
   * through the provided client data and organizes the data into tables
   * for easy viewing in email notifications. The method also ensures
   * that appropriate styles are applied to the generated HTML.
   *
   * @param  dataBody - An object containing client data,
   * where each key represents a client and contains details about
   * associated feedcodes and their rows.
   * @returns  The generated HTML string for the email body.
   * @throws  Throws an error if the rows for a feedcode are not set.
   */
  createFeedcodesRowsInEmail(dataBody: DataBody): string {
    // Add styles at the beginning of the HTML
    let htmlBody = emailStyleMock;

    // Iterate through each client in the data body
    Object.keys(dataBody).forEach((clientName) => {
      const clientData = dataBody[clientName];

      // Add client header to HTML
      htmlBody += `<div class='hyper-header'>Client: ${clientData.client}</div>`;
      // Iterate through each feedcode for the client
      Object.keys(clientData.feedcodes).forEach((feedcodeName) => {
        const feedcode = clientData.feedcodes[feedcodeName];

        // Add feedcode header if the name is valid
        if (feedcodeName && feedcodeName != "0") {
          htmlBody += `<div class='hyper-sub-header'>Feedcode: ${feedcodeName}</div>`;
        }

        // Start constructing the table for the feedcode
        htmlBody += "<table class='hyperTable'>";

        // Check if rows are set for the feedcode
        if (!feedcode?.rows?.length) {
          throw new HttpException("Rows not set", HttpStatus.BAD_REQUEST);
        }

        // Construct table header using the keys of the first row
        htmlBody += "<thead><tr>";
        const tableHead = Object.keys(feedcode.rows[0]);
        tableHead.forEach((header) => {
          htmlBody += `<th>${this.capitalize(header)}</th>`;
        });
        htmlBody += "</tr></thead>";
        htmlBody += "<tbody>";

        // Construct table body by iterating through the rows
        feedcode.rows.forEach((row: any) => {
          htmlBody += "<tr>";
          Object.values(row).forEach((value) => {
            htmlBody += `<td>${value}</td>`;
          });
          htmlBody += "</tr>";
        });
        htmlBody += "</tbody>";
        htmlBody += "</table>";
      });
    });

    // Wrap the generated HTML in a table structure for the email body
    const bodyToReturn = `<table bgcolor="#FFFFFF" width="100%" cellpadding="0" cellspacing="0" border="0" style="border-radius: 0px 0px 8px 8px; "><tbody><tr><td style="padding: 20px">${htmlBody}</td></tr></tbody></table>`;
    return bodyToReturn;
  }

  /**
   * Capitalizes the first letter of the given string.
   *
   * This method takes a string input and returns a new string
   * with the first character converted to uppercase. The
   * remaining characters of the string remain unchanged.
   *
   * @param  text - The input string to be capitalized.
   * @returns  The capitalized string with the first letter
   * in uppercase and the rest unchanged. If the input is an empty
   * string, it returns an empty string.
   */
  capitalize(text: string): string {
    return text.charAt(0).toUpperCase() + text.slice(1);
  }

  /**
   * Sends email notifications based on specified event triggers.
   *
   * This method attempts to send emails using the provided email
   * data and handles the outcome by updating the notified
   * statuses of campaigns accordingly. If the email sending fails,
   * it ensures the appropriate entries in the `updateNotified`
   * structure are cleared.
   *
   * @param emailData - The data to be sent in the email request.
   * @param  dataBody - The structured data body containing
   * client and campaign information for tracking notifications.
   * @param  updateNotified - An object tracking the notification
   * statuses of various campaigns.
   * @param  type - The type of notification being sent, which
   * determines how to update the statuses in the `updateNotified`
   * object.
   *
   * @returns  A promise that resolves when the emails
   * have been processed and the notification statuses updated.
   */
  async sendEmailsEndpoint(emailData: any, dataBody: DataBody, updateNotified: any, type: string) {
    try {
      await this.axios.post(`${process.env.URL_SEND_EMAILS}`, emailData);
      Logger.log(`[ExternalCampaign] [emailSent] email sent correctly`);
    } catch (error: any) {
      Logger.warn(`[sendEmailsEndpoint] Email was not sent: ${error.message}`);
      // If sending fails, update the notified statuses
      Object.keys(dataBody).forEach((clientName) => {
        const clientData = dataBody[clientName];
        Object.values(clientData.ids).forEach((campaign) => {
          let status: string = "";
          // Determine the status key based on the notification type
          if (type === "sendAccounts") {
            status = "yesAccount";
          } else if (type === "sendCampaigns") {
            status = "yesCampaign";
          } else if (type === "sendLiveCampaigns") {
            status = "yesLiveCampaign";
          }
          // Clear the notified status for the campaign if applicable
          if (status != "" && updateNotified[status] != undefined) {
            const campaignKey = Object.keys(campaign)[0];
            delete updateNotified[status][campaignKey];
          }
        });
      });
    } finally {
      // Update the notified field for each campaign in the repository
      for (const newStatus of Object.keys(updateNotified)) {
        const campaigns = updateNotified[newStatus];
        for (const campaign of Object.keys(campaigns)) {
          await this.externalCampaignRepository.updateNotifiedField(Number(campaign), newStatus);
          Logger.log(`[ExternalCampaign] [UpdateNotified] campaign with id ${campaign}`);
        }
      }
    }
  }

  /**
   * Function to create a new campaign in Talent through the transactional campaign creation process.
   * @param createTalentCampaignDto
   * @param userId
   * @returns
   */
  async createTalentCampaign(createTalentCampaignDto: CreateTalentCampaignDto, userId: number) {
    // Get AutoCampaignEntry
    try {
      const callApi = await this.externalCampaignRepository.readAutoCampaignEntry(
        "apiCampaignId",
        createTalentCampaignDto.apiCampaignId,
        1,
      );
      const call = callApi[0];

      // Special creation conditions for XML ingestion
      if (call.source === "xml") {
        call.campaignStatus = "active";
      }

      // We add the userId to the call to create the campaign
      call.userId = userId;

      // Create the campaign with createCampaignAutomatically()
      await this.createCampaignAutomatically(call.talentFeedcode, call);

      // If success we register the change in the auto_campaign_api bucket
      call.date = new Date();
      call.action = "-";
      const callModified = this.convertKeysToSnakeCase(call);
      await this.createAutoCampaignEntry(callModified);

      // If success, we return the campaign in Talent to go to the Edit page
      const campaign = await this.campaignsService.findOneByApiCampaignId(call.apiCampaignId);

      return {
        apiCampaignId: createTalentCampaignDto.apiCampaignId,
        success: true,
        message: "Campaign created successfully",
        campaignCreatedInTalent: campaign,
      };
    } catch (error: any) {
      if (error.message === "User privileges not found") {
        throw new HttpException(`Error user privileges: ${error.message}`, HttpStatus.UNAUTHORIZED);
      }
      if (error.message === "Http Exception") {
        throw new HttpException(
          `Error HttpException: ${error.response.message}`,
          HttpStatus.BAD_REQUEST,
        );
      } else {
        throw new HttpException(`Error bad request: ${error.message}`, HttpStatus.BAD_REQUEST);
      }
    }
  }

  /**
   * Returns account information.
   * @param field
   * @param value
   * @returns
   */
  async findAccountBy(field: any, value: any) {
    // Get account
    const account = await this.accountService.findBy(field, value, true);

    // Account validation
    if (!account[0] || Object.keys(account[0]).length < 1) {
      throw new HttpException("'account' not found", HttpStatus.NOT_FOUND);
    }

    // Return result
    return account;
  }

  /**
   * Validate feedcode depend on the publisher_id, tag and currency
   * @param id
   * @param currency
   * @param tag
   */
  async validateFeedcodeCreation(id: string, currency: string, tag: string) {
    if (id.trim() === "" || currency.trim() === "" || tag.trim() === "") {
      throw new HttpException("The id, currency or tag are required", HttpStatus.BAD_REQUEST);
    }

    // Validate the id, currency and tag
    const externalResults = await this.externalCampaignRepository.searchFeedcodeValidation(
      id,
      currency,
      tag,
    );

    // Validate the result of the feedcode
    return externalResults ? externalResults.feedcode : "";
  }

  /**
   * Get tag name by id
   * @param id
   *
   * @returns
   */
  getTagNameByid(account: any) {
    // validate the id
    if (!account) {
      throw new HttpException(`The id os required`, HttpStatus.BAD_REQUEST);
    }
    // search in tag with the id providen
    const externalCampaign = account.accountTag.find(
      (accountTag: any) =>
        accountTag.status === 1 && accountTag.tag.typeId === 7 && accountTag.tag.status === 1,
    )?.tag.name;
    // validate the result of the tag
    if (!externalCampaign) {
      throw new HttpException(`Tag not found for account ${account.id}`, HttpStatus.NOT_FOUND);
    }
    // retunr the tag name
    return externalCampaign;
  }

  /**
   * Compares two JSON objects and returns a new object containing only the differing key-value pairs from the second JSON.
   * If the first JSON is empty or undefined, the function returns the second JSON as is.
   *
   * @param json1 - The reference JSON object.
   * @param json2 - The JSON object to compare against json1.
   * @returns A new JSON object containing only the differences from json2.
   */
  compareJsons(json1: Record<string, any>, json2: Record<string, any>): Record<string, any> {
    // If json1 is empty or undefined, return json2 as the complete result.
    if (!json1 || Object.entries(json1).length === 0) {
      return json2;
    }

    const result: Record<string, any> = {};

    // Iterate over all keys in json2
    for (const key in json2) {
      if (Object.prototype.hasOwnProperty.call(json2, key)) {
        const value1 = json1[key]; // Value from json1
        const value2 = json2[key]; // Value from json2

        if (value1 == value2) {
          continue;
        }
        // Special cases for undefined and null values
        if (value2 === undefined && value1 !== undefined) {
          result[key] = value2; // Keep undefined values from json2
        }
        // If one value is null and the other is not, or if the values are different
        else if (
          (value1 === null && value2 !== null) ||
          (value1 !== null && value2 === null) ||
          value1 !== value2
        ) {
          result[key] = value2; // Store the differing value from json2
        }
      }
    }

    return result;
  }

  /**
   * Defines the update parameters for a campaign by comparing the existing data with the new transactional parameters.
   * It identifies changes and updates relevant fields accordingly.
   *
   * @param campaignData - The current campaign data (either as a record or a Campaigns entity).
   * @param transactionalParams - The new campaign parameters, including details like budget, apply params, and filters.
   * @param updateData - Contains the campaign conversion type.
   * @param campaignWhitRelations - The campaign entity with related data, such as budget and apply details.
   * @param actualTalentCampaign - The current talent campaign name.
   * @returns An object containing the updated parameters for the campaign, apply params, and budget params.
   */
  defineUpdateParams(
    transactionalParams: {
      newCampaignParams: any;
      applyParams: any;
      filterTextParams: any;
      budgetParams: any;
    },
    updateData: {
      campaignConversionType: any;
      campaignTargetCost: any;
    },
    campaignWhitRelations: Campaigns,
    actualTalentCampaign: string,
  ) {
    const { newCampaignParams, applyParams, budgetParams } = transactionalParams;
    const campaignParamsUpdate = this.compareJsons(campaignWhitRelations, newCampaignParams);
    campaignParamsUpdate.conversionTargetCost = newCampaignParams.conversionTargetCost;

    if (campaignParamsUpdate.conversionTargetCost) {
      Object.assign(campaignParamsUpdate, {
        conversionType: newCampaignParams.conversionType ?? updateData.campaignConversionType,
      });
    }

    if (campaignParamsUpdate.ppc || campaignParamsUpdate.status) {
      Object.assign(campaignParamsUpdate, {
        sponsored: newCampaignParams.sponsored,
        applyType: newCampaignParams.applyType,
        ppc: newCampaignParams.ppc ?? campaignWhitRelations.ppc,
      });
    }

    if (actualTalentCampaign !== newCampaignParams.campaignName) {
      campaignParamsUpdate.campaignName = newCampaignParams.campaignName;
    }

    Object.assign(campaignParamsUpdate, {
      dateStart: newCampaignParams.dateStart,
      dateEnd: newCampaignParams.dateEnd,
      accountId: newCampaignParams.accountId,
      updatedBy: newCampaignParams.updatedBy,
      status: newCampaignParams.status,
    });

    const applyParamsUpdate = this.compareJsons(campaignWhitRelations.campaignApply, applyParams);

    let budgetParamsUpdate = this.compareJsons(campaignWhitRelations.campaignsBudget, budgetParams);

    if (budgetParamsUpdate.budget) {
      Object.assign(campaignParamsUpdate, {
        conversionType: newCampaignParams.conversionType ?? updateData.campaignConversionType,
        applyType: newCampaignParams.applyType,
      });
      budgetParamsUpdate.budgetType = budgetParams.budgetType;
      budgetParamsUpdate.dateStart =
        campaignParamsUpdate.dateStart != null
          ? campaignParamsUpdate.dateStart
          : campaignWhitRelations.dateStart;
      if (budgetParamsUpdate.hasPacing === 2) {
        budgetParamsUpdate.dateEnd = campaignParamsUpdate.dateEnd ?? campaignWhitRelations.dateEnd;
        campaignParamsUpdate.dateEnd =
          campaignParamsUpdate.dateEnd ?? campaignWhitRelations.dateEnd;
      }
    } else {
      budgetParamsUpdate = {};
    }

    if (
      campaignParamsUpdate.conversionTargetCost === "" ||
      campaignParamsUpdate.conversionTargetCost === 0 ||
      campaignParamsUpdate.conversionTargetCost === "0" ||
      campaignParamsUpdate.conversionTargetCost == null
    ) {
      campaignParamsUpdate.conversionType = null;
      campaignParamsUpdate.conversionTargetCost = null;
    }

    if (
      !newCampaignParams.country ||
      (newCampaignParams.country != "" && budgetParams.country != "")
    ) {
      campaignParamsUpdate.country = budgetParams.country;
    }

    return { campaignParamsUpdate, applyParamsUpdate, budgetParamsUpdate };
  }

  /**
   * Formats a Date object into a "YYYY-MM-DD" string.
   * @param date - The input Date object.
   * @returns A formatted date string in "YYYY-MM-DD" format.
   */
  formatDateToYYYYMMDD(date: string | Date): string | null {
    if (!date) {
      return null;
    }

    const parsedDate = new Date(date);
    if (isNaN(parsedDate.getTime())) {
      return null;
    }

    return parsedDate.toISOString().split("T")[0];
  }

  /**
   *
   * @param feedCodes
   * @returns
   */
  async accountswithOwners(feedCodes: string[]): Promise<AccountWithOwners[]> {
    // search all the accounts related to the feedcodes
    const accounts = await this.accountService.findByWithOwners(feedCodes);

    // initialize an empty array for store the enrich accounts
    const accountsWithOwners: AccountWithOwners[] = [];

    // iterate all the accounts
    for (const account of accounts) {
      const { accountSalesOwner, accountCSOwner, accountOwner } =
        await this.validateAccountOwners(account);

      accountsWithOwners.push({
        account,
        accountSalesOwner,
        accountCSOwner,
        accountOwner,
      });
    }

    return accountsWithOwners;
  }

  /**
   * Helper function to sort data by column either asc or desc.
   * @param array
   * @param key
   * @param direction
   * @returns
   */
  private sortArray<T>(array: T[], key: keyof T, direction: SortType): T[] {
    /**
     * Normalizes a value for comparison by converting it to a number or lowercase string.
     * - Treats null, undefined, and certain placeholder strings (e.g., "n/a", "--") as 0.
     * - Parses percentages and numeric strings to floats if possible.
     * - Converts non-numeric strings to lowercase for consistent sorting.
     *
     * @param value - The value to normalize and compare.
     * @returns A comparable number or string.
     */
    const toComparableValue = (value: any): number | string => {
      if (value === null || value === undefined) return 0;

      if (typeof value === "string") {
        const normalized = value.trim().toLowerCase();

        // Treat special strings as 0
        if (["n/a", "--", "na", "", "-"].includes(normalized)) return 0;

        // Remove % if present, then parse
        const cleaned = normalized.replace("%", "");
        const parsed = parseFloat(cleaned);

        // If it's still not a number, keep it as string
        return isNaN(parsed) ? normalized : parsed;
      }

      return typeof value === "number" ? value : value.toString().toLowerCase();
    };

    return [...array].sort((a, b) => {
      const valA = toComparableValue(a[key]);
      const valB = toComparableValue(b[key]);

      let comparison = 0;
      if (valA < valB) {
        comparison = -1;
      } else if (valA > valB) {
        comparison = 1;
      }
      return direction === SortType.ASC ? comparison : -comparison;
    });
  }
}
