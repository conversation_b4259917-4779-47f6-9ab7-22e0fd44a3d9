import { Column, Entity, PrimaryGeneratedColumn } from "typeorm";

/**
 * Main not updated entries' for external campaigns entity representation
 */
@Entity("external_campaigns_not_updated", { schema: "talent_rearch_employers" })
export class NotUpdated {
  @PrimaryGeneratedColumn({
    type: "int",
    name: "id",
    comment: "Unique table ID",
    unsigned: true,
  })
  id!: number;

  @Column("varchar", {
    name: "api_campaign_id",
    comment: "Unique ID from bucket external_campaign_api (Ex. appcast_",
    length: 100,
  })
  apiCampaignId!: string;

  @Column("datetime", { name: "created", comment: "Date inserted" })
  created!: Date;

  @Column("varchar", {
    name: "created_by",
    comment: "Email of the user who created the tag",
    length: 200,
  })
  createdBy!: number;
}
