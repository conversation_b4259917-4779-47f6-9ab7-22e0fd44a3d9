import { Body, Controller, Delete, Get, Param, Patch, Post } from "@nestjs/common";
import { NotUpdatedService } from "../services/not-updated.service";
import { CreateNotUpdatedDto } from "../dto/create-not-updated.dto";
import { UpdateNotUpdatedDto } from "../dto/update-not-updated.dto";
import { UserAuthDto } from "../../common/dtos/user-auth.dto";
import { User } from "../../auth/decorators/user.decorator";

/**
 * External Campaign Not Updated main endpoints definition
 */
@Controller({
  path: "external-campaign-not-updated",
  version: "1",
})
export class NotUpdatedController {
  /**
   * Injecting ExternalCampaignNotUpdated services for connecting the endpoints to the actual working flow
   */
  constructor(private readonly notUpdatedService: NotUpdatedService) {}

  /**
   * Endpoint to fetch a single record by ID
   * @param id
   * @returns
   */
  @Get(":id")
  findOne(@Param("id") id: string) {
    return this.notUpdatedService.findOne(+id);
  }

  /**
   * Endpoint to create a new record
   * @param createNotUpdatedDto
   * @returns
   */
  @Post()
  create(@User() user: UserAuthDto, @Body() createNotUpdatedDto: CreateNotUpdatedDto) {
    return this.notUpdatedService.create(createNotUpdatedDto, user.user_id);
  }

  /**
   * Endpoint to update an existing record
   * @param id
   * @param updateNotUpdatedDto
   * @returns
   */
  @Patch(":id")
  update(@Param("id") id: string, @Body() updateNotUpdatedDto: UpdateNotUpdatedDto) {
    return this.notUpdatedService.update(+id, updateNotUpdatedDto);
  }

  /**
   * Endpoint to delete a record by ID
   * @param apiCampaignId
   * @returns
   */
  @Delete(":apicampaignid")
  remove(@Param("apicampaignid") apiCampaignId: string) {
    return this.notUpdatedService.remove(apiCampaignId);
  }
}
