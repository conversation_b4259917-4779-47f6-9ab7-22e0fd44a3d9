/* eslint-disable @typescript-eslint/no-explicit-any */
import { Controller, Get, Post, Delete, Body, Param, Query, ValidationPipe } from "@nestjs/common";
import { ExternalCampaignService } from "../services/external-campaign.service";
import { FeedcodeExternalCampaignDto } from "../dto/feedcode-external-campaign.dto";
import { SearchExternalCampaignDto } from "../dto/search-external-campaign.dto";
import { ClientExternalCampaignDto } from "../dto/client-external-campaign.dto";
import { RequirePermissions } from "../../auth/decorators/permissions.decorator";
import { Permission } from "../../auth/resources/permission.enum";
import { SwitchAutomaticStatusDto } from "../dto/switch-status.dto";
import { CreateTalentCampaignDto } from "../dto/create-talent-campaign.dto";
import { User } from "../../auth/decorators/user.decorator";
import { UserAuthDto } from "../../common/dtos/user-auth.dto";

/**
 * External Campaign main endpoints definition
 */
@Controller({
  path: "external-campaign",
  version: "1",
})
export class ExternalCampaignController {
  /**
   * Injecting ExternalCampaign services for connecting the endpoints to the actual working flow
   */
  constructor(private readonly externalCampaignService: ExternalCampaignService) {}

  /**
   * Get one External campaign entry based on a key:value pair. e.g:
   * {api_campaign_id: "autest-12345"}
   */
  @Get(":field/:value")
  @RequirePermissions(Permission.GetExternalCampaignById)
  async getExternalCampaignEntry(@Param("field") field: string, @Param("value") value: string) {
    return this.externalCampaignService.findOne(field, value);
  }

  /**
   * Get the client list to feed the dashboard for ingested campaigns
   * @returns An array of client's tags
   */
  @Get("clients")
  @RequirePermissions(Permission.GetAllExternalCampaigns)
  async getClientsList() {
    return await this.externalCampaignService.getClientsList();
  }

  /**
   * Get a feedcodeList to feed the dashboard for ingested campaigns
   * @returns An array of feedcodes
   */
  @Get("feedcodes")
  @RequirePermissions(Permission.GetAllExternalCampaigns)
  async getFeedcodeList(
    @Query(new ValidationPipe()) clientDto: ClientExternalCampaignDto,
  ): Promise<any> {
    return await this.externalCampaignService.getFeedcodeListWithTalentCheck(clientDto);
  }

  /**
   * Get a companyNameList to feed the dashboard for ingested campaigns
   * @returns An array of companyName
   */
  @Get("companies")
  @RequirePermissions(Permission.GetAllExternalCampaigns)
  async getCompanyNameList(
    @Query(new ValidationPipe()) feedcodeDto: FeedcodeExternalCampaignDto,
  ): Promise<string[]> {
    return await this.externalCampaignService.getCompanyNameListByFeedcode(feedcodeDto);
  }

  /**
   * Search campaigns by filters in the bucket of ingested campaigns.
   * Returns all the campaigns related to the filters.
   */
  @Get("campaigns")
  @RequirePermissions(Permission.GetAllExternalCampaigns)
  async search(@Query(new ValidationPipe()) searchDto: SearchExternalCampaignDto): Promise<any> {
    return await this.externalCampaignService.getCampaignsByFilters(searchDto);
  }

  /**
   * Endpoint for creating a Talent campaign through the transaction process
   */
  @Post("createTalentCampaign")
  async createTalentCampaign(
    @User() user: UserAuthDto,
    @Body() createTalentCampaignDto: CreateTalentCampaignDto,
  ) {
    return this.externalCampaignService.createTalentCampaign(createTalentCampaignDto, user.user_id);
  }

  /**
   * Endpoint for deleting an External campaign entry out of an ID
   */
  @Delete("/:id")
  async deleteExternalCampaignById(@Param("id") id: number) {
    return this.externalCampaignService.delete(id);
  }

  /**
   * Endpoint to toggle the automatic status of a campaign.
   *
   * This method handles a GET request to the "switchstatus" endpoint and is protected
   * by the `RequirePermissions` decorator, which ensures that only users with the
   * specified permissions can access this method.
   *
   * @param params - The query parameters required to switch the campaign status.
   *                 These are passed as an instance of `SwitchAutomaticStatusDto`.
   * @returns The result of the `switchAutomaticStatus` method from the `externalCampaignService`,
   *          which processes the campaign details and toggles the automatic status.
   */
  @Get("switchstatus")
  @RequirePermissions(Permission.GetAllExternalCampaigns)
  async switchAutomaticStatus(@Query() params: SwitchAutomaticStatusDto) {
    // Call the external campaign service to toggle the automatic status of the campaign
    // The parameters are trimmed to remove any leading or trailing whitespace
    return await this.externalCampaignService.switchAutomaticStatus(params.apiCampaignId.trim());
  }
}
