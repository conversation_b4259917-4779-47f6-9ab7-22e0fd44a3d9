import { Module, forwardRef } from "@nestjs/common";
import { ClientApiService } from "./services/client-api.service";
import { HttpModule } from "@nestjs/axios";
import { ConfigModule } from "@nestjs/config";
import { ExternalCampaignModule } from "../external-campaign.module";
import { EmployerErrorLogsModule } from "../../common/employer-error-logs/employer-error-logs.module";
import { ConsumersModule } from "../consumers/consumers.module";
import { ExternalCampaignIngestionService } from "../services/abstract/external-campaign-ingestion.service";

/**
 *Define the ClientApiModule
 */
@Module({
  // Import the HttpModule and KafkaModule as dependencies
  imports: [
    HttpModule,
    ConfigModule.forRoot(),
    forwardRef(() => ExternalCampaignModule),
    EmployerErrorLogsModule,
    forwardRef(() => ConsumersModule),
  ],

  // Register the ClientApiService, CustomAxiosAdapter, and PublishersConsumer as providers
  providers: [ClientApiService, ExternalCampaignIngestionService],

  // Register the ClientApiController as a controller
  controllers: [],
  exports: [ClientApiService],
})
export class ClientApiModule {}
