/* eslint-disable @typescript-eslint/no-explicit-any */
import { Test, TestingModule } from "@nestjs/testing";
import { ClientApiService } from "../../../../src/external-campaign/client-api/services/client-api.service";
import { CustomAxiosAdapter } from "../../../../src/common/adapters/axios.adapter";
import { NotUpdatedService } from "../../../../src/external-campaign/services/not-updated.service";
import { ExternalCampaignService } from "../../../../src/external-campaign/services/external-campaign.service";
import { HttpModule } from "@nestjs/axios";
import { ConfigModule } from "@nestjs/config";
import {
  publishersAnswer,
  employersAnswer,
  campaignsAnswer,
  mockDefineSouceFiels,
  mockSourceFields,
  mockDefineCompareKeys,
  mockCompareKeys,
  mockPublisherData,
  mockEmployerData,
  mockCampaignData,
  mockApiClientName,
} from "../../../../src/common/mocks/client-api.mock";
import { newEntryDataMock } from "../../../common/mocks/external-campaign.service.mock";
import { ToolsTrackingLogService } from "packages/backend/services/employers-ff/src/common/employer-error-logs/services/tools-tracking.service";
import { ConsumerServices } from "packages/backend/services/employers-ff/src/external-campaign/consumers/services/consumers.services";

jest.mock("@talent-back-libs/queue");

describe("ClientApiService", () => {
  let service: ClientApiService;
  let consumerServices: ConsumerServices;
  let mockAxios: CustomAxiosAdapter;
  let notUpdatedService: NotUpdatedService;
  let externalCampaignService: ExternalCampaignService;
  let toolsTrackingLogService: ToolsTrackingLogService;

  const isNonExternalCampaignsMock = jest.fn().mockReturnValue(false);

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ClientApiService,
        {
          provide: ConsumerServices,
          useValue: {
            sendMessages: jest.fn(),
            //consumerServices,
          },
        },
        {
          provide: CustomAxiosAdapter,
          useValue: { get: jest.fn(), generateAction: jest.fn(), mockAxios },
        },
        {
          provide: NotUpdatedService,
          useValue: notUpdatedService,
        },
        {
          provide: ExternalCampaignService,
          useValue: {
            generateLinks: jest.fn(),
            getMinimumBudgetValues: jest.fn(),
            cleanParam: jest.fn(),
            createRepoInstance: jest.fn(),
            findOne: jest.fn(),
            duplicatedCampaign: jest.fn(),
            pausedCampaignTalent: jest.fn(),
            validateFeedcodeCreation: jest.fn(),
          },
        },
        {
          provide: ToolsTrackingLogService,
          useValue: {
            toolsTrackingLogService,
            create: jest.fn(),
          },
        },
      ],
      imports: [HttpModule.register({}), ConfigModule.forRoot()],
    }).compile();

    jest.spyOn(mockDefineSouceFiels, "defineSourceKeys").mockImplementation(() => {
      mockDefineSouceFiels.sourceKeys = mockSourceFields;
    });
    jest.spyOn(mockDefineCompareKeys, "defineCompareKeys").mockImplementation(() => {
      mockDefineCompareKeys.compareKeys = mockCompareKeys;
    });

    consumerServices = module.get<ConsumerServices>(ConsumerServices);
    service = module.get<ClientApiService>(ClientApiService);
    mockAxios = module.get<CustomAxiosAdapter>(CustomAxiosAdapter);
    notUpdatedService = module.get<NotUpdatedService>(NotUpdatedService);
    externalCampaignService = module.get<ExternalCampaignService>(ExternalCampaignService);
    toolsTrackingLogService = module.get<ToolsTrackingLogService>(ToolsTrackingLogService);

    service["publisher"] = mockPublisherData;
    service["employer"] = mockEmployerData;
    service["campaign"] = mockCampaignData;
    service["apiClientName"] = mockApiClientName;
    service["apiCampaignId"] =
      `${mockPublisherData.publisher_name}_${mockPublisherData.publisher_id}_${mockCampaignData.campaign_id}`;
    service["talentCampaignName"] =
      `${mockEmployerData.employer_name} - ${mockPublisherData.publisher_name} - ${mockCampaignData.campaign_id} - ${mockCampaignData.campaign_name}`;
    service["talentFeedcodeName"] =
      `${mockApiClientName}-${mockPublisherData.enterprise_name}-${mockEmployerData.currency_name}`;
    service["source"] = "client-api";
    service["isNonExternalCampaigns"] = isNonExternalCampaignsMock;
    service["isFullyAutoCampaign"] = false;
    service.insertAutoCampaign = jest.fn();
    service.processExternalCampaign = jest.fn();
    //externalCampaignService['minimumBudgetValues'].set("monthly", 300)
  });

  it("should fetch data for publishers", async () => {
    // Configura el mock para devolver datos simulados
    jest.spyOn(mockAxios, "get").mockResolvedValue(publishersAnswer);

    // Llama al método 'getData' con el parámetro 'publishers'
    const result = await service.getData("publishers");
    // Verifica que el resultado coincida con los datos simulados
    expect(result).toHaveProperty("publishers");
    expect(Array.isArray(result.publishers)).toBe(true);

    // Loops through each publisher in the response
    result.publishers.forEach((publisher: any) => {
      // Uses toMatchObject for object structure validation
      expect(publisher).toMatchObject({
        enterprise_id: expect.any(Number),
        enterprise_name: expect.any(String),
        publisher_id: expect.any(Number),
        publisher_name: expect.any(String),
        currency: expect.any(String),
        currency_name: expect.any(String),
        publisher_status: expect.any(String),
      });
    });
    expect(result).toEqual(publishersAnswer);
    expect(mockAxios.get).toHaveBeenCalledWith(expect.any(String), expect.any(Object));
  });

  it("shoud fetch data from employers api", async () => {
    jest.spyOn(mockAxios, "get").mockResolvedValue(employersAnswer);

    const publisherId = 549;
    const result = await service.getData("employers", publisherId);
    // Ensures the response has an 'employers' property
    expect(result).toHaveProperty("employers");
    // Ensures 'employers' is an array
    expect(Array.isArray(result.employers)).toBe(true);

    // Loops through each employer in the response
    result.employers.forEach((employer: any) => {
      // Validates object structure
      expect(employer).toMatchObject({
        employer_id: expect.any(Number),
        employer_name: expect.any(String),
        currency: expect.any(String),
        currency_name: expect.any(String),
        connection_status: expect.any(String),
        employer_status: expect.any(String),
        beginning_of_week: expect.any(String),
        // Commented out original expectation (use the approach discussed)
        //xml_feed: expect(employer.xml_feed).toEqual(expect.any(String) || null),
        publisher_monthly_budget: expect.any(String),
        publisher_weekly_budget: expect.any(String),
        countries: expect.any(String),
      });
    });
    expect(result).toEqual(employersAnswer);
    expect(mockAxios.get).toHaveBeenCalledWith(expect.any(String), expect.any(Object));
  });

  it("should fetch data from campaigns", async () => {
    jest.spyOn(mockAxios, "get").mockResolvedValue(campaignsAnswer);
    const publisherId = 549;
    const employerId = 4233;
    const result = await service.getData("campaigns", publisherId, employerId);

    // Ensures the response has a 'campaigns' property
    expect(result).toHaveProperty("campaigns");
    // Ensures 'campaigns' is an array
    expect(Array.isArray(result.campaigns)).toBe(true);

    // Loops through each campaign in the response and validates its structure with toMatchObject
    result.campaigns.forEach((campaign: any) => {
      // Validates properties and their data types
      expect(campaign).toMatchObject({
        exported_jobs_count: expect.any(Number),
        employer_id: expect.any(Number),
        campaign_id: expect.any(Number),
        campaign_name: expect.any(String),
        campaign_status: expect.any(String),
        connection_status: expect.any(String),
        publisher_monthly_budget: expect.any(String),
        publisher_weekly_budget: expect.any(String),
        bid_model: expect.any(String),
        bid: expect.any(String),
        estimated_budget_days_remaining: expect.any(Number),
        sponsorship_ended: expect.any(Boolean),
        paid_cpa_goal: expect.any(Number),
        bid_type: expect.any(String),
      });
    });

    expect(result).toEqual(campaignsAnswer);
    expect(mockAxios.get).toHaveBeenCalledWith(expect.any(String), expect.any(Object));
  });

  it("should define the correct source keys", () => {
    // Llamar al método que debería activar la actualización de sourceKeys
    service.defineSourceKeys();

    // Verificar que sourceKeys se haya actualizado según lo esperado
    expect(mockDefineSouceFiels.sourceKeys).toEqual(mockSourceFields);
  });

  it("should define the correct compare keys", () => {
    service.defineCompareKeys();

    expect(mockDefineCompareKeys.compareKeys).toEqual(expect.arrayContaining(mockCompareKeys));
  });

  describe("validateCriticalParams", () => {
    it("should throw an error if publisher is undefined", () => {
      service["publisher"] = {};
    });
    it("should throw an error if publisher is undefined new method", () => {
      const mockPublisher: any = undefined; // Usamos any para evitar problemas de tipo
      const mockEmployer: any = {
        /* Mock employer object */
      }; // Usamos any para evitar problemas de tipo
      const mockCampaign: any = {
        /* Mock campaign object */
      }; // Usamos any para evitar problemas de tipo

      service["publisher"] = mockPublisher;
      service["employer"] = mockEmployer;
      service["campaign"] = mockCampaign;

      expect(() => service.validateCriticalParams()).toThrow("publisher is undefined");
    });
    it("should throw an error if employer is undefined", () => {
      const mockPublisher: any = {
        /* Mock publisher object */
      }; // Usamos any para evitar problemas de tipo
      const mockEmployer: any = undefined; // Usamos any para evitar problemas de tipo
      const mockCampaign: any = {
        /* Mock campaign object */
      }; // Usamos any para evitar problemas de tipo

      service["publisher"] = mockPublisher;
      service["employer"] = mockEmployer;
      service["campaign"] = mockCampaign;

      expect(() => service.validateCriticalParams()).toThrow("employer is empty");
    });

    it("should throw an error if campaign is undefined", () => {
      const mockPublisher: any = {
        /* Mock publisher object */
      }; // Usamos any para evitar problemas de tipo
      const mockEmployer: any = {
        /* Mock employer object */
      }; // Usamos any para evitar problemas de tipo
      const mockCampaign: any = undefined; // Usamos any para evitar problemas de tipo

      service["publisher"] = mockPublisher;
      service["employer"] = mockEmployer;
      service["campaign"] = mockCampaign;

      expect(() => service.validateCriticalParams()).toThrow("campaign is empty");
    });

    it("should not throw any error if all critical params are defined", () => {
      const mockPublisher: any = {
        /* Mock publisher object */
      }; // Usamos any para evitar problemas de tipo
      const mockEmployer: any = {
        /* Mock employer object */
      }; // Usamos any para evitar problemas de tipo
      const mockCampaign: any = {
        /* Mock campaign object */
      }; // Usamos any para evitar problemas de tipo

      service["publisher"] = mockPublisher;
      service["employer"] = mockEmployer;
      service["campaign"] = mockCampaign;

      expect(() => service.validateCriticalParams()).not.toThrow();
    });
  });

  it("should generate the correct API campaign ID", () => {
    service.generateApiCampaignId();
    const expectedApiCampaignId = `${mockPublisherData.tag}_${mockPublisherData.publisher_id}_${mockCampaignData.campaign_id}`;
    expect(service["apiCampaignId"]).toBe(expectedApiCampaignId);
  });

  it("should generate the correct talent campaign name", () => {
    service.generateTalentCampaignName();
    const expectedTalentCampaignName = `${mockEmployerData.employer_name} - ${mockPublisherData.publisher_name} - ${mockCampaignData.campaign_id} - ${mockCampaignData.campaign_name}`;
    expect(service["talentCampaignName"]).toBe(expectedTalentCampaignName);
  });
  it('should generate feed code name correctly when enterprise name is "Appcast Exchanges"', () => {
    // Mocking publisher, apiClientName, and employer
    const mockPublisher = {
      enterprise_name: "Appcast Exchanges",
      publisher_name: "Talent.com Publisher",
    };
    const mockApiClientName = "ApiClientName";
    const mockEmployer = {
      currency_name: "USD",
    };

    // Setting up mocks
    service["publisher"] = mockPublisher;
    service["apiClientName"] = mockApiClientName;
    service["employer"] = mockEmployer;

    // Call the method
    service.generateFeedCodeName();

    // Assert the generated feed code name
    expect(service["talentFeedcodeName"]).toBe("api-client-name-Compañía ABC-USD");
  });
  it('should generate feed code name correctly when enterprise name is not "Appcast Exchanges"', () => {
    // Mocking publisher, apiClientName, and employer
    const mockPublisher = {
      enterprise_name: "Other Enterprise",
      publisher_name: "Other Publisher",
    };
    const mockApiClientName = "ApiClientName";
    const mockEmployer = {
      currency_name: "EUR",
    };

    // Setting up mocks
    service["publisher"] = mockPublisher;
    service["apiClientName"] = mockApiClientName;
    service["employer"] = mockEmployer;

    jest
      .spyOn(externalCampaignService, "validateFeedcodeCreation")
      .mockResolvedValue("api-client-name-Compañía ABC-USD1");
    // Call the method
    service.generateFeedCodeName();
    expect(externalCampaignService.validateFeedcodeCreation).toHaveBeenCalledWith(
      "undefined",
      "EUR",
      "ApiClientName",
    );

    // Assert the generated feed code name
    expect(service["talentFeedcodeName"]).toBe("api-client-name-Compañía ABC-USD");
  });
  it("should build a new entry with the correct values", () => {
    const action = "update";
    const newEntry = service.buildNewEntry(action);

    expect(newEntry.get("campaign_budget_type")).toBe("monthly");
    expect(newEntry.get("campaign_budget_value")).toBe("no limit");
    expect(newEntry.get("api_campaign_id")).toBe(
      `${mockPublisherData.publisher_name}_${mockPublisherData.publisher_id}_${mockCampaignData.campaign_id}`,
    );
    expect(newEntry.get("account_id")).toBe(mockPublisherData.enterprise_id);
    expect(newEntry.get("account_name")).toBe(mockPublisherData.enterprise_name);
    expect(newEntry.get("account_currency_name")).toBe(mockPublisherData.currency_name);
    expect(newEntry.get("company_id")).toBe(mockEmployerData.employer_id);
    expect(newEntry.get("company_name")).toBe(mockEmployerData.employer_name);
    expect(newEntry.get("company_currency_name")).toBe(mockEmployerData.currency_name);
    expect(newEntry.get("company_country")).toBe(mockEmployerData.countries);
    expect(newEntry.get("company_monthly_budget")).toBe(mockEmployerData.publisher_monthly_budget);
    expect(newEntry.get("xml_feed_link")).toBe(mockEmployerData.xml_feed);
    expect(newEntry.get("campaign_id")).toBe(mockCampaignData.campaign_id);
    expect(newEntry.get("campaign_name")).toBe(mockCampaignData.campaign_name);
    expect(newEntry.get("campaign_status")).toBe(mockCampaignData.campaign_status);
    expect(newEntry.get("campaign_country")).toBe(mockEmployerData.countries);
    expect(newEntry.get("campaign_sponsorship_ended")).toBe(mockCampaignData.sponsorship_ended);
    expect(newEntry.get("campaign_target_cost")).toBe(mockCampaignData.paid_cpa_goal);
    expect(newEntry.get("estimated_budget_days_remaining")).toBe(
      mockCampaignData.estimated_budget_days_remaining,
    );
    expect(newEntry.get("campaign_cpc")).toBe(mockCampaignData.bid);
    expect(newEntry.get("xml_num_jobs")).toBe(mockCampaignData.exported_jobs_count);
    expect(newEntry.get("talent_campaign")).toBe(
      `${mockEmployerData.employer_name} - ${mockPublisherData.publisher_name} - ${mockCampaignData.campaign_id} - ${mockCampaignData.campaign_name}`,
    );
    expect(newEntry.get("talent_feedcode")).toBe(
      `${mockApiClientName}-${mockPublisherData.enterprise_name}-${mockEmployerData.currency_name}`,
    );
    expect(newEntry.get("action")).toBe(action);
    expect(newEntry.get("source")).toBe("client-api");
    expect(newEntry.get("campaign_conversion_type")).toBe("");
  });

  it("should process the campaign correctly", async () => {
    const dataNew: Map<string, string> = new Map();
    dataNew.set("publisher", JSON.parse(JSON.stringify(mockPublisherData)));
    dataNew.set("employer", JSON.parse(JSON.stringify(mockEmployerData)));
    dataNew.set("campaign", JSON.parse(JSON.stringify(mockCampaignData)));

    service.setFields(dataNew);
    jest
      .spyOn(externalCampaignService, "getMinimumBudgetValues")
      .mockImplementation((): Map<string, number> => {
        const map = new Map<string, number>();
        map.set("monthly", 300);
        // Puedes agregar más entradas según sea necesario
        map.set("weekly", 20);
        map.set("daily", 10);
        return map;
      });

    const generateAppcastCriticalPropertiesSpy = jest.spyOn(
      service,
      "generateAppcastCriticalProperties",
    );
    const generateApiCampaignIdSpy = jest.spyOn(service, "generateApiCampaignId");
    const generateFeedCodeNameSpy = jest.spyOn(service, "generateFeedCodeName");
    const generateTalentCampaignNameSpy = jest.spyOn(service, "generateTalentCampaignName");
    const buildNewEntrySpy = jest.spyOn(service, "buildNewEntry");
    const map = new Map([["action", "campaign"]]);
    const generateLinks = jest
      .spyOn(externalCampaignService, "generateLinks")
      .mockResolvedValueOnce(map);
    jest.spyOn(service, "calculateNotifyAndStatus").mockResolvedValue({});

    await service.preProcess();

    expect(generateAppcastCriticalPropertiesSpy).toHaveBeenCalled();
    expect(generateApiCampaignIdSpy).toHaveBeenCalled();
    expect(generateFeedCodeNameSpy).toHaveBeenCalled();
    expect(generateTalentCampaignNameSpy).toHaveBeenCalled();
    expect(generateLinks).toHaveBeenCalledWith(
      service["talentFeedcodeName"],
      service["talentCampaignName"],
      service["apiCampaignId"],
      "USD",
    );
    expect(isNonExternalCampaignsMock).toHaveBeenCalledWith(service["apiCampaignId"]);
    expect(buildNewEntrySpy).toHaveBeenCalledWith("campaign");
  });
  it("should throw an error if the campaign is excluded from the automation process", async () => {
    const dataNew: Map<string, string> = new Map();
    dataNew.set("publisher", JSON.parse(JSON.stringify(mockPublisherData)));
    dataNew.set("employer", JSON.parse(JSON.stringify(mockEmployerData)));
    dataNew.set("campaign", JSON.parse(JSON.stringify(mockCampaignData)));
    service.setFields(dataNew);
    service["isNonExternalCampaigns"] = jest.fn().mockResolvedValue(true);
    service["isFullyAutoCampaign"] = true;

    jest
      .spyOn(externalCampaignService, "getMinimumBudgetValues")
      .mockImplementation((): Map<string, number> => {
        const map = new Map<string, number>();
        map.set("monthly", 300);
        // Puedes agregar más entradas según sea necesario
        map.set("weekly", 20);
        map.set("daily", 10);
        return map;
      });
    // Expect preProcess to throw an error with the appropriate message
    await expect(service.preProcess()).rejects.toThrow(
      "Error: The campaign is excluded from the automation process",
    );
  });
  describe("buildBudgetTypeAndValue", () => {
    it("should build weekly budget type and value if publisher_weekly_budget is set", () => {
      // Arrange
      service["campaign"] = { publisher_weekly_budget: "100" };

      // Act
      const result = (service as any).buildBudgetTypeAndValue(); // Accedemos al método privado

      // Assert
      expect(result).toEqual({ budgetType: "weekly", budgetValue: "100" });
    });
  });
  describe("sendPublisherData", () => {
    it("should send publisher data successfully", async () => {
      // Mock getData to return publishers data
      jest.spyOn(service, "getData").mockResolvedValueOnce({
        publishers: [
          { publisher_id: "1", publisher_name: "Publisher 1", publisher_status: "active" },
          { publisher_id: "2", publisher_name: "Publisher 2", publisher_status: "inactive" },
        ],
      });

      // Mock sendMessages method
      const sendMessagesMock = jest
        .spyOn(consumerServices, "sendMessages")
        .mockResolvedValueOnce(undefined);

      // Call the method
      await service.sendPublisherData();

      // Expect sendMessages to be called once for each active publisher
      expect(sendMessagesMock).toHaveBeenCalledTimes(1);
    });

    it("should handle error when fetching publisher data", async () => {
      // Mock getData to throw an error
      jest.spyOn(service, "getData").mockRejectedValueOnce(new Error("Failed to fetch publishers"));

      // Expect sendPublisherData to throw an error
      await expect(service.sendPublisherData()).rejects.toThrow("Failed to fetch publishers");
    });
  });

  describe("publishersConsumer", () => {
    it("should send messages to active employers", async () => {
      const mockValidateProdMessage = jest
        .spyOn(service, "validateProdMessage")
        .mockReturnValueOnce({
          publisher_id: "somePublisherId",
        });
      const mockGetData = jest.spyOn(service, "getData").mockResolvedValueOnce({
        employers: [
          { employer_status: "active" /* Other relevant data */ },
          { employer_status: "inactive" /* Other relevant data */ },
          { employer_status: "active" /* Other relevant data */ },
        ],
      });
      const mockSendMessages = jest.spyOn(consumerServices, "sendMessages").mockResolvedValueOnce();

      const message = {}; // Your test message data

      await service.publishersConsumer(message);

      expect(mockValidateProdMessage).toHaveBeenCalledWith(message);
      expect(mockGetData).toHaveBeenCalledWith("employers", "somePublisherId");
      expect(mockSendMessages).toHaveBeenCalledTimes(2); // Two active employers
    });
    it("should handle errors", async () => {
      const mockValidateProdMessage = jest
        .spyOn(service, "validateProdMessage")
        .mockImplementation(() => {
          throw new Error("Mocked error");
        });

      const message = {}; // Your test message data

      await expect(service.publishersConsumer(message)).rejects.toThrow("Mocked error");

      expect(mockValidateProdMessage).toHaveBeenCalledWith(message);
    });
  });
  describe("employersConsumer", () => {
    it("should send messages for each campaign", async () => {
      const mockValidateProdMessage = jest
        .spyOn(service, "validateProdMessage")
        .mockReturnValueOnce([
          { publisher_id: "somePublisherId" },
          { employer_id: "someEmployerId" },
        ]);
      const mockGetData = jest.spyOn(service, "getData").mockResolvedValueOnce({
        campaigns: [
          {
            /* Campaign data */
          },
          {
            /* Campaign data */
          },
          {
            /* Campaign data */
          },
        ],
      });
      const mockSendMessages = jest.spyOn(consumerServices, "sendMessages").mockResolvedValueOnce();

      const message = {}; // Your test message data

      await service.employersConsumer(message);

      expect(mockValidateProdMessage).toHaveBeenCalledWith(message);
      expect(mockGetData).toHaveBeenCalledWith("campaigns", "somePublisherId", "someEmployerId");
      expect(mockSendMessages).toHaveBeenCalledTimes(3); // Three campaigns
    });
    it("should generate an error if publisherData or employerData is empty", async () => {
      jest.spyOn(service, "validateProdMessage").mockReturnValue([{}, {}]);
      const message = {}; // Tu dato de mensaje de prueba

      await expect(service.employersConsumer(message)).rejects.toThrow(
        new Error("Publisher info or Employer info doesn't exist"),
      );
    });
  });
  describe("campaignsConsumer", () => {
    it("should call setFields and preProcess with valid data", async () => {
      const mockValidateProdMessage = jest
        .spyOn(service, "validateProdMessage")
        .mockReturnValueOnce({
          /* Mocked data returned by validateProdMessage */
        });
      const mockSetFields = jest.spyOn(service, "setFields").mockImplementation();
      const mockProcess = jest.spyOn(service, "processExternalCampaign").mockImplementation();

      const message = {}; // Your test message data

      await service.campaignsConsumer(message);

      expect(mockValidateProdMessage).toHaveBeenCalledWith(message);
      expect(mockSetFields).toHaveBeenCalledWith(
        new Map([
          ["publisher", undefined],
          ["employer", undefined],
          ["campaign", undefined],
        ]),
      );
      expect(mockProcess).toHaveBeenCalled();
    });

    it("should handle errors", async () => {
      const mockValidateProdMessage = jest
        .spyOn(service, "validateProdMessage")
        .mockImplementation(() => {
          throw new Error("Mocked error");
        });
      const mockSetFields = jest.spyOn(service, "setFields").mockImplementation();
      const mockPreProcess = jest.spyOn(service, "preProcess").mockImplementation();

      const message = {}; // Your test message data

      try {
        await service.campaignsConsumer(message);
      } catch (error) {
        console.log(error);
      }

      expect(mockValidateProdMessage).toHaveBeenCalledWith(message);
      expect(mockSetFields).not.toHaveBeenCalled();
      expect(mockPreProcess).not.toHaveBeenCalled();
    });
  });
  describe("validateProdMessage", () => {
    it("should parse a valid JSON message correctly", () => {
      const mockMessage = { value: Buffer.from(JSON.stringify({ key: "value" })) };
      const result = service.validateProdMessage(mockMessage);
      expect(result).toEqual({ key: "value" });
    });

    it("should throw an error if the message value is empty", () => {
      const mockMessage = { value: null };

      expect(() => {
        service.validateProdMessage(mockMessage);
      }).toThrow("Error: Couldn't read a value from the producer");
    });

    it("should throw an error if the message is not valid JSON", () => {
      const mockMessage = { value: Buffer.from("invalid JSON") };

      expect(() => {
        service.validateProdMessage(mockMessage);
      }).toThrow();
    });

    it("should log the error if JSON parsing fails", () => {
      const consoleLogSpy = jest.spyOn(console, "log").mockImplementation(() => {});
      const mockMessage = { value: Buffer.from("invalid JSON") };

      try {
        service.validateProdMessage(mockMessage);
      } catch (error) {
        console.log(error);
      }

      expect(consoleLogSpy).toHaveBeenCalled();
    });

    it("should return parsed data if valid JSON is provided", () => {
      const mockMessage = { value: Buffer.from(JSON.stringify({ test: "data" })) };
      const result = service.validateProdMessage(mockMessage);
      expect(result).toEqual({ test: "data" });
    });
  });
  describe("insertAutoCampaign", () => {
    it("try to test te insert", async () => {
      const result = await service.insertAutoCampaign(newEntryDataMock);
      expect(result).toBe(undefined);
    });
    it("process completer", async () => {
      await service.processExternalCampaign();
    });
  });
  describe("buildNewEntry", () => {
    it("should build a new entry correctly", () => {
      jest.spyOn(service as any, "buildBudgetTypeAndValue").mockReturnValue({
        budgetType: "weekly",
        budgetValue: "100",
      });

      jest.spyOn(service as any, "validateBudget").mockReturnValue(
        new Map<string, string>([
          ["action", "create"],
          ["campaignStatus", "active"],
        ]),
      );

      const result = service["buildNewEntry"]("create");

      expect(result.get("action")).toBe("create");
      expect(result.get("campaign_budget_type")).toBe("weekly");
      expect(result.get("campaign_budget_value")).toBe("100");
    });
  });
  // eslint-disable-next-line jest/no-identical-title
  describe("buildBudgetTypeAndValue", () => {
    it("should return weekly budget type and value", () => {
      const result = service["buildBudgetTypeAndValue"]();

      expect(result.budgetType).toBe("monthly");
      expect(result.budgetValue).toBe("no limit");
    });

    it("should return monthly budget type and value", () => {
      service["campaign"].publisher_weekly_budget = "";
      const result = service["buildBudgetTypeAndValue"]();

      expect(result.budgetType).toBe("monthly");
      expect(result.budgetValue).toBe("no limit");
    });
  });
  describe("populateBasicFields", () => {
    it("should populate basic fields correctly", () => {
      const validateBudget = new Map<string, string>([["campaignStatus", "active"]]);
      const result = service["populateBasicFields"](validateBudget);

      expect(result.get("api_campaign_id")).toBe("Publicador XYZ_456_37852");
      expect(result.get("api_client_key")).toBe("");
      expect(result.get("api_client_name")).toBe("api-client-name");
      expect(result.get("account_id")).toBe("123");
      expect(result.get("account_name")).toBe("Compañía ABC");
      expect(result.get("account_currency_name")).toBe("USD");
      expect(result.get("company_id")).toBe("11241");
      expect(result.get("company_name")).toBe("Mercy - AF");
      expect(result.get("company_currency_name")).toBe("USD");
      expect(result.get("company_country")).toBe("United States");
      expect(result.get("company_monthly_budget")).toBe("no limit");
      expect(result.get("xml_feed_link")).toBe("1");
      expect(result.get("campaign_id")).toBe("37852");
      expect(result.get("campaign_name")).toBe("Distribution - Leesport");
      expect(result.get("campaign_status")).toBe("active");
      expect(result.get("campaign_country")).toBe("United States");
    });
  });
  describe("populateComplexFields", () => {
    it("should populate complex fields correctly", () => {
      const result = service["populateComplexFields"]();

      expect(result.get("estimated_budget_days_remaining")).toBe("10");
      expect(result.get("campaign_cpc")).toBe("N/A");
      expect(result.get("xml_num_jobs")).toBe("0");
      expect(result.get("campaign_target_cost")).toBe("3.51");
      expect(result.get("campaign_sponsorship_ended")).toBe("1");
    });
  });
  describe("populateNewEntry", () => {
    it("should populate a new entry correctly", () => {
      const validateBudget = new Map<string, string>([["campaignStatus", "active"]]);
      const result = service["populateNewEntry"]("create", "monthly", "500", validateBudget);

      expect(result.get("talent_campaign")).toBe(
        "Mercy - AF - Publicador XYZ - 37852 - Distribution - Leesport",
      );
      expect(result.get("talent_feedcode")).toBe("api-client-name-Compañía ABC-USD");
      expect(result.get("action")).toBe("create");
      expect(result.get("source")).toBe("client-api");
      expect(result.get("campaign_delivery")).toBe("");
      expect(result.get("campaign_conversion_type")).toBe("");
      expect(result.get("campaign_budget_type")).toBe("monthly");
      expect(result.get("campaign_budget_value")).toBe("500");
    });
  });
  describe("stopDuplicationProcess", () => {
    it('should return an empty object if action is "campaign", campaign is duplicated, and isFullyAutoCampaign is true', async () => {
      service["apiClientName"] = "appcast";
      service["apiCampaignId"] = "appcast_12492_abc";
      service["isFullyAutoCampaign"] = true;
      jest.spyOn(externalCampaignService, "duplicatedCampaign").mockResolvedValue(true);
      const result = await service.stopDuplicationProcess("campaign");
      expect(result).toStrictEqual({});
    });
    it("should handle the action -", async () => {
      service["apiClientName"] = "appcast";
      service["apiCampaignId"] = "appcast_12492_abc";
      service["isFullyAutoCampaign"] = false;
      jest.spyOn(externalCampaignService, "duplicatedCampaign").mockResolvedValue(true);
      jest.spyOn(externalCampaignService, "pausedCampaignTalent").mockResolvedValue(true);
      await service.stopDuplicationProcess("-");
    });
  });
});
