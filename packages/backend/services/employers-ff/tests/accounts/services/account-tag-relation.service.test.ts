import { Test, TestingModule } from "@nestjs/testing";
import { AccountTagRelationRepository } from "../../../src/accounts/repositories/account-tag-relation.repository";
import { AccountTagRelationService } from "../../../src/accounts/services/account-tag-relation.service";
import { EntityManager } from "typeorm";
import { AccountTagRelationDto } from "../../../src/accounts/dto/account-tag-relation.dto";
import { AccountTag } from "../../../src/accounts/entities/account-tag-relation.entity";
import {
  accountTagRelationNewDataMock,
  accountTagRelationResponseDataMock,
} from "../../../src/common/mocks/accounts.data.mock";
import { GetAccountTagsDto } from "../../../src/accounts/dto/get-account-tag-relation.dto";
import { Logger } from "@nestjs/common";
import { CampaignTagService } from "../../../src/campaigns/services/campaign-tag-relation.service";

describe("AccountTagRelationService", () => {
  let service: AccountTagRelationService;
  let repository: AccountTagRelationRepository;
  let manager: EntityManager;
  let campaignTagService: CampaignTagService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AccountTagRelationService,
        {
          provide: AccountTagRelationRepository,
          useValue: {
            create: jest.fn(),
            getAccountTags: jest.fn(),
            delete: jest.fn(),
            update: jest.fn(),
          },
        },
        {
          provide: CampaignTagService,
          useValue: {
            assignCampaignTags: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<AccountTagRelationService>(AccountTagRelationService);
    repository = module.get<AccountTagRelationRepository>(AccountTagRelationRepository);
    campaignTagService = module.get<CampaignTagService>(CampaignTagService);
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });

  //Testing methods definition
  it("should have a method called createAccount", () => {
    expect(service.createTagRelation).toBeDefined(); // Check the method to exist.
  });

  // Testing accounts creation
  it("should create an account-relation", async () => {
    const createTagRelationDto: AccountTagRelationDto = accountTagRelationNewDataMock; // provide necessary data for testing
    const createdTagRelation: AccountTag = accountTagRelationResponseDataMock; // provide expected data for testing

    // Simulate value returned by createAccount
    jest.spyOn(repository, "create").mockResolvedValue(createdTagRelation);
    const result = await service.createTagRelation(createTagRelationDto, manager);
    expect(result).toEqual(createdTagRelation); // check if the expected value is as defined
  });

  describe("getAccountTags", () => {
    it("should return accoutn tag relation", async () => {
      const filters: GetAccountTagsDto = { tagId: 123 };
      const expectedResult = { accountTagRelationNewDataMock };

      (repository.getAccountTags as jest.Mock).mockResolvedValue(expectedResult);
      const result = await service.getAccountTags(filters);

      expect(result).toEqual(expectedResult);
      expect(repository.getAccountTags).toHaveBeenCalledWith(filters, false);
    });
    it("should throw an error if repository throws", async () => {
      const filters: GetAccountTagsDto = {}; // Coloca tus filtros aquí
      const error = new Error("Something went wrong");
      jest.spyOn(Logger, "error").mockImplementation(() => {});

      (repository.getAccountTags as jest.Mock).mockRejectedValue(error);

      await expect(service.getAccountTags(filters)).rejects.toThrow(error);
      expect(repository.getAccountTags).toHaveBeenCalledWith(filters, false);
      Logger.error(
        new Error(`Error getAccountTags with these filters: ${JSON.stringify({ filters })}`),
      );
    });
  });
  describe("delete", () => {
    it("should delete an account tag relation", async () => {
      const accountId = 1;

      jest.spyOn(repository, "delete").mockResolvedValue({ affected: 1 } as any);

      await service.delete(accountId);

      expect(repository.delete).toHaveBeenCalledWith(accountId);
    });

    it("should handle errors thrown by the repository", async () => {
      const accountId = 1;
      const error = new Error("Some error");

      jest.spyOn(repository, "delete").mockRejectedValue(error);

      await expect(service.delete(accountId)).rejects.toThrow(error);
      expect(repository.delete).toHaveBeenCalledWith(accountId);
    });
  });

  describe("update", () => {
    it("should update an account tag relation", async () => {
      const accountTagDto: AccountTagRelationDto = {
        id: 1,
        accountId: 1,
        tagId: 0,
      };

      jest.spyOn(repository, "update").mockResolvedValue({ affected: 1 } as any);

      await service.update(accountTagDto);

      expect(repository.update).toHaveBeenCalledWith(accountTagDto);
    });

    it("should handle errors thrown by the repository", async () => {
      const accountTagDto: AccountTagRelationDto = {
        id: 1,
        accountId: 1,
        tagId: 0,
      };
      const error = new Error("Some error");

      jest.spyOn(repository, "update").mockRejectedValue(error);

      await expect(service.update(accountTagDto)).rejects.toThrow(error);
      expect(repository.update).toHaveBeenCalledWith(accountTagDto);
    });
  });

  describe("updateAccountTags", () => {
    it("should update or create allowed tags and deactivate old tags", async () => {
      const accountId = 1;
      const tags = {
        Pixel: 1001,
        Postback: 1002,
        Reconciliation: 1003,
      };

      const organizedTags = {
        tagsListByType: {
          Pixel: [accountTagRelationResponseDataMock],
        },
        tagsActiveAccount: {
          Pixel: accountTagRelationResponseDataMock,
        },
      };

      jest.spyOn(service, "getOrganizedTagsByType").mockResolvedValue(organizedTags);
      jest.spyOn(service, "deactivateTags").mockResolvedValue(undefined);
      jest.spyOn(service, "updateOrCreateTagRelation").mockResolvedValue(undefined);

      const result = await service.updateAccountTags(accountId, tags, 80010);

      expect(service.getOrganizedTagsByType).toHaveBeenCalledWith(accountId);
      expect(service.updateOrCreateTagRelation).toHaveBeenCalledWith(
        accountId,
        1001,
        "Pixel",
        organizedTags.tagsListByType,
        80010,
      );
      expect(result).toBeUndefined(); // as it does not return anything
    });
  });

  describe("getOrganizedTagsByType", () => {
    it("should return organized tags by type and active tags", async () => {
      const accountId = 1;
      const tagsList = [
        { tag: { tagType: { name: "Pixel" } }, tagId: 1000, status: true },
        { tag: { tagType: { name: "Postback" } }, tagId: 1002, status: false },
      ];

      jest.spyOn(service, "getAccountTags").mockResolvedValue(tagsList);

      const result = await service.getOrganizedTagsByType(accountId);

      expect(service.getAccountTags).toHaveBeenCalledWith({ accountId }, true);
      expect(result).toEqual({
        tagsListByType: {
          Pixel: [{ tag: { tagType: { name: "Pixel" } }, tagId: 1000, status: true }],
          Postback: [{ tag: { tagType: { name: "Postback" } }, tagId: 1002, status: false }],
        },
        tagsActiveAccount: {
          Pixel: { tag: { tagType: { name: "Pixel" } }, tagId: 1000, status: true },
        },
      });
    });
  });

  describe("updateOrCreateTagRelation", () => {
    const accountId = 1;
    const tagID = 1001;
    const tagType = "Pixel";
    let tagsListByType: any;

    beforeEach(() => {
      tagsListByType = {
        Pixel: [accountTagRelationResponseDataMock],
      };
    });

    it("should update an existing inactive tag and set status to true", async () => {
      // Mock del tiempo para fechas fijas
      jest.useFakeTimers().setSystemTime(new Date("2024-01-15").getTime());

      // Modificar el mock para el caso de prueba
      tagsListByType.Pixel[0].status = false;
      tagsListByType.Pixel[0].tagId = tagID;

      jest.spyOn(service, "update").mockResolvedValue(accountTagRelationResponseDataMock);

      // Ejecutar el método
      await service.updateOrCreateTagRelation(accountId, tagID, tagType, tagsListByType, 80010);

      // Verificar que el servicio de actualización fue llamado correctamente
      expect(service.update).toHaveBeenCalledWith({
        id: tagsListByType.Pixel[0].id,
        tagId: tagsListByType.Pixel[0].tagId,
        accountId: tagsListByType.Pixel[0].accountId,
        status: true,
        updated: new Date("2024-01-15"),
        updatedBy: 80010,
      });

      // Restaurar los temporizadores reales después del test
      jest.useRealTimers();
    });

    it("should not update when tag is already active and matches the tagID", async () => {
      tagsListByType.Pixel[0].status = true;
      tagsListByType.Pixel[0].tagId = tagID;

      const updateSpy = jest
        .spyOn(service, "update")
        .mockResolvedValue(accountTagRelationResponseDataMock);

      await service.updateOrCreateTagRelation(accountId, tagID, tagType, tagsListByType, 80010);

      expect(updateSpy).not.toHaveBeenCalled(); // No update should happen
    });

    it("should deactivate an active tag with a different tagID", async () => {
      jest.useFakeTimers().setSystemTime(new Date("2024-01-15").getTime());
      tagsListByType.Pixel[0].status = true;
      tagsListByType.Pixel[0].tagId = 2001; // Different tagID
      jest.spyOn(service, "update").mockResolvedValue(accountTagRelationResponseDataMock);

      await service.updateOrCreateTagRelation(accountId, tagID, tagType, tagsListByType, 80010);

      expect(service.update).toHaveBeenCalledWith({
        id: tagsListByType.Pixel[0].id,
        tagId: tagsListByType.Pixel[0].tagId,
        accountId: tagsListByType.Pixel[0].accountId,
        status: false,
        updated: new Date("2024-01-15"),
        updatedBy: 80010,
      });
      jest.useRealTimers();
    });

    it("should create a new tag relation if no matching tag exists", async () => {
      jest.useFakeTimers().setSystemTime(new Date("2024-01-15").getTime());
      const newTagID = 3001;
      tagsListByType = {}; // No existing tags
      jest
        .spyOn(service, "createTagRelation")
        .mockResolvedValue(accountTagRelationResponseDataMock);

      await service.updateOrCreateTagRelation(accountId, newTagID, tagType, tagsListByType, 80010);

      expect(service.createTagRelation).toHaveBeenCalledWith({
        tagId: newTagID,
        accountId,
        created: new Date("2024-01-15"),
        createdBy: 80010,
      });
      jest.useRealTimers();
    });
    jest.restoreAllMocks();
  });

  describe("deactivateTags", () => {
    it("should deactivate the active tags", async () => {
      jest.useFakeTimers().setSystemTime(new Date("2024-01-15").getTime());
      const tags = [accountTagRelationResponseDataMock];
      const createdTagRelation: AccountTag = accountTagRelationResponseDataMock;

      jest.spyOn(service, "update").mockResolvedValue(createdTagRelation);

      await service.deactivateTags(tags, 80010);

      expect(service.update).toHaveBeenCalledWith({
        id: 9999,
        tagId: 2001,
        accountId: 1,
        status: false,
        updated: new Date("2024-01-15"),
        updatedBy: 80010,
      });
      jest.useRealTimers();
    });
  });
});
