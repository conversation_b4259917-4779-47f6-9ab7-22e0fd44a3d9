import { Test, TestingModule } from "@nestjs/testing";
import { QuickbooksController } from "../../../src/billings/controllers/quickbooks.controller";
import { QuickbooksService } from "../../../src/billings/services/quickbooks.service";
import { CreateQuickBooksCustomerDto } from "../../../src/billings/dto/create-quickbooks-customer.dto";
import {
  Customer,
  FormattedCreditMemo,
  FormattedPayment,
} from "../../../src/common/interfaces/quickbooks.interface";
import { CustomAxiosAdapter } from "../../../src/common/adapters/axios.adapter";
import { BillingsRepository } from "../../../src/billings/repositories/billings.repository";
import { LeadsLegacyRepository } from "../../../src/billings/repositories/leads-legacy.repository";
import { PpcLegacyRepository } from "../../../src/billings/repositories/ppc-legacy.repository";
import { QuickbooksBackupsRepository } from "../../../src/billings/repositories/quickbooks-backups.repository";
import { UsersLegacyRepository } from "../../../src/billings/repositories/users-legacy.repository";
import { QuickbooksSettingsRepository } from "../../../src/billings/repositories/quickbooks-settings.repository";
import { MetricsService } from "../../../src/common/services/metrics.service";
import { Metrics } from "@talent-back-libs/metrics";
import { AccountRepository } from "../../../src/accounts/repositories/account.repository";
import { UserAuthDto } from "../../../src/common/dtos/user-auth.dto";
import { AccountIdDto } from "../../../src/billings/dto/accountId.dto";
import { BillingPermissionService } from "../../../src/billings/services/billing-permission.service";

describe("QuickbooksController", () => {
  let service: QuickbooksService;
  let controller: QuickbooksController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [QuickbooksController],
      providers: [
        QuickbooksService,
        {
          provide: CustomAxiosAdapter,
          useValue: {
            get: jest.fn(),
            post: jest.fn(),
          },
        },
        {
          provide: BillingsRepository,
          useValue: {
            getByQuickbooksId: jest.fn(),
            getCompaniesBankingInformation: jest.fn(),
          },
        },
        {
          provide: LeadsLegacyRepository,
          useValue: {
            find: jest.fn(),
          },
        },
        {
          provide: PpcLegacyRepository,
          useValue: {
            find: jest.fn(),
          },
        },
        {
          provide: QuickbooksBackupsRepository,
          useValue: {
            find: jest.fn(),
          },
        },
        {
          provide: UsersLegacyRepository,
          useValue: {
            find: jest.fn(),
          },
        },
        {
          provide: QuickbooksSettingsRepository,
          useValue: {
            create: jest.fn(),
            update: jest.fn(),
            getByCompanyName: jest.fn(),
            delete: jest.fn(),
            findOne: jest.fn(),
            findAll: jest.fn(),
          },
        },
        {
          provide: Metrics,
          useValue: {
            initializeMetrics: jest.fn(),
            setMeter: jest.fn(),
            getMeter: jest.fn(),
            createCounter: jest.fn(),
            createObservableGauge: jest.fn(),
            addObservable: jest.fn(),
            addValueToCounter: jest.fn(),
            createHistogram: jest.fn(),
            startHistogramMeasure: jest.fn(),
            finishHistogramMeasure: jest.fn(),
          },
        },
        {
          provide: MetricsService,
          useValue: {
            setupMetricsVariables: jest.fn(),
          },
        },
        {
          provide: AccountRepository,
          useValue: {
            getAccountsBillings: jest.fn(),
          },
        },
        {
          provide: BillingPermissionService,
          useValue: {
            checkPrivilagesByUserId: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<QuickbooksService>(QuickbooksService);
    controller = module.get<QuickbooksController>(QuickbooksController);
  });

  describe("getCreditMemoById", () => {
    it("should call getCompanyFormattedCreditMemoById method of service and return the result", async () => {
      const id = 1;
      const company = "example company";
      const formattedCreditMemo: FormattedCreditMemo = {
        cc: "",
        companyInfo: [],
        companyLabel: "",
        country: "",
        creditTo: {
          address: [],
          email: "",
          company: "",
          name: "",
          addressDetails: { address: "" },
        },
        date: "",
        ht: "",
        items: [],
        language: "",
        number: 0,
        rebate: "",
        subtotal: "",
        taxes: [],
        total: { "": "" },
      };

      jest
        .spyOn(service, "getCompanyFormattedCreditMemoById")
        .mockResolvedValue(formattedCreditMemo);

      const result = await controller.getCreditMemoById({} as UserAuthDto, id, company);

      expect(service.getCompanyFormattedCreditMemoById).toHaveBeenCalledWith(id, company);
      expect(result).toBe(formattedCreditMemo);
    });
  });

  describe("getPaymentById", () => {
    it("should call getCompanyFormattedPaymentById method of service and return the result", async () => {
      const id = 1;
      const company = "example company";
      const formattedPayment: FormattedPayment = {
        companyLabel: "",
        companyInfo: {
          address: [],
          email: "",
          details: undefined,
          legal: undefined,
        },
        PaymentDate: "",
        ReceivedFrom: {
          company: "",
          name: "",
          email: "",
          address: [],
        },
        PaymentMethod: "",
        ReferenceNumber: "123",
        items: [
          {
            Payment: "100.00",
            InvoiceNumber: 1,
            InvoiceDate: "2021-01-01",
            DueDate: "",
            OriginalAmount: "",
            Balance: "",
          },
        ],
        country: "",
        language: "",
        total: { total: "100.00" },
      };

      jest.spyOn(service, "getCompanyFormattedPaymentById").mockResolvedValue(formattedPayment);

      const result = await controller.getPaymentById({} as UserAuthDto, id, company);

      expect(service.getCompanyFormattedPaymentById).toHaveBeenCalledWith(id, company);
      expect(result).toBe(formattedPayment);
    });
  });

  describe("createClientQbo", () => {
    it("should call createCompanyCustomer method of service and return no result", async () => {
      const createClientQboDto: CreateQuickBooksCustomerDto = {
        company: "",
        firstName: "",
        lastName: "",
        companyName: "",
        displayName: "",
        email: "",
        address: "",
        city: "",
        state: "",
        country: "",
        postalCode: "",
        currency: "",
      };

      jest.spyOn(service, "createCompanyCustomer").mockResolvedValueOnce(undefined);

      const result = await controller.createClientQbo({} as UserAuthDto, createClientQboDto, {
        accountId: 1,
      } as AccountIdDto);

      expect(service.createCompanyCustomer).toHaveBeenCalledWith(createClientQboDto);

      expect(result).toBeUndefined();
    });
  });
});
