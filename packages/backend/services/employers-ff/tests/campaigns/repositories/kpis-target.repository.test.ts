import { Test, TestingModule } from "@nestjs/testing";
import { KpisTargetRepository } from "../../../src/campaigns/repositories/kpis-target.repository";
import { KpiTarget } from "../../../src/campaigns/entities/kpi-target.entity";

import { Repository } from "typeorm";
import { getRepositoryToken } from "@nestjs/typeorm";
import {
  kpisTargetListDataMock,
  kpisTargetNewDataMock,
} from "../../../src/common/mocks/accounts.data.mock";
import { CreateKpiTargetDto } from "../../../src/campaigns/dto/create-kpi-target.dto";
import { NotFoundException } from "@nestjs/common";
import { UpdateKpiTargetDto } from "../../../src/campaigns/dto/update-kpi-target.dto";

describe("KpisTargetRepository", () => {
  //let service: KpisTargetService;
  let repository: KpisTargetRepository;
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  let mockRepository: Repository<KpiTarget>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        KpisTargetRepository,
        {
          provide: getRepositoryToken(KpiTarget),
          useValue: {
            findKpiTargetsByCampaignId: jest.fn(),
            findAll: jest.fn(),
            findOne: jest.fn(),
            create: jest.fn(),
            save: jest.fn(),
          },
        },
      ],
    }).compile();

    repository = module.get<KpisTargetRepository>(KpisTargetRepository);
    mockRepository = module.get<Repository<KpiTarget>>(getRepositoryToken(KpiTarget));
  });

  it("should be defined", () => {
    expect(repository).toBeDefined();
  });

  describe("findKpiTargetsByCampaignId", () => {
    it("should return all KpiTargets by campaignID", async () => {
      const kpisTarget = kpisTargetListDataMock;
      repository.findKpiTargetsByCampaignId = jest.fn().mockResolvedValue(kpisTarget);

      const result = await repository.findKpiTargetsByCampaignId(1);

      expect(result).toEqual(kpisTarget);
    });

    it("should throw a NotFoundException when campaignID does not exist", async () => {
      const mockCampaignId = 999999999; // Campaign ID that does not exist

      // Mock the findKpiTargetsByCampaignId method to return null
      repository.findKpiTargetsByCampaignId = jest
        .fn()
        .mockRejectedValue(new NotFoundException("campaignID cannot be null"));

      await expect(repository.findKpiTargetsByCampaignId(mockCampaignId)).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe("findAll", () => {
    it("should return all KpiTargets", async () => {
      const kpiTargets = kpisTargetListDataMock;
      repository.findAll = jest.fn().mockResolvedValue(kpiTargets);
      const result = await repository.findAll();

      expect(result).toEqual(kpiTargets);
    });
  });

  describe("findOne", () => {
    it("should return a KpiTarget object when a valid id is provided", async () => {
      // Mocking the findOne method of the repository to return a dummy KpiTarget
      const mockKpiTarget = kpisTargetNewDataMock;
      jest.spyOn(repository, "findOne").mockResolvedValue(mockKpiTarget);

      // Call the findOne method with a valid id
      const result = await repository.findOne(1);

      // Assert that the result is the same as the mock KpiTarget
      expect(result).toEqual(mockKpiTarget);
    });

    it("should return null when an invalid id is provided", async () => {
      // Mocking the findOne method of the repository to return null
      jest.spyOn(repository, "findOne").mockResolvedValue(null);

      // Call the findOne method with an invalid id
      const result = await repository.findOne(9999999);

      // Assert that the result is null
      expect(result).toBeNull();
    });
  });

  describe("create", () => {
    it("should create a new KpiTarget", async () => {
      const createDto: CreateKpiTargetDto = kpisTargetNewDataMock;
      const createdKpiTarget: KpiTarget = kpisTargetNewDataMock;
      repository.create = jest.fn().mockReturnValue(createDto);
      repository.save = jest.fn().mockResolvedValue(createdKpiTarget);

      const result = await repository.create(createDto);

      expect(result).toEqual(createdKpiTarget);
    });
  });

  describe("save", () => {
    it("should save and return the updated KpiTarget", async () => {
      // Create a mock KpiTarget object
      const mockKpiTarget: KpiTarget = kpisTargetNewDataMock;

      // Mock the save method of the repository to return the mock KpiTarget
      jest.spyOn(repository, "save").mockResolvedValue(mockKpiTarget);

      // Call the save method with the mock KpiTarget
      const result = await repository.save(mockKpiTarget);

      // Assert that the result is the same as the mock KpiTarget
      expect(result).toEqual(mockKpiTarget);
    });

    it("should throw NotFoundException if kpiToUpdate is null", async () => {
      // Mock the repository method to throw NotFoundException
      repository.save = jest.fn().mockImplementation(async (kpiToUpdate: KpiTarget & null) => {
        if (!kpiToUpdate) {
          throw new NotFoundException(`Kpi target not found`);
        }
        // Return whatever you want if kpiToUpdate is not null
        return Promise.resolve(kpiToUpdate);
      });

      // Call the method with a non-null argument
      const kpiToUpdate: KpiTarget = kpisTargetNewDataMock;
      await expect(repository.save(kpiToUpdate)).resolves.not.toThrow(NotFoundException);
    });
  });
});
