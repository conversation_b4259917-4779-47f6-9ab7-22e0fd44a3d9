import { Test, TestingModule } from "@nestjs/testing";
import stringToOptions, {
  getDealsStageOptions,
} from "../../../src/deals/decorators/stringToOptions";
import { stringToDateRange } from "../../../src/deals/decorators/stringToDateRange";
import { IsDateRangeValid } from "../../../src/deals/decorators/isValidDateRange";
import { Validator } from "class-validator";
import exp from "constants";

describe("Deals Decorators", () => {
  const validator = new Validator();
  class TestClass {
    @IsDateRangeValid()
    closeDateRange: { startDate: Date; endDate: Date };
  }

  beforeEach(async () => {});

  it("should parse closeDateRange if string", () => {
    let startDateString = "Wed Jan 17 2024";
    let endDateString = "Fri May 17 2024";
    let response = stringToDateRange(`${startDateString}-${endDateString}`);
    expect(response).toEqual({
      startDate: new Date(startDateString),
      endDate: new Date(endDateString),
    });
  });

  it("should parse closeDateRange if object", () => {
    let startDateString = "Wed Jan 17 2024";
    let endDateString = "Fri May 17 2024";
    let expectedResponse = {
      startDate: new Date(startDateString),
      endDate: new Date(endDateString),
    }
    let response = stringToDateRange(expectedResponse);
    expect(response).toEqual(expectedResponse);
  });

  it("should parse dealStages Options if string", () => {
    let response = stringToOptions("Closed won,Dormant", getDealsStageOptions());
    expect(response).toEqual([4581172, 4654391]);
  });

  it("should parse dealStages Options if array", () => {
    let expectedResponse = [4581172, 4654391];
    let response = stringToOptions(expectedResponse, getDealsStageOptions());
    expect(response).toEqual(expectedResponse);
  });

  it("validate date range", () => { 
    let object = { startDate: new Date("Wed Jan 17 2024"), endDate: new Date("Fri May 17 2024") };
    const testObject = new TestClass();
    testObject.closeDateRange = object;
    const errors = validator.validateSync(testObject);
    expect(errors).toHaveLength(0);
  });

  it("validate date range -error", () => { 
    let object = { startDate: new Date("Wed Jan 17 2025"), endDate: new Date("Fri May 17 2024") };
    const testObject = new TestClass();
    testObject.closeDateRange = object;
    const errors = validator.validateSync(testObject);
    expect(errors).toHaveLength(1);
  });
});
