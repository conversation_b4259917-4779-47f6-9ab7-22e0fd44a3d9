import { Test, TestingModule } from "@nestjs/testing";
import { StreamJobsService } from "../../src/stream-jobs/stream-jobs.service";
import { KafkaManagerService } from "../../../../libs/ts/queue/src/kafka/kafka-manager.service";
import {
  ContentType,
  EventVersionType,
} from "../../../../libs/ts/queue/src/kafka/types/event-message.interface";
import {
  JOB_EVENT_TYPES,
  KAFKA_BROKER,
  KAFKA_BROKER_EVENTS,
  KAFKA_CLIENT_ID,
  KAFKA_DEAD_LETTER_TOPIC,
  KAFKA_EVENT_MANAGER_TOPIC,
  KAFKA_LOG_JOBS,
  KAFKA_STREAM_TOPIC,
  PROCESS_STATUS,
} from "../../src/common/utils/constants";
import { Metrics } from "@talent-back-libs/metrics";
import { ProcessedEventMessageDto } from "../../src/common/dto/post-processing-jobs.dto";

describe("StreamJobsService", () => {
  let streamJobsService: StreamJobsService;
  let kafkaBrokerJobsEvents: KafkaManagerService;
  let kafkaBrokerEventManager: KafkaManagerService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        StreamJobsService,
        {
          provide: Metrics,
          useValue: {
            initializeMetrics: jest.fn(),
            setMeter: jest.fn(),
            getMeter: jest.fn(),
            createCounter: jest.fn(),
            addValueToCounter: jest.fn(),
            createHistogram: jest.fn(),
            startHistogramMeasure: jest.fn(),
            finishHistogramMeasure: jest.fn(),
          },
        },
        {
          provide: "KAFKA_JOBS_EVENTS",
          useValue: {
            producer: {
              sendMessages: jest.fn(),
            },
          },
          /**
           *
           */
          useFactory: () =>
            KafkaManagerService.getInstanceReplication(KAFKA_BROKER, KAFKA_CLIENT_ID),
        },
        {
          provide: "KAFKA_EVENTS_MANAGER",
          useValue: {
            producer: {
              sendMessages: jest.fn(),
            },
          },
          /**
           *
           */
          useFactory: () =>
            KafkaManagerService.getInstanceReplication(KAFKA_BROKER_EVENTS, KAFKA_CLIENT_ID),
        },
      ],
    }).compile();

    streamJobsService = module.get<StreamJobsService>(StreamJobsService);
    kafkaBrokerJobsEvents = module.get<jest.Mocked<KafkaManagerService>>("KAFKA_JOBS_EVENTS");
    kafkaBrokerEventManager = module.get<jest.Mocked<KafkaManagerService>>("KAFKA_EVENTS_MANAGER");
  });

  const version: EventVersionType = "1.0"; // Correct
  const __mockJobEvent: ProcessedEventMessageDto = {
    headers: {
      version: version,
      event_type: JOB_EVENT_TYPES.NEW,
      content_type: ContentType.JSON,
      correlation_id: "1234",
    },
    value: {
      id: "1234",
      source_title: "Software Engineer",
      system_scanid: 123456,
      source_company_name: "Tech Company",
      source_location: "San Francisco, CA",
      system_feedcode: "feedcode1",
      system_feed_type: "CAR",
      system_crawlgroup: 2,
      system_status: 1,
      system_hash_source_unique_job: "hash1",
      system_jobhash: "jobhash1",
      source_jobdesc_text: "Job description here",
      enrich_geo_city: "city",
      enrich_geo_country: "country",
      enrich_geo_region1: "region1",
      enrich_geo_lat: 123,
      enrich_geo_lon: 456,
    },
  };

  it("should be defined", () => {
    expect(streamJobsService).toBeDefined();
  });

  describe("stream job", () => {
    
    it("should call kafkaService.producer.sendMessages with error", async () => {
      // Arrange
      const processedJobs = [__mockJobEvent];

      // Mock KafkaManagerService's sendMessages method
      jest
        .spyOn(kafkaBrokerJobsEvents.producer, "sendMessages")
        .mockRejectedValueOnce(new Error("Error sending messages to Kafka"));

      // Act & Assert
      await expect(
        streamJobsService["sendMessagesJobsEvents"](KAFKA_STREAM_TOPIC, processedJobs as any),
      ).rejects.toThrow("Error sending messages to Kafka");
    });

    it("should call kafkaService.producer.sendMessages with not allowed status", async () => {
      // Arrange
      const __mockWrongJobEvent: ProcessedEventMessageDto = {
        headers: {
          version: version,
          event_type: JOB_EVENT_TYPES.NEW,
          content_type: ContentType.JSON,
          correlation_id: "1234",
        },
        value: {
          id: "1234",
          system_scanid: 123456,
          source_title: "Software Engineer",
          source_company_name: "Tech Company",
          source_location: "San Francisco, CA",
          system_feedcode: "feedcode1",
          system_crawlgroup: 2,
          system_status: undefined,
          system_hash_source_unique_job: "hash1",
          system_jobhash: "jobhash1",
          source_jobdesc_text: "Job description here",
          enrich_geo_city: "city",
          enrich_geo_country: "country",
          enrich_geo_region1: "region1",
          enrich_geo_lat: 123,
          enrich_geo_lon: 456,
        },
      };
      const processedJobs = [__mockWrongJobEvent];

      // Mock KafkaManagerService's sendMessages method
      const sendMessagesSpy = jest.spyOn(kafkaBrokerJobsEvents.producer, "sendMessages");

      // Act
      streamJobsService.streamJob(processedJobs);

      // Assert
      expect(sendMessagesSpy).not.toHaveBeenCalledWith({
        topic: KAFKA_STREAM_TOPIC,
        messages: processedJobs.map((job) => {
          return { key: job.value.id, value: JSON.stringify(job) };
        }),
      });
    });
  });

  describe("Send messages to dead letter", () => {
    it("should send the message to dead-letter topic when exceed the retry limit", async () => {
      const processedJobs = [
        {
          ...__mockJobEvent,
          headers: {
            version: version,
            event_type: JOB_EVENT_TYPES.NEW,
            content_type: ContentType.JSON,
            correlation_id: "1234",
            retry: 5,
          },
        },
      ];

      // Mock KafkaManagerService's sendMessages method
      const sendMessagesSpy = jest.spyOn(kafkaBrokerJobsEvents.producer, "sendMessages");

      streamJobsService.sendMessagesToDeadLetter(processedJobs);

      expect(sendMessagesSpy).toHaveBeenCalledWith({
        topic: KAFKA_DEAD_LETTER_TOPIC,
        messages: processedJobs.map((job) => {
          return { key: job.value.id, value: JSON.stringify(job) };
        }),
      });
    });
  });

  describe("Send messages to log", () => {
    it("should send the message to log topic", async () => {
      const processedJobs = [__mockJobEvent];

      // Mock KafkaManagerService's sendMessages method
      const sendMessagesSpy = jest.spyOn(kafkaBrokerEventManager.producer, "sendMessages");

      // Mock Unixtime
      const mockDate = new Date("2025-01-27T18:00:00Z"); // UTC time for the given date
      jest.spyOn(Date, "now").mockImplementation(() => mockDate.getTime());
      const expectedUnixtime = Math.floor(mockDate.getTime() / 1000);

      streamJobsService.sendMessagesToSendLogs(processedJobs);

      expect(sendMessagesSpy).toHaveBeenCalledWith({
        topic: KAFKA_LOG_JOBS,
        messages: processedJobs.map((job) => {
          return {
            key: job.value.system_scanid.toString(),
            value: JSON.stringify({
              unixtime: expectedUnixtime,
              event_type: "job_report",
              job_event_type: job.headers?.event_type,
              id: job.value?.id,
              correlation_id: job.headers?.correlation_id,
              system_scanid: job.value?.system_scanid,
              system_feedcode: job.value?.system_feedcode,
              system_feed_type: job.value?.system_feed_type,
              system_status: job.value?.system_status,
              system_status_reason: job.value?.system_status_reason,
              system_hash_source_unique_job: job.value?.system_hash_source_unique_job,
              system_jobhash: job.value?.system_jobhash,
              system_date_expired: job.value?.system_date_expired,
              system_date_found: job.value?.system_date_found,
              system_date_updated: job.value?.system_date_updated,
              system_date_re_found: job.value?.system_date_re_found,
              system_parent_id: job.value?.system_parent_id,
              system_expansion_rule: job.value?.system_expansion_rule,
              aws_account: job.headers?.aws_account,
              process_status: PROCESS_STATUS.FINISHED,
            }),
          };
        }),
      });
    });

    it("should call kafkaService.producer.sendMessages with error", async () => {
      // Arrange
      const processedJobs = [__mockJobEvent];

      // Mock KafkaManagerService's sendMessages method
      jest
        .spyOn(kafkaBrokerEventManager.producer, "sendMessages")
        .mockRejectedValueOnce(new Error("Error sending messages to Kafka"));

      // Act & Assert
      await expect(
        streamJobsService["sendMessagesEventManager"](
          KAFKA_EVENT_MANAGER_TOPIC,
          processedJobs as any,
        ),
      ).rejects.toThrow("Error sending messages to Kafka");
    });
  });
});
