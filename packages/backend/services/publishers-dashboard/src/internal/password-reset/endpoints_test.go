package passwordreset

import (
	"context"
	"net/http"
	"talent/publishers-dashboard/src/internal/auth"
	"talent/publishers-dashboard/src/internal/models"
	"talent/publishers-dashboard/src/internal/tools"
	"testing"
)

func TestMakePasswordResetRequestSuccess(t *testing.T) {
	tools.LoadTestEnvVariables()
	auth.ResetSigningKey()
	mockPasswordResetSvc := tools.MockPasswordResetService{}
	endpoint := MakeResetPasswordEndpoint(&mockPasswordResetSvc)
	passwordResetRequest := models.PasswordResetRequest{
		Email: "<EMAIL>",
	}

	// mock HTTP request with authorization header
	req, _ := http.NewRequest("GET", "/api/v1/password-reset-request", nil)
	req.Header.Set("Authorization", tools.AuthTokenForTests)
	req.AddCookie(tools.UserCookie())
	ctx := context.WithValue(context.Background(), models.HttpRequest, req)
	_, err := endpoint(ctx, passwordResetRequest)
	if err != nil {
		t.<PERSON><PERSON><PERSON>("Error: %v", err)
	}
}

func TestMakePasswordResetRequestFailsBadRequest(t *testing.T) {
	tools.LoadTestEnvVariables()
	auth.ResetSigningKey()

	// mock HTTP request without authorization header
	req, _ := http.NewRequest("GET", "/api/v1/password-reset-request", nil)
	req.Header.Set("Authorization", tools.AuthTokenForTests)
	ctx := context.WithValue(context.Background(), models.HttpRequest, req)

	// test using a wrong request
	mockPasswordResetSvc := tools.MockPasswordResetService{}
	endpoint := MakeResetPasswordEndpoint(&mockPasswordResetSvc)
	_, err := endpoint(ctx, models.UserRequest{
		EncryptedId: "12345",
	})
	if err == nil {
		t.Errorf("Expected error, got nil")
	}

}

func TestMakePasswordResetRequestFails(t *testing.T) {
	tools.LoadTestEnvVariables()
	auth.ResetSigningKey()

	// mock HTTP request without authorization header
	req, _ := http.NewRequest("GET", "/api/v1/password-reset-request", nil)
	req.Header.Set("Authorization", tools.AuthTokenForTests)
	req.AddCookie(tools.UserCookie())
	ctx := context.WithValue(context.Background(), models.HttpRequest, req)

	// test using a wrong request
	mockPasswordResetSvc := tools.MockPasswordResetService{}
	endpoint := MakeResetPasswordEndpoint(&mockPasswordResetSvc)
	resp, _ := endpoint(ctx, models.PasswordResetRequest{
		Email: "<EMAIL>",
	})
	response := resp.(models.ServerResponse)
	if response.Payload != nil {
		t.Errorf("Expected error, got nil")
	}
}

func TestMakeGetUserFromTokenEndpointSuccess(t *testing.T) {
	tools.LoadTestEnvVariables()
	auth.ResetSigningKey()
	mockPasswordResetSvc := tools.MockPasswordResetService{}
	endpoint := MakeGetUserFromTokenEndpoint(&mockPasswordResetSvc)
	passwordResetRequest := models.PasswordResetToken{
		UserToken: "asdfdsafasdfsdf",
	}

	// mock HTTP request with authorization header
	req, _ := http.NewRequest("GET", "/api/v1/get-user-by-token/{token}", nil)
	req.Header.Set("Authorization", tools.AuthTokenForTests)
	req.AddCookie(tools.UserCookie())
	ctx := context.WithValue(context.Background(), models.HttpRequest, req)
	_, err := endpoint(ctx, passwordResetRequest)
	if err != nil {
		t.Errorf("Error: %v", err)
	}
}

func TestMakeGetUserFromTokenEndpointFails(t *testing.T) {
	tools.LoadTestEnvVariables()
	auth.ResetSigningKey()

	// mock HTTP request without authorization header
	req, _ := http.NewRequest("GET", "/api/v1/get-user-by-token/{token}", nil)
	req.Header.Set("Authorization", tools.AuthTokenForTests)
	req.AddCookie(tools.UserCookie())
	ctx := context.WithValue(context.Background(), models.HttpRequest, req)

	// test using a wrong request
	mockPasswordResetSvc := tools.MockPasswordResetService{}
	endpoint := MakeGetUserFromTokenEndpoint(&mockPasswordResetSvc)
	resp, _ := endpoint(ctx, models.PasswordResetToken{
		UserToken: "wrongtoken",
	})
	response := resp.(models.ServerResponse)
	if response.Payload != nil {
		t.Errorf("Expected error, got nil")
	}

}

func TestMakeConfirmPasswordResetEndpointSuccess(t *testing.T) {
	tools.LoadTestEnvVariables()
	auth.ResetSigningKey()
	mockPasswordResetSvc := tools.MockPasswordResetService{}
	endpoint := MakeConfirmPasswordResetEndpoint(&mockPasswordResetSvc)
	passwordResetRequest := models.PasswordResetConfirm{
		EncryptedToken: "asdadasdasdas",
		Password:       "Password123|||@@@@",
	}

	// mock HTTP request with authorization header
	req, _ := http.NewRequest("GET", "/api/v1/confirm-password-reset", nil)
	req.Header.Set("Authorization", tools.AuthTokenForTests)
	req.AddCookie(tools.UserCookie())
	ctx := context.WithValue(context.Background(), models.HttpRequest, req)
	_, err := endpoint(ctx, passwordResetRequest)
	if err != nil {
		t.Errorf("Error: %v", err)
	}
}

func TestMakeConfirmPasswordResetEndpointFails(t *testing.T) {
	tools.LoadTestEnvVariables()
	auth.ResetSigningKey()

	// mock HTTP request without authorization header
	req, _ := http.NewRequest("GET", "/api/v1/confirm-password-reset", nil)
	req.Header.Set("Authorization", tools.AuthTokenForTests)
	req.AddCookie(tools.UserCookie())
	ctx := context.WithValue(context.Background(), models.HttpRequest, req)

	// test using a wrong request
	mockPasswordResetSvc := tools.MockPasswordResetService{}
	endpoint := MakeConfirmPasswordResetEndpoint(&mockPasswordResetSvc)
	resp, _ := endpoint(ctx, models.PasswordResetConfirm{
		EncryptedToken: "wrongtoken",
		Password:       "Password123|||@@@@",
	})
	response := resp.(models.ServerResponse)
	if response.Payload != nil {
		t.Errorf("Expected error, got nil")
	}

}
