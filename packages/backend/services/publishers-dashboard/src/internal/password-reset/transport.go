package passwordreset

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"talent/publishers-dashboard/src/internal/models"
	"talent/publishers-dashboard/src/internal/tools"
)

var searchRequest models.DashboardEvents

const (
	invalidToken         = "invalid token"
	PasswordResetEvent   = "dashboard_passwordreset"
	ConfirmPasswordEvent = "dashboard_confirmpassword"
)

func DecodePasswordResetRequest(ctx context.Context, r *http.Request) (interface{}, error) {
	var req models.PasswordResetRequest

	err := json.NewDecoder(r.Body).Decode(&req)
	if err != nil {
		searchRequest.TotalErrors = 1
	} else {
		searchRequest.TotalUpdated = 1
		searchRequest.TotalPasswordReseted = 1
	}
	defer func() {
		go func() {
			tools.SendEvent(searchRequest, PasswordResetEvent)
		}()
	}()
	if err != nil {
		return nil, err
	}
	return req, nil
}

func EncodeResponse(ctx context.Context, w http.ResponseWriter, response interface{}) error {
	w.Header().Set("Content-Type", "application/json")
	if err, ok := response.(error); ok {
		w.WriteHeader(http.StatusUnauthorized)
		return json.NewEncoder(w).Encode(map[string]string{"error": err.Error()})
	}
	if resp, ok := response.(models.ServerResponse); ok {
		if resp.Status == http.StatusNoContent {
			w.WriteHeader(http.StatusNoContent)
			return nil
		}
		w.WriteHeader(resp.Status)
		return json.NewEncoder(w).Encode(resp)
	}

	w.WriteHeader(http.StatusOK)
	return json.NewEncoder(w).Encode(response)
}

func DecodeGetUserFromTokenRequest(ctx context.Context, r *http.Request) (interface{}, error) {
	var req models.PasswordResetToken
	token := r.PathValue("token")
	decryptedToken, err := tools.Decrypt(token)
	if err != nil {
		return models.ServerResponse{
			Status:  http.StatusBadRequest,
			Message: invalidToken,
		}, fmt.Errorf(invalidToken)
	}

	err = json.Unmarshal([]byte(decryptedToken), &req)
	if err != nil {
		return nil, err
	}
	return req, nil
}

func DecodeConfirmPasswordRequest(ctx context.Context, r *http.Request) (interface{}, error) {
	var req models.PasswordResetConfirm

	err := json.NewDecoder(r.Body).Decode(&req)
	if err != nil {
		searchRequest.TotalErrors = 1
	} else {
		searchRequest.TotalConfirmPassword = 1
	}
	defer func() {
		go func() {
			tools.SendEvent(searchRequest, ConfirmPasswordEvent)
		}()
	}()
	if err != nil {
		return models.ServerResponse{
			Status:  http.StatusBadRequest,
			Message: "invalid request",
		}, fmt.Errorf("invalid request")
	}

	decrypted, _ := tools.Decrypt(req.EncryptedToken)
	err = json.Unmarshal([]byte(decrypted), &req.Token)
	if err != nil {
		return models.ServerResponse{
			Status:  http.StatusBadRequest,
			Message: invalidToken,
		}, fmt.Errorf(invalidToken)
	}
	return req, nil
}
