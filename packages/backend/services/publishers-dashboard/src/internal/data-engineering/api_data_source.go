package dataengineering

import (
	"context"
	"database/sql"
	"fmt"
	"log/slog"
	"math"
	"strings"
	database "talent/libs/database/src"
	"talent/publishers-dashboard/src/internal/models"
	"talent/publishers-dashboard/src/internal/tools"
	publishercache "talent/publishers-dashboard/src/internal/tools/cache"
	"time"
)

type apiDataSource struct {
	db    database.DbRead[database.Model]
	cache *publishercache.PublisherCache
}

// apiDataRow represents a single row of API data without embedded interfaces
type apiDataRow struct {
	Date             time.Time
	TotalRevenue     float64
	TotalClicks      int
	ApiInvalidClicks int
	QualityApply     float64
	QualityViews     float64
}

func NewApiDataSource(db database.DbRead[database.Model], cache *publishercache.PublisherCache) apiDataSource {
	return apiDataSource{
		db:    db,
		cache: cache,
	}
}

func (ods *apiDataSource) FetchData(ctx context.Context, sr models.SearchRequest) (models.StatsResponse, error) {
	now := time.Now()

	// Parse and validate dates
	dates, err := tools.ParseAndFormatDateRange(sr.Date)
	if err != nil || len(dates) < 2 {
		return models.StatsResponse{}, fmt.Errorf("invalid date format")
	}

	// Pre-calculate country ACR if needed
	var countryAcr float64
	if sr.Country != "" && sr.Token != "" {
		countryAcr, err = tools.GetCountryEcpc(ctx, sr.Country, "partnerApi")
		if err != nil {
			slog.Warn("calculating country ACR from monetization", "err", err)
		}
	}

	// Build query conditions more efficiently
	whereConditions, whereValues := ods.buildWhereClause(dates, sr)

	// Build the complete query
	query := fmt.Sprintf(`
		SELECT %s 
		FROM %s 
		WHERE %s 
		GROUP BY day 
		ORDER BY day ASC`,
		strings.Join(queryFieldsApi, ", "),
		string(models.PublishersExternal),
		strings.Join(whereConditions, " AND "))

	fmt.Println("Query built in:", time.Since(now))
	queryStart := time.Now()

	// Execute query directly using QueryRows
	rows, err := ods.db.QueryRows(ctx, query, whereValues...)
	if err != nil {
		return models.StatsResponse{}, fmt.Errorf("database query failed: %w", err)
	}
	defer rows.Close()

	fmt.Println("Query executed in:", time.Since(queryStart))
	parseStart := time.Now()

	// Parse results directly without reflection
	apiDataRows, err := ods.parseRows(rows)
	if err != nil {
		return models.StatsResponse{}, fmt.Errorf("failed to parse rows: %w", err)
	}

	if len(apiDataRows) == 0 {
		return models.StatsResponse{}, fmt.Errorf("404")
	}

	fmt.Println("Rows parsed in:", time.Since(parseStart))
	processingStart := time.Now()

	// Check user authorization
	userAuth := ctx.Value(models.AuthKey).(models.UserAuth)
	isAdmin := userAuth.IsSuperUser || userAuth.Privilege == "admin"

	// Get currency info
	currency := ctx.Value(models.AccountCurrency).(models.Currency)
	needsCurrencyConversion := !strings.EqualFold(currency.Currency, "CAD")

	// Initialize response
	response := models.StatsResponse{
		DataTable: &[]models.ClickStat{},
		Currency:  currency.Currency,
	}

	// Process data if user has access
	if isAdmin || tools.InSlice(userAuth.Access, "api") {
		ods.processApiData(apiDataRows, &response, countryAcr, needsCurrencyConversion, currency.Currency, sr)
	}

	fmt.Println("Data processed in:", time.Since(processingStart))
	fmt.Println("Total time:", time.Since(now))

	return response, nil
}

// buildWhereClause constructs WHERE conditions and values more efficiently
func (ods *apiDataSource) buildWhereClause(dates []string, sr models.SearchRequest) ([]string, []interface{}) {
	// Pre-allocate slices with estimated capacity
	whereConditions := make([]string, 0, 8) // Most queries will have < 8 conditions
	whereValues := make([]interface{}, 0, 8)

	// Mandatory conditions
	whereConditions = append(whereConditions,
		"day >= $1",
		"day <= $2",
		"account_id = $3",
		"product_type = 'PartnerApi'")
	whereValues = append(whereValues, dates[0], dates[1], sr.AccountID)

	paramIndex := 4

	// Optional conditions - use a slice of condition builders to avoid repetition
	optionalConditions := []struct {
		value string
		field string
	}{
		{sr.Token, "product"},
		{sr.Country, "country"},
		{sr.Channel1, "channel_1"},
		{sr.Channel2, "channel_2"},
		{sr.Channel3, "channel_3"},
		{sr.SubID, "subid"},
		{sr.JobType, "job_type"},
	}

	for _, cond := range optionalConditions {
		if cond.value != "" {
			whereConditions = append(whereConditions, fmt.Sprintf("%s = $%d", cond.field, paramIndex))
			whereValues = append(whereValues, cond.value)
			paramIndex++
		}
	}

	return whereConditions, whereValues
}

// parseRows directly scans database rows into our struct without reflection
func (ods *apiDataSource) parseRows(rows *sql.Rows) ([]apiDataRow, error) {
	var results []apiDataRow

	for rows.Next() {
		var row apiDataRow
		err := rows.Scan(
			&row.Date,             // day as date
			&row.TotalClicks,      // SUM(valid_clicks) AS total_clicks
			&row.ApiInvalidClicks, // SUM(invalid_clicks) AS api_invalid_clicks
			&row.TotalRevenue,     // ROUND(SUM(cost)::numeric, 3) AS total_revenue
			&row.QualityApply,     // sum(quality_apply) as quality_apply
			&row.QualityViews,     // sum(quality_view) as quality_view
			// Note: avg_cpc is calculated but we can derive it from revenue/clicks
		)
		if err != nil {
			return nil, fmt.Errorf("error scanning row: %w", err)
		}
		results = append(results, row)
	}

	return results, rows.Err()
}

// processApiData handles the business logic for processing API data
func (ods *apiDataSource) processApiData(
	apiDataRows []apiDataRow,
	response *models.StatsResponse,
	countryAcr float64,
	needsCurrencyConversion bool,
	targetCurrency string,
	sr models.SearchRequest) {

	// Pre-allocate the data table slice
	dataTable := make([]models.ClickStat, 0, len(apiDataRows))

	// Initialize totals
	var (
		totalClicks        int
		totalInvalidClicks int
		totalRevenue       float64
		totalQualityApply  float64
		totalQualityView   float64
	)

	// Pre-fetch all exchange rates if currency conversion is needed
	exchangeRates := make(map[string]float64)
	if needsCurrencyConversion {
		for _, row := range apiDataRows {
			dateStr := row.Date.Format(time.DateOnly)
			if _, exists := exchangeRates[dateStr]; !exists {
				rate, ok := ods.cache.GetCurrencies(dateStr)
				if !ok {
					slog.Error("no exchange rate found in api", "date", dateStr)
					exchangeRates[dateStr] = 1.0 // fallback
				} else {
					exchangeRate, _ := tools.CurrencyRateForDate(rate, "CAD", targetCurrency, dateStr)
					exchangeRates[dateStr] = exchangeRate
				}
			}
		}
	}

	// Process each row
	for _, row := range apiDataRows {
		// Apply currency conversion if needed
		revenue := row.TotalRevenue
		if needsCurrencyConversion {
			dateStr := row.Date.Format(time.DateOnly)
			if exchangeRate, exists := exchangeRates[dateStr]; exists {
				revenue *= exchangeRate
			}
		}

		// Update totals
		totalClicks += row.TotalClicks
		totalInvalidClicks += row.ApiInvalidClicks
		totalRevenue += revenue
		totalQualityApply += row.QualityApply
		totalQualityView += row.QualityViews

		// Calculate metrics for this row
		var avgCPC float64
		if row.TotalClicks > 0 {
			avgCPC = revenue / float64(row.TotalClicks)
		}

		var acr float64
		var acrColor string = "red"
		if row.QualityApply > 0 && sr.Token != "" && sr.Country != "" && row.QualityViews > 0 {
			acr = (row.QualityApply / row.QualityViews) * 100
			if acr >= countryAcr*90 { // countryAcr*0.9 * 100
				acrColor = "green"
			}
		}

		// Build data table entry
		dataTable = append(dataTable, models.ClickStat{
			Date: row.Date,
			Clicks: models.Clicks{
				Total:   row.TotalClicks,
				Invalid: row.ApiInvalidClicks,
			},
			Revenue: models.Revenue{
				Total: revenue,
			},
			AverageCPC: models.AverageCPC{
				Total: avgCPC,
			},
			ACR: models.ACR{
				Total: acr,
			},
			ACRColor: acrColor,
		})
	}

	// Set response totals
	response.TotalRevenue = math.Round(totalRevenue*1000) / 1000
	response.TotalInvalid = totalInvalidClicks
	response.TotalClicks = totalClicks
	response.DataTable = &dataTable

	// Calculate overall average CPC
	if totalClicks > 0 {
		response.AverageCPC.Total = totalRevenue / float64(totalClicks)
	}

	// Calculate overall ACR
	if sr.Token != "" && sr.Country != "" && totalQualityApply > 0 && totalQualityView > 0 {
		overallACR := (totalQualityApply / totalQualityView) * 100
		response.ACR.Total = overallACR

		acrColor := "red"
		if overallACR >= countryAcr*90 { // countryAcr*0.9 * 100
			acrColor = "green"
		}
		response.ACRColor = acrColor
	}
}

var (
	queryFieldsApi = []string{
		`day as date`,
		`SUM(valid_clicks) AS total_clicks`,
		`SUM(invalid_clicks) AS api_invalid_clicks`,
		`ROUND(SUM(cost)::numeric, 3) AS total_revenue`,
		`sum(quality_apply) as quality_apply`,
		`sum(quality_view) as quality_view`,
	}
)
