package utils

import (
	"os"
	"strconv"
	"strings"

	"log/slog"

	"github.com/joho/godotenv"
)

func init() {
	InitEnv()
}

func InitEnv() {
	// Load .env file
	err := godotenv.Load()
	if err != nil {
		slog.Warn("Did not find .env file to load", "error", err)
	}
}

func GetEnvStr(envVar string, defaultValue string) string {
	v := os.Getenv(envVar)

	if v == "" {
		slog.Warn("Using default string value", "envVar", envVar, "default", defaultValue)
		return defaultValue
	}

	return v
}

// Get environment variable value and return it or use provided default.
func GetEnvDefaultStr(envVar string, d string) string {
	strVal, exists := os.LookupEnv(envVar)
	if !exists {
		slog.Warn("Using default string value", "envVar", envVar, "default", d)
		return d
	}
	return strVal
}

// Get environment variable value and convert to list of strings or use provided default.
func GetEnvDefaultList(envVar string, d []string) []string {
	strVal, exists := os.LookupEnv(envVar)
	if !exists {
		slog.Warn("Using default list value", "envVar", envVar, "default", d)
		return d
	}
	return strings.Split(strVal, ",")
}

func GetEnvInt(envVar string, defaultValue int) int {
	v := os.Getenv(envVar)

	if v == "" {
		slog.Warn("Using default int value", "envVar", envVar, "default", defaultValue)
		return defaultValue
	}

	vInt, err := strconv.ParseInt(v, 10, 32)
	if err != nil {
		slog.Warn("Could not parse env variable", "value", v, "error", err)

		return defaultValue
	}

	return int(vInt)
}

// Get environment variable value and convert to list of strings or use provided default.
func GetEnvArrStr(envVar string, d []string) []string {
	strVal, exists := os.LookupEnv(envVar)
	if !exists {
		slog.Warn("Using default list value", "envVar", envVar, "default", d)
		return d
	}
	return strings.Split(strVal, ",")
}

func GetEnvBoolean(envVar string, defaultValue bool) bool {
	v := os.Getenv(envVar)

	if v == "" {
		slog.Warn("Using default boolean value", "envVar", envVar, "default", defaultValue)
		return defaultValue
	}

	vBool, err := strconv.ParseBool(v)
	if err != nil {
		slog.Warn("Could not parse env variable", "value", v, "error", err)

		return defaultValue
	}

	return vBool
}
