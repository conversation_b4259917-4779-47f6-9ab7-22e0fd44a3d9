import { Test, TestingModule } from "@nestjs/testing";
import { NotificationsSendEmailService } from "../src/notifications-send-email.service";
import * as secrets from "../../notifications/src/common/secrets";
import { Consumer } from "kafkajs";
import axios from "axios";
import { Logger } from "@nestjs/common";

jest.mock("axios");
jest.mock("kafkajs", () => ({
  Kafka: jest.fn().mockImplementation(() => ({
    consumer: jest.fn().mockReturnValue({
      connect: jest.fn(),
      subscribe: jest.fn(),
      run: jest.fn(),
      disconnect: jest.fn(),
    }),
  })),
}));

describe("NotificationsSendEmailService", () => {
  let service: NotificationsSendEmailService;
  let consumer: Consumer;
  let logger: Logger;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [NotificationsSendEmailService],
    }).compile();

    service = module.get<NotificationsSendEmailService>(NotificationsSendEmailService);
    consumer = service["consumer"];
    logger = service["logger"];
    jest.spyOn(logger, "log").mockImplementation(jest.fn());
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("onModuleInit", () => {
    it("should initialize the Kafka consumer and start it", async () => {
      await service.onModuleInit();
      expect(consumer.connect).toHaveBeenCalled();
      expect(consumer.subscribe).toHaveBeenCalledWith({
        topic: "test-topic", //secrets.kafkaNotificationsQueue,
        fromBeginning: true,
      });
      expect(consumer.run).toHaveBeenCalled();
    });

    it("should process received messages", async () => {
      const message = {
        value: Buffer.from(
          JSON.stringify({
            email_address: "V001fKGJoD_0lDMbm_rZj1WFkDw1KA1mz2YBT0LN1bO2HKs6Q2meQTia45E2WBipbiRK",
            template_id: 2,
            sender: "sender123",
            data: {
              user_id: "test123",
              user_data: {
                user_id: "test123",
              },
              user_seed_data: [
                {
                  eventType: "jbalerts_customized",
                  keyword: "developer",
                  location: {
                    lat: 25.7616798,
                    lon: -80.1917902,
                    location_string: "Miami, Florida, United States",
                  },
                  id: 0,
                  userId: "1",
                  country: "ca",
                  eventId: "test",
                  language: "en",
                  eventSource: "consumer_notification_retargeting",
                  eventDate: new Date(),
                  dateCreated: new Date(),
                },
                {
                  eventType: "jbalerts_customized",
                  keyword: "developer",
                  location: {
                    lat: 25.7616798,
                    lon: -80.1917902,
                    location_string: "Miami, Florida, United States",
                  },
                  id: 0,
                  userId: "1",
                  country: "ca",
                  eventId: "test",
                  language: "en",
                  eventSource: "consumer_notification_retargeting",
                  eventDate: new Date(),
                  dateCreated: new Date(),
                },
              ],
              template_data: {
                id: 2,
                tplName: "ja_general",
                label: "Job Alert",
                description: "Job Alert (Cards)",
                status: "active",
                subject: "no",
                preview: "no",
                header: "no",
                body: "no",
                footer: "no",
                searchAlgo: "emailRetargetingEExclusiveList",
                configKey: "default",
                classification: "ja",
                editFields: [
                  {
                    field: "user_id",
                    label: "User Id",
                    type: "text",
                    testValue: "1468ce4d1f281f8a3fe36047c9919ca7dcd8e673",
                  },
                  { field: "country", label: "Country", type: "text", testValue: "gb" },
                  { field: "language", label: "Language", type: "text", testValue: "en" },
                  {
                    field: "keyword",
                    label: "Keyword",
                    type: "text",
                    testValue: "Software Developer Remote",
                  },
                  { field: "location", label: "Location", type: "text", testValue: "london" },
                  { field: "debug", label: "Debug", type: "text", testValue: "1" },
                  { field: "sender", label: "Sender", type: "text", testValue: "alerts_aws" },
                ],
                dateUpdated: "2024-05-02T23:54:30.193Z",
                dateLastUsed: null,
              },
            },
          }),
        ),
      };

      (consumer.run as jest.Mock).mockImplementation(({ eachMessage }) => eachMessage({ message }));
      (axios.post as jest.Mock).mockResolvedValueOnce({
        user_id: "test123",
        eventType: "job_card_clicked",
        country: "us",
        eventId: "test123",
        location: {
          lat: 25.7616798,
          lon: -80.1917902,
          location_string: "Miami, Florida, United States",
        },
        language: "en",
        keyword: "Software Developer II",
        eventDate: "2024-08-09T15:07:01.028Z",
      });
      (axios.post as jest.Mock).mockResolvedValueOnce({ status: 200, data: [] });
      await service.onModuleInit();

      expect(logger.log).toHaveBeenCalled();
    });
  });

  describe("onModuleDestroy", () => {
    it("should disconnect the Kafka consumer", async () => {
      await service.onModuleDestroy();
      expect(consumer.disconnect).toHaveBeenCalledTimes(1);
    });
  });
});
