import { Test, TestingModule } from "@nestjs/testing";
import { TaxcalRepository } from "../../../src/salary/repositories/taxcal.repository";
import { getRepositoryToken } from "@nestjs/typeorm";
import { SalarySettings, Tax } from "../../../src/salary/entities";
import { Repository } from "typeorm";

// Mock data based on the updated SalarySettings entity
const mockSalarySettings: SalarySettings = {
  id: 1,
  country: "US",
  currency: "USD",
  currency_symbol: "$",
  symbol_position: "before",
  thousand_separate: ",",
  weekly_hours: 40,
  hour_min_wage: 15.5,
  month_median_wage: 3000,
  min_salary: 2000,
  max_salary: 5000,
  step: 1,
  active: true,
};

describe("TaxcalRepository", () => {
  let taxcalRepository: TaxcalRepository;
  let salarySettingsRepository: Repository<SalarySettings>;
  let taxRepository: Repository<Tax>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TaxcalRepository,
        {
          provide: getRepositoryToken(SalarySettings, "taxcal"),
          useValue: {
            findOne: jest.fn(), // Mock findOne method
          },
        },
        {
          provide: getRepositoryToken(Tax, "taxcal"),
          useValue: {
            findOne: jest.fn(), // Mock findOne method
          },
        },
      ],
    }).compile();

    taxcalRepository = module.get<TaxcalRepository>(TaxcalRepository);
    salarySettingsRepository = module.get<Repository<SalarySettings>>(
      getRepositoryToken(SalarySettings, "taxcal"),
    );
  });

  it("should be defined", () => {
    expect(taxcalRepository).toBeDefined();
  });

  describe("getSalarySettings", () => {
    it("should return salary settings for a valid country", async () => {
      // Arrange: mock the repository's findOne method to return mock data
      jest.spyOn(salarySettingsRepository, "findOne").mockResolvedValue(mockSalarySettings);

      // Act: call the method
      const result = await taxcalRepository.getSalarySettings("US");

      // Assert: Check that the result matches the mock data
      expect(result).toEqual(mockSalarySettings);
      expect(salarySettingsRepository.findOne).toHaveBeenCalledWith({
        where: { country: "US" },
      });
    });

    it("should return null if no salary settings are found for a country", async () => {
      // Arrange: mock the repository's findOne method to return null
      jest.spyOn(salarySettingsRepository, "findOne").mockResolvedValue(null);

      // Act: call the method
      const result = await taxcalRepository.getSalarySettings("IN");

      // Assert: Check that the result is null (not found)
      expect(result).toBeNull();
      expect(salarySettingsRepository.findOne).toHaveBeenCalledWith({
        where: { country: "IN" },
      });
    });

    it("should throw an error if an exception occurs while fetching salary settings", async () => {
      // Arrange: mock the repository's findOne method to throw an error
      jest
        .spyOn(salarySettingsRepository, "findOne")
        .mockRejectedValue(new Error("Database error"));

      // Act & Assert: Expect the method to throw an error
      await expect(taxcalRepository.getSalarySettings("US")).rejects.toThrowError("Database error");
    });
  });
});
