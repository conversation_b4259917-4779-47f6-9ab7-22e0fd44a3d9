import { IsString, IsNotEmpty, Length, IsLowercase, IsOptional } from "class-validator";
import { ApiProperty } from "@nestjs/swagger";

/**
 * Salary by category DTO
 */
export class SalaryByRegionDto {
  @ApiProperty({ description: "Country code", required: true })
  @IsString()
  @IsNotEmpty()
  @IsLowercase({ message: "Country code must be in lowercase." })
  country: string;

  @ApiProperty({ description: "Language", required: true })
  @IsString()
  @IsNotEmpty()
  @IsLowercase({ message: "Language code must be in lowercase." })
  language: string;

  @ApiProperty({ description: "Keywords", required: false, default: "" })
  @IsString()
  @IsOptional()
  keywords: string = "";

  @ApiProperty({ description: "Region", required: false, default: "", })
  @IsString()
  @IsOptional()
  region: string = "";
}
