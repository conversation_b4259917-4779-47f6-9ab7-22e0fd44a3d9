package service

// import
import (
	domain "bid-processing/src/internal/domain"
	cache "bid-processing/src/internal/infrastructure/cache"
	"bid-processing/src/internal/shared/types"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"strconv"
	"time"

	atomicStore "atomic-store/src"

	"golang.org/x/exp/slog"
)

const LIGHT = "budetRulesServiceLight"

type (
	BudgetRulesService interface {
		InitCache()
		InitJobLevelBudgetStore()
		GetAdsBudgetRules(jobData *types.JobRulesProcessResult, channel, source string, explanationRequired bool) (*types.JobRulesProcessResult, error)
		SetContext(context.Context)
		SetLogger(*slog.Logger)
		GetCampaignModifier(jobData *types.JobRulesProcessResult, explanationRequired bool) (*types.JobRulesProcessResult, error)
		ExtractModifierValues(explanationValues []map[string]interface{}) (float64, float64, float64)
	}
	BudgetRulesServiceMiddleware func(next BudgetRulesService) BudgetRulesService
	BudgetRulesServiceImpl       struct {
		ctx                     context.Context
		logger                  *slog.Logger
		HttpClient              types.HttpClient
		budgetRulesCacheHandler *cache.Cache
		budgetRulesData         map[int]*domain.NecessaryCampaignData
		atomicBudget            *atomicStore.AtomicStore[map[int]*domain.NecessaryCampaignData]
		updateTicker            *time.Ticker
		atomicJobBudget         *atomicStore.AtomicStore[map[int]int]
		jobBudgetTicker         *time.Ticker
	}
)

func NewBudgetRulesService(ctx context.Context, logger *slog.Logger) BudgetRulesService {
	budgetService := &BudgetRulesServiceImpl{
		ctx:                     ctx,
		logger:                  logger,
		HttpClient:              &http.Client{},
		budgetRulesCacheHandler: cache.NewCache(),
		budgetRulesData:         make(map[int]*domain.NecessaryCampaignData),
		updateTicker:            time.NewTicker(2 * time.Minute),
		jobBudgetTicker:         time.NewTicker(1 * time.Minute),
	}

	deepCopyFunc := func(src map[int]*domain.NecessaryCampaignData) map[int]*domain.NecessaryCampaignData {
		dst := make(map[int]*domain.NecessaryCampaignData, len(src))
		for k, v := range src {
			copy := *v
			dst[k] = &copy
		}
		return dst
	}

	budgetService.atomicBudget = atomicStore.NewAtomicStore(make(map[int]*domain.NecessaryCampaignData), 10*time.Hour, deepCopyFunc, true, true)

	deepJobLevelBudgetCopyFunc := func(src map[int]int) map[int]int {
		dst := make(map[int]int, len(src))
		for k, v := range src {
			copy := v
			dst[k] = copy
		}
		return dst
	}

	budgetService.atomicJobBudget = atomicStore.NewAtomicStore(make(map[int]int), 10*time.Hour, deepJobLevelBudgetCopyFunc, true, true)

	go budgetService.periodicUpdate()

	return budgetService
}

func (b *BudgetRulesServiceImpl) periodicUpdate() {
	for {
		select {
		case <-b.updateTicker.C:
			b.InitCache()
		case <-b.jobBudgetTicker.C:
			b.InitJobLevelBudgetStore()
		case <-b.ctx.Done():
			b.updateTicker.Stop()
			return
		}
	}
}

func (s *BudgetRulesServiceImpl) SetHttpClient(client types.HttpClient) {
	s.HttpClient = client
}
func (s *BudgetRulesServiceImpl) SetContext(ctx context.Context) {
	s.ctx = ctx
}
func (s *BudgetRulesServiceImpl) SetLogger(logger *slog.Logger) {
	s.logger = logger
}

func (b *BudgetRulesServiceImpl) ReplaceMap(newMap map[int]*domain.NecessaryCampaignData) {
	b.atomicBudget.Write(func(budgetMap *map[int]*domain.NecessaryCampaignData) {
		*budgetMap = newMap
	})
	b.atomicBudget.Swap()
}

func (b *BudgetRulesServiceImpl) ReplaceJobBudgetMap(newMap map[int]int) {
	b.atomicJobBudget.Write(func(jobBudgetMap *map[int]int) {
		*jobBudgetMap = newMap
	})
	b.atomicJobBudget.Swap()
}

func (b *BudgetRulesServiceImpl) getData(id int) (*domain.NecessaryCampaignData, bool) {
	result, _ := b.atomicBudget.Read(func(data *map[int]*domain.NecessaryCampaignData) interface{} {
		campaign, exists := (*data)[id]
		return struct {
			campaign *domain.NecessaryCampaignData
			exists   bool
		}{campaign, exists}
	}).(struct {
		campaign *domain.NecessaryCampaignData
		exists   bool
	})

	return result.campaign, result.exists

}
func (b *BudgetRulesServiceImpl) getJobLevelBudgetData(id int) (int, bool) {
	result, _ := b.atomicJobBudget.Read(func(data *map[int]int) interface{} {
		job, exists := (*data)[id]
		return struct {
			job    int
			exists bool
		}{job, exists}
	}).(struct {
		job    int
		exists bool
	})

	return result.job, result.exists

}

func (b *BudgetRulesServiceImpl) InitCache() {
	startTime := time.Now()

	// Read data from the endpoint
	data, err := b.readEndpointData()
	if err != nil {
		b.logger.ErrorCtx(b.ctx, "Error reading endpoint data", "message", err)
		return
	}

	if len(data.Campaigns) == 0 {
		b.logger.WarnCtx(b.ctx, "No campaigns data found")
	}

	// Store the data in the cache

	aux := make(map[int]*domain.NecessaryCampaignData)

	for _, item := range data.Campaigns {
		if item.CPCModifier == 0 || item.CPCModifier == 0.0 {
			item.CPCModifier = 1.0
		}
		aux[item.CampaignData.CampaignID] = &domain.NecessaryCampaignData{
			CampaignID:                   item.CampaignData.CampaignID,
			AccountID:                    item.Account.AccountID,
			CampaignStatus:               item.Status,
			AccountStatus:                item.Account.Status,
			AcrProfitProtection:          item.Account.AccountSettings.AcrProfitProtection,
			EstimatedBudgetRemainingDays: item.CampaignData.EstimatedBudgetRemainingDays,
			CampaignPPCModifier:          item.CPCModifier,
			PPCDiscount:                  item.Account.Discount,
			CurrencyRate:                 item.Account.CurrencyRate,
		}
	}

	b.ReplaceMap(aux)
	b.logger.InfoCtx(b.ctx, "Budget Rules cache creation", "duration", time.Since(startTime))

}

func (s *BudgetRulesServiceImpl) readEndpointData() (domain.CampaignsData, error) {

	if os.Getenv("BUDGETING_ENDPOINT") == "" || os.Getenv("BUDGETING_CAMPAIGNS_BUDGET_ENDPOINT") == "" {
		return domain.CampaignsData{}, fmt.Errorf("BUDGETING_ENDPOINT or BUDGETING_CAMPAIGNS_BUDGET_ENDPOINT is not set")
	}

	endpoint := fmt.Sprint(os.Getenv("BUDGETING_ENDPOINT"), os.Getenv("BUDGETING_CAMPAIGNS_BUDGET_ENDPOINT"))

	req, err := http.NewRequest("GET", endpoint, nil)
	if err != nil {
		return domain.CampaignsData{}, err
	}
	req.Close = true
	// Perform the request
	res, err := s.HttpClient.Do(req)
	if err != nil {
		return domain.CampaignsData{}, err
	}
	if res.StatusCode != http.StatusOK {
		return domain.CampaignsData{}, err
	}
	defer res.Body.Close()
	if res.Body != nil {
		bodyBytes, err := io.ReadAll(res.Body)
		if err != nil {
			return domain.CampaignsData{}, fmt.Errorf("error reading response body:", err)
		}
		var apiResponse domain.CampaignsData
		if err := json.Unmarshal(bodyBytes, &apiResponse); err != nil {
			return domain.CampaignsData{}, fmt.Errorf("error unmarshalling response:", err)
		}
		return apiResponse, nil
	} else {
		fmt.Println("Response body is nil")
	}
	return domain.CampaignsData{}, nil
}

func (s *BudgetRulesServiceImpl) GetAdsBudgetRules(jobData *types.JobRulesProcessResult, channel, source string, explanationRequired bool) (*types.JobRulesProcessResult, error) {
	result := s.ProcessJobData(jobData, channel, source, explanationRequired)
	return result, nil
}

func (s *BudgetRulesServiceImpl) ProcessJobData(jobData *types.JobRulesProcessResult, channel, source string, explanationRequired bool) *types.JobRulesProcessResult {

	// Convert CampaignID from string to int
	campaignID, err := strconv.Atoi(jobData.CampaignID)
	if err != nil {
		// Handle the error, maybe log it or return from the function
		log.Fatalf("invalid campaign ID: %v", err)
	}

	jobID, err := strconv.Atoi(jobData.JobID)
	if err != nil {
		// Handle the error, maybe log it or return from the function
		log.Fatalf("invalid job ID: %v", err)
	}

	var explanationFilters []map[string]interface{}
	var explanationValues []map[string]interface{}

	if _, ok := s.getJobLevelBudgetData(jobID); ok {
		explanationFilters = append(explanationFilters, map[string]interface{}{"JobID": jobID})
		explanationValues = append(explanationValues, map[string]interface{}{
			"JobBudgetStatus": "inactive",
		})
		jobData.Ppc = 0
		jobData.PpcU = 0
		jobData.InitialPpcU = 0
		jobData.InitialPpc = 0
		if jobData.Feedtype == "AGG" || jobData.Feedtype == "MIX" || jobData.Feedtype == "COL" || jobData.Feedtype == "PRB" {
			jobData.Exposure = 0 // dumb stuff coming from PHP
			if channel == "partnerXML" || channel == "partnerApi" {
				jobData.Display = false
			}
		}
		jobData.Explanation.BudgetRulesNode.Filters = explanationFilters
		jobData.Explanation.BudgetRulesNode.Values = explanationValues
		return jobData
	}

	if val, ok := s.getData(campaignID); ok {
		explanationFilters = append(explanationFilters, map[string]interface{}{"CampaignID": campaignID})
		explanationValues = append(explanationValues, map[string]interface{}{
			"CampaignStatus":               val.CampaignStatus,
			"AccountStatus":                val.AccountStatus,
			"AcrProfitProtection":          val.AcrProfitProtection,
			"EstimatedBudgetRemainingDays": val.EstimatedBudgetRemainingDays,
		})
		if val.CampaignStatus != "active" || val.AccountStatus != "active" {
			jobData.Ppc = 0
			jobData.PpcU = 0
			jobData.InitialPpcU = 0
			jobData.InitialPpc = 0
			if jobData.Feedtype == "AGG" || jobData.Feedtype == "MIX" || jobData.Feedtype == "COL" || jobData.Feedtype == "PRB" {
				jobData.Exposure = 0 // dumb stuff coming from PHP
				if channel == "partnerXML" || channel == "partnerApi" {
					jobData.Display = false
				}
			}
		}
		if val.AcrProfitProtection {
			jobData = applyAutoCampaignRules(jobData, val.EstimatedBudgetRemainingDays, channel, source)
		}
	} else {
		explanationFilters = append(explanationFilters, map[string]interface{}{"CampaignID": campaignID})
		explanationValues = append(explanationValues, map[string]interface{}{
			"CampaignStatus": "Not found/inactive",
			"AccountStatus":  "Not found/inactive",
		})
		jobData.Ppc = 0
		jobData.PpcU = 0
		jobData.InitialPpcU = 0
		jobData.InitialPpc = 0
		if jobData.Feedtype == "AGG" || jobData.Feedtype == "MIX" || jobData.Feedtype == "COL" || jobData.Feedtype == "PRB" {
			jobData.Exposure = 0
			if channel == "partnerXML" || channel == "partnerApi" {
				jobData.Display = false
			}
		}
	}
	jobData.Explanation.BudgetRulesNode.Filters = explanationFilters
	jobData.Explanation.BudgetRulesNode.Values = explanationValues
	return jobData
}

func applyAutoCampaignRules(jobData *types.JobRulesProcessResult, estimatedBudgetRemainingDays int64, channel, source string) *types.JobRulesProcessResult {
	if estimatedBudgetRemainingDays > 0 && channel == "partnerXML" && source != "linkedin-premium" {
		jobData.Exposure = 0
		jobData.Display = false
	}
	return jobData

}

func (b *BudgetRulesServiceImpl) GetCampaignModifier(jobData *types.JobRulesProcessResult, explanationRequired bool) (*types.JobRulesProcessResult, error) {
	var explanationFilters []map[string]interface{}
	var explanationValues []map[string]interface{}

	campaignID, err := strconv.Atoi(jobData.CampaignID)
	if err != nil {
		// Handle the error, maybe log it or return from the function
		return jobData, err
	}
	explanationFilters = append(explanationFilters, map[string]interface{}{"CampaignID": campaignID})

	// This is not gonna work if the campaign is inactive but, in that case, the ppc modifier won't matter either
	if value, ok := b.getData(campaignID); ok {
		jobData.InitialPpc = jobData.InitialPpc * value.CampaignPPCModifier
		jobData.InitialPpcU = jobData.InitialPpcU * value.CampaignPPCModifier
		explanationValues = append(explanationValues, map[string]interface{}{
			"PPC Modifier": value.CampaignPPCModifier,
			"Initial Ppc":  jobData.InitialPpc,
			"Initial PpcU": jobData.InitialPpcU,
		})
		if value.PPCDiscount != 1 && value.PPCDiscount > 0 {
			jobData.Discount = value.PPCDiscount
			explanationValues = append(explanationValues, map[string]interface{}{
				"PPC Discount": value.PPCDiscount,
			})
		}
		if value.CurrencyRate != nil && value.CurrencyRate != 1 {
			currencyRate := 1 / value.CurrencyRate.(float64)
			jobData.InitialPpcU = jobData.InitialPpc * currencyRate * 10
			jobData.CurrencyRate = currencyRate
			explanationValues = append(explanationValues, map[string]interface{}{
				"Currency Operation": map[string]interface{}{
					"Currency from campaigns": true,
					"Currency Rate":           currencyRate,
					"Initial PpcU":            jobData.InitialPpcU,
				},
			})
		} else if jobData.Ppc > 0 && jobData.PpcU > 0 {
			jobData.CurrencyRate = jobData.PpcU / (jobData.Ppc * 10)
			explanationValues = append(explanationValues, map[string]interface{}{
				"Currency Operation": map[string]interface{}{
					"Currency from campaigns": false,
					"Currency Rate":           jobData.CurrencyRate,
					"Initial PpcU":            jobData.InitialPpcU,
				},
			})
		}
	}

	jobData.Explanation.PpcModifierRulesNode.Filters = explanationFilters
	jobData.Explanation.PpcModifierRulesNode.Values = explanationValues
	return jobData, nil
}

func (b *BudgetRulesServiceImpl) ExtractModifierValues(explanationValues []map[string]interface{}) (float64, float64, float64) {
	var ppcModifier float64 = 1.0
	var ppcDiscount float64 = 0.0
	var currencyRate float64 = 1.0

	for _, value := range explanationValues {
		if modifier, ok := value["PPC Modifier"]; ok {
			ppcModifier = modifier.(float64)
		}
		if discount, ok := value["PPC Discount"]; ok {
			ppcDiscount = discount.(float64)
		}
		if currencyOp, ok := value["Currency Operation"]; ok {
			if currencyMap, ok := currencyOp.(map[string]interface{}); ok {
				if rate, ok := currencyMap["Currency Rate"]; ok {
					currencyRate = rate.(float64)
				}
			}
		}
	}

	return ppcModifier, ppcDiscount, currencyRate
}

/////////////// JOB LEVEL BUDGET ATOMIC STORE SETTING

func (b *BudgetRulesServiceImpl) InitJobLevelBudgetStore() {
	startTime := time.Now()

	// Read data from the endpoint
	data, err := b.readJobBudgetEndpoint()
	if err != nil {
		b.logger.ErrorCtx(b.ctx, "Error reading endpoint data", "message", err)
		return
	}

	if len(data) == 0 {
		b.logger.WarnCtx(b.ctx, "No job budgets data found")
	}

	// Store the data in the atomic store
	b.ReplaceJobBudgetMap(data)
	b.logger.InfoCtx(b.ctx, "Job Budget Rules cache creation", "duration", time.Since(startTime))
}

func (s *BudgetRulesServiceImpl) readJobBudgetEndpoint() (map[int]int, error) {

	if os.Getenv("BUDGETING_ENDPOINT") == "" || os.Getenv("BUDGETING_CAMPAIGNS_BUDGET_ENDPOINT") == "" {
		return map[int]int{}, fmt.Errorf("BUDGETING_ENDPOINT or BUDGETING_JOB_LEVEL_BUDGETS_ENDPOINT is not set")
	}

	endpoint := fmt.Sprint(os.Getenv("BUDGETING_ENDPOINT"), os.Getenv("BUDGETING_JOB_LEVEL_BUDGETS_ENDPOINT"))

	req, err := http.NewRequest("GET", endpoint, nil)
	if err != nil {
		return map[int]int{}, err
	}
	req.Close = true
	// Perform the request
	res, err := s.HttpClient.Do(req)
	if err != nil {
		return map[int]int{}, err
	}
	if res.StatusCode != http.StatusOK {
		return map[int]int{}, err
	}
	defer res.Body.Close()
	if res.Body != nil {
		bodyBytes, err := io.ReadAll(res.Body)
		if err != nil {
			return map[int]int{}, fmt.Errorf("error reading response body: ", err)
		}
		var apiResponse []types.JobBudgetResponse
		if err := json.Unmarshal(bodyBytes, &apiResponse); err != nil {
			return map[int]int{}, fmt.Errorf("error unmarshalling response:", err)
		}

		result := map[int]int{}

		for _, value := range apiResponse {
			jobID, err := strconv.Atoi(value.ID)
			if err != nil {
				return map[int]int{}, fmt.Errorf("error converting job ID to int: %v", err)
			}
			result[jobID] = 1
		}

		return result, nil
	} else {
		fmt.Println("Response body is nil")
	}
	return map[int]int{}, nil
}
