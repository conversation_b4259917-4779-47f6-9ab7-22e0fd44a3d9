package adBidService

import (
	types "bid-processing/src/internal/shared/types"
	"bytes"
	"context"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"

	"github.com/go-chi/chi"
	"github.com/go-chi/chi/middleware"
	"github.com/stretchr/testify/assert"
	"golang.org/x/exp/slog"
)

func TestMakeAdBidsRoutes(t *testing.T) {

	os.Setenv("EVENTS_BROKER_ADDRESS", "kafka-broker-monetization-events-0.talent.private:9092")
	os.Setenv("API_EVENTS_TOPIC", "monetization-events-test")
	logger := slog.Default()

	mockService := &MockAdBidsService{
		CalculateBidsFunc: func(ctx context.Context, creq types.AdBidRetrievalRequest, fromGRPC, debug bool) (types.BidResults, error) {
			return types.BidResults{}, nil
		},
	}
	router := chi.NewRouter()
	router.Use(middleware.Logger)

	router.Use(middleware.AllowContentType("application/json"))
	router = MakeAdBidsRoutes(logger, router, mockService)

	ts := httptest.NewServer(router)
	defer ts.Close()

	tests := []struct {
		method     string
		target     string
		wantStatus int
		body       []byte
	}{
		{"GET", "/get_bids?job_ids=1&channel=test&partner=test", http.StatusOK, nil},
	}

	for _, tc := range tests {
		bodyReader := bytes.NewReader(tc.body)
		req, err := http.NewRequest(tc.method, ts.URL+tc.target, bodyReader)
		if err != nil {
			t.Fatal(err)
		}

		resp, err := http.DefaultClient.Do(req)
		if err != nil {
			t.Fatal(err)
		}
		resp.Body.Close()
	}
}

func TestDecodeGetAdBidsRequest(t *testing.T) {

	os.Setenv("EVENTS_BROKER_ADDRESS", "kafka-broker-monetization-events-0.talent.private:9092")
	os.Setenv("API_EVENTS_TOPIC", "monetization-events-test")
	req := httptest.NewRequest("GET", "/get_bids?job_ids=1&channel=test&partner=test", nil)

	t.Run("valid request", func(t *testing.T) {
		expected := types.AdBidRetrievalRequest{
			JobIDs:  "1",
			Channel: "test",
			Partner: "test",
		}

		got, err := decodeGetAdBidsRequest(context.Background(), req)
		assert.NoError(t, err)
		assert.Equal(t, expected, got)
	})

	t.Run("missing required parameters", func(t *testing.T) {
		req := httptest.NewRequest("GET", "/get_bids", nil)

		_, err := decodeGetAdBidsRequest(context.Background(), req)
		assert.Error(t, err)
	})
}
