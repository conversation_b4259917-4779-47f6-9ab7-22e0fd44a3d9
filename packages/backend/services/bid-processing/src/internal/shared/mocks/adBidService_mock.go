package mocks

import (
	exposureServices "bid-processing/src/internal/application/exposure-algorithms"
	"bid-processing/src/internal/shared/types"
	"context"
	model "talent/monetization-shared-libs/src/domain"
	pb "talent/pb/ad_bid_service"
	"time"

	"github.com/stretchr/testify/mock"
)

// mock adBidService is a mock implementation of the adBidService interface
type MockAdBidService struct {
	mock.Mock
}

func (m *MockAdBidService) CalculateBids(ctx context.Context, request types.AdBidRetrievalRequest, fromGRPC, debug bool) (types.BidResults, error) {
	args := m.Called(ctx, request, fromGRPC, debug)
	return args.Get(0).(types.BidResults), args.Error(1)
}

func (m *MockAdBidService) InitTrees(test bool, duration time.Duration) {
	m.Called(test, duration)
}

func (m *MockAdBidService) FindByIDs(jobIDs []string) []*model.Job {
	args := m.Called(jobIDs)
	return args.Get(0).([]*model.Job)
}

func (m *MockAdBidService) CalculateBidsPrefilter(ctx context.Context, request types.AdBidRetrievalRequest) (types.JobPrefilterResult, error) {
	args := m.Called(ctx, request)
	return args.Get(0).(types.JobPrefilterResult), args.Error(1)
}

// func (m *MockAdBidService) GetFeedcodeRulesService() exposureServices.FeedcodeRulesService {
// 	args := m.Called()
// 	return args.Get(0).(exposureServices.FeedcodeRulesService)
// }

func (m *MockAdBidService) AssignInitialData(jobsData []*model.Job) []*types.JobRulesProcessResult {
	args := m.Called(jobsData)
	return args.Get(0).([]*types.JobRulesProcessResult)
}

func (m *MockAdBidService) PrefilterV2(ctx context.Context, request types.AdBidRetrievalRequest, jobs []*model.Job) (*pb.PrefilterResponse, error) {
	args := m.Called(ctx, request)
	return args.Get(0).(*pb.PrefilterResponse), args.Error(1)
}

func (m *MockAdBidService) GetFeedcodeRulesService() exposureServices.FeedcodeRulesService {
	args := m.Called()
	return args.Get(0).(exposureServices.FeedcodeRulesService)
}
