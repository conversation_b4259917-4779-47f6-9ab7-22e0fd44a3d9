package model

import (
	"bid-processing/src/internal/infrastructure/talent_db/interact/models"
	"database/sql"
)

type (
	FeedtypeRulesModel interface {
	}

	FeedtypeModel struct {
		Id       int64
		Country  string
		Domain   string
		Channel  string
		Feedtype string
		Status   string
	}
)

func NewFeedtypeModel() FeedtypeRulesModel {
	return &FeedtypeModel{}
}

func NewFeedtypeRulesRepository(db *sql.DB) models.SQL {
	mod := models.NewSQLModel("monetization.feedtype_rules", "id", FeedtypeModel{})
	mod.PrepareModel(db)
	return mod
}
