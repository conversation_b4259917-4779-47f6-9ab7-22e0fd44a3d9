package config

import (
	"os"
	jobsapi "talent/publishers-shared-libs/src/jobs-api"
)

var (
	dbCOUCHBASE map[string]string
	commonVars  map[string]string
)

func InitDBConnectionMaps() {
	dbCOUCHBASE = map[string]string{
		"HOSTS":            os.<PERSON><PERSON><PERSON>("COUCH_BASE_HOST"),
		"BUCKET":           os.<PERSON><PERSON><PERSON>("COUCH_BASE_BUCKET"),
		"SCOPE":            os.<PERSON><PERSON><PERSON>("COUCH_BASE_SCOPE"),
		"JOBS_COLLECTION":  os.<PERSON><PERSON><PERSON>("COUCH_BASE_JOBS_COLLECTION"),
		"FEEDS_COLLECTION": os.<PERSON><PERSON><PERSON>("COUCH_BASE_FEEDS_COLLECTION"),
		"USER":             os.<PERSON><PERSON><PERSON>("COUCH_BASE_USER"),
		"PASS":             os.<PERSON><PERSON><PERSON>("COUCH_BASE_PASSWORD"),
	}

	commonVars = map[string]string{
		"COUNTRIES_1":           "'us'",
		"COUNTRIES_2":           "'gb'",
		"COUNTRIES_3":           "'de','in','ru','br','ch','mx','za','au'",
		"COUNTRIES_4":           "'ar','at','cl','co','cz','es','ca','jp'",
		"COUNTRIES_5":           "'ie','it','lu','nl','pe','pl','pt','se','tr','ro','hu','ng','my','nz','ae','sg','fi','no','be'",
		"COUNTRIES_6":           "'fr','dk','hk','id','ph','uy','cn','ec','pa','gt','cr','pr','kr','sa','ve','pk','ug','ke','qa','sn','zm','eg','kw','ma','bh','tn','dz','om','il','gr','th','vn','tw','ci','ao','ua','gh','kz','cm','lb','mz','mg','cu','dm','gd','ht','jm','kn','lc','vc','tt'",
		"JOB_XML_TPL":           "\n<%s>\n%s\n%s</%s>",
		"FIELD_XML_TPL":         "<{{.name}}>{{if .cdata}}<![CDATA[{{end}}{{.value}}{{if .cdata}}]]>{{end}}</{{.name}}>",
		"FIELD_DISPLAY_XML_TPL": "{{if eq .%s \"1\"}}{{template \"field\" (params \"name\" %s \"value\" .%s \"cdata\" .cdata)}}\n{{end}}",
		"TEST_MODE":             "0",
		"JOBS_FIELDS_LIST":      jobsapi.CompressString("id,enrich_geo_country,system_feedcode,system_feed_type,enrich_company_name,source_title,enrich_soc_major_group,enrich_soc_onetsoc_code,enrich_geo_city,enrich_geo_region1,source_client_tag,source_reqid,system_status,employer", "", ","),
		"FEED_FIELDS_LIST":      "job_id,partner,country,ppc,ppc_u,cpc,cpc_u,exposure,display,bid,cpc_display,bidshare",
		"JOBS_NUM_FOR_CACHE":    os.Getenv("JOBS_NUM_FOR_CACHE"),
		"XML_CACHE_AGE":         os.Getenv("XML_CACHE_AGE"),
	}
}

func GetConfig(group string, key string) string {

	switch group {
	case "couchbase":
		keyValue, ok := dbCOUCHBASE[key]
		if ok {
			return keyValue
		}
	case "common":
		keyValue, ok := commonVars[key]
		if ok {
			return keyValue
		}
	}
	return ""
}

func SetConfigTest(group string, key string, value string) bool {
	switch group {
	case "couchbase":
		dbCOUCHBASE[key] = value
	case "common":
		commonVars[key] = value
	default:
		return false
	}

	return true
}

func LoadTestEnvVariables() {
	//hR := []rune{0x0063, 0x006F, 0x0075, 0x0063, 0x0068, 0x0062, 0x0061, 0x0073, 0x0065, 0x002D, 0x0070, 0x0075, 0x0062, 0x006C, 0x0069, 0x0073, 0x0068, 0x0065, 0x0072, 0x0073, 0x002D, 0x0078, 0x006D, 0x006C, 0x002D, 0x0066, 0x0065, 0x0065, 0x0064, 0x0073, 0x002E, 0x0074, 0x0061, 0x006C, 0x0065, 0x006E, 0x0074, 0x002E, 0x006C, 0x006F, 0x0063, 0x0061, 0x006C}
	hR := []rune{0x0031, 0x0030, 0x002E, 0x0031, 0x0030, 0x002E, 0x0031, 0x0035, 0x002E, 0x0031, 0x0035, 0x0037, 0x003A, 0x0038, 0x0030, 0x0039, 0x0031}
	pR := []rune{0x0070, 0x0034, 0x0062, 0x006C, 0x0031, 0x0073, 0x0068, 0x0033, 0x0072, 0x0073, 0x002A, 0x0032, 0x0030, 0x0032, 0x0034, 0x0074}
	uR := []rune{0x0041, 0x0064, 0x006D, 0x0069, 0x006E, 0x0069, 0x0073, 0x0074, 0x0072, 0x0061, 0x0074, 0x006F, 0x0072}

	os.Setenv("COUCH_BASE_HOST", string(hR))
	os.Setenv("COUCH_BASE_BUCKET", "feedsxml")
	os.Setenv("COUCH_BASE_SCOPE", "store")
	os.Setenv("COUCH_BASE_JOBS_COLLECTION", "jobs_test")
	os.Setenv("COUCH_BASE_FEEDS_COLLECTION", "feeds_test")
	os.Setenv("COUCH_BASE_USER", string(uR))
	os.Setenv("COUCH_BASE_PASSWORD", string(pR))
	InitDBConnectionMaps()
}
