syntax = "proto3";

package jobmeta;

option go_package = "talent/pb/jobmeta";

service JobMeta {
  rpc GetMeta(GetMetaRequest) returns (GetMetaResponse);
}

message GetMetaRequest {
  repeated string ids = 1;   // List of job IDs
  MetaType meta_type = 2;    // list of properties to load (from Metadata)
  string search_phrase = 3;  // Search phrase for generating snippets
  bool debug = 4;
  repeated string legacy_ids = 5;
}

enum MetaType {
  META_TYPE_UNSPECIFIED = 0;
  META_TYPE_SNIPPET = 1;
  META_TYPE_HTML = 2;
  META_TYPE_JOBDESC = 3;
  META_TYPE_META_ONLY = 4;
}

message GetMetaResponse {
  repeated Meta metas = 1;    // List of metadata.
}

message Meta {
  string id = 1;
  MetaData data = 2;
  MetaJobdesc jobdesc = 3;
  MetaDebug debug = 4;
  bool is_alien = 5; // Would be set to True if this id is not from this shard.
}

message MetaDebug {
  string meta_source = 1;
  string html_source = 2;
  string text_source = 3;
  string snippet_source = 4;
}

message MetaData {
  MetaSystem system = 1;
  MetaSource source = 2;
  MetaEnrich enrich = 3;
  MetaDates date = 4;
  MetaGeo geo = 5;
  MetaEmployer employer = 6;
}

message MetaJobdesc {
  optional string text=1;
  optional string html=2;
  optional string snippet=3;
}

message MetaSystem {
  string feedcode = 1;
  string company_name = 4;
  string legacy_id = 5;
  int32 status = 6;
  string apply_type = 7;
}

message MetaSource {
  string title = 1;
  string link = 2;
  string location = 3;
  string jobtype = 4;
}

message MetaEnrich {
  optional string company_name = 1;
  string category = 4;
  string soc_onetsoc_code = 5 [deprecated=true];
  string language = 6;
  repeated string jobtypes = 7;
  MetaSoc soc = 8;
  MetaSalary salary = 9;
}

message MetaSoc {
  string onetsoc_code = 1;
  string matched_title = 2;
}

message MetaSalary {
  string type = 1;
  string currency = 2;
  float min = 3;
  float max = 4;
  float avg = 5;
}

message MetaGeo {
  string city = 1;
  string country = 2;
  string region1 = 3;
  double lat = 4;
  double lon = 5;
  string remote = 6;
}

message MetaDates {
  string found = 1;
  optional string posted = 2;
  optional string re_found = 3;
  optional string expired = 4;
}

message MetaEmployer {
  reserved 1;
  string extra_questions = 2;
  string standard_questions = 3;
  int64 ppc = 4;
  int64 ppcu = 5;
  int64 campaign_id = 6;
  string apply_type = 7;
}
