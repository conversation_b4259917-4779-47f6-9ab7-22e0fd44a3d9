{"name": "reconciliation", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "sourceRoot": "{projectRoot}/src", "version": "0.0.0", "targets": {"build": {"dependsOn": ["download"], "executor": "nx:run-commands", "options": {"command": "go build -o ../../../../dist/{projectRoot}/src/cmd/main src/cmd/main.go", "cwd": "{projectRoot}"}}, "coverage": {"cache": false, "executor": "nx:run-commands", "options": {"commands": ["node ../../../../infrastructure/scripts/tests/utils.js runCoverageTest GO {projectRoot}"], "parallel": false, "cwd": "{projectRoot}"}}, "dev": {"executor": "nx:run-commands", "options": {"command": "$HOME/go/bin/CompileDaemon -build='go build -o ../../../../dist/{projectRoot}/src/cmd/main src/cmd/main.go' -command=../../../../dist/{projectRoot}/src/cmd/main -directory=./ -color=true", "cwd": "{projectRoot}"}}, "docker": {}, "download": {"executor": "nx:run-commands", "options": {"command": "go mod download", "cwd": "{projectRoot}"}}, "install": {"executor": "nx:run-commands", "options": {"command": "go get {args.package}", "cwd": "{projectRoot}"}}, "lint": {"executor": "nx:run-commands", "options": {"command": "golangci-lint run", "cwd": "{projectRoot}/src"}}, "serve": {"executor": "nx:run-commands", "options": {"command": "go run src/cmd/main.go", "cwd": "{projectRoot}"}}, "test": {"cache": false, "executor": "nx:run-commands", "options": {"command": "go test ./... -v", "cwd": "{projectRoot}"}}, "tidy": {"executor": "nx:run-commands", "options": {"command": "go mod tidy", "cwd": "{projectRoot}"}}}, "tags": ["language:go", "type:service"]}