FROM public.ecr.aws/docker/library/golang:1.22-alpine3.19 AS build

# Deactivate the gowork usage to avoid using all the services files in the docker build
ENV GOWORK off

# Use talent as the workdir
WORKDIR /talent

# Copy the go mod and the pb libraries to download the needed packages. Cache strategy
COPY packages/backend/libs packages/backend/libs
COPY packages/backend/services/reconciliation/go* packages/backend/services/reconciliation/
COPY pb /talent/pb

# Move the workdir to the service to be built
WORKDIR /talent/packages/backend/services/reconciliation/

# Download all nedded libraries
RUN go mod download

# Copy the rest of files
COPY packages/backend/services/reconciliation .

# Build the service binaries
RUN GOMAXPROCS=1 go build -o src/cmd/main src/cmd/main.go

# Build partitions cron
RUN go build -o src/cmd/partitions src/cmd/partitions/main.go

# Build parity cron
RUN go build -o src/cmd/parity src/cmd/parity/main.go

# Build sftp cron
RUN go build -o src/cmd/sftp src/cmd/sftp/main.go

# Build errors-avg cron
RUN go build -o src/cmd/errors-avg src/cmd/errors-avg/main.go

# Dev stage
FROM public.ecr.aws/docker/library/golang:1.22-alpine3.19 AS dev

# Deactivate the gowork usage to avoid using all the services files in the docker build
ENV GOWORK off

# Use the talent workdir
WORKDIR /talent

# Copy the go mod and the pb libraries to download the needed packages. Cache strategy
COPY packages/backend/libs packages/backend/libs
COPY packages/backend/services/reconciliation/go* packages/backend/services/reconciliation/
COPY pb /talent/pb

# Move to the service folder
WORKDIR /talent/packages/backend/services/reconciliation/

# Env variables
ENV ENVIRON remote
ENV HTTP_PORT ":8080"
# Postgre
ENV DB_HOST_WRITER "postgres-budgeting-events.talent.private"
ENV DB_HOST_READER "postgres-budgeting-events.talent.private"
ENV DB_TABLE "cpc_trsx_jobredirects"
ENV DB_PORT "5432"
ENV DB_TIMEZONE "America/Montreal"
# Empoyers services
ENV TAG_FEEDCODES_ENDPOINT "http://employers-ff-service.apps.talent.com/api/v1/tags-internal/extract-tag-feedcodes"
ENV TAGS_SFTP_ACTIVE_ENDPOINT "http://employers-service.apps.talent.com/api/v1/tags-internal/search?status=1&typeId=4"
# AWS endpoints
ENV S3_BUCKET_SFTP "talent-dev-reconciliation-api-sftp"
# WEBHOOK for teams notifications
ENV WEBHOOK_REC_API "https://talentcom.webhook.office.com/webhookb2/e431f074-0462-476a-b3a8-3291227193b2@a7b464e0-870c-43fc-8e72-6de7aeddfcd5/IncomingWebhook/3dc542baed434b88b26b14c9203c581c/7e2775e3-298b-4225-81b3-ddd584c5dc85/V2U37EOZcjNVXCsBlYfF7q0UXaKJoXyRNlz1BNQlx7EG81"
# KAFKA config
ENV BROKER_ADDRESS_0 "kafka-broker-event-manager-0.talent.private:9092"
ENV BROKER_ADDRESS_1 "kafka-broker-event-manager-1.talent.private:9092"
ENV BROKER_ADDRESS_2 "kafka-broker-event-manager-2.talent.private:9092"
ENV API_EVENTS_TOPIC "employers-events"

# Download all nedded libraries
RUN go mod download

# Copy the rest of files
COPY packages/backend/services/reconciliation .

# Build the service binaries
RUN go build -o src/cmd/main src/cmd/main.go

# Install the Compile daemon
RUN go get github.com/githubnemo/CompileDaemon
RUN go install github.com/githubnemo/CompileDaemon

# Expose the needed ports
EXPOSE 8080

# Start the compile daemon with the main file
CMD CompileDaemon -build="go build -o src/cmd/main src/cmd/main.go" -command=src/cmd/main

# Prod stage
FROM public.ecr.aws/debian/debian:stable-slim AS prod

# Needed to use AWS services
RUN apt-get update && apt-get install -y --no-install-recommends ca-certificates

WORKDIR /talent/packages/backend/services/reconciliation/

# Copy the needed file
COPY --from=build /talent/packages/backend/services/reconciliation/src/cmd/main src/cmd/main
COPY --from=build /talent/packages/backend/services/reconciliation/src/cmd/partitions src/cmd/partitions
COPY --from=build /talent/packages/backend/services/reconciliation/src/cmd/parity src/cmd/parity
COPY --from=build /talent/packages/backend/services/reconciliation/src/cmd/sftp src/cmd/sftp
COPY --from=build /talent/packages/backend/services/reconciliation/src/cmd/errors-avg src/cmd/errors-avg

# Expose the needed ports
EXPOSE 8080

# Start the server
ENTRYPOINT src/cmd/main

# Env variables
ENV ENVIRON remote
ENV HTTP_PORT ":8080"

# Postgre
ENV DB_HOST_WRITER "postgres-budgeting-events.talent.private"
ENV DB_HOST_READER "postgres-budgeting-events-reader.talent.private"
ENV DB_TABLE "cpc_trsx_jobredirects"
ENV DB_PORT "5432"
ENV DB_TIMEZONE "America/Montreal"
# Empoyers services
ENV TAG_FEEDCODES_ENDPOINT "http://employers-ff-service.apps.talent.com/api/v1/tags-internal/extract-tag-feedcodes"
ENV TAGS_SFTP_ACTIVE_ENDPOINT "http://employers-ff-service.apps.talent.com/api/v1/tags-internal/search?status=1&typeId=4"
# AWS endpoints
ENV S3_BUCKET_SFTP "talent-prod-reconciliation-api-sftp"
ENV S3_BUCKET_QUERYS_ATHENA "s3://talent-prod-athena-queries-saved/reconcialiation-api/"
# WEBHOOK for teams notifications
ENV WEBHOOK_REC_API "https://talentcom.webhook.office.com/webhookb2/e431f074-0462-476a-b3a8-3291227193b2@a7b464e0-870c-43fc-8e72-6de7aeddfcd5/IncomingWebhook/3dc542baed434b88b26b14c9203c581c/7e2775e3-298b-4225-81b3-ddd584c5dc85/V2U37EOZcjNVXCsBlYfF7q0UXaKJoXyRNlz1BNQlx7EG81"
# KAFKA config
ENV BROKER_ADDRESS_0 "kafka-broker-event-manager-0.talent.private:9092"
ENV BROKER_ADDRESS_1 "kafka-broker-event-manager-1.talent.private:9092"
ENV BROKER_ADDRESS_2 "kafka-broker-event-manager-2.talent.private:9092"
ENV API_EVENTS_TOPIC "employers-events"