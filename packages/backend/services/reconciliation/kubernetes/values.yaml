version: "0.0.0"
# Name of the service
name: reconciliation
# Registry used by default to get the docker images
registry: 730335359603.dkr.ecr.us-east-1.amazonaws.com/services
# Tag used by default in all docker images
tag: latest
# Default environment
environment: dev

releases:
  - mode: Deployment
    replicas: 1
    perEnvironmentOverride:
      dev:
        minReplicas: 1
        maxReplicas: 2
        resources:
          requests:
            memory: 64Mi
            cpu: 57m
          limits:
            memory: 640Mi
    ports:
      - 8080
    service:
      create: true
      ports:
        - port: 8080
          targetPort: 8080
          protocol: TCP
          name: http
    horizontalPodAutoscaler:
      create: true
      min: 3
      max: 9
      cpuPercentage: 90
      memoryPercentage: 80
    nodeSelector:
      key: app
      value: cpu-optimized-svc
    resources:
      limits:
        memory: 4Gi
      requests:
        cpu: 50m
        memory: 600Mi
    env:
      - name: DB_DBNAME
        valueFrom:
          secretKeyRef:
            name: secret-budgeting
            key: cpc_trsx_dbname
      - name: DB_USER
        valueFrom:
          secretKeyRef:
            name: secret-budgeting
            key: cpc_trsx_user
      - name: DB_PASS
        valueFrom:
          secretKeyRef:
            name: secret-budgeting
            key: cpc_trsx_password

virtualServices:
  - hosts:
      - reconciliation-service.apps.talent.com
    gateways:
      - apps-gateway
    http:
      - match:
          - uri:
              prefix: /
        route:
          - destination:
              host: reconciliation-service.apps.svc.cluster.local
              port:
                number: 8080

extraObjects:
  - apiVersion: batch/v1
    kind: CronJob
    metadata:
      name: reconciliation-cronjob-partitions
    spec:
      schedule: "0 10 * * *"
      jobTemplate:
        spec:
          template:
            metadata:
              annotations:
                prometheus.io/scrape: "true"
                prometheus.io/port: "9090"
                prometheus.io/path: "/metrics"
                sidecar.istio.io/inject: "false"
                sidecar.opentelemetry.io/inject: "istioless-collector"
            spec:
              shareProcessNamespace: true
              nodeSelector:
                app: cronjob-svc
              tolerations:
                - key: app
                  operator: Equal
                  value: cronjob-svc
                  effect: NoSchedule
              containers:
              - name: reconciliation-cronjob-partitions
                image: "{{ .Values.registry }}/{{ .Values.name }}:{{ .Values.tag }}"
                imagePullPolicy: Always
                command: ["/bin/sh","-c"]
                args:
                  - |
                    echo "Running Go program if exists:"
                    if [ -f src/cmd/partitions/main ]; then
                      src/cmd/partitions/main
                      echo "Sleeping for 90 seconds before stopping opentelemetry collector"
                      sleep 90
                      echo "Stopping opentelemetry collector sidecar"
                      kill $(pidof otelcol-contrib)
                    else
                      echo "Go file not found"
                      echo "Stopping opentelemetry collector sidecar"
                      kill $(pidof otelcol-contrib)
                    fi
                resources:
                  requests:
                    cpu: "200m"
                    memory: "500Mi"
                  limits:
                    memory: "1Gi"
                env:
                  - name: DB_DBNAME
                    valueFrom:
                      secretKeyRef:
                        name: secret-budgeting
                        key: cpc_trsx_dbname
                  - name: DB_USER
                    valueFrom:
                      secretKeyRef:
                        name: secret-budgeting
                        key: cpc_trsx_user
                  - name: DB_PASS
                    valueFrom:
                      secretKeyRef:
                        name: secret-budgeting
                        key: cpc_trsx_password
              restartPolicy: Never
          backoffLimit: 2
  - apiVersion: batch/v1
    kind: CronJob
    metadata:
      name: reconciliation-cronjob-sftp
    spec:
      schedule: "0 3 * * *"
      jobTemplate:
        spec:
          template:
            metadata:
              annotations:
                prometheus.io/scrape: "true"
                prometheus.io/port: "9090"
                prometheus.io/path: "/metrics"
                sidecar.istio.io/inject: "false"
                sidecar.opentelemetry.io/inject: "istioless-collector"
            spec:
              shareProcessNamespace: true
              serviceAccountName: aws-role-s3
              nodeSelector:
                app: cronjob-svc
              tolerations:
                - key: app
                  operator: Equal
                  value: cronjob-svc
                  effect: NoSchedule
              containers:
              - name: reconciliation-cronjob-sftp
                image: "{{ .Values.registry }}/{{ .Values.name }}:{{ .Values.tag }}"
                imagePullPolicy: Always
                command: ["/bin/sh","-c"]
                args:
                  - |
                    echo "Running Go program if exists:"
                    if [ -f src/cmd/sftp/main ]; then
                      src/cmd/sftp/main
                      echo "Sleeping for 90 seconds before stopping opentelemetry collector"
                      sleep 90
                      echo "Stopping opentelemetry collector sidecar"
                      kill $(pidof otelcol-contrib)
                    else
                      echo "Go file not found"
                      echo "Stopping opentelemetry collector sidecar"
                      kill $(pidof otelcol-contrib)
                    fi
                resources:
                  requests:
                    cpu: "200m"
                    memory: "500Mi"
                  limits:
                    memory: "1Gi"
                env:
                  - name: DB_DBNAME
                    valueFrom:
                      secretKeyRef:
                        name: secret-budgeting
                        key: cpc_trsx_dbname
                  - name: DB_USER
                    valueFrom:
                      secretKeyRef:
                        name: secret-budgeting
                        key: cpc_trsx_user
                  - name: DB_PASS
                    valueFrom:
                      secretKeyRef:
                        name: secret-budgeting
                        key: cpc_trsx_password
              restartPolicy: Never
          backoffLimit: 2
  - apiVersion: batch/v1
    kind: CronJob
    metadata:
      name: reconciliation-cronjob-parity
    spec:
      schedule: "5,35 * * * *"
      jobTemplate:
        spec:
          template:
            metadata:
              annotations:
                prometheus.io/scrape: "true"
                prometheus.io/port: "9090"
                prometheus.io/path: "/metrics"
                sidecar.istio.io/inject: "false"
                sidecar.opentelemetry.io/inject: "istioless-collector"
            spec:
              shareProcessNamespace: true
              serviceAccountName: aws-role-s3
              nodeSelector:
                app: cronjob-svc
              tolerations:
                - key: app
                  operator: Equal
                  value: cronjob-svc
                  effect: NoSchedule
              containers:
              - name: reconciliation-cronjob-parity
                image: "{{ .Values.registry }}/{{ .Values.name }}:{{ .Values.tag }}"
                imagePullPolicy: Always
                command: ["/bin/sh","-c"]
                args:
                  - |
                    echo "Running Go program if exists:"
                    if [ -f src/cmd/parity/main ]; then
                      src/cmd/parity/main
                      echo "Sleeping for 90 seconds before stopping opentelemetry collector"
                      sleep 90
                      echo "Stopping opentelemetry collector sidecar"
                      kill $(pidof otelcol-contrib)
                    else
                      echo "Go file not found"
                      echo "Stopping opentelemetry collector sidecar"
                      kill $(pidof otelcol-contrib)
                    fi
                resources:
                  requests:
                    cpu: "200m"
                    memory: "500Mi"
                  limits:
                    memory: "1Gi"
                env:
                  - name: ENVIRONMENT
                    value: "{{ .Values.environment }}"
                  - name: DB_DBNAME
                    valueFrom:
                      secretKeyRef:
                        name: secret-budgeting
                        key: cpc_trsx_dbname
                  - name: DB_USER
                    valueFrom:
                      secretKeyRef:
                        name: secret-budgeting
                        key: cpc_trsx_user
                  - name: DB_PASS
                    valueFrom:
                      secretKeyRef:
                        name: secret-budgeting
                        key: cpc_trsx_password
              restartPolicy: Never
          backoffLimit: 2
  - apiVersion: batch/v1
    kind: CronJob
    metadata:
      name: reconciliation-cronjob-errors-avg
    spec:
      schedule: "0 0,6,12,18 * * *"
      jobTemplate:
        spec:
          template:
            metadata:
              annotations:
                prometheus.io/scrape: "true"
                prometheus.io/port: "9090"
                prometheus.io/path: "/metrics"
                sidecar.istio.io/inject: "false"
                sidecar.opentelemetry.io/inject: "istioless-collector"
            spec:
              shareProcessNamespace: true
              serviceAccountName: aws-role-s3
              nodeSelector:
                app: cronjob-svc
              tolerations:
                - key: app
                  operator: Equal
                  value: cronjob-svc
                  effect: NoSchedule
              containers:
              - name: reconciliation-cronjob-errors-avg
                image: "{{ .Values.registry }}/{{ .Values.name }}:{{ .Values.tag }}"
                imagePullPolicy: Always
                command: ["/bin/sh","-c"]
                args:
                  - |
                    echo "Running Go program if exists:"
                    if [ -f src/cmd/errors-avg/main ]; then
                      src/cmd/errors-avg/main
                      echo "Sleeping for 90 seconds before stopping opentelemetry collector"
                      sleep 90
                      echo "Stopping opentelemetry collector sidecar"
                      kill $(pidof otelcol-contrib)
                    else
                      echo "Go file not found"
                      echo "Stopping opentelemetry collector sidecar"
                      kill $(pidof otelcol-contrib)
                    fi
                resources:
                  requests:
                    cpu: "200m"
                    memory: "500Mi"
                  limits:
                    memory: "1Gi"
                env:
                  - name: ENVIRONMENT
                    value: "{{ .Values.environment }}"
                  - name: DB_DBNAME
                    valueFrom:
                      secretKeyRef:
                        name: secret-budgeting
                        key: cpc_trsx_dbname
                  - name: DB_USER
                    valueFrom:
                      secretKeyRef:
                        name: secret-budgeting
                        key: cpc_trsx_user
                  - name: DB_PASS
                    valueFrom:
                      secretKeyRef:
                        name: secret-budgeting
                        key: cpc_trsx_password
              restartPolicy: Never
          backoffLimit: 2


