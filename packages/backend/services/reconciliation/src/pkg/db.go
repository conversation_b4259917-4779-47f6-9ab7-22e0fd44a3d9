package pkg

import (
	"fmt"
	"log/slog"
	"os"
	"talent/reconciliation/src/internal/models"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"gorm.io/plugin/dbresolver"
)

// InitDB initializes a database connection with appropriate configuration
// It can be configured for writer-reader separation by providing readerHost parameter
func InitDB(appCtx *models.AppContext, readerHost string) error {
	writerDSN := fmt.Sprintf(
		"host=%s user=%s password=%s dbname=%s port=%s sslmode=disable TimeZone=%s",
		os.Getenv("DB_HOST_WRITER"),
		os.<PERSON>env("DB_USER"),
		os.<PERSON>v("DB_PASS"),
		os.<PERSON>env("DB_DBNAME"),
		os.<PERSON>env("DB_PORT"),
		os.Getenv("DB_TIMEZONE"),
	)
	writerDialector := postgres.Open(writerDSN)

	var err error
	appCtx.Db, err = gorm.Open(writerDialector, &gorm.Config{
		Logger: logger.New(nil, logger.Config{
			LogLevel: logger.Silent,
		}),
	})
	if err != nil {
		slog.Error("DB Connection failed", "err", err)
		return err
	}

	// If a reader host is specified, set up read replica
	if readerHost != "" {
		readerDSN := fmt.Sprintf(
			"host=%s user=%s password=%s dbname=%s port=%s sslmode=disable TimeZone=%s",
			readerHost,
			os.Getenv("DB_USER"),
			os.Getenv("DB_PASS"),
			os.Getenv("DB_DBNAME"),
			os.Getenv("DB_PORT"),
			os.Getenv("DB_TIMEZONE"),
		)
		readerDialector := postgres.Open(readerDSN)

		appCtx.Db.Use(dbresolver.Register(dbresolver.Config{
			Replicas: []gorm.Dialector{readerDialector},
		}))
	}

	return nil
}

// InitDBSimple initializes a simple database connection without read replicas
func InitDBSimple() (*gorm.DB, error) {
	dsn := fmt.Sprintf(
		"host=%s user=%s password=%s dbname=%s port=%s sslmode=disable TimeZone=%s",
		os.Getenv("DB_HOST_WRITER"),
		os.Getenv("DB_USER"),
		os.Getenv("DB_PASS"),
		os.Getenv("DB_DBNAME"),
		os.Getenv("DB_PORT"),
		os.Getenv("DB_TIMEZONE"),
	)

	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %v", err)
	}

	return db, nil
}

// TableExists checks if a table exists in the database
func TableExists(db *gorm.DB, tableName string) bool {
	var exists bool
	query := fmt.Sprintf("SELECT EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = '%s')", tableName)
	err := db.Raw(query).Scan(&exists).Error
	if err != nil {
		slog.Error("Error checking table existence", "err", err)
		return false
	}
	return exists
}
