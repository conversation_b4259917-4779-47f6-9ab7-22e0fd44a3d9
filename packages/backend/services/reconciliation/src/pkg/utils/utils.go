package utils

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"net/http"
	"os"
	"strings"
	"time"

	privacy "talent/libs/privacy/src"
	"talent/reconciliation/src/internal/models"

	"github.com/joho/godotenv"
	"go.opentelemetry.io/otel/attribute"
)

var (
	loc *time.Location
)

const (
	errInvalidKeyMessage      = "Invalid key, please enter a valid key. You can reach out to your account management team for additional assistance"
	errNoDataMessage          = "There is no data available with the given parameters"
	errHandlingRequestMessage = "There was a problem handling your request. Please try again later or contact your account management team for assistance"
	errNoDataTimeframeMessage = "No data available during specified timeframe"
	errMaxDaysMessage         = "Maximum 7 days of data available through API per call"
	errTooManyRequestsMessage = "Too many requests in one minute. Maximum is 1 request per minute"
	errSystemProblemMessage   = "There was a problem with the system. Please try again later or contact your account management team for assistance"
	errRealtimeDataMessage    = "Data cannot be called in real time. The earliest available hour range is the current time (Eastern time), minus two hours (floored)"
)

// Initialize the cache once when the application starts
func init() {
	loc, _ = time.LoadLocation("America/Toronto")
}

func LoadEnv() {
	if os.Getenv("ENVIRON") != "remote" {
		if err := godotenv.Load(); err != nil {
			log.Fatal("Error loading .env file" + err.Error())
		}
	}
}

// PrintJSON converts any data to JSON and prints it.
func PrintJSON(data interface{}) {
	jsonData, err := json.MarshalIndent(data, "", "  ")
	if err != nil {
		fmt.Println("Error converting to JSON:", err)
		return
	}
	fmt.Println(string(jsonData))
}

func TraceAttrKeyValue(input interface{}) []attribute.KeyValue {
	jsonMap, _ := json.Marshal(input)
	var newMap map[string]interface{}
	json.Unmarshal(jsonMap, &newMap)

	if len(newMap) <= 0 {
		return []attribute.KeyValue{}
	}

	var attr []attribute.KeyValue
	for k, v := range newMap {
		switch v := v.(type) {
		case string:
			attr = append(attr, attribute.String(k, v))
		case int:
			attr = append(attr, attribute.Int(k, v))
		case int64:
			attr = append(attr, attribute.Int64(k, v))
		case float64:
			attr = append(attr, attribute.Float64(k, v))
		case bool:
			attr = append(attr, attribute.Bool(k, v))
		default:
			subAttr := TraceAttrKeyValue(v)
			if len(subAttr) <= 0 {
				continue
			}

			attr = append(attr, subAttr...)
		}
	}

	return attr
}

func ExtractTag(key string) (string, int, int) {
	keyDecrypt, err := safeFlo(key)
	if err != nil || keyDecrypt == "" {
		return "", http.StatusBadRequest, 2
	}

	// Validate that the Key contains "talent-"
	if !strings.Contains(keyDecrypt, "talent-") {
		return "", http.StatusBadRequest, 2
	}

	keyExplode := strings.SplitN(keyDecrypt, "talent-", 2)
	if len(keyExplode) < 2 || strings.TrimSpace(keyExplode[1]) == "" {
		return "", http.StatusBadRequest, 2
	}

	tag := strings.ToLower(strings.TrimSpace(keyExplode[1]))

	if tag == "" {
		return "", http.StatusNotFound, 3
	}

	return tag, 0, 0
}

func safeFlo(key string) (string, error) {
	var result string
	var err error

	defer func() {
		if r := recover(); r != nil {
			err = errors.New("ErrorKey: decryption failed due to panic")
		}
	}()

	result = privacy.Flo(key)

	return result, err
}

func HandleValidationError(logMsg string, errorCode int, httpStatus int, params ...interface{}) models.ErrorOnProcess {
	//slog.Error(logMsg, "params", params)

	// Use the provided httpStatus or fallback to http.StatusBadRequest
	status := httpStatus
	if status == 0 { // Treat 0 as the default case
		status = http.StatusBadRequest
	}

	return models.ErrorOnProcess{
		HttpCode:   status,
		Message:    ReturnErrorMsg(errorCode),
		LogMessage: logMsg,
	}
}

func ReturnErrorMsg(errorCode int) string {
	errorMessages := map[int]string{
		1:  errInvalidKeyMessage,
		2:  errInvalidKeyMessage,
		3:  errInvalidKeyMessage,
		4:  errInvalidKeyMessage,
		5:  errNoDataMessage,
		6:  errNoDataTimeframeMessage,
		7:  errNoDataTimeframeMessage,
		8:  errMaxDaysMessage,
		9:  errNoDataMessage,
		10: errNoDataMessage,
		11: errTooManyRequestsMessage,
		12: errHandlingRequestMessage,
		13: errHandlingRequestMessage,
		14: errSystemProblemMessage,
		15: errRealtimeDataMessage,
		16: errNoDataMessage,
		17: errHandlingRequestMessage,
	}

	msgDisplay, exists := errorMessages[errorCode]
	if !exists {
		msgDisplay = "Unknown error"
	}

	return msgDisplay
}

// FloorDateRange adjusts dateFrom to HH:00:00 and dateTo to HH:59:59
// while preserving the original hours
func FloorDateRange(dateFrom, dateTo int64) (int64, int64) {

	// Convert Unix timestamps to time.Time
	fromTime := time.Unix(dateFrom, 0).In(loc)
	toTime := time.Unix(dateTo, 0).In(loc)

	// Floor dateFrom to HH:00:00
	flooredFrom := time.Date(
		fromTime.Year(),
		fromTime.Month(),
		fromTime.Day(),
		fromTime.Hour(),
		0, // Set minutes to 00
		0, // Set seconds to 00
		0, // Set nanoseconds to 0
		fromTime.Location(),
	)

	// Determine the correct hour for flooredTo
	// Case if time starts like HH:00:00 it should take hour before
	var flooredHour int
	if toTime.Minute() == 0 && toTime.Second() == 0 { // If dateTo is exactly H:00:00
		flooredHour = toTime.Hour() - 1
		if flooredHour < 0 { // Handle midnight case
			flooredHour = 23
			toTime = toTime.AddDate(0, 0, -1) // Move to the previous day
		}
	} else {
		flooredHour = toTime.Hour()
	}

	// Floor dateTo to the calculated hour:59:59
	flooredTo := time.Date(
		toTime.Year(),
		toTime.Month(),
		toTime.Day(),
		flooredHour,
		59, // Set minutes to 59
		59, // Set seconds to 59
		0,  // Set nanoseconds to 0
		toTime.Location(),
	)

	// Return as Unix timestamps
	return flooredFrom.Unix(), flooredTo.Unix()
}

func SendTeamsNotification(webhookURL string, message string) error {

	// Case if we are testing on dev enviroment - do not notify in teams
	if os.Getenv("ENVIRON") != "remote" {
		return nil
	}

	// Create a message payload that Teams can parse
	payload := models.TeamsMessage{
		Text: message,
	}

	// Convert payload to JSON
	jsonPayload, err := json.Marshal(payload)
	if err != nil {
		return fmt.Errorf("error marshaling JSON: %v", err)
	}

	// Send POST request to Teams webhook
	resp, err := http.Post(webhookURL, "application/json", bytes.NewBuffer(jsonPayload))
	if err != nil {
		return fmt.Errorf("error sending request: %v", err)
	}
	defer resp.Body.Close()

	// Check response status
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("failed to send notification, status code: %d", resp.StatusCode)
	}

	return nil
}

func SendTeamsNotificationWithFormat(webhookURL string, title string, message string, isError bool) error {
	// Case if we are testing on dev environment - do not notify in teams
	if os.Getenv("ENVIRON") != "remote" {
		return nil
	}

	// Dynamic color of the card theme based on the title
	themeColor := "00FF37"
	if isError {
		themeColor = "FF0000" // Red for error
	}

	// Replace line breaks with HTML line breaks
	message = strings.ReplaceAll(message, "\n", "<br>")

	// Create a message payload that Teams can parse
	payload := models.TeamsMessage{
		Context:    "https://schema.org/extensions",
		Type:       "MessageCard",
		ThemeColor: themeColor,
		Title:      title,
		Text:       message,
	}

	// Convert payload to JSON
	jsonPayload, err := json.Marshal(payload)
	if err != nil {
		return fmt.Errorf("error marshaling JSON: %v", err)
	}

	// Send POST request to Teams webhook
	resp, err := http.Post(webhookURL, "application/json", bytes.NewBuffer(jsonPayload))
	if err != nil {
		return fmt.Errorf("error sending request: %v", err)
	}
	defer resp.Body.Close()

	// Check response status
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("failed to send notification, status code: %d", resp.StatusCode)
	}

	return nil
}

// Helper function to escape XML special characters
func XmlEscape(s string) string {
	replacer := strings.NewReplacer(
		"&", "&amp;",
		"<", "&lt;",
		">", "&gt;",
		"\"", "&quot;",
		"'", "&apos;",
	)
	return replacer.Replace(s)
}
