package utils

import (
	"encoding/json"
	"fmt"
	"os"
	publish "talent/libs/publishers-shared-libs/src/events"
	"talent/reconciliation/src/internal/models"
)

func TrackEvent(broker []string, topic string, data []byte) error {
	kakfaP := publish.NewKafkaPublisher(broker)
	publisher := publish.NewPublisher(kakfaP)
	return publisher.Publish(topic, data)
}

func SendEvent(event interface{}, eventType string) error {
	// return nil
	et := models.EventTracker{
		Type:     eventType,
		Category: "operational event",
		Event:    event,
	}
	flattened, err := flattenEventTracker(&et)
	if err != nil {
		return err
	}
	bytes, _ := json.<PERSON>(flattened)

	return TrackEvent([]string{os.Getenv("BROKER_ADDRESS_0"), os.<PERSON>env("BROKER_ADDRESS_1"), os.<PERSON>env("BROKER_ADDRESS_2")}, os.Getenv("API_EVENTS_TOPIC"), bytes)
}

func flattenEventTracker(et *models.EventTracker) (map[string]interface{}, error) {
	// Marshal the EventTracker into JSON
	data, err := json.Marshal(et)
	if err != nil {
		return nil, err
	}

	// Unmarshal the JSON into a map
	var result map[string]interface{}
	if err := json.Unmarshal(data, &result); err != nil {
		return nil, err
	}

	// Extract the "event" field
	eventData, ok := result["event"]
	if !ok {
		return nil, fmt.Errorf("event field is missing")
	}

	// Convert the "event" field to a map
	eventMap, err := structToMap(eventData)
	if err != nil {
		return nil, err
	}

	// Delete the "event" field from the result map
	delete(result, "event")

	// Flatten the event map into the result map
	for key, value := range eventMap {
		result[key] = value
	}

	return result, nil
}

// Helper function to convert an interface{} to a map[string]interface{}
func structToMap(data interface{}) (map[string]interface{}, error) {
	dataBytes, err := json.Marshal(data)
	if err != nil {
		return nil, err
	}
	var result map[string]interface{}
	if err := json.Unmarshal(dataBytes, &result); err != nil {
		return nil, err
	}
	return result, nil
}
