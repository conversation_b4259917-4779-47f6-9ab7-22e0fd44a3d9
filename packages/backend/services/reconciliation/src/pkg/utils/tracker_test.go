package utils

import (
	"os"
	"talent/reconciliation/src/internal/models"
	"testing"
)

func TestTrackEvent(t *testing.T) {
	os.Setenv("BROKER_ADDRESS_0", "kafka-broker-event-manager-0.talent.private:9092")
	os.Setenv("BROKER_ADDRESS_1", "kafka-broker-event-manager-1.talent.private:9092")
	os.Setenv("BROKER_ADDRESS_2", "kafka-broker-event-manager-2.talent.private:9092")
	os.Setenv("API_EVENTS_TOPIC_TEST", "publisher-events-test")
	err := TrackEvent([]string{os.Getenv("BROKER_ADDRESS_0"), os.<PERSON><PERSON>("BROKER_ADDRESS_1"), os.<PERSON><PERSON><PERSON>("BROKER_ADDRESS_2")}, os.<PERSON>en<PERSON>("API_EVENTS_TOPIC_TEST"), []byte("data"))
	if err != nil {
		t.<PERSON><PERSON><PERSON>("TrackEvent() failed, expected nil, got %v", err)
	}
}

func TestSendEvent(t *testing.T) {
	os.<PERSON>env("BROKER_ADDRESS_0", "kafka-broker-event-manager-0.talent.private:9092")
	os.Setenv("BROKER_ADDRESS_1", "kafka-broker-event-manager-1.talent.private:9092")
	os.Setenv("BROKER_ADDRESS_2", "kafka-broker-event-manager-2.talent.private:9092")
	os.Setenv("API_EVENTS_TOPIC", "publisher-events-test")
	request := models.TrackingEvent{
		Id:  "1",
		Key: "testkey",
	}
	err := SendEvent(request, "test")
	if err != nil {
		t.Errorf("SendEvent() failed, expected nil, got %v", err)
	}
}
