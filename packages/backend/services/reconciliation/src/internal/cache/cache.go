package cache

import (
	"context"
	"errors"
	"sync"
	"time"

	"github.com/redis/go-redis/v9"
)

// Cache interface for different caching backends
type Cache interface {
	Set(ctx context.Context, key string, expiration time.Duration) error
	Exists(ctx context.Context, key string) bool
}

// InMemoryCache (for local caching)
type InMemoryCache struct {
	store map[string]time.Time
	mu    sync.RWMutex
	ttl   time.Duration
}

// NewInMemoryCache initializes an in-memory cache
func NewInMemoryCache(ttl time.Duration) *InMemoryCache {
	return &InMemoryCache{
		store: make(map[string]time.Time),
		ttl:   ttl,
	}
}

// Set stores a key in the in-memory cache
func (c *InMemoryCache) Set(ctx context.Context, key string, expiration time.Duration) error {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.store[key] = time.Now().Add(expiration)
	return nil
}

// Exists checks if a key is still valid in the in-memory cache
func (c *InMemoryCache) Exists(ctx context.Context, key string) bool {
	c.mu.RLock()
	defer c.mu.RUnlock()
	expireTime, exists := c.store[key]
	if !exists || time.Now().After(expireTime) {
		return false
	}
	return true
}

// RedisCache (for distributed caching)
type RedisCache struct {
	client *redis.Client
	ttl    time.Duration
}

// NewRedisCache initializes Redis caching
func NewRedisCache(redisAddr string, ttl time.Duration) *RedisCache {
	client := redis.NewClient(&redis.Options{
		Addr: redisAddr,
	})
	return &RedisCache{client: client, ttl: ttl}
}

// Set stores a key in Redis with expiration
func (r *RedisCache) Set(ctx context.Context, key string, expiration time.Duration) error {
	return r.client.Set(ctx, key, "1", expiration).Err()
}

// Exists checks if a key exists in Redis
func (r *RedisCache) Exists(ctx context.Context, key string) bool {
	_, err := r.client.Get(ctx, key).Result()
	return err == nil
}

// controlCacheLimit ensures a request is limited to one per minute
func ControlCacheLimit(ctx context.Context, cache Cache, key string) error {
	cacheKey := "talent-rec-api-02-" + key

	if cache.Exists(ctx, cacheKey) {
		return errors.New("request limit reached: try again after one minute")
	}

	// Store in cache for 1 minute
	cache.Set(ctx, cacheKey, 1*time.Minute)
	return nil
}
