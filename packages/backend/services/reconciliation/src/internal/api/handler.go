package api

import (
	"bytes"
	"context"
	"encoding/json"
	"encoding/xml"
	"fmt"
	"html"
	"log/slog"
	"net/http"
	"strconv"
	"strings"
	"talent/reconciliation/src/internal/cache"
	"talent/reconciliation/src/internal/models"
	"talent/reconciliation/src/internal/service"
	"talent/reconciliation/src/pkg/utils"
	"time"

	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
)

var (
	globalCache cache.Cache
	loc         *time.Location
)

const dateTimeFormat = "2006-01-02 15:04:05"
const logRequestLabel = "rec_api_requested"
const logErrorLabel = "rec_api_failed"
const logResponseLabel = "rec_api_responded"

// Initialize the cache once when the application starts
func init() {
	globalCache = cache.NewInMemoryCache(1 * time.Minute)
	loc, _ = time.LoadLocation("America/Toronto")
}

func ReconciliationData(appCtx *models.AppContext) echo.HandlerFunc {
	return func(c echo.Context) error {

		ctx := context.Background()

		// Step 1: Parse and validate request parameters
		qParams, err := parseAndValidateRequest(c)
		if err != nil {
			return err
		}

		// ---------------------------------------------------------------- LOGGING REQUEST ----------------------------------------------------------------
		id := uuid.New()
		event := buildEvent(id, qParams)
		handleLogError(models.LogErrorParams{
			LogLabel: logRequestLabel,
			Key:      qParams.Key,
			QParams:  qParams,
			Event:    &event,
		})

		// --------------------------------------------------------------------------------------------------------------------------------------------------

		// Initialize the reconciliation service
		recService := service.NewReconciliation(c.Request().Context(), appCtx.Db, appCtx.HttpClient, qParams, globalCache)

		// Validate basic parameters
		if validationparamsErr := recService.ValidateParameters(); validationparamsErr.Message != "" {
			handleLogError(models.LogErrorParams{
				LogLabel:     logErrorLabel,
				Key:          qParams.Key,
				QParams:      qParams,
				LogMsg:       validationparamsErr.LogMessage,
				ErrMsg:       validationparamsErr.Message,
				FunctionName: "ValidateParameters()",
				Event:        &event,
			})
			return serviceError(validationparamsErr.HttpCode, validationparamsErr.Message)
		}

		// Validate key and get tag in one step - using the service directly
		keyTag, validationTagErr := recService.ValidateKeyTag()
		if validationTagErr.Message != "" {
			handleLogError(models.LogErrorParams{
				LogLabel:     logErrorLabel,
				Key:          qParams.Key,
				QParams:      qParams,
				LogMsg:       validationTagErr.LogMessage,
				ErrMsg:       validationTagErr.Message,
				FunctionName: "ValidateKeyTag()",
				Event:        &event,
			})
			return serviceError(validationTagErr.HttpCode, validationTagErr.Message)
		}

		// Check cache limit - 1 minute per request
		if qParams.SkipLimit == nil {
			if err := recService.CheckCacheLimit(ctx, keyTag); err != nil {
				handleLogError(models.LogErrorParams{
					LogLabel:     logErrorLabel,
					Key:          qParams.Key,
					QParams:      qParams,
					LogMsg:       "max ping error",
					ErrMsg:       err.Error(),
					FunctionName: "CheckCacheLimit()",
					Event:        &event,
					KeyTag:       &keyTag,
				})
				return serviceError(http.StatusTooManyRequests, err.Error())
			}
		}

		// Get feedcodes or tag info from employers service
		feedcodes, tagInfoErr := recService.GetTagInfo(keyTag)
		if tagInfoErr.Message != "" {
			handleLogError(models.LogErrorParams{
				LogLabel:     logErrorLabel,
				Key:          qParams.Key,
				QParams:      qParams,
				LogMsg:       tagInfoErr.LogMessage,
				ErrMsg:       tagInfoErr.Message,
				FunctionName: "GetTagInfo()",
				Event:        &event,
				KeyTag:       &keyTag,
			})
			return serviceError(tagInfoErr.HttpCode, tagInfoErr.Message)
		}

		// Case if we are debugging
		if qParams.Debug != nil && *qParams.Debug {
			return handleDebugResponse(c, qParams, keyTag, feedcodes, recService, &event)
		}

		// Step 5: Stream data
		return streamData(c, recService, qParams, feedcodes, keyTag, &event)
	}
}

func handleLogError(params models.LogErrorParams) {
	// Choose log level based on log label
	if params.LogLabel == logRequestLabel || params.LogLabel == logResponseLabel {
		slog.Info(params.LogLabel, "key", params.Key, "logMsg", params.LogMsg, "functionName", params.FunctionName, "params", params.QParams)
		params.Event.Data = params.Data
	} else {
		slog.Error(params.LogLabel, "key", params.Key, "logMsg", params.LogMsg, "errMsg", params.ErrMsg, "functionName", params.FunctionName, "params", params.QParams)
		params.Event.Data = `{"ErrMsg": "` + fmt.Sprint(params.ErrMsg) + `", "LogMsg": "` + fmt.Sprint(params.LogMsg) + `" , "Function": "` + fmt.Sprint(params.FunctionName) + `"}`
	}

	// Send event asynchronously
	go utils.SendEvent(*params.Event, params.LogLabel)
}

func buildEvent(id uuid.UUID, req models.DownloadRequestParams) models.TrackingEvent {
	timeNow := time.Now()

	// Convert int64 timestamps to time.Time
	dateFrom := time.Unix(req.DateFrom, 0)
	dateTo := time.Unix(req.DateTo, 0)

	debugValue := 0
	if req.Debug != nil && *req.Debug {
		debugValue = 1
	}

	result := models.TrackingEvent{
		Id:       id.String(),
		Key:      req.Key,
		Date:     timeNow.Format(time.RFC3339),
		Unixtime: int(timeNow.Unix()),
		DateFrom: dateFrom.Format(dateTimeFormat),
		DateTo:   dateTo.Format(dateTimeFormat),
		Debug:    debugValue,
		Uri:      req.Uri,
	}

	return result
}

func handleDebugResponse(c echo.Context, qParams models.DownloadRequestParams, keyTag string, feedcodes []string, recService service.Reconciliation, event *models.TrackingEvent) error {

	// Floor the date values
	//flooredDateFrom, flooredDateTo := utils.FloorDateRange(qParams.DateFrom, qParams.DateTo)

	startTime := time.Unix(qParams.DateFrom, 0).UTC()
	endTime := time.Unix(qParams.DateTo, 0).UTC()

	startTimeMtl := time.Unix(qParams.DateFrom, 0).In(loc)
	endTimeMtl := time.Unix(qParams.DateTo, 0).In(loc)

	// Convert feedcodes to quoted strings
	quotedFeedcodes := make([]string, len(feedcodes))
	for i, code := range feedcodes {
		quotedFeedcodes[i] = fmt.Sprintf("'%s'", code)
	}

	queryStr := fmt.Sprintf(`SELECT original_id, job_id, job_empcode, job_title, joblocation, job_empname, job_country, job_url, host, useragent, unixtime_utc AT TIME ZONE 'UTC' AT TIME ZONE 'America/Montreal' AS unixtime, billed_ppc, billed_ppc_cad, billed_warning, billed_warning_group, billed_currency, ip_hash, campaign_name, campaign_id, ip_mask, job_reqid FROM cpc_trsx_jobredirects WHERE unixtime_utc >= '%s' AND unixtime_utc <= '%s' AND job_empcode IN (%s)`,
		startTime.Format(dateTimeFormat),
		endTime.Format(dateTimeFormat),
		strings.Join(quotedFeedcodes, ","))

	// After getting feedcodes, add this validation step
	checkSumResult, _ := recService.CheckSumValidation(startTime, endTime, feedcodes)

	response := map[string]interface{}{
		"parameters": qParams,
		"query":      html.UnescapeString(queryStr),
		"keyTag":     keyTag,
		"feedcodes":  feedcodes,
		"checkSum": map[string]string{
			"clicks":         fmt.Sprintf("%d", checkSumResult.Clicks),
			"sum_billed_cad": fmt.Sprintf("%.2f", checkSumResult.SumBilledCad/100),
			"sum_billed":     fmt.Sprintf("%.2f", checkSumResult.SumBilled/100),
		},
		"datesFlooredUtc": map[string]string{
			"dateFrom": startTime.Format(dateTimeFormat),
			"dateTo":   endTime.Format(dateTimeFormat),
		},
		"datesFlooredMtl": map[string]string{
			"dateFrom": startTimeMtl.Format(dateTimeFormat),
			"dateTo":   endTimeMtl.Format(dateTimeFormat),
		},
	}

	handleLogError(models.LogErrorParams{
		LogLabel:     logResponseLabel,
		Key:          qParams.Key,
		QParams:      qParams,
		FunctionName: "handleDebugResponse()",
		Event:        event,
		KeyTag:       &keyTag,
		Data:         `{"data_source": "falcon", "clicks": "` + fmt.Sprintf("%d", checkSumResult.Clicks) + `", "sum_billed_cad": "` + fmt.Sprintf("%.2f", checkSumResult.SumBilledCad/100) + `" , "sum_billed": "` + fmt.Sprintf("%.2f", checkSumResult.SumBilled/100) + `"}`,
	})

	// Return JSON response
	return c.JSON(http.StatusOK, response)
}

// serviceError creates a new service error
func serviceError(code int, message string) *echo.HTTPError {

	return echo.NewHTTPError(code, models.ErrorResponse{
		StatusCode:    code,
		StatusMessage: http.StatusText(code),
		Success:       false,
		Errors:        []string{message},
	})
}

func parseAndValidateRequest(c echo.Context) (models.DownloadRequestParams, error) {
	var qParams models.DownloadRequestParams
	err := c.Bind(&qParams)
	if err != nil {
		//slog.Error("Error initial request", "err", err)
		return qParams, serviceError(http.StatusBadRequest, "Bad request")
	}

	// Manually parse debug query parameter
	debugParam := c.QueryParam("debug")
	if debugParam != "" {
		if debugParam == "1" || debugParam == "true" {
			trueValue := true
			qParams.Debug = &trueValue
		} else if debugParam == "0" || debugParam == "false" {
			falseValue := false
			qParams.Debug = &falseValue
		} else {
			//slog.Error("Invalid debug parameter", "debug", debugParam)
			return qParams, serviceError(http.StatusBadRequest, "Invalid debug parameter")
		}
	}

	// Manually parse skipLimit query parameter
	skipLimitParam := c.QueryParam("skipLimit")
	if skipLimitParam != "" {
		qParams.SkipLimit = &skipLimitParam
	}

	// Manually parse skipLimit query parameter
	skipPhpParam := c.QueryParam("skipPhp")
	if skipPhpParam != "" {
		qParams.SkipPhp = &skipPhpParam
	}

	// Set the default format if not provided
	if qParams.Format == "" {
		qParams.Format = "json"
	}

	qParams.Uri = c.Request().URL.Path + "?" + c.Request().URL.RawQuery

	return qParams, nil
}

func streamData(c echo.Context, recService service.Reconciliation, params models.DownloadRequestParams, feedcodes []string, keyTag string, event *models.TrackingEvent) error {
	dataChan, errChan := recService.GetCpcTrsxData(feedcodes)

	// Add a counter to track if any data was processed
	dataCount := 0

	filename := fmt.Sprintf("%s - %s - %s.%s", keyTag,
		strconv.FormatInt(params.DateFrom, 10),
		strconv.FormatInt(params.DateTo, 10),
		params.Format)

	// Buffer the response until we confirm there's data
	var responseBuffer bytes.Buffer

	// Start constructing the response in the buffer
	switch params.Format {
	case "xml":
		responseBuffer.Write([]byte("<?xml version=\"1.0\" encoding=\"UTF-8\"?><rows><data>"))
	case "csv":
		// CSV header could be written here
	default:
		// Add the data wrapper for JSON format
		responseBuffer.Write([]byte("{\"data\":["))
	}

	firstItem := true
	for {
		select {
		case data, ok := <-dataChan:
			if !ok {
				// Channel closed, check if we received any data
				if dataCount == 0 {
					// No data was found, return an appropriate error
					handleLogError(models.LogErrorParams{
						LogLabel:     logErrorLabel,
						Key:          params.Key,
						QParams:      params,
						LogMsg:       "No data found for the specified criteria",
						ErrMsg:       "No data found",
						FunctionName: "streamData()",
						Event:        event,
						KeyTag:       &keyTag,
					})
					return serviceError(http.StatusNotFound, "No data found for the specified criteria")
				}

				// Close the JSON array or XML document
				if params.Format == "xml" {
					responseBuffer.Write([]byte("</data><status><result>success</result></status></rows>")) // Temp patch, php has it but is useless in Falcon
				} else if params.Format == "json" {
					// Close the JSON array and data wrapper
					responseBuffer.Write([]byte(`],"status": {"result": "success"}}`))
				} else {
					responseBuffer.Write([]byte("]"))
				}

				// Now that we've confirmed we have data, set the headers and write the response
				c.Response().Header().Set(echo.HeaderContentDisposition, fmt.Sprintf("attachment; filename=%s", filename))
				c.Response().Header().Set(echo.HeaderCacheControl, "no-cache")

				switch params.Format {
				case "xml":
					c.Response().Header().Set(echo.HeaderContentType, echo.MIMEApplicationXMLCharsetUTF8)
				case "csv":
					c.Response().Header().Set(echo.HeaderContentType, "text/csv")
				default:
					c.Response().Header().Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
				}

				c.Response().WriteHeader(http.StatusOK)
				c.Response().Write(responseBuffer.Bytes())

				handleLogError(models.LogErrorParams{
					LogLabel:     logResponseLabel,
					Key:          params.Key,
					QParams:      params,
					FunctionName: "streamData()",
					Event:        event,
					KeyTag:       &keyTag,
					Data:         `{"data_source": "falcon", "clicks": "` + fmt.Sprintf("%d", dataCount) + `"}`,
				})

				return nil
			}

			// Increment the data counter
			dataCount++

			// Handle comma for JSON format
			if !firstItem && params.Format != "xml" {
				responseBuffer.Write([]byte(","))
			}
			firstItem = false

			// Write data to the buffer instead of directly to the response
			if err := writeDataToBuffer(&responseBuffer, data, params, keyTag); err != nil {
				handleLogError(models.LogErrorParams{
					LogLabel:     logErrorLabel,
					Key:          params.Key,
					QParams:      params,
					LogMsg:       err.Error(),
					ErrMsg:       "Error writing response",
					FunctionName: "streamData()",
					Event:        event,
					KeyTag:       &keyTag,
				})
				return serviceError(http.StatusInternalServerError, "Error writing response")
			}

		case err := <-errChan:
			if err != nil {
				// Log the error and stop further processing
				handleLogError(models.LogErrorParams{
					LogLabel:     logErrorLabel,
					Key:          params.Key,
					QParams:      params,
					LogMsg:       err.Error(),
					ErrMsg:       "Error retrieving data from the database",
					FunctionName: "streamData()",
					Event:        event,
					KeyTag:       &keyTag,
				})
				return serviceError(http.StatusInternalServerError, "Error retrieving data from the database")
			}
		}
	}
}

// Helper function to write data to a buffer instead of directly to the response
func writeDataToBuffer(buffer *bytes.Buffer, data models.CpcTrsxJobredirect, params models.DownloadRequestParams, keyTag string) error {
	var row []byte
	var err error

	// Force to dont show redqid info if is not requested
	if params.Reqid == nil || *params.Reqid != "1" {
		data.JobReqid = ""
	}

	// Force to dont show redqid info if is not requested
	if params.Applydata == nil || *params.Applydata != "1" {
		data.ApplicationSession = ""
	}

	// Prepare formatted dates
	localDate := data.Unixtime.Format(dateTimeFormat)
	utcDate := data.UnixtimeUtc.Format(dateTimeFormat)

	switch params.Format {
	case "xml":
		xmlData := models.XmlOutput{
			ID:             data.OriginalId,
			JobID:          data.JobId,
			JobEmpcode:     data.JobEmpcode,
			JobTitle:       html.UnescapeString(data.JobTitle),
			JobLocation:    data.Joblocation,
			JobCountry:     data.JobCountry,
			JobURL:         data.JobUrl,
			Host:           data.Host,
			UserAgent:      data.Useragent,
			Date:           localDate,
			DateUTC:        utcDate,
			BilledPPC:      data.BilledPpc,
			BilledPPCCad:   data.BilledPpcCad,
			BilledWarning:  data.BilledWarningGroup,
			BilledCurrency: data.BilledCurrency,
			IPHash:         data.IpHash,
			CampaignName:   data.CampaignName,
			IPMask:         data.IpMask,
			JobReqid:       data.JobReqid,
			ApplicationId:  data.ApplicationSession,
		}

		row, err = xml.MarshalIndent(xmlData, "", "    ")
		if err != nil {
			return err
		}
	default:
		jsonData := models.JsonOutput{
			ID:             data.OriginalId,
			JobID:          data.JobId,
			JobEmpcode:     data.JobEmpcode,
			JobTitle:       html.UnescapeString(data.JobTitle),
			JobLocation:    data.Joblocation,
			JobCountry:     data.JobCountry,
			JobURL:         data.JobUrl,
			Host:           data.Host,
			UserAgent:      data.Useragent,
			Date:           localDate,
			DateUTC:        utcDate,
			BilledPPC:      data.BilledPpc,
			BilledPPCCad:   data.BilledPpcCad,
			BilledWarning:  data.BilledWarningGroup,
			BilledCurrency: data.BilledCurrency,
			IPHash:         data.IpHash,
			CampaignName:   data.CampaignName,
			IPMask:         data.IpMask,
			JobReqid:       data.JobReqid,
			ApplicationId:  data.ApplicationSession,
		}

		// Use an encoder with HTML escaping disabled.
		var buf bytes.Buffer
		enc := json.NewEncoder(&buf)
		enc.SetEscapeHTML(false)
		if err = enc.Encode(jsonData); err != nil {
			return err
		}
		// Remove the trailing newline that Encode adds.
		row = bytes.TrimRight(buf.Bytes(), "\n")
	}

	buffer.Write(row)
	buffer.Write([]byte("\n"))
	return nil
}

func writeResponseData(c echo.Context, data models.CpcTrsxJobredirect, params models.DownloadRequestParams, keyTag string) error {
	var row []byte
	var err error

	// Force to dont show redqid info if is not requested
	if params.Reqid == nil || *params.Reqid != "1" {
		data.JobReqid = ""
	}

	// Force to dont show redqid info if is not requested
	if params.Applydata == nil || *params.Applydata != "1" {
		data.ApplicationSession = ""
	}

	// Prepare formatted dates
	localDate := data.Unixtime.Format(dateTimeFormat)
	utcDate := data.UnixtimeUtc.Format(dateTimeFormat)

	switch params.Format {
	case "xml":
		xmlData := models.XmlOutput{
			ID:             data.OriginalId,
			JobID:          data.JobId,
			JobEmpcode:     data.JobEmpcode,
			JobTitle:       html.UnescapeString(data.JobTitle),
			JobLocation:    data.Joblocation,
			JobCountry:     data.JobCountry,
			JobURL:         data.JobUrl,
			Host:           data.Host,
			UserAgent:      data.Useragent,
			Date:           localDate,
			DateUTC:        utcDate,
			BilledPPC:      data.BilledPpc,
			BilledPPCCad:   data.BilledPpcCad,
			BilledWarning:  data.BilledWarningGroup,
			BilledCurrency: data.BilledCurrency,
			IPHash:         data.IpHash,
			CampaignName:   data.CampaignName,
			IPMask:         data.IpMask,
			JobReqid:       data.JobReqid,
			ApplicationId:  data.ApplicationSession,
		}

		row, err = xml.MarshalIndent(xmlData, "", "    ")
		if err != nil {
			return err
		}
	default:

		jsonData := models.JsonOutput{
			ID:             data.OriginalId,
			JobID:          data.JobId,
			JobEmpcode:     data.JobEmpcode,
			JobTitle:       html.UnescapeString(data.JobTitle),
			JobLocation:    data.Joblocation,
			JobCountry:     data.JobCountry,
			JobURL:         data.JobUrl,
			Host:           data.Host,
			UserAgent:      data.Useragent,
			Date:           localDate,
			DateUTC:        utcDate,
			BilledPPC:      data.BilledPpc,
			BilledPPCCad:   data.BilledPpcCad,
			BilledWarning:  data.BilledWarningGroup,
			BilledCurrency: data.BilledCurrency,
			IPHash:         data.IpHash,
			CampaignName:   data.CampaignName,
			IPMask:         data.IpMask,
			JobReqid:       data.JobReqid,
			ApplicationId:  data.ApplicationSession,
		}

		// Use an encoder with HTML escaping disabled.
		var buf bytes.Buffer
		enc := json.NewEncoder(&buf)
		enc.SetEscapeHTML(false)
		if err = enc.Encode(jsonData); err != nil {
			return err
		}
		// Remove the trailing newline that Encode adds.
		row = bytes.TrimRight(buf.Bytes(), "\n")
	}

	if _, err := c.Response().Write(row); err != nil {
		return err
	}
	c.Response().Write([]byte("\n"))
	c.Response().Flush()
	return nil
}
