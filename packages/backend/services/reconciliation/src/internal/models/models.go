package models

import (
	"encoding/xml"
	"talent/reconciliation/src/internal/interfaces"
	"time"

	"gorm.io/gorm"
)

type CpcTrsxJobredirect struct {
	OriginalId         string    `json:"id" gorm:"column:original_id"`
	JobId              string    `json:"jobid" gorm:"index:,type:hash"`
	JobEmpcode         string    `json:"job_empcode"`
	JobTitle           string    `json:"job_title"`
	Joblocation        string    `json:"joblocation"`
	JobCountry         string    `json:"job_country"`
	JobUrl             string    `json:"job_url"`
	Host               string    `json:"host"`
	Useragent          string    `json:"useragent"`
	Unixtime           time.Time `json:"unixtime" gorm:"type:timestamptz;column:unixtime"`
	UnixtimeUtc        time.Time `json:"unixtime_utc" gorm:"type:timestamp;column:unixtime_utc"`
	BilledPpc          *int64    `json:"billed_ppc" gorm:"type:numeric"`
	BilledPpcCad       *int64    `json:"billed_ppc_cad" gorm:"type:numeric"`
	BilledWarningGroup *string   `json:"billed_warning"`
	BilledCurrency     *string   `json:"billed_currency"`
	IpHash             string    `json:"ip_hash"`
	CampaignName       *string   `json:"campaign_name"`
	IpMask             string    `json:"ip_mask"`
	JobReqid           string    `json:"job_reqid"`
	ApplicationSession string    `json:"application_session"`
	CampaignId         int64     `json:"campaign_id"`
}

type AppContext struct {
	Db         *gorm.DB
	HttpClient interfaces.HttpClientInterface
}

type DownloadRequestParams struct {
	Applydata        *string `query:"applydata"`
	DateFrom         int64   `query:"dateFrom"`
	DateTo           int64   `query:"dateTo"`
	Format           string  `query:"format"`
	Key              string  `query:"key"`
	Reqid            *string `query:"reqid"`
	Uri              string  `json:"uri,omitempty"`
	SkipPhp          *string `json:"skipPhp,omitempty"`
	SkipLimit        *string `query:"skipLimit,omitempty"`
	DisplayReqid     *bool
	DisplayApplyData *bool
	Debug            *bool
}

type ErrorOnProcess struct {
	HttpCode   int
	Message    string
	LogMessage string
}

type ErrorResponse struct {
	StatusCode    int      `json:"statusCode"`
	StatusMessage string   `json:"statusMessage"`
	Success       bool     `json:"success"`
	Errors        []string `json:"errors"`
}

type GetTagInfoResponse struct {
	Payload []string `json:"payload"`
}

type DataStream struct {
	Id                 string `json:"id"`
	Jobid              string `json:"jobid"`
	JobEmpcode         string `json:"job_empcode"`
	JobTitle           string `json:"job_title"`
	Joblocation        string `json:"joblocation"`
	JobEmpname         string `json:"job_empname"`
	JobCountry         string `json:"job_country"`
	JobUrl             string `json:"job_url"`
	Host               string `json:"host"`
	Useragent          string `json:"useragent"`
	Date               string `json:"date"`
	DateUTC            string `json:"dateUTC"`
	BilledPpc          int64  `json:"billed_ppc"`
	BilledPpcCad       int64  `json:"billed_ppc_cad"`
	BilledWarning      string `json:"billed_warning"`
	BilledWarningGroup string `json:"billed_warning_group"`
	BilledCurrency     string `json:"billed_currency"`
	IpHash             string `json:"ip_hash"`
	JobCampaignId      string `json:"job_campaign_id"`
	JobCampaignName    string `json:"job_campaign_name"`
	IpMask             string `json:"ip_mask"`
	ApplicationSession string `json:"application_session"`
	JobReqid           string `json:"job_reqid"`
}

type JsonOutput struct {
	ID             string  `json:"id"`
	JobID          string  `json:"jobid"`
	JobEmpcode     string  `json:"job_empcode"`
	JobTitle       string  `json:"job_title"`
	JobLocation    string  `json:"joblocation"`
	JobCountry     string  `json:"job_country"`
	JobURL         string  `json:"job_url"`
	Host           string  `json:"host"`
	UserAgent      string  `json:"useragent"`
	Date           string  `json:"date"`
	DateUTC        string  `json:"dateUTC"`
	BilledPPC      *int64  `json:"billed_ppc"`
	BilledPPCCad   *int64  `json:"billed_ppc_cad"`
	BilledWarning  *string `json:"billed_warning"`
	BilledCurrency *string `json:"billed_currency"`
	IPHash         string  `json:"ip_hash"`
	CampaignName   *string `json:"campaign_name"`
	IPMask         string  `json:"ip_mask"`
	JobReqid       string  `json:"job_reqid,omitempty"`
	ApplicationId  string  `json:"application_id,omitempty"`
}

type XmlOutput struct {
	XMLName        xml.Name `xml:"click"`
	ID             string   `xml:"id"`
	JobID          string   `xml:"jobid"`
	JobEmpcode     string   `xml:"job_empcode"`
	JobTitle       string   `xml:"job_title"`
	JobLocation    string   `xml:"joblocation"`
	JobCountry     string   `xml:"job_country"`
	JobURL         string   `xml:"job_url"`
	Host           string   `xml:"host"`
	UserAgent      string   `xml:"useragent"`
	Date           string   `xml:"date"`
	DateUTC        string   `xml:"dateUTC"`
	BilledPPC      *int64   `xml:"billed_ppc"`
	BilledPPCCad   *int64   `xml:"billed_ppc_cad"`
	BilledWarning  *string  `xml:"billed_warning"`
	BilledCurrency *string  `xml:"billed_currency"`
	IPHash         string   `xml:"ip_hash"`
	CampaignName   *string  `xml:"campaign_name"`
	IPMask         string   `xml:"ip_mask"`
	JobReqid       string   `xml:"job_reqid,omitempty"`
	ApplicationId  string   `xml:"application_id,omitempty"`
}

type ClickData struct {
	XMLName        xml.Name  `xml:"click"`
	ID             string    `xml:"id"`
	JobID          string    `xml:"jobid"`
	JobEmpcode     string    `xml:"job_empcode"`
	JobTitle       string    `xml:"job_title"`
	JobLocation    string    `xml:"joblocation"`
	JobCountry     string    `xml:"job_country"`
	JobURL         string    `xml:"job_url"`
	Host           string    `xml:"host"`
	UserAgent      string    `xml:"useragent"`
	Date           time.Time `xml:"date"`
	DateUTC        time.Time `xml:"dateUTC"`
	BilledPPC      *int64    `xml:"billed_ppc"`
	BilledPPCCad   *int64    `xml:"billed_ppc_cad"`
	BilledWarning  *string   `xml:"billed_warning"`
	BilledCurrency *string   `xml:"billed_currency"`
	IPHash         string    `xml:"ip_hash"`
	CampaignName   *string   `xml:"campaign_name"`
	IPMask         string    `xml:"ip_mask"`
}

type XMLRoot struct {
	XMLName xml.Name   `xml:"rows"`
	Data    []ClickRow `xml:"data"`
}

type ClickRow struct {
	Click ClickData `xml:"click"`
}

type GetTagActiveResponse struct {
	Tags []TagActive `json:"tags"`
}

type TagActive struct {
	Name          string `json:"name"`
	SftpSiteID    string `json:"sftpSiteId"`
	MappingFields string `json:"mappingFields"`
}

type CheckSumResult struct {
	Clicks       int64   `gorm:"column:clicks" json:"clicks"`
	SumBilledCad float64 `gorm:"column:sum_billed_cad" json:"sum_billed_cad"`
	SumBilled    float64 `gorm:"column:sum_billed" json:"sum_billed"`
}

// TeamsMessage represents the structure for a Teams message
type TeamsMessage struct {
	Context    string `json:"@context"`
	Type       string `json:"@type"`
	ThemeColor string `json:"themeColor"`
	Title      string `json:"title"`
	Text       string `json:"text"`
}

type EventTracker struct {
	Type     string      `json:"event_type"`
	Category string      `json:"category"`
	Event    interface{} `json:"event"`
}

type TrackingEvent struct {
	Id       string `json:"id"`
	Key      string `json:"key"`
	Date     string `json:"date"`
	DateFrom string `json:"date_from"`
	DateTo   string `json:"date_to"`
	Unixtime int    `json:"unixtime"`
	Data     any    `json:"data,omitempty"`
	Debug    int    `json:"debug"`
	Uri      string `json:"uri"`
}

type LogResponse struct {
	Result    string `json:"result"`     // Indicates if the operation succeeded or failed (e.g., "success" or "fail")
	KeyTag    string `json:"key_tag"`    //
	LogMsg    string `json:"log_msg"`    // Message for logging purposes
	LogDetail string `json:"log_detail"` // Detailed log message
	ErrMsg    string `json:"err_msg"`    // Error message for the response
	Message   string `json:"message"`    // Additional error message for the client
	Function  string `json:"function"`   // The function where the error occurred
}

type LogErrorParams struct {
	Result       string
	LogLabel     string
	Key          string
	QParams      DownloadRequestParams
	LogMsg       string
	ErrMsg       string
	FunctionName string
	Data         any `json:"data,omitempty"`
	Event        *TrackingEvent
	KeyTag       *string
}

// -------------------------------------------------------------------------------------------------------------------------------------------
//
//	PHP API
//
// -------------------------------------------------------------------------------------------------------------------------------------------
type APIResponse struct {
	Data          []any    `json:"data,omitempty"`
	Status        *Status  `json:"status,omitempty"`
	Errors        []string `json:"errors,omitempty"`
	StatusCode    int      `json:"statusCode,omitempty"`
	StatusMessage string   `json:"statusMessage,omitempty"`
	Success       *bool    `json:"success,omitempty"`
}

type Status struct {
	Result string `json:"result"`
}

// -------------------------------------------------------------------------------------------------------------------------------------------
