package service

import (
	"context"
	"encoding/csv"
	"encoding/json"
	"fmt"
	"io"
	"log/slog"
	"net/http"
	"net/url"
	"os"
	"talent/reconciliation/src/internal/cache"
	"talent/reconciliation/src/internal/interfaces"
	"talent/reconciliation/src/internal/models"
	"talent/reconciliation/src/pkg/utils"
	"time"

	"gorm.io/gorm"
)

type (
	Reconciliation interface {
		ValidateParameters() models.ErrorOnProcess
		ValidateKeyTag() (string, models.ErrorOnProcess)
		GetTagInfo(tag string) ([]string, models.ErrorOnProcess)
		GetTagsSftpActive() (models.GetTagActiveResponse, models.ErrorOnProcess)
		GetCpcTrsxData(feedcodes []string) (<-chan models.CpcTrsxJobredirect, <-chan error)
		CheckSumValidation(startTime, endTime time.Time, feedcodes []string) (*models.CheckSumResult, error)
		CheckCacheLimit(ctx context.Context, keyTag string) error
	}

	reconciliation struct {
		ctx        context.Context
		db         *gorm.DB
		httpClient interfaces.HttpClientInterface
		qParams    models.DownloadRequestParams
		cache      cache.Cache
	}
)

func NewReconciliation(ctx context.Context, db *gorm.DB, httpClient interfaces.HttpClientInterface, qParams models.DownloadRequestParams, cache cache.Cache) Reconciliation {
	return &reconciliation{
		ctx:        ctx,
		db:         db,
		httpClient: httpClient,
		qParams:    qParams,
		cache:      cache,
	}
}

/*
* To validate each parameter in the request
*
 */
func (s *reconciliation) ValidateParameters() models.ErrorOnProcess {

	//_, span := s.oTel.StartSpan(s.ctx, "validate-parameters")
	//defer span.End()
	//span.SetAttributes(utils.TraceAttrKeyValue(s.qParams)...)

	if s.qParams.Format != "xml" {
		s.qParams.Format = "json"
	}

	if s.qParams.Applydata != nil && *s.qParams.Applydata == "1" {
		s.qParams.DisplayApplyData = new(bool)
		*s.qParams.DisplayApplyData = true
	}

	if s.qParams.Reqid != nil && *s.qParams.Reqid == "1" {
		s.qParams.DisplayReqid = new(bool)
		*s.qParams.DisplayReqid = true
	}

	// Validate required params
	requiredParameters := []struct {
		Name      string
		Value     interface{}
		ErrorCode int
	}{
		{"key", s.qParams.Key, 1},
		{"dateFrom", s.qParams.DateFrom, 5},
		{"dateTo", s.qParams.DateTo, 5},
	}

	// Iterate and validate each parameter
	for _, param := range requiredParameters {
		switch v := param.Value.(type) {
		case string:
			if v == "" {
				return utils.HandleValidationError(fmt.Sprintf("Parameter %s is empty", param.Name), param.ErrorCode, http.StatusBadRequest)
			}
		case int64:
			if v == 0 { // Explicitly check if dateFrom or dateTo is 0
				return utils.HandleValidationError(fmt.Sprintf("Parameter %s is missing or invalid", param.Name), param.ErrorCode, http.StatusBadRequest)
			}
		default:
			if param.Value == nil {
				return utils.HandleValidationError(fmt.Sprintf("Parameter %s is empty", param.Name), param.ErrorCode, http.StatusBadRequest)
			}
		}
	}

	// Specific validation rules
	firstDateValid := time.Now().AddDate(0, 0, -40).Unix()
	maximumDateSpan := 60 * 60 * 24 * 7
	twoHoursAgo := time.Now().Add(-2 * time.Hour).Unix()

	if s.qParams.DateFrom < firstDateValid {
		//slog.Error("DateFrom is lower than FirstDateValid", "d1", s.qParams.DateFrom, "d2", firstDateValid)
		return utils.HandleValidationError("DateFrom is lower than FirstDateValid", 6, http.StatusBadRequest, firstDateValid, s.qParams.DateFrom)
	}

	// Validate dateTo is after dateFrom
	if s.qParams.DateFrom >= s.qParams.DateTo {
		//slog.Error("DateFrom is greater than DateTo", "DateFrom", s.qParams.DateFrom, "DateTo", s.qParams.DateTo)
		return utils.HandleValidationError("DateFrom is greater than DateTo", 7, http.StatusBadRequest, s.qParams.DateFrom, s.qParams.DateTo)
	}
	// Validate date span does not exceed maximum
	if (s.qParams.DateTo - s.qParams.DateFrom) > int64(maximumDateSpan) {
		//slog.Error("Dateframe is greater than 7 days")
		return utils.HandleValidationError("Date range exceeds 7 days", 8, http.StatusBadRequest, s.qParams.DateTo, s.qParams.DateFrom)
	}

	// Validate dateTo is not in the last 2 hours
	if s.qParams.DateTo > twoHoursAgo {
		//slog.Error("DateTo is within the last 2 hours")
		return utils.HandleValidationError("DateTo is within the last 2 hours", 15, http.StatusBadRequest, s.qParams.DateTo, s.qParams.DateFrom)
	}

	// Flooring dates after all validations
	//s.qParams.DateFrom, s.qParams.DateTo = utils.FloorDateRange(s.qParams.DateFrom, s.qParams.DateTo)

	return models.ErrorOnProcess{HttpCode: 0, Message: ""}
}

func (s *reconciliation) ValidateKeyTag() (string, models.ErrorOnProcess) {

	//_, span := s.oTel.StartSpan(s.ctx, "extract-key-tag", trace.WithAttributes(attribute.String("keyTag", s.qParams.Key)))
	//defer span.End()

	tag, httpCode, errorCode := utils.ExtractTag(s.qParams.Key)

	if tag == "" {
		//span.SetStatus(codes.Error, fmt.Sprintf("Error extracting keyTag with errorCode %d and httpCode %d", errorCode, httpCode))
		//span.RecordError(fmt.Errorf(utils.ReturnErrorMsg(15)))

		return "", models.ErrorOnProcess{
			HttpCode:   httpCode,
			Message:    utils.ReturnErrorMsg(errorCode),
			LogMessage: "Error extracting keyTag",
		}
	}

	return tag, models.ErrorOnProcess{}
}

func (s *reconciliation) GetTagInfo(tag string) ([]string, models.ErrorOnProcess) {

	//_, span := s.oTel.StartSpan(s.ctx, "get-tag-info", trace.WithAttributes(attribute.String("tag", tag)))
	//defer span.End()

	tagFeedcodesURL := fmt.Sprintf("%s?tagName=%s", os.Getenv("TAG_FEEDCODES_ENDPOINT"), url.QueryEscape(tag))

	req, err := http.NewRequestWithContext(s.ctx, http.MethodGet, tagFeedcodesURL, nil)
	if err != nil {

		//span.SetStatus(codes.Error, fmt.Sprintf("HTTP Client Get failed %s?tagName=%s", os.Getenv("TAG_FEEDCODES_ENDPOINT"), tag))
		//span.RecordError(err)

		return []string{}, models.ErrorOnProcess{
			HttpCode:   http.StatusServiceUnavailable,
			Message:    utils.ReturnErrorMsg(14),
			LogMessage: fmt.Sprintf("HTTP Client - Invalid URL: %s", tagFeedcodesURL),
		}
	}
	resp, err := s.httpClient.Do(req)

	if err != nil {

		//span.SetStatus(codes.Error, fmt.Sprintf("HTTP Client Get failed %s?tagName=%s", os.Getenv("TAG_FEEDCODES_ENDPOINT"), tag))
		//span.RecordError(err)

		utils.SendTeamsNotification(os.Getenv("WEBHOOK_REC_API"), fmt.Sprintf("RECONCIALIATION API ERROR - File: reconciliation.go - Line: 181 - Error: GetTagInfo() HTTP Client Get failed %s", os.Getenv("TAG_FEEDCODES_ENDPOINT")))

		return []string{}, models.ErrorOnProcess{
			HttpCode:   http.StatusServiceUnavailable,
			Message:    utils.ReturnErrorMsg(14),
			LogMessage: fmt.Sprintf("HTTP Client Get failed: %s", tagFeedcodesURL),
		}
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return []string{}, models.ErrorOnProcess{
			HttpCode:   http.StatusServiceUnavailable,
			Message:    utils.ReturnErrorMsg(14),
			LogMessage: fmt.Sprintf("Could not read data from tag info endpoint: %s", err),
		}
	}

	if resp.StatusCode != http.StatusOK {

		//span.SetStatus(codes.Error, fmt.Sprintf("GetTagInfo error with statusCode %d and body %s", resp.StatusCode, string(body)))
		//span.RecordError(fmt.Errorf(string(body)))

		codeErrorMsg := 14
		if resp.StatusCode == http.StatusNotFound {
			codeErrorMsg = 4
		}
		return []string{}, models.ErrorOnProcess{
			HttpCode:   resp.StatusCode,
			Message:    utils.ReturnErrorMsg(codeErrorMsg),
			LogMessage: fmt.Sprintf("GetTagInfo endpoint responded with error %s - URL: %s", string(body), tagFeedcodesURL),
		}
	}

	var data models.GetTagInfoResponse
	if err := json.Unmarshal(body, &data); err != nil {

		//span.SetStatus(codes.Error, fmt.Sprintf("Unmarshal error %e", err))
		//span.RecordError(err)

		return []string{}, models.ErrorOnProcess{
			HttpCode:   http.StatusServiceUnavailable,
			Message:    utils.ReturnErrorMsg(14),
			LogMessage: fmt.Sprintf("Unmarshal error %s", err),
		}
	}

	//span.SetAttributes(attribute.String("response", string(body)))
	return data.Payload, models.ErrorOnProcess{}
}

func (s *reconciliation) GetTagsSftpActive() (models.GetTagActiveResponse, models.ErrorOnProcess) {

	//_, span := s.oTel.StartSpan(s.ctx, "get-tags-sftp-active", trace.WithAttributes())
	//defer span.End()

	req, _ := http.NewRequestWithContext(s.ctx, http.MethodGet, os.Getenv("TAGS_SFTP_ACTIVE_ENDPOINT"), nil)
	req.Header.Set("Content-Type", "application/json")
	resp, err := s.httpClient.Do(req)
	if err != nil {
		slog.Error("HTTP Client Get failed", "url", os.Getenv("TAGS_SFTP_ACTIVE_ENDPOINT"))
		utils.SendTeamsNotification(os.Getenv("WEBHOOK_REC_API"), fmt.Sprintf("RECONCIALIATION API ERROR - File: reconciliation.go - Line: 257 - Error: HTTP Client Get failed %s", os.Getenv("TAGS_SFTP_ACTIVE_ENDPOINT")))
		//span.SetStatus(codes.Error, fmt.Sprintf("HTTP Client Get failed %s", os.Getenv("TAGS_SFTP_ACTIVE_ENDPOINT")))
		//span.RecordError(err)

		return models.GetTagActiveResponse{}, models.ErrorOnProcess{
			HttpCode: http.StatusServiceUnavailable,
			Message:  utils.ReturnErrorMsg(14),
		}
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		slog.Error("io.ReadAll in GetTagsSftpActive", "err", err)

		return models.GetTagActiveResponse{}, models.ErrorOnProcess{
			HttpCode: http.StatusServiceUnavailable,
			Message:  utils.ReturnErrorMsg(14),
		}
	}

	if resp.StatusCode != http.StatusOK {
		slog.Error("GetTagInfo responded with error", "body", string(body), "url", ("TAGS_SFTP_ACTIVE_ENDPOINT"))

		//span.SetStatus(codes.Error, fmt.Sprintf("GetTagInfo error with statusCode %d and body %s", resp.StatusCode, string(body)))
		//span.RecordError(fmt.Errorf(string(body)))

		codeErrorMsg := 14
		if resp.StatusCode == http.StatusNotFound {
			codeErrorMsg = 4
		}
		return models.GetTagActiveResponse{}, models.ErrorOnProcess{
			HttpCode: resp.StatusCode,
			Message:  utils.ReturnErrorMsg(codeErrorMsg),
		}
	}

	// Temporary struct to extract only the required fields
	var rawResponse struct {
		Payload struct {
			Tags []models.TagActive `json:"tags"`
		} `json:"payload"`
	}

	if err := json.Unmarshal(body, &rawResponse); err != nil {
		slog.Error("Unmarshal error in GetTagsSftpActive", "err", err)

		//span.SetStatus(codes.Error, fmt.Sprintf("Unmarshal error %e", err))
		//span.RecordError(err)

		return models.GetTagActiveResponse{}, models.ErrorOnProcess{
			HttpCode: http.StatusServiceUnavailable,
			Message:  utils.ReturnErrorMsg(14),
		}
	}

	//span.SetAttributes(attribute.String("response", string(body)))

	// Extract only relevant fields
	return models.GetTagActiveResponse{Tags: rawResponse.Payload.Tags}, models.ErrorOnProcess{}
}

func (s *reconciliation) GetCpcTrsxData(feedcodes []string) (<-chan models.CpcTrsxJobredirect, <-chan error) {

	dataChan := make(chan models.CpcTrsxJobredirect)
	errChan := make(chan error, 1)

	go func() {
		defer close(dataChan)
		defer close(errChan)

		startTime := time.Unix(s.qParams.DateFrom, 0).UTC()
		endTime := time.Unix(s.qParams.DateTo, 0).UTC()

		rows, err := s.db.Model(&models.CpcTrsxJobredirect{}).
			Where("unixtime_utc >= ? AND unixtime_utc <= ? AND job_empcode IN ?", startTime, endTime, feedcodes).
			Select("original_id, job_id, job_empcode, job_title, joblocation, job_empname, job_country, job_url, host, useragent, unixtime_utc AT TIME ZONE 'UTC' AT TIME ZONE 'America/Montreal' AS unixtime, unixtime_utc, billed_ppc, billed_ppc_cad, billed_warning, billed_warning_group, billed_currency, ip_hash, campaign_name, campaign_id, ip_mask, job_reqid, application_session").
			Rows()
		if err != nil {
			errChan <- err
			return
		}
		defer rows.Close()

		for rows.Next() {
			var cpcTrsxData models.CpcTrsxJobredirect
			if err := s.db.ScanRows(rows, &cpcTrsxData); err != nil {
				errChan <- err
				return
			}
			dataChan <- cpcTrsxData
		}

	}()

	return dataChan, errChan
}

func writeCSVToFile(filePath string, dataChan <-chan models.CpcTrsxJobredirect) error {
	file, err := os.Create(filePath)
	if err != nil {
		return fmt.Errorf("failed to create CSV file: %w", err)
	}
	defer file.Close()

	writer := csv.NewWriter(file)
	defer writer.Flush()

	// Write CSV headers
	writer.Write([]string{"Job ID", "Title", "Location", "Country", "URL"})

	for data := range dataChan {
		err := writer.Write([]string{data.JobId, data.JobTitle, data.Joblocation, data.JobCountry, data.JobUrl})
		if err != nil {
			return fmt.Errorf("failed to write CSV row: %w", err)
		}
	}

	return nil
}

func (s *reconciliation) CheckSumValidation(startTime, endTime time.Time, feedcodes []string) (*models.CheckSumResult, error) {

	startTimeStr := startTime.Format("2006-01-02 15:04:05")
	endTimeStr := endTime.Format("2006-01-02 15:04:05")

	var checkSumResult models.CheckSumResult

	// Execute the query
	err := s.db.Model(&models.CpcTrsxJobredirect{}).
		Select("count(*) as clicks, sum(billed_ppc_cad) as sum_billed_cad, sum(billed_ppc) as sum_billed").
		Where("unixtime_utc >= ? AND unixtime_utc <= ? AND job_empcode IN ?", startTimeStr, endTimeStr, feedcodes).
		Scan(&checkSumResult).Error
	if err != nil {
		slog.Error("error running validation query", "error", err)
		return &checkSumResult, fmt.Errorf("error running validation query: %w", err)
	}

	return &checkSumResult, nil
}

func (s *reconciliation) CheckCacheLimit(ctx context.Context, keyTag string) error {
	return cache.ControlCacheLimit(ctx, s.cache, keyTag)
}
