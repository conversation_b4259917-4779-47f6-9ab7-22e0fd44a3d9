package main

import (
	"context"
	"encoding/csv"
	"fmt"
	"log"
	"net/http"
	"os"
	"strconv"
	"strings"
	"talent/reconciliation/src/internal/cache"
	"talent/reconciliation/src/internal/models"
	"talent/reconciliation/src/internal/service"
	"talent/reconciliation/src/pkg"
	"talent/reconciliation/src/pkg/utils"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	s3Types "github.com/aws/aws-sdk-go-v2/service/s3/types"
)

var (
	loc *time.Location
)

// Initialize the cache once when the application starts
func init() {
	loc, _ = time.LoadLocation("America/Toronto")
}

func main() {
	utils.LoadEnv()
	ctx := context.Background()

	db, err := pkg.InitDBSimple()
	if err != nil {
		utils.SendTeamsNotification(os.Getenv("WEBHOOK_REC_API"), fmt.Sprintf("RECONCIALIATION API ERROR - SFTP - File: sftp/main.go - Line: 23 - Error: Database initialization failed (sftp): %v", err))
		log.Fatalf("Database initialization failed: %v", err)
	}

	httpClient := http.DefaultClient
	dayStart, dayEnd := getYesterdayTimestamps()

	qParams := models.DownloadRequestParams{
		DateFrom: dayStart.Unix(),
		DateTo:   dayEnd.Unix(),
		Format:   "json",
	}

	// Initialize the cache instance
	requestCache := cache.NewInMemoryCache(1 * time.Minute)

	recService := service.NewReconciliation(ctx, db, httpClient, qParams, requestCache)

	// Get active SFTP tags
	tagsActive, errInfo := recService.GetTagsSftpActive()
	if errInfo.Message != "" {
		utils.SendTeamsNotification(os.Getenv("WEBHOOK_REC_API"), fmt.Sprintf("RECONCIALIATION API ERROR - SFTP - File: sftp/main.go - Line: 23 - Error: fetching active SFTP tags: %s", errInfo.Message))
		log.Fatalf("Error fetching active SFTP tags: %s", errInfo.Message)
	}

	// Process each active tag
	for _, tag := range tagsActive.Tags {
		processTag(ctx, recService, tag, dayStart, dayEnd)
	}

	fmt.Println("-- SFTP process completed --")
}

// Fetch timestamps for the previous day
func getYesterdayTimestamps() (time.Time, time.Time) {
	yesterday := time.Now().In(loc).AddDate(0, 0, -1)
	dayStart := time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 0, 0, 0, 0, loc)
	dayEnd := dayStart.Add(24*time.Hour - time.Second)
	return dayStart, dayEnd
}

// Process a single tag
func processTag(ctx context.Context, recService service.Reconciliation, tag models.TagActive, dayStart, dayEnd time.Time) {
	fmt.Printf("Processing Tag: %s, SFTP Site ID: %s\n", tag.Name, tag.SftpSiteID)

	feedcodes, errInfo := recService.GetTagInfo(tag.Name)
	if errInfo.Message != "" {
		utils.SendTeamsNotification(os.Getenv("WEBHOOK_REC_API"), fmt.Sprintf("RECONCIALIATION API ERROR - SFTP - File: sftp/main.go - Line: 82 - Error: fetching feedcodes for tag %s: %s", tag.Name, errInfo.Message))
		log.Printf("Error fetching feedcodes for tag %s: %s", tag.Name, errInfo.Message)
		return
	}

	checkSumResult, err := recService.CheckSumValidation(dayStart, dayEnd, feedcodes)
	if err != nil || checkSumResult.SumBilled == 0 {
		utils.SendTeamsNotification(os.Getenv("WEBHOOK_REC_API"), fmt.Sprintf("RECONCIALIATION API ERROR - SFTP - File: sftp/main.go - Line: 89 - Error: CheckSum validation failed for tag %s: %v", tag.Name, err))
		log.Printf("CheckSum validation failed for tag %s: %v", tag.Name, err)
		log.Printf("CheckSum result: %+v", checkSumResult)
		return
	}

	csvData, err := streamDataToCSV(recService, feedcodes)
	if err != nil {
		utils.SendTeamsNotification(os.Getenv("WEBHOOK_REC_API"), fmt.Sprintf("RECONCIALIATION API ERROR - SFTP - File: sftp/main.go - Line: 97 - Error: streaming CSV data for tag %s: %v", tag.Name, err))
		log.Printf("Error streaming CSV data for tag %s: %v", tag.Name, err)
		return
	}

	fileName := fmt.Sprintf("%s/%s_%s.csv", tag.Name, tag.SftpSiteID, time.Now().AddDate(0, 0, -1).Format("01_02_2006"))
	if err := uploadCSVToS3(os.Getenv("S3_BUCKET_SFTP"), fileName, csvData); err != nil {
		utils.SendTeamsNotification(os.Getenv("WEBHOOK_REC_API"), fmt.Sprintf("RECONCIALIATION API ERROR - SFTP - File: sftp/main.go - Line: 105 - Error: Error uploading CSV for tag %s: %v", tag.Name, err))
		log.Printf("Error uploading CSV for tag %s: %v", tag.Name, err)
	}
}

// Stream data to CSV format
func streamDataToCSV(recService service.Reconciliation, feedcodes []string) (string, error) {
	dataChan, errChan := recService.GetCpcTrsxData(feedcodes)
	var rows []models.CpcTrsxJobredirect

	for {
		select {
		case data, ok := <-dataChan:
			if !ok {
				return convertToCSV(rows)
			}
			rows = append(rows, data)
		case err := <-errChan:
			if err != nil {
				return "", fmt.Errorf("Error fetching data: %v", err)
			}
		}
	}
}

// Convert job data to CSV format
func convertToCSV(data []models.CpcTrsxJobredirect) (string, error) {
	if len(data) == 0 {
		return "", fmt.Errorf("No data available")
	}

	var builder strings.Builder
	writer := csv.NewWriter(&builder)
	headers := []string{"campaign_id", "campaign_name", "user_agent", "timestamp", "click_cost",
		"job_id", "click_id", "job_url", "currency_code", "tracking_code", "cost_model"}

	if err := writer.Write(headers); err != nil {
		return "", fmt.Errorf("Failed to write CSV headers: %v", err)
	}

	for _, job := range data {
		clickCost := "0"
		if job.BilledPpc != nil && *job.BilledPpc != 0 {
			clickCost = fmt.Sprintf("%.2f", float64(*job.BilledPpc)/100.0)
		}

		row := []string{
			strconv.FormatInt(job.CampaignId, 10),
			stringOrEmpty(job.CampaignName),
			strings.NewReplacer(",", "-", ";", "-").Replace(job.Useragent),
			job.Unixtime.UTC().Format("01/02/2006 15:04"),
			clickCost,
			"\t" + job.JobId,
			job.OriginalId,
			job.JobUrl,
			stringOrEmpty(job.BilledCurrency),
			job.JobReqid,
			"cpc",
		}

		if err := writer.Write(row); err != nil {
			return "", fmt.Errorf("Failed to write CSV row: %v", err)
		}
	}

	writer.Flush()
	if err := writer.Error(); err != nil {
		return "", fmt.Errorf("Failed to flush CSV writer: %v", err)
	}

	return builder.String(), nil
}

// Helper function to handle nil strings
func stringOrEmpty(val *string) string {
	if val == nil {
		return ""
	}
	return *val
}

// Upload CSV file to S3
func uploadCSVToS3(bucketName, key, csvData string) error {
	cfg, err := config.LoadDefaultConfig(context.TODO())
	if err != nil {
		return fmt.Errorf("AWS config load failed: %v", err)
	}

	s3Client := s3.NewFromConfig(cfg)
	_, err = s3Client.PutObject(context.TODO(), &s3.PutObjectInput{
		Bucket:      aws.String(bucketName),
		Key:         aws.String(key),
		Body:        strings.NewReader(csvData),
		ACL:         s3Types.ObjectCannedACLPrivate,
		ContentType: aws.String("text/csv"),
	})

	if err != nil {
		return fmt.Errorf("Failed to upload CSV to S3: %v", err)
	}

	fmt.Printf("CSV uploaded to S3: %s/%s\n", bucketName, key)
	return nil
}
