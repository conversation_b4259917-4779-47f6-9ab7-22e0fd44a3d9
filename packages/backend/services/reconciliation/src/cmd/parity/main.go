package main

import (
	"context"
	"fmt"
	"log"
	"log/slog"
	"math"
	"os"
	"strings"
	"talent/reconciliation/src/internal/models"
	"talent/reconciliation/src/pkg"
	"talent/reconciliation/src/pkg/utils"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/athena"
	"github.com/aws/aws-sdk-go-v2/service/athena/types"
)

const (
	database       = "datalake"
	dateTimeFormat = "2006-01-02 15:04:05"
)

var (
	// Setting up destinations where querys will saved in s3.
	env            = getEnvOrDefault("ENVIRONMENT", "dev")
	outputLocation = fmt.Sprintf("s3://talent-%s-athena-queries-saved/reconcialiation-api/", strings.ToLower(env))
)

func main() {

	// Init Timing
	now := time.Now().UTC()

	fmt.Println("****************************** ENVIRONMENT: ", os.Getenv("ENVIRONMENT"))
	fmt.Println("****************************** outputLocation: ", outputLocation)

	// Load environment variables
	utils.LoadEnv()

	// Initialize application context
	appCtx := &models.AppContext{}

	// Create a context for the query execution
	ctx := context.Background()

	// Initialize DB
	if err := pkg.InitDB(appCtx, os.Getenv("DB_HOST_WRITER")); err != nil {
		utils.SendTeamsNotification(os.Getenv("WEBHOOK_REC_API"), fmt.Sprintf("RECONCILIATION API ERROR - CRON/parity - File: parity/main.go - Line: 44 - Error:Failed to initialize database %s", err))
		slog.Error("Failed to initialize database", "err", err)
		return
	}

	cfg, err := config.LoadDefaultConfig(context.TODO(), config.WithRegion("us-east-1"))
	if err != nil {
		utils.SendTeamsNotification(os.Getenv("WEBHOOK_REC_API"), fmt.Sprintf("RECONCILIATION API ERROR - CRON/parity - File: parity/main.go - Line: 48 - Error:unable to load SDK config, %v", err))
		slog.Error("unable to load SDK config, %v", err)
		return
	}

	client := athena.NewFromConfig(cfg)

	// Setting range - 30 minutes ago floored
	startStr, endStr := GetLast30MinWindow()

	// Display current time for reference
	fmt.Printf("Start time: %s\n", startStr)
	fmt.Printf("End time: %s\n", endStr)

	// ---------------------------------------------------------------------- GETTING DATA FROM ATHENA ----------------------------------------------------------------------
	athenaTotals, err := athenaGetCheckSumData(client, startStr, endStr)
	if err != nil {
		utils.SendTeamsNotification(os.Getenv("WEBHOOK_REC_API"), fmt.Sprintf("RECONCILIATION API ERROR - CRON/parity - File: parity/main.go - Line: 63 - Error: Getting data from Athena  %v", err))
		slog.Error("Error getting data from Athena: %v", err)
		return
	}

	if athenaTotals.Clicks == 0 {
		utils.SendTeamsNotification(os.Getenv("WEBHOOK_REC_API"), fmt.Sprintf("RECONCILIATION API ERROR - CRON/parity - File: parity/main.go - Line: 63 - Error: Getting data from Athena - no clicks found"))
		slog.Error("Error getting data from Athena - no clicks found")
		return
	}
	// ----------------------------------------------------------------------------------------------------------------------------------------------------------------------

	// ----------------------------------------------------------------- GETTING DATA FROM CPC_TRSX (POSGRESS) --------------------------------------------------------------
	postgresTotals, err := postgresCheckSumGetData(ctx, appCtx, startStr, endStr)
	if err != nil {
		utils.SendTeamsNotification(os.Getenv("WEBHOOK_REC_API"), fmt.Sprintf("RECONCILIATION API ERROR - CRON/parity - File: parity/main.go - Line: 72 - Error: Getting data from postgres: %v", err))
		slog.Error("Error getting data from postgres: %v", err)
		return
	}

	if postgresTotals.Clicks == 0 {
		utils.SendTeamsNotification(os.Getenv("WEBHOOK_REC_API"), fmt.Sprintf("RECONCILIATION API ERROR - CRON/parity - File: parity/main.go - Line: 79 - Error: Getting data from postgres - no clicks found"))
		slog.Error("Error getting data from postgres - no clicks found")
		return
	}
	// ----------------------------------------------------------------------------------------------------------------------------------------------------------------------

	// ---------------------------------------------------------------------- CHECKSUM AND RECONCILIATION ----------------------------------------------------------------------
	diff := athenaTotals.Clicks - postgresTotals.Clicks
	absDiff := math.Abs(float64(diff))
	base := float64(postgresTotals.Clicks)

	// Avoid division by zero
	var diffPercent float64
	if base != 0 {
		diffPercent = (absDiff / base) * 100
	}

	fmt.Printf("Absolute difference: %f (%.2f%%)\n", absDiff, diffPercent)

	if diffPercent > 0.5 {
		notificationMessage := fmt.Sprintf(`
				Time range: %s - %s

				Athena Total Clicks: **%d**
				Postgres Total Clicks: **%d**
				The difference is: **%d** - **(%.2f%%)**`,
			startStr, endStr, athenaTotals.Clicks-postgresTotals.Clicks, athenaTotals.Clicks, postgresTotals.Clicks, diffPercent)

		utils.SendTeamsNotificationWithFormat(os.Getenv("WEBHOOK_REC_API"), "Rec API difference [FALCON]", notificationMessage, true)
		slog.Error("Rec API difference [FALCON]: ", notificationMessage)
	}

	// Record the end time and calculate the elapsed time
	timeFinish := time.Now()
	elapsedTime := timeFinish.Sub(now)

	// Print elapsed time
	fmt.Printf("Total execution time: %v\n", elapsedTime)
	// ----------------------------------------------------------------------------------------------------------------------------------------------------------------------

}

func postgresCheckSumGetData(ctx context.Context, appCtx *models.AppContext, startStr string, endStr string) (*models.CheckSumResult, error) {

	var checkSumResult models.CheckSumResult

	// Execute the query
	err := appCtx.Db.Model(&models.CpcTrsxJobredirect{}).
		Select("count(*) as clicks, sum(billed_ppc_cad) as sum_billed_cad, sum(billed_ppc) as sum_billed").
		Where("unixtime_utc >= ? AND unixtime_utc <= ?", startStr, endStr).
		Scan(&checkSumResult).Error
	if err != nil {
		slog.Error("error running validation query", "error", err)
		return &checkSumResult, fmt.Errorf("error running validation query: %w", err)
	}

	return &checkSumResult, nil
}

func athenaGetData(client *athena.Client, startStr string, endStr string) (*models.CheckSumResult, error) {
	query := fmt.Sprintf(`SELECT *
		FROM datalake.jobredirect_event
		WHERE unixtime >= CAST('%s' AS TIMESTAMP(3))
		AND unixtime <= CAST('%s' AS TIMESTAMP(3))
		AND event_type = 'jobredirect'`, startStr, endStr)

	startQueryInput := &athena.StartQueryExecutionInput{
		QueryString: aws.String(query),
		QueryExecutionContext: &types.QueryExecutionContext{
			Database: aws.String(database),
		},
		ResultConfiguration: &types.ResultConfiguration{
			OutputLocation: aws.String(outputLocation),
		},
	}

	startQueryOutput, err := client.StartQueryExecution(context.TODO(), startQueryInput)
	if err != nil {
		return nil, fmt.Errorf("failed to start query execution: %v", err)
	}

	queryExecutionID := startQueryOutput.QueryExecutionId
	fmt.Printf("Query execution ID: %s\n", *queryExecutionID)

	queryExecutionStatus := athenaWaitForQueryToComplete(client, queryExecutionID)
	if queryExecutionStatus != types.QueryExecutionStateSucceeded {
		return nil, fmt.Errorf("query execution failed or was cancelled, final state: %s", queryExecutionStatus)
	}

	results, err := athenaGetQueryResults(client, queryExecutionID)
	if err != nil {
		return nil, fmt.Errorf("failed to get query results: %v", err)
	}

	fmt.Println("************************************************")
	fmt.Println(results)
	fmt.Println("************************************************")

	return &models.CheckSumResult{}, nil
}

func athenaGetCheckSumData(client *athena.Client, startStr string, endStr string) (*models.CheckSumResult, error) {

	// Parse endStr to get year, month, and day
	endTime, err := time.Parse("2006-01-02 15:04:05", endStr)
	if err != nil {
		return nil, fmt.Errorf("invalid endStr format: %v", err)
	}
	// Setting dates to optimize query
	year := endTime.Year()
	month := int(endTime.Month())
	day := endTime.Day()

	query := fmt.Sprintf(`SELECT COUNT(*) AS total_clicks,
		sum(billed_ppc) as billed_ppc, 
		sum(billed_ppc_cad) as billed_ppc_cad
		FROM datalake.jobredirect_event
		WHERE unixtime >= CAST('%s' AS TIMESTAMP(3))
		AND unixtime <= CAST('%s' AS TIMESTAMP(3))
		AND event_type = 'jobredirect'
		AND year = %d
		AND month = %d
		AND day = %d`, startStr, endStr, year, month, day)

	startQueryInput := &athena.StartQueryExecutionInput{
		QueryString: aws.String(query),
		QueryExecutionContext: &types.QueryExecutionContext{
			Database: aws.String(database),
		},
		ResultConfiguration: &types.ResultConfiguration{
			OutputLocation: aws.String(outputLocation),
		},
	}

	startQueryOutput, err := client.StartQueryExecution(context.TODO(), startQueryInput)
	if err != nil {
		return nil, fmt.Errorf("failed to start query execution: %v", err)
	}

	queryExecutionID := startQueryOutput.QueryExecutionId
	fmt.Printf("Query execution ID: %s\n", *queryExecutionID)

	queryExecutionStatus := athenaWaitForQueryToComplete(client, queryExecutionID)
	if queryExecutionStatus != types.QueryExecutionStateSucceeded {
		return nil, fmt.Errorf("query execution failed or was cancelled, final state: %s", queryExecutionStatus)
	}

	results, err := athenaGetQueryResults(client, queryExecutionID)
	if err != nil {
		return nil, fmt.Errorf("failed to get query results: %v", err)
	}

	return athenaFormatResult(results)
}

func athenaWaitForQueryToComplete(client *athena.Client, queryExecutionID *string) types.QueryExecutionState {
	for {
		getQueryExecutionInput := &athena.GetQueryExecutionInput{
			QueryExecutionId: queryExecutionID,
		}

		getQueryExecutionOutput, err := client.GetQueryExecution(context.TODO(), getQueryExecutionInput)
		if err != nil {
			log.Printf("failed to get query execution: %v", err)
			return types.QueryExecutionStateFailed
		}

		status := getQueryExecutionOutput.QueryExecution.Status.State
		if status == types.QueryExecutionStateSucceeded ||
			status == types.QueryExecutionStateFailed ||
			status == types.QueryExecutionStateCancelled {
			return status
		}

		// Wait before polling again
		fmt.Printf("Query status: %s. Waiting...\n", status)
		time.Sleep(5 * time.Second)
	}
}

func athenaGetQueryResults(client *athena.Client, queryExecutionID *string) (*athena.GetQueryResultsOutput, error) {
	getQueryResultsInput := &athena.GetQueryResultsInput{
		QueryExecutionId: queryExecutionID,
	}

	return client.GetQueryResults(context.TODO(), getQueryResultsInput)
}

func athenaFormatResult(results *athena.GetQueryResultsOutput) (*models.CheckSumResult, error) {
	if len(results.ResultSet.Rows) < 2 {
		return &models.CheckSumResult{}, nil // No data rows
	}

	dataRow := results.ResultSet.Rows[1].Data
	if len(dataRow) < 3 {
		return nil, fmt.Errorf("not enough columns returned")
	}

	var result models.CheckSumResult
	_, err := fmt.Sscanf(
		fmt.Sprintf("%s %s %s",
			getValue(dataRow[0].VarCharValue),
			getValue(dataRow[1].VarCharValue),
			getValue(dataRow[2].VarCharValue)),
		"%d %f %f",
		&result.Clicks,
		&result.SumBilledCad,
		&result.SumBilled,
	)

	if err != nil {
		return nil, fmt.Errorf("failed to parse result: %v", err)
	}

	return &result, nil
}

func getValue(val *string) string {
	if val == nil {
		return "0"
	}
	return *val
}

func GetLast30MinWindow() (string, string) {
	// Get current UTC time
	now := time.Now().UTC()
	//now := time.Now().UTC().Add(30 * time.Minute)

	// Determine which 30-minute window we're in
	currentMinute := now.Minute()
	var startTime time.Time

	if currentMinute < 30 {
		// We're in the 0-29 minute window, so previous window was from previous hour's 30-59
		startTime = time.Date(
			now.Year(),
			now.Month(),
			now.Day(),
			now.Hour()-1,
			30,
			0,
			0,
			time.UTC,
		)
	} else {
		// We're in the 30-59 minute window, so previous window was from current hour's 0-29
		startTime = time.Date(
			now.Year(),
			now.Month(),
			now.Day(),
			now.Hour(),
			0,
			0,
			0,
			time.UTC,
		)
	}

	// End time is 29:59 after start time
	endTime := startTime.Add(29*time.Minute + 59*time.Second)

	// Format times as strings
	startStr := startTime.Format(dateTimeFormat)
	endStr := endTime.Format(dateTimeFormat)

	return startStr, endStr
}

func getEnvOrDefault(key string, defaultVal string) string {
	val := os.Getenv(key)
	if val == "" {
		return defaultVal
	}
	return val
}
