package main

import (
	"context"
	"fmt"
	"log"
	"log/slog"
	"os"
	"talent/reconciliation/src/internal/models"
	"talent/reconciliation/src/pkg"
	"talent/reconciliation/src/pkg/utils"
	"time"
)

func main() {
	// Load environment variables
	utils.LoadEnv()

	// Initialize application context
	appCtx := &models.AppContext{}

	// Initialize DB
	if err := pkg.InitDB(appCtx, os.Getenv("DB_HOST_WRITER")); err != nil {
		utils.SendTeamsNotification(os.Getenv("WEBHOOK_REC_API"), fmt.Sprintf("RECONCIALIATION API ERROR - CRON/PARTITIONS - File: partitions/main.go - Line: 24 - Error:Failed to initialize database %s", err))
		slog.Error("Failed to initialize database", "err", err)
		os.Exit(1)
	}

	log.Println("Reconciliation partitions managment running...")

	// Create a context for the query execution
	//ctx := context.Background()
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	// Manage partitions separately
	if err := createPartitions(ctx, appCtx); err != nil {
		utils.SendTeamsNotification(os.Getenv("WEBHOOK_REC_API"), fmt.Sprintf("RECONCIALIATION API ERROR - CRON/PARTITIONS - File: partitions/main.go - Line: 37 - Error: Create partition failed %s", err))
		slog.Error("Failed to create partitions", "err", err)
	}

	if err := dropOldPartitions(ctx, appCtx); err != nil {
		utils.SendTeamsNotification(os.Getenv("WEBHOOK_REC_API"), fmt.Sprintf("RECONCIALIATION API ERROR - CRON/PARTITIONS - File: partitions/main.go - Line: 42 - Error: Failed to drop old partitions %s", err))
		slog.Error("Failed to drop old partitions", "err", err)
	}

	slog.Info("Partition management completed")
	fmt.Println("Partition management completed")
}

// Function to create partitions for the next 10 days
func createPartitions(ctx context.Context, appCtx *models.AppContext) error {
	today := time.Now().Truncate(24 * time.Hour)
	tomorrow := today.AddDate(0, 0, 1)

	var errors []error

	for i := 0; i < 10; i++ {
		nextDate := tomorrow.AddDate(0, 0, i)
		partitionName := fmt.Sprintf("daily_clicks_%s", nextDate.Format("2006_01_02"))

		// 1. Check if the partition already exists
		var exists bool
		checkQuery := `
			SELECT EXISTS (
				SELECT FROM pg_tables 
				WHERE schemaname = 'public' AND tablename = $1
			)
		`
		err := appCtx.Db.WithContext(ctx).Raw(checkQuery, partitionName).Scan(&exists).Error
		if err != nil {
			slog.Error("Error checking if partition exists", "err", err, "partition", partitionName)
			errors = append(errors, fmt.Errorf("check exists (%s): %w", partitionName, err))
			continue
		}

		if exists {
			slog.Warn("Partition already exists, skipping.", "name", partitionName)
			continue
		}

		// 2. Create partition query
		query := fmt.Sprintf(`
			CREATE TABLE %s PARTITION OF cpc_trsx_jobredirects
			FOR VALUES FROM ('%s 00:00:00') TO ('%s 00:00:00')
		`, partitionName,
			nextDate.Format("2006-01-02"),
			nextDate.AddDate(0, 0, 1).Format("2006-01-02"))

		result := appCtx.Db.WithContext(ctx).Exec(query)
		if result.Error != nil {
			slog.Error("Error creating partition", "err", result.Error, "partition", partitionName)
			errors = append(errors, fmt.Errorf("create partition (%s): %w", partitionName, result.Error))
			continue
		}

		slog.Info("Partition created", "name", partitionName)
	}

	// Concat errors in case we have them
	if len(errors) > 0 {
		errStr := "Errors creating partitions:\n"
		for _, e := range errors {
			errStr += "- " + e.Error() + "\n"
		}
		return fmt.Errorf(errStr)
	}

	return nil
}

// Function to drop old partitions older than 41 days
func dropOldPartitions(ctx context.Context, appCtx *models.AppContext) error {
	// Get cutoff date (41 days ago)
	today := time.Now().Truncate(24 * time.Hour)
	cutoffDate := today.AddDate(0, 0, -61)
	cutoffDateStr := cutoffDate.Format("2006_01_02")

	// Find old partitions
	var oldPartitions []string
	findQuery := `
		SELECT tablename 
		FROM pg_tables 
		WHERE tablename LIKE 'daily_clicks_%' 
		  AND substring(tablename from 14 for 10) < ?
	`

	result := appCtx.Db.WithContext(ctx).Raw(findQuery, cutoffDateStr).Scan(&oldPartitions)
	if result.Error != nil {
		return fmt.Errorf("failed to query old partitions: %w", result.Error)
	}

	// Drop old partitions
	for _, oldPartition := range oldPartitions {
		dropResult := appCtx.Db.WithContext(ctx).Exec(fmt.Sprintf("DROP TABLE IF EXISTS %s", oldPartition))
		if dropResult.Error != nil {
			return fmt.Errorf("failed to drop old partition %s: %w", oldPartition, dropResult.Error)
		}

		slog.Info("Dropped old partition", "name", oldPartition)
	}
	return nil
}
