package main

import (
	"log/slog"
	"net/http"
	"os"

	"talent/reconciliation/src/internal/api"
	"talent/reconciliation/src/internal/models"
	"talent/reconciliation/src/pkg"
	"talent/reconciliation/src/pkg/utils"

	"github.com/labstack/echo/v4"
)

var (
	appCtx *models.AppContext = &models.AppContext{}
)

func main() {
	//ctx := context.Background()

	utils.LoadEnv()

	// Initialize DB with reader-writer separation
	if err := pkg.InitDB(appCtx, os.Getenv("DB_HOST_READER")); err != nil {
		utils.SendTeamsNotification(os.Getenv("WEBHOOK_REC_API"), "RECONCIALIATION API ERROR - File: main.go - Line: 33 - Error: Failed to initialize database")
		slog.Error("Failed to initialize database", "err", err)
		os.Exit(1)
	}

	appCtx.HttpClient = http.DefaultClient

	e := echo.New()

	e.GET("/api/v1/reconciliation", api.ReconciliationData(appCtx))

	slog.Info("Reconciliation service running...")
	e.Logger.Fatal(e.Start(os.Getenv("HTTP_PORT")))
}
