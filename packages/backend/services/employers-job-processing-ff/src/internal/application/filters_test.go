package application

import (
	"employers-job-processing/src/internal/models"
	"employers-job-processing/src/internal/shared"
	"encoding/json"
	"log"
	"testing"
)

const (
	rawJob                       = `{"key":"job_created-{a}","event":"job_ppc_processed","headers":{"version":"1.0","event_type":"job_ppc_processed","content_type":"json","trace_source":"/data-in/services/pre-proccesing","retry":0,"ingestion":{"engine":"xml","crawl_id":"A001-1234-1234"}},"value":{"id":"263430","system_feedcode":"cbss-shaws-storemanager-randolphvt","system_scanid":263430,"system_crawlgroup":0,"system_status":0,"system_date_found":"2024-04-25","system_jobhash":"c336b605b2ff","system_hash_source_unique_job":"","system_hash_jobdesc_company":"","system_hash_title_company":"","system_hash_jobdesc_title":"","system_date_updated":null,"system_date_expired":null,"system_date_re_found":null,"system_updates_today":0,"system_job_source":"xml","system_apply_type":"","system_feed_type":"","system_company_name":"Shaw’s","system_flag_postprocess":0,"source_title":"Shop Sales","source_location":"Randolph, VT","source_location_raw":"","source_jobtype":"","source_company_name":"Shaw’s","source_link":"https://jobsexample.com/id=098765","source_jobdesc_html":"","source_jobdesc_text":"","source_date_posted":"","source_date_expired":"","source_apply_email":"","source_reqid":"","source_city":"Sharon","source_state":"Vermont","source_country":"ch","source_job_pixel":"","source_job_temp":"","source_ppc":null,"source_cpa":null,"source_job_budget":null,"source_salary":"$100.000 USD yearly","source_benefits":"","source_experience_required":"","source_logo":"","source_remote":"","source_apply_api_endpoint":"","source_apply_api_labels":[],"source_apply_client_jobid":"","source_client_tag":"","source_client_jobid":"","source_apply_extra_info":"","source_vendor_campaign":"","enrich_date_posted":null,"enrich_date_expired":null,"enrich_geo_city":"Sharon","enrich_geo_country":"ch","enrich_geo_region1":"Vermont","enrich_geo_region2":"Windsor County","enrich_geo_remote":"","enrich_geo_lat":0.0,"enrich_geo_lon":0.0,"enrich_geo_location_searched":"","enrich_jobdesc_length":0,"enrich_language":"","enrich_soc_onetsoc_code":"","enrich_jobtype":[],"enrich_jobtype_version":"","enrich_is_general_labor":0,"enrich_general_labor_version":"","enrich_company_name_normalized":"","enrich_company_id":"","enrich_company_industry":"","enrich_company_name":"Shaw’s"}}`
	expectedOneMatch             = "Expected 1 match, got none"
	expectedValueGotAnotherValue = "Expected %v, got %v"
	expectedZeroMatch            = "Expected 0 match, got 1"
)

var campaign models.Campaign
var job = NewJobMessage()

func init() {
	err := json.Unmarshal([]byte(shared.RawCampaign), &campaign)
	if err != nil {
		log.Fatalf("error unmarshalling campaign: %v", err)
	}
	err = json.Unmarshal([]byte(rawJob), &job)
	if err != nil {
		log.Fatalf("error unmarshalling job: %v", err)
	}
}

func TestFilterLocationSucceeds(t *testing.T) {
	filter := models.ByCountryFilter{
		Country: "ch",
	}
	locationFilter := LocationFilter{filter}
	_ = json.Unmarshal([]byte(rawJob), job)

	ok := locationFilter.Match(job)
	if !ok {
		t.Errorf(expectedOneMatch)
	}
}

func TestFilterLocationFail(t *testing.T) {
	filter := models.ByCountryFilter{
		Country: "ca",
	}
	locationFilter := LocationFilter{filter}
	_ = json.Unmarshal([]byte(rawJob), job)

	ok := locationFilter.Match(job)
	if ok {
		t.Errorf(expectedZeroMatch)
	}
}

func TestFilterByFeedCodeSucceeds(t *testing.T) {
	filter := models.ByFeedCodeFilter{
		FeedCode: "cbss-shaws-storemanager-randolphvt",
	}
	feedCodeFilter := FeedCodeFilter{filter}
	_ = json.Unmarshal([]byte(rawJob), job)

	ok := feedCodeFilter.Match(job)
	if !ok {
		t.Errorf(expectedOneMatch)
	}
}

func TestFilterByFeedCodeFails(t *testing.T) {
	filter := models.ByFeedCodeFilter{
		FeedCode: "cbss-shaws-storemanager-randolphvtaaaa",
	}
	feedCodeFilter := FeedCodeFilter{filter}
	_ = json.Unmarshal([]byte(rawJob), job)

	ok := feedCodeFilter.Match(job)
	if ok {
		t.Errorf(expectedZeroMatch)
	}
}

func TestFilterByTagSucceeds(t *testing.T) {
	filter := models.ByTagFilter{
		Tags:    []string{"Shaw’s", "Shaws"},
		Country: "ch",
	}
	tagFilter := TagFilter{filter}
	_ = json.Unmarshal([]byte(rawJob), job)
	job.SetSourceClientTag("Shaw’s")

	ok := tagFilter.Match(job)
	if !ok {
		t.Errorf(expectedOneMatch)
	}
}

func TestFilterByTagMultiCountrySucceeds(t *testing.T) {
	filter := models.ByTagFilter{
		Tags:          []string{"Shaw’s", "Shaws"},
		Country:       "ch",
		CountryFilter: "ch",
	}
	tagFilter := TagFilter{filter}
	_ = json.Unmarshal([]byte(rawJob), job)
	job.SetSourceClientTag("Shaw’s")

	ok := tagFilter.Match(job)
	if !ok {
		t.Errorf(expectedOneMatch)
	}

}
func TestFilterByTagMultiCountryAllSucceeds(t *testing.T) {
	filter := models.ByTagFilter{
		Tags:          []string{"Shaw’s", "Shaws"},
		Country:       "ch",
		CountryFilter: "All Countries",
	}
	tagFilter := TagFilter{filter}
	_ = json.Unmarshal([]byte(rawJob), job)
	job.SetSourceClientTag("Shaw’s")

	ok := tagFilter.Match(job)
	if !ok {
		t.Errorf(expectedOneMatch)
	}
}

func TestFilterByTagFails(t *testing.T) {
	filter := models.ByTagFilter{
		Tags: []string{"Shaw’s", "Shaws"},
	}
	tagFilter := TagFilter{filter}
	_ = json.Unmarshal([]byte(rawJob), job)
	job.SetSourceClientTag("Walmart")

	ok := tagFilter.Match(job)
	if ok {
		t.Errorf(expectedZeroMatch)
	}
}

func TestNormalize(t *testing.T) {
	source := "söùrcéß"
	expected := "sourcess"
	filter := models.ByFiltersFilter{
		Filters: []models.FilterFilter{},
	}
	filterFilter := FiltersFilter{filter}
	actual := filterFilter.normalize(source)
	if actual != expected {
		t.Errorf(expectedValueGotAnotherValue, expected, actual)
	}
}

func TestTryMatch(t *testing.T) {
	source := "source"
	target := "source"
	expected := true
	filter := models.ByFiltersFilter{
		Filters: []models.FilterFilter{},
	}
	filterFilter := FiltersFilter{filter}
	actual := filterFilter.tryMatch(source, target)
	if actual != expected {
		t.Errorf(expectedValueGotAnotherValue, expected, actual)
	}
}

func TestFailTryMatch(t *testing.T) {
	source := "source"
	target := "target"
	expected := false
	filter := models.ByFiltersFilter{
		Filters: []models.FilterFilter{},
	}
	filterFilter := FiltersFilter{filter}
	actual := filterFilter.tryMatch(source, target)
	if actual != expected {
		t.Errorf(expectedValueGotAnotherValue, expected, actual)
	}
}

func TestFilterByFiltersSucceed(t *testing.T) {
	filter := models.ByFiltersFilter{
		Filters: []models.FilterFilter{
			{
				Title: "Shop Sales",
			},
			{
				Empname: "Shaw’s",
			},
			{
				City: "Berlin",
			},
			{
				ReqId: "1234",
			},
		},
	}
	filtersFilter := FiltersFilter{filter}
	_ = json.Unmarshal([]byte(rawJob), job)
	job.SetSourceReqId("1234")
	job.SetEnrichGeoRegion1("Berlin")
	job.SetEnrichGeoRegion2("Berlin")
	job.SetEnrichGeoCity("Berlin")
	ok := filtersFilter.Match(job)
	if !ok {
		t.Errorf(expectedOneMatch)
	}
}

func TestFilterByFiltersSucceedEmpname(t *testing.T) {
	filter := models.ByFiltersFilter{
		Filters: []models.FilterFilter{
			{
				Empname: "Shaw’s",
			},
		},
	}
	filtersFilter := FiltersFilter{filter}
	_ = json.Unmarshal([]byte(rawJob), job)
	ok := filtersFilter.Match(job)
	if !ok {
		t.Errorf(expectedOneMatch)
	}
}

func TestFilterByFiltersSucceedReqid(t *testing.T) {
	filter := models.ByFiltersFilter{
		Filters: []models.FilterFilter{
			{
				ReqId: "1234",
			},
		},
	}
	filtersFilter := FiltersFilter{filter}
	_ = json.Unmarshal([]byte(rawJob), job)
	job.SetSourceReqId("1234")
	ok := filtersFilter.Match(job)
	if !ok {
		t.Errorf(expectedOneMatch)
	}
}

func TestFilterByFiltersSucceedCity(t *testing.T) {
	filter := models.ByFiltersFilter{
		Filters: []models.FilterFilter{
			{
				City:   "Berlin",
				Region: "Berlin",
			},
		},
	}
	filtersFilter := FiltersFilter{filter}
	_ = json.Unmarshal([]byte(rawJob), job)
	job.SetEnrichGeoRegion1("Berlin")
	job.SetEnrichGeoRegion2("Berlin")
	job.SetEnrichGeoCity("Berlin")
	ok := filtersFilter.Match(job)
	if !ok {
		t.Errorf(expectedOneMatch)
	}
}

func TestFilterByFiltersRawSucceed(t *testing.T) {
	filter := models.ByFiltersFilter{
		Filters: []models.FilterFilter{
			{
				Title:      "Shop Sales",
				RawFilter:  1,
				RawFilters: "title",
			},
			{
				Empname:    "Shaw’s",
				RawFilter:  1,
				RawFilters: "empname",
			},
		},
	}
	filtersFilter := FiltersFilter{filter}
	_ = json.Unmarshal([]byte(rawJob), job)

	ok := filtersFilter.Match(job)
	if !ok {
		t.Errorf(expectedOneMatch)
	}
}

func TestFilterByJobIDSucceed(t *testing.T) {
	filter := models.ByJobIDsFilter{
		JobIDs: []string{"263430"},
	}
	filtersFilter := JobIDsFilter{filter}
	_ = json.Unmarshal([]byte(rawJob), job)

	ok := filtersFilter.Match(job)
	if !ok {
		t.Errorf(expectedOneMatch)
	}
}
func TestFilterByJobIDFails(t *testing.T) {
	filter := models.ByJobIDsFilter{
		JobIDs: []string{"2634301"},
	}
	filtersFilter := JobIDsFilter{filter}
	_ = json.Unmarshal([]byte(rawJob), job)

	ok := filtersFilter.Match(job)
	if ok {
		t.Errorf(expectedZeroMatch)
	}
}

func TestFilterByFiltersFails(t *testing.T) {
	filter := models.ByFiltersFilter{
		Filters: []models.FilterFilter{
			{
				Title: "Shop Sales1ƒ",
			},
		},
	}
	filtersFilter := FiltersFilter{filter}
	_ = json.Unmarshal([]byte(rawJob), job)

	ok := filtersFilter.Match(job)
	if ok {
		t.Errorf(expectedZeroMatch)
	}
}

func TestFilterFactory(t *testing.T) {
	var campaign models.Campaign
	err := json.Unmarshal([]byte(shared.RawCampaign), &campaign)
	if err != nil {
		t.Error("error unmarshalling campaign", err)
	}
	cs := NewCampaignService(campaign)
	filters, err := cs.ParseJSONToFilters()
	if err != nil {
		t.Error("error parsing filters", err)
	}
	fs := FilterFactory(*cs, *filters)
	if fs == nil {
		t.Error("Expected a new FilterStrategy object, got nil")
	}
}

func TestFilterByReqId(t *testing.T) {
	filter := models.FilterFilter{
		ReqId: "1234",
	}
	_ = json.Unmarshal([]byte(rawJob), job)
	job.SetSourceReqId("12345")
	filtersFilter := FiltersFilter{}
	ok := filtersFilter.filterByReqId(job, filter)
	if !ok {
		t.Errorf(expectedOneMatch)
	}
	filter = models.FilterFilter{
		ReqId:      "1234",
		RawFilter:  1,
		RawFilters: "reqId",
	}
	job.SetSourceReqId("1234")
	ok = filtersFilter.filterByReqId(job, filter)
	if !ok {
		t.Errorf(expectedOneMatch)
	}
}

func TestFilterFactoryByFilter(t *testing.T) {
	var campaign models.Campaign
	err := json.Unmarshal([]byte(shared.RawCampaign), &campaign)
	if err != nil {
		t.Error("error unmarshalling campaign", err)
	}
	campaign.Type = string(models.ByFilters)
	cs := NewCampaignService(campaign)
	filters, err := cs.ParseJSONToFilters()
	if err != nil {
		t.Error("error parsing filters", err)
	}
	fs := FilterFactory(*cs, *filters)
	if fs == nil {
		t.Error("Expected a new FilterStrategy object, got nil")
	}
}

func TestFilterFactoryByJobs(t *testing.T) {
	var campaign models.Campaign
	err := json.Unmarshal([]byte(shared.RawCampaign), &campaign)
	if err != nil {
		t.Error("error unmarshalling campaign", err)
	}
	campaign.Type = string(models.ByJobs)
	cs := NewCampaignService(campaign)
	filters, err := cs.ParseJSONToFilters()
	if err != nil {
		t.Error("error parsing filters", err)
	}
	fs := FilterFactory(*cs, *filters)
	if fs == nil {
		t.Error("Expected a new FilterStrategy object, got nil")
	}
}

func TestFilterFactoryByTag(t *testing.T) {
	var campaign models.Campaign
	err := json.Unmarshal([]byte(shared.RawCampaign), &campaign)
	if err != nil {
		t.Error("error unmarshalling campaign", err)
	}
	campaign.Type = string(models.ByTags)
	cs := NewCampaignService(campaign)
	filters, err := cs.ParseJSONToFilters()
	if err != nil {
		t.Error("error parsing filters", err)
	}
	fs := FilterFactory(*cs, *filters)
	if fs == nil {
		t.Error("Expected a new FilterStrategy object, got nil")
	}
}

func TestFilterFactoryByLocation(t *testing.T) {
	var campaign models.Campaign
	err := json.Unmarshal([]byte(shared.RawCampaign), &campaign)
	if err != nil {
		t.Error("error unmarshalling campaign", err)
	}
	campaign.Type = string(models.ByCountry)
	cs := NewCampaignService(campaign)
	filters, err := cs.ParseJSONToFilters()
	if err != nil {
		t.Error("error parsing filters", err)
	}
	fs := FilterFactory(*cs, *filters)
	if fs == nil {
		t.Error("Expected a new FilterStrategy object, got nil")
	}
}

func TestFilterFactoryNil(t *testing.T) {
	var campaign models.Campaign
	err := json.Unmarshal([]byte(shared.RawCampaign), &campaign)
	if err != nil {
		t.Error("error unmarshalling campaign", err)
	}
	campaign.Type = ""
	cs := NewCampaignService(campaign)
	filters, err := cs.ParseJSONToFilters()
	if err != nil {
		t.Error("error parsing filters", err)
	}
	fs := FilterFactory(*cs, *filters)
	if fs != nil {
		t.Error("Expected a new FilterStrategy object, got nil")
	}
}
