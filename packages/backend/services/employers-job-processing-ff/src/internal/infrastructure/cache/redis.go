package cache

import (
	"crypto/tls"
	"fmt"
	"os"
	"strconv"
	"time"

	"github.com/go-redis/redis"
	"github.com/joho/godotenv"
)

type RedisCache struct {
	writeClient *redis.Client
	readClient  *redis.Client
	field       string
}

func NewRedisCache(field string) *RedisCache {
	godotenv.Load()
	poolSize, _ := strconv.Atoi(os.Getenv("REDIS_POOL_SIZE"))
	MinIdleConns, _ := strconv.Atoi(os.Getenv("REDIS_MIN_IDLE_CONNS"))

	// Primary client for write operations
	writeClient := redis.NewClient(&redis.Options{
		Addr:         os.Getenv("REDIS_HOST") + ":" + os.Getenv("REDIS_PORT"),
		Password:     os.Getenv("REDIS_PASS"),
		DB:           DB,
		PoolSize:     poolSize,
		MinIdleConns: MinIdleConns,
		PoolTimeout:  10 * time.Second,
		IdleTimeout:  5 * time.Hour,
		ReadTimeout:  5 * time.Second,
		TLSConfig: &tls.Config{
			InsecureSkipVerify: true,
		},
	})

	// Read replica client for read operations
	readClient := redis.NewClient(&redis.Options{
		Addr:         os.Getenv("REDIS_REPLICA_HOST") + ":" + os.Getenv("REDIS_REPLICA_PORT"),
		Password:     os.Getenv("REDIS_PASS"),
		DB:           DB,
		PoolSize:     poolSize,
		MinIdleConns: MinIdleConns,
		PoolTimeout:  10 * time.Second,
		IdleTimeout:  5 * time.Hour,
		ReadTimeout:  5 * time.Second,
		TLSConfig: &tls.Config{
			InsecureSkipVerify: true,
		},
	})

	return &RedisCache{
		writeClient: writeClient,
		readClient:  readClient,
		field:       field,
	}
}

func (r *RedisCache) Get(key string) ([]byte, error) {
	val, err := r.readClient.HGet(key, r.field).Bytes()
	if (err != nil && err != redis.Nil) || len(val) == 0 {
		return nil, err
	}
	return val, nil
}

func (r *RedisCache) Set(key string, value []byte, expiration *time.Duration) error {
	err := r.writeClient.HSet(key, r.field, value).Err()
	if err != nil {
		return err
	}

	if expiration != nil {
		err = r.writeClient.Expire(key, *expiration).Err()
		if err != nil {
			return err
		}
	}

	return nil
}

func (r *RedisCache) Close() {
	r.writeClient.Close()
	r.readClient.Close()
}

func (r *RedisCache) Delete(key string) error {
	// Check existence on read replica to reduce load on primary
	exists := r.readClient.HExists(key, r.field)
	if !exists.Val() {
		return fmt.Errorf("key not found")
	}
	// Use primary for delete operations
	return r.writeClient.Del(key).Err()
}

// Fallback to primary if replica is unavailable
func (r *RedisCache) GetWithFallback(key string) ([]byte, error) {
	val, err := r.readClient.HGet(key, r.field).Bytes()
	if err != nil && err != redis.Nil {
		val, err = r.writeClient.HGet(key, r.field).Bytes()
	}

	if (err != nil && err != redis.Nil) || len(val) == 0 {
		return nil, err
	}
	return val, nil
}

func (r *RedisCache) HealthCheck() (bool, bool) {
	primaryOK := r.writeClient.Ping().Err() == nil
	replicaOK := r.readClient.Ping().Err() == nil
	return primaryOK, replicaOK
}
