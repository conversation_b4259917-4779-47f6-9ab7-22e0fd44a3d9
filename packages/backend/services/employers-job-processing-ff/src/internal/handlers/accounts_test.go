package handlers

import (
	"bytes"
	"employers-job-processing/src/internal/infrastructure/cache"
	"employers-job-processing/src/internal/models"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
)

type mockLogger struct{}

func (m *mockLogger) Info(message string, args ...interface{}) {
	fmt.Println(args...)
}

func (m *mockLogger) Error(message string, args ...interface{}) {
	fmt.Println(args...)
}

func (m *mockLogger) Warn(message string, args ...interface{}) {
	fmt.Println(args...)
}

func (m *mockLogger) Debug(message string, args ...interface{}) {
	fmt.Println(args...)
}

type mockCache struct {
	store map[string][]byte
}

func (m *mockCache) Set(key string, value []byte) error {
	m.store[key] = value
	return nil
}

func (m *mockCache) Close() {
	fmt.Println("close")
}

func (m *mockCache) Get(key string) ([]byte, error) {
	if val, ok := m.store[key]; ok {
		return val, nil
	}
	return nil, errors.New("not found")
}

func (m *mockCache) Delete(key string) error {
	fmt.Println("delete")
	if key == "invalid-feedcode" {
		return errors.New("cache error")
	}
	return nil
}

type mockAccountService struct{}

func (m *mockAccountService) ProcessAccountDataToRedis(data []byte, c cache.Cache) error {
	dataString := string(data)

	if dataString == `{"id":2}` {
		return fmt.Errorf("unexpected data: %s", dataString)
	}
	return nil
}

func (m *mockAccountService) IsCampaignsDataValid([]models.Campaign) error {
	return nil
}

func (m *mockAccountService) DeleteAccountFromCache(feedcode string, c cache.Cache) error {
	if feedcode == `invalid` {
		return fmt.Errorf("unexpected data: %s", feedcode)
	}
	return nil
}

func createRequest(body interface{}) *http.Request {
	b, _ := json.Marshal(body)
	req := httptest.NewRequest("POST", "/accounts", bytes.NewReader(b))
	req.Header.Set("Content-Type", "application/json")
	return req
}

// TestAccountsCacheHandlerSuccess tests the successful handling of an account.
func TestAccountsCacheHandlerSuccess(t *testing.T) {
	log := &mockLogger{}
	c := &mockCache{store: make(map[string][]byte)}
	as := &mockAccountService{}

	handler := AddAccountToCacheHandler(log, c, as)

	account := models.Account{ID: 1}
	req := createRequest(account)
	rr := httptest.NewRecorder()

	handler.ServeHTTP(rr, req)

	if status := rr.Code; status != http.StatusOK {
		t.Errorf("handler returned wrong status code: got %v want %v", status, http.StatusOK)
	}

	expected := `{"message":"account and campaigns data processed"}`
	if rr.Body.String() != expected+"\n" { // Encoder adds a newline
		t.Errorf("Unexpected body: got %v want %v", rr.Body.String(), expected)
	}
}

// TestAccountsCacheHandlerDecodeFailure tests decode failure scenario.
func TestAccountsCacheHandlerDecodeFailure(t *testing.T) {
	log := &mockLogger{}
	c := &mockCache{store: make(map[string][]byte)}
	as := &mockAccountService{}

	handler := AddAccountToCacheHandler(log, c, as)

	req := httptest.NewRequest("POST", "/accounts", bytes.NewReader([]byte(`{"id": "bad type", "name": 123}`)))
	rr := httptest.NewRecorder()

	handler.ServeHTTP(rr, req)

	if status := rr.Code; status != http.StatusBadRequest {
		t.Errorf("handler returned wrong status code: got %v want %v", status, http.StatusBadRequest)
	}

	req = httptest.NewRequest("POST", "/accounts", bytes.NewReader([]byte(`{"id":2}`)))
	handler.ServeHTTP(rr, req)
	if condition := rr.Code; condition != http.StatusBadRequest {
		t.Errorf("handler returned wrong status code: got %v want %v", condition, http.StatusBadRequest)
	}
}

func TestDeleteAccountFromCacheSuccess(t *testing.T) {
	log := &mockLogger{}
	c := &mockCache{store: make(map[string][]byte)}
	as := &mockAccountService{}

	handler := RemoveAccountToCacheHandler(log, c, as)

	req := httptest.NewRequest("DELETE", "/accounts", bytes.NewReader([]byte(`{"feedcode": "1"}`)))
	rr := httptest.NewRecorder()

	handler.ServeHTTP(rr, req)

	if status := rr.Code; status != http.StatusOK {
		t.Errorf("handler returned wrong status code: got %v want %v", status, http.StatusOK)
	}

	expected := `{"message":"account deleted from cache"}`
	if rr.Body.String() != expected+"\n" { // Encoder adds a newline
		t.Errorf("Unexpected body: got %v want %v", rr.Body.String(), expected)
	}
}

func TestDeleteAccountFromCacheFailsBadRequest(t *testing.T) {
	log := &mockLogger{}
	c := &mockCache{store: make(map[string][]byte)}
	as := &mockAccountService{}

	handler := RemoveAccountToCacheHandler(log, c, as)
	rr := httptest.NewRecorder()

	req := httptest.NewRequest("DELETE", "/accounts", bytes.NewReader([]byte(`feedcode: ""`)))
	handler.ServeHTTP(rr, req)

	if status := rr.Code; status != http.StatusBadRequest {
		t.Errorf("handler returned wrong status code: got %v want %v", status, http.StatusInternalServerError)
	}
	expected := `{"message":"failed to decode request"}`
	if rr.Body.String() != expected+"\n" { // Encoder adds a newline
		t.Errorf("Unexpected body: got %v want %v", rr.Body.String(), expected)
	}
}

func TestDeleteAccountFromCacheFailsMissingFeedcode(t *testing.T) {
	log := &mockLogger{}
	c := &mockCache{store: make(map[string][]byte)}
	as := &mockAccountService{}

	handler := RemoveAccountToCacheHandler(log, c, as)
	rr := httptest.NewRecorder()

	req := httptest.NewRequest("DELETE", "/accounts", bytes.NewReader([]byte(`{"feedcode": ""}`)))
	handler.ServeHTTP(rr, req)

	if status := rr.Code; status != http.StatusBadRequest {
		t.Errorf("handler returned wrong status code: got %v want %v", status, http.StatusInternalServerError)
	}
	expected := `{"message":"feedcode is missing"}`
	if rr.Body.String() != expected+"\n" { // Encoder adds a newline
		t.Errorf("Unexpected body: got %v want %v", rr.Body.String(), expected)
	}
}

func TestDeleteAccountFromCacheFails(t *testing.T) {
	log := &mockLogger{}
	c := &mockCache{store: make(map[string][]byte)}
	as := &mockAccountService{}

	handler := RemoveAccountToCacheHandler(log, c, as)
	rr := httptest.NewRecorder()

	req := httptest.NewRequest("DELETE", "/accounts", bytes.NewReader([]byte(`{"feedcode": "invalid"}`)))
	handler.ServeHTTP(rr, req)

	if status := rr.Code; status != http.StatusInternalServerError {
		t.Errorf("handler returned wrong status code: got %v want %v", status, http.StatusInternalServerError)
	}
	expected := `{"message":"failed to delete account data"}`
	if rr.Body.String() != expected+"\n" { // Encoder adds a newline
		t.Errorf("Unexpected body: got %v want %v", rr.Body.String(), expected)
	}
}
