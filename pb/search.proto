syntax = "proto3";

package search;

import "geocode.proto";
import "adBidService.proto";

option go_package = "talent/pb/search";

service Search {
  rpc GetRankedJobs(GetRankedJobsRequest) returns (GetRankedJobsResponse) {}
}

// Request message
message GetRankedJobsRequest {
  string keyword = 1;
  ner nerInfo = 2;
  location location = 3;
  int32 radius = 4;
  string country = 5;
  int32 page = 6;
  string userId = 7;
  int32 size = 8;
  bool setRandom = 9;
  config config = 10;
  string requestSource = 11;
  string clientIP = 12;
  map<string, string> headers = 13;
  map<string, string> headersGrpc = 14;
  bool debug = 15;
  geocode.geoData geo = 16;
}

message ner {
  string occupation = 1;
  string jobtype = 2;
  bool remote = 3;
}

message config {
  string retrieverName = 1;
  bool trackHits = 2;
  collapse collapse = 3;
  highlight highlight = 4;
  parser parser = 5;
  script script = 6;
  string channel = 7;
  string partner = 8;
  jobsense jobsense = 9;
  repeated relatedItem relatedJobs = 10;
  string searchFields = 11;
  string searchFilters = 12;
  termsFilter termsFilter = 13;
  int32 minPpcu = 14;
  bool glBackfill = 15;
  bool includeRemote = 16;
  ranking ranking = 17;
  limitEmpnames limitEmpnames = 18;
  mustNotFilter mustNotFilter = 19;
  bool jobMetaSkip = 20;
  // double scoreThreshold = 21;
  SemsearchConfig semsearch_config = 22;

}

message mustNotFilter {
  string field = 1;
  bool active = 2;
  string value = 3;
}

message limitEmpnames {
  bool active = 1;
  int32 size = 2;
  bool includeTitle = 3;
}

message jobsense {
  bool  active = 1;
  int32 size = 2;
  string keyword = 3;
}

message ranking {
  ctrPrediction ctrPrediction = 1;
  string calculateNewScore = 2;
  bool interleave = 3;
}

message ctrPrediction {
  bool active = 1;
  string endpoint = 2;
}

message termsFilter {
  string field = 1;
  bool active = 2;
  string value = 3;
}

message relatedItem {
  string key = 1;
  string label = 2;
  int32 total = 3;
  string percent = 4;
  int32 this = 5;
}

message parser {
  bool ner = 1;
}

message collapse {
  int32 size = 1;
  string field = 2;
  bool active = 3;
}
message highlight {
  string field = 1;
  string openTag = 2;
  string closeTag = 3;
  bool active = 4;
}
message script {
  string expression = 1;
  string params = 2;
  bool active = 3;
}
//response message
message GetRankedJobsResponse {
	payload payload = 1;
	string err = 2;
}

message location {
  double lat = 1;
  double lon = 2;
}

message payload {
	bool            timeout         = 1;
	processingTimes processingTimes = 2;
	serviceErrors serviceErrors = 3;
	totalHits       totalHits       = 4;
	repeated hit    hits            = 5;
  string searchRequestID = 6;
  string query = 7;
}

message serviceErrors {
  	bool csRules = 1;
    bool ctrPrediction = 2;
    bool jobsService = 3;
    bool jobsenseService = 4;
    
}

message processingTimes {
    int32 total = 1;
    int32 esFetch = 2;
    int32 sonicInitialFetch = 3;
    int32 jobsenseSonicMultiQuery = 4;
    int32 jobsenseKeywordExpansion = 5;
    int32 glSonicQuery = 6;
    csRules csRules = 7;
    sortingTimes sortingTimes = 8;
    int32 ranking = 9;
    int32 ctrPrediction = 10;
    int32 getJobsData = 11;
}

message sortingTimes {
  calculateAndSort elasticTimes = 1;
  calculateAndSort sonicTimes = 2;
  calculateAndSort jobsenseTimes = 3;
  calculateAndSort glTimes = 4;
  int32 totalScoringTimes = 5;
}

message calculateAndSort {
  int32 total = 1;
  int32 fetchJobsMeta = 2;
  int32 sorting = 3;
}

message csRules {
    int32 total = 1;
    int32 initial = 2;
    int32 jobsenseSonicMultiQuery = 3;
    int32 glSonicQuery = 4;
}
message totalHits {
    int32 value = 1;
    string relation = 2;
    int32 returned = 3;
    pages pages = 4;
}

message pages {
  int32 current = 1;
  int32 totalPages = 2;
}

message hit {
    string index = 1;
    rankinInfo score = 2;
    string id = 3;
    int32 originalPpcU = 4;
    adBidService.BidResult bidResult = 5;
    querySource querySource = 6;
    repeated string snippets = 7;
    int32 shardId = 8;
    double lat = 9;
    double lon = 10;
    string empname = 11;
    string title = 12;
    string country = 13;
    string language = 14;
    string socOnetsocCode = 15;
}

message querySource {
  string initiator = 1;
  string engine=  2;
  string queryType = 3;
}

message rankinInfo {
    float finalScore = 1;
    float organicScore = 2;
    float ctrPrediction = 3;
}

enum InterleaveType {
  INTERLEAVE_TYPE_UNSPECIFIED = 0;
  INTERLEAVE_TYPE_PPC_FIRST = 1;
  INTERLEAVE_TYPE_ORGANIC_FIRST = 2;
}

enum RerankType {
  RERANK_TYPE_UNSPECIFIED = 0;
  RERANK_TYPE_NONE = 1;
  RERANK_TYPE_NORMALIZED_PPC_SCORE = 2;
  RERANK_TYPE_LLM_JUDGE = 3;
  RERANK_TYPE_LLM_JUDGE_FILTER_IRRELEVANT = 4;
  RERANK_TYPE_WEIGHTED_COMBINED = 5;
}

message SemsearchConfig {
  double score_threshold=1;
  InterleaveType interleave_type=2;
  RerankType rerank_type=3;
  int32 pool_size=4;
}